{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\SellerProducts.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useSellerAuth } from '../contexts/SellerAuthContext';\nimport { useError } from '../contexts/ErrorContext';\nimport { useLoading } from '../contexts/LoadingContext';\nimport { LoadingSpinner, LoadingButton } from '../components/LoadingComponents';\nimport { ErrorAlert } from '../components/ErrorComponents';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nconst SellerProducts = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    seller,\n    isAuthenticated,\n    getAuthHeaders\n  } = useSellerAuth();\n  const {\n    addError,\n    handleApiError\n  } = useError();\n  const {\n    isLoading,\n    withLoading\n  } = useLoading();\n  const [products, setProducts] = useState([]);\n  const [pagination, setPagination] = useState({});\n  const [filters, setFilters] = useState({\n    search: '',\n    status: '',\n    page: 1\n  });\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!isAuthenticated()) {\n      navigate('/seller/login');\n      return;\n    }\n    fetchProducts();\n  }, [isAuthenticated, navigate, filters]);\n  const fetchProducts = async () => {\n    try {\n      await withLoading('products_list', async () => {\n        const queryParams = new URLSearchParams({\n          page: filters.page,\n          per_page: 20,\n          ...(filters.search && {\n            search: filters.search\n          }),\n          ...(filters.status && {\n            status: filters.status\n          })\n        });\n        const response = await fetch(`${API_BASE_URL}/seller/products?${queryParams}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            ...getAuthHeaders()\n          }\n        });\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to load products');\n        }\n        const data = await response.json();\n        setProducts(data.data.products);\n        setPagination(data.data.pagination);\n      }, 'Loading products...');\n    } catch (error) {\n      handleApiError(error, {\n        action: 'fetch_products'\n      });\n    }\n  };\n  const handleDeleteProduct = async (productId, productName) => {\n    if (!window.confirm(`Are you sure you want to delete \"${productName}\"? This action cannot be undone.`)) {\n      return;\n    }\n    try {\n      await withLoading(`delete_product_${productId}`, async () => {\n        const response = await fetch(`${API_BASE_URL}/seller/products/${productId}`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            ...getAuthHeaders()\n          }\n        });\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to delete product');\n        }\n\n        // Remove product from list\n        setProducts(prev => prev.filter(p => p.id !== productId));\n      }, 'Deleting product...');\n    } catch (error) {\n      handleApiError(error, {\n        action: 'delete_product'\n      });\n    }\n  };\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value,\n      page: 1 // Reset to first page when filtering\n    }));\n  };\n  const handlePageChange = newPage => {\n    setFilters(prev => ({\n      ...prev,\n      page: newPage\n    }));\n  };\n  if (!isAuthenticated()) {\n    return null; // Will redirect\n  }\n  const getStockStatus = product => {\n    if (product.stock_quantity === 0) return {\n      text: 'Out of Stock',\n      color: 'text-red-600 bg-red-100'\n    };\n    if (product.stock_quantity <= product.low_stock_threshold) return {\n      text: 'Low Stock',\n      color: 'text-yellow-600 bg-yellow-100'\n    };\n    return {\n      text: 'In Stock',\n      color: 'text-green-600 bg-green-100'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/seller/dashboard\",\n              className: \"text-gray-500 hover:text-gray-700\",\n              children: \"\\u2190 Back to Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"My Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/seller/products/add\",\n            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n            children: \"Add New Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Search Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: filters.search,\n              onChange: e => handleFilterChange('search', e.target.value),\n              placeholder: \"Search by product name...\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Stock Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.status,\n              onChange: e => handleFilterChange('status', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"active\",\n                children: \"In Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"out_of_stock\",\n                children: \"Low Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"inactive\",\n                children: \"Out of Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setFilters({\n                search: '',\n                status: '',\n                page: 1\n              }),\n              className: \"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), isLoading('products_list') ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this) : products.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow overflow-hidden sm:rounded-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"divide-y divide-gray-200\",\n          children: products.map(product => {\n            const stockStatus = getStockStatus(product);\n            return /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: product.image,\n                    alt: product.name,\n                    className: \"h-16 w-16 object-cover rounded-lg\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: product.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-4 mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: [\"\\u20B9\", product.price]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`,\n                        children: stockStatus.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 207,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\"Stock: \", product.stock_quantity]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: `/seller/products/${product.id}/edit`,\n                    className: \"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n                    onClick: () => handleDeleteProduct(product.id, product.name),\n                    loading: isLoading(`delete_product_${product.id}`),\n                    className: \"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 21\n              }, this)\n            }, product.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this), pagination.pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-between sm:hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handlePageChange(pagination.page - 1),\n              disabled: !pagination.has_prev,\n              className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handlePageChange(pagination.page + 1),\n              disabled: !pagination.has_next,\n              className: \"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700\",\n                children: [\"Showing page \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: pagination.page\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 36\n                }, this), \" of\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: pagination.pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this), \" (\", pagination.total, \" total products)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"nav\", {\n                className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePageChange(pagination.page - 1),\n                  disabled: !pagination.has_prev,\n                  className: \"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePageChange(pagination.page + 1),\n                  disabled: !pagination.has_next,\n                  className: \"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: \"Next\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto h-12 w-12 text-gray-400\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"No products found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: filters.search || filters.status ? 'Try adjusting your filters.' : 'Get started by adding your first product.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/seller/products/add\",\n            className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ErrorAlert, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(SellerProducts, \"CllMSJ5UemQ3FHPWBDwot6kQQsQ=\", false, function () {\n  return [useNavigate, useSellerAuth, useError, useLoading];\n});\n_c = SellerProducts;\nexport default SellerProducts;\nvar _c;\n$RefreshReg$(_c, \"SellerProducts\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Link", "useSellerAuth", "useError", "useLoading", "LoadingSpinner", "LoadingButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "SellerProducts", "_s", "navigate", "seller", "isAuthenticated", "getAuthHeaders", "addError", "handleApiError", "isLoading", "with<PERSON>oa<PERSON>", "products", "setProducts", "pagination", "setPagination", "filters", "setFilters", "search", "status", "page", "fetchProducts", "queryParams", "URLSearchParams", "per_page", "response", "fetch", "headers", "ok", "errorData", "json", "Error", "error", "data", "action", "handleDeleteProduct", "productId", "productName", "window", "confirm", "method", "prev", "filter", "p", "id", "handleFilterChange", "key", "value", "handlePageChange", "newPage", "getStockStatus", "product", "stock_quantity", "text", "color", "low_stock_threshold", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "e", "target", "placeholder", "onClick", "size", "length", "map", "stockStatus", "src", "image", "alt", "name", "category", "price", "loading", "pages", "disabled", "has_prev", "has_next", "total", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/SellerProducts.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useSellerAuth } from '../contexts/SellerAuthContext';\nimport { useError } from '../contexts/ErrorContext';\nimport { useLoading } from '../contexts/LoadingContext';\nimport { LoadingSpinner, LoadingButton } from '../components/LoadingComponents';\nimport { ErrorAlert } from '../components/ErrorComponents';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nconst SellerProducts = () => {\n  const navigate = useNavigate();\n  const { seller, isAuthenticated, getAuthHeaders } = useSellerAuth();\n  const { addError, handleApiError } = useError();\n  const { isLoading, withLoading } = useLoading();\n  \n  const [products, setProducts] = useState([]);\n  const [pagination, setPagination] = useState({});\n  const [filters, setFilters] = useState({\n    search: '',\n    status: '',\n    page: 1\n  });\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!isAuthenticated()) {\n      navigate('/seller/login');\n      return;\n    }\n    \n    fetchProducts();\n  }, [isAuthenticated, navigate, filters]);\n\n  const fetchProducts = async () => {\n    try {\n      await withLoading('products_list', async () => {\n        const queryParams = new URLSearchParams({\n          page: filters.page,\n          per_page: 20,\n          ...(filters.search && { search: filters.search }),\n          ...(filters.status && { status: filters.status })\n        });\n\n        const response = await fetch(`${API_BASE_URL}/seller/products?${queryParams}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            ...getAuthHeaders()\n          }\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to load products');\n        }\n\n        const data = await response.json();\n        setProducts(data.data.products);\n        setPagination(data.data.pagination);\n      }, 'Loading products...');\n    } catch (error) {\n      handleApiError(error, { action: 'fetch_products' });\n    }\n  };\n\n  const handleDeleteProduct = async (productId, productName) => {\n    if (!window.confirm(`Are you sure you want to delete \"${productName}\"? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      await withLoading(`delete_product_${productId}`, async () => {\n        const response = await fetch(`${API_BASE_URL}/seller/products/${productId}`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            ...getAuthHeaders()\n          }\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to delete product');\n        }\n\n        // Remove product from list\n        setProducts(prev => prev.filter(p => p.id !== productId));\n      }, 'Deleting product...');\n    } catch (error) {\n      handleApiError(error, { action: 'delete_product' });\n    }\n  };\n\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value,\n      page: 1 // Reset to first page when filtering\n    }));\n  };\n\n  const handlePageChange = (newPage) => {\n    setFilters(prev => ({\n      ...prev,\n      page: newPage\n    }));\n  };\n\n  if (!isAuthenticated()) {\n    return null; // Will redirect\n  }\n\n  const getStockStatus = (product) => {\n    if (product.stock_quantity === 0) return { text: 'Out of Stock', color: 'text-red-600 bg-red-100' };\n    if (product.stock_quantity <= product.low_stock_threshold) return { text: 'Low Stock', color: 'text-yellow-600 bg-yellow-100' };\n    return { text: 'In Stock', color: 'text-green-600 bg-green-100' };\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <Link to=\"/seller/dashboard\" className=\"text-gray-500 hover:text-gray-700\">\n                ← Back to Dashboard\n              </Link>\n              <h1 className=\"text-2xl font-bold text-gray-900\">My Products</h1>\n            </div>\n            <Link\n              to=\"/seller/products/add\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n            >\n              Add New Product\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Search Products\n              </label>\n              <input\n                type=\"text\"\n                value={filters.search}\n                onChange={(e) => handleFilterChange('search', e.target.value)}\n                placeholder=\"Search by product name...\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Stock Status\n              </label>\n              <select\n                value={filters.status}\n                onChange={(e) => handleFilterChange('status', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\"\n              >\n                <option value=\"\">All Products</option>\n                <option value=\"active\">In Stock</option>\n                <option value=\"out_of_stock\">Low Stock</option>\n                <option value=\"inactive\">Out of Stock</option>\n              </select>\n            </div>\n            <div className=\"flex items-end\">\n              <button\n                onClick={() => setFilters({ search: '', status: '', page: 1 })}\n                className=\"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n              >\n                Clear Filters\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Products List */}\n        {isLoading('products_list') ? (\n          <div className=\"flex justify-center py-12\">\n            <LoadingSpinner size=\"large\" />\n          </div>\n        ) : products.length > 0 ? (\n          <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n            <ul className=\"divide-y divide-gray-200\">\n              {products.map((product) => {\n                const stockStatus = getStockStatus(product);\n                return (\n                  <li key={product.id} className=\"px-6 py-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-4\">\n                        <img\n                          src={product.image}\n                          alt={product.name}\n                          className=\"h-16 w-16 object-cover rounded-lg\"\n                        />\n                        <div>\n                          <h3 className=\"text-lg font-medium text-gray-900\">{product.name}</h3>\n                          <p className=\"text-sm text-gray-500\">{product.category}</p>\n                          <div className=\"flex items-center space-x-4 mt-1\">\n                            <span className=\"text-lg font-semibold text-gray-900\">₹{product.price}</span>\n                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>\n                              {stockStatus.text}\n                            </span>\n                            <span className=\"text-sm text-gray-500\">\n                              Stock: {product.stock_quantity}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <Link\n                          to={`/seller/products/${product.id}/edit`}\n                          className=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n                        >\n                          Edit\n                        </Link>\n                        <LoadingButton\n                          onClick={() => handleDeleteProduct(product.id, product.name)}\n                          loading={isLoading(`delete_product_${product.id}`)}\n                          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\n                        >\n                          Delete\n                        </LoadingButton>\n                      </div>\n                    </div>\n                  </li>\n                );\n              })}\n            </ul>\n\n            {/* Pagination */}\n            {pagination.pages > 1 && (\n              <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">\n                <div className=\"flex-1 flex justify-between sm:hidden\">\n                  <button\n                    onClick={() => handlePageChange(pagination.page - 1)}\n                    disabled={!pagination.has_prev}\n                    className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    Previous\n                  </button>\n                  <button\n                    onClick={() => handlePageChange(pagination.page + 1)}\n                    disabled={!pagination.has_next}\n                    className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    Next\n                  </button>\n                </div>\n                <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-700\">\n                      Showing page <span className=\"font-medium\">{pagination.page}</span> of{' '}\n                      <span className=\"font-medium\">{pagination.pages}</span> ({pagination.total} total products)\n                    </p>\n                  </div>\n                  <div>\n                    <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                      <button\n                        onClick={() => handlePageChange(pagination.page - 1)}\n                        disabled={!pagination.has_prev}\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        Previous\n                      </button>\n                      <button\n                        onClick={() => handlePageChange(pagination.page + 1)}\n                        disabled={!pagination.has_next}\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        Next\n                      </button>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"mx-auto h-12 w-12 text-gray-400\">\n              <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n              </svg>\n            </div>\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No products found</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {filters.search || filters.status ? 'Try adjusting your filters.' : 'Get started by adding your first product.'}\n            </p>\n            <div className=\"mt-6\">\n              <Link\n                to=\"/seller/products/add\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n              >\n                Add Product\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Global Error Display */}\n      <ErrorAlert />\n    </div>\n  );\n};\n\nexport default SellerProducts;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,EAAEC,aAAa,QAAQ,iCAAiC;AAC/E,SAASC,UAAU,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,MAAM;IAAEC,eAAe;IAAEC;EAAe,CAAC,GAAGjB,aAAa,CAAC,CAAC;EACnE,MAAM;IAAEkB,QAAQ;IAAEC;EAAe,CAAC,GAAGlB,QAAQ,CAAC,CAAC;EAC/C,MAAM;IAAEmB,SAAS;IAAEC;EAAY,CAAC,GAAGnB,UAAU,CAAC,CAAC;EAE/C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC;IACrCgC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACAjC,SAAS,CAAC,MAAM;IACd,IAAI,CAACmB,eAAe,CAAC,CAAC,EAAE;MACtBF,QAAQ,CAAC,eAAe,CAAC;MACzB;IACF;IAEAiB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACf,eAAe,EAAEF,QAAQ,EAAEY,OAAO,CAAC,CAAC;EAExC,MAAMK,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMV,WAAW,CAAC,eAAe,EAAE,YAAY;QAC7C,MAAMW,WAAW,GAAG,IAAIC,eAAe,CAAC;UACtCH,IAAI,EAAEJ,OAAO,CAACI,IAAI;UAClBI,QAAQ,EAAE,EAAE;UACZ,IAAIR,OAAO,CAACE,MAAM,IAAI;YAAEA,MAAM,EAAEF,OAAO,CAACE;UAAO,CAAC,CAAC;UACjD,IAAIF,OAAO,CAACG,MAAM,IAAI;YAAEA,MAAM,EAAEH,OAAO,CAACG;UAAO,CAAC;QAClD,CAAC,CAAC;QAEF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG5B,YAAY,oBAAoBwB,WAAW,EAAE,EAAE;UAC7EK,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,GAAGpB,cAAc,CAAC;UACpB;QACF,CAAC,CAAC;QAEF,IAAI,CAACkB,QAAQ,CAACG,EAAE,EAAE;UAChB,MAAMC,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,KAAK,IAAI,yBAAyB,CAAC;QAC/D;QAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCjB,WAAW,CAACoB,IAAI,CAACA,IAAI,CAACrB,QAAQ,CAAC;QAC/BG,aAAa,CAACkB,IAAI,CAACA,IAAI,CAACnB,UAAU,CAAC;MACrC,CAAC,EAAE,qBAAqB,CAAC;IAC3B,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdvB,cAAc,CAACuB,KAAK,EAAE;QAAEE,MAAM,EAAE;MAAiB,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,WAAW,KAAK;IAC5D,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oCAAoCF,WAAW,kCAAkC,CAAC,EAAE;MACtG;IACF;IAEA,IAAI;MACF,MAAM1B,WAAW,CAAC,kBAAkByB,SAAS,EAAE,EAAE,YAAY;QAC3D,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG5B,YAAY,oBAAoBsC,SAAS,EAAE,EAAE;UAC3EI,MAAM,EAAE,QAAQ;UAChBb,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,GAAGpB,cAAc,CAAC;UACpB;QACF,CAAC,CAAC;QAEF,IAAI,CAACkB,QAAQ,CAACG,EAAE,EAAE;UAChB,MAAMC,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,KAAK,IAAI,0BAA0B,CAAC;QAChE;;QAEA;QACAnB,WAAW,CAAC4B,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,SAAS,CAAC,CAAC;MAC3D,CAAC,EAAE,qBAAqB,CAAC;IAC3B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdvB,cAAc,CAACuB,KAAK,EAAE;QAAEE,MAAM,EAAE;MAAiB,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMW,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACzC9B,UAAU,CAACwB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACK,GAAG,GAAGC,KAAK;MACZ3B,IAAI,EAAE,CAAC,CAAC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,gBAAgB,GAAIC,OAAO,IAAK;IACpChC,UAAU,CAACwB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPrB,IAAI,EAAE6B;IACR,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAI,CAAC3C,eAAe,CAAC,CAAC,EAAE;IACtB,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,MAAM4C,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAIA,OAAO,CAACC,cAAc,KAAK,CAAC,EAAE,OAAO;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAA0B,CAAC;IACnG,IAAIH,OAAO,CAACC,cAAc,IAAID,OAAO,CAACI,mBAAmB,EAAE,OAAO;MAAEF,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAgC,CAAC;IAC/H,OAAO;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAA8B,CAAC;EACnE,CAAC;EAED,oBACEzD,OAAA;IAAK2D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtC5D,OAAA;MAAQ2D,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC7C5D,OAAA;QAAK2D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD5D,OAAA;UAAK2D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5D,OAAA;YAAK2D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5D,OAAA,CAACR,IAAI;cAACqE,EAAE,EAAC,mBAAmB;cAACF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPjE,OAAA;cAAI2D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNjE,OAAA,CAACR,IAAI;YACHqE,EAAE,EAAC,sBAAsB;YACzBF,SAAS,EAAC,iNAAiN;YAAAC,QAAA,EAC5N;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETjE,OAAA;MAAK2D,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D5D,OAAA;QAAK2D,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClD5D,OAAA;UAAK2D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD5D,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAO2D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA;cACEkE,IAAI,EAAC,MAAM;cACXhB,KAAK,EAAE/B,OAAO,CAACE,MAAO;cACtB8C,QAAQ,EAAGC,CAAC,IAAKpB,kBAAkB,CAAC,QAAQ,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;cAC9DoB,WAAW,EAAC,2BAA2B;cACvCX,SAAS,EAAC;YAA6H;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjE,OAAA;YAAA4D,QAAA,gBACE5D,OAAA;cAAO2D,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA;cACEkD,KAAK,EAAE/B,OAAO,CAACG,MAAO;cACtB6C,QAAQ,EAAGC,CAAC,IAAKpB,kBAAkB,CAAC,QAAQ,EAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;cAC9DS,SAAS,EAAC,6HAA6H;cAAAC,QAAA,gBAEvI5D,OAAA;gBAAQkD,KAAK,EAAC,EAAE;gBAAAU,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCjE,OAAA;gBAAQkD,KAAK,EAAC,QAAQ;gBAAAU,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCjE,OAAA;gBAAQkD,KAAK,EAAC,cAAc;gBAAAU,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CjE,OAAA;gBAAQkD,KAAK,EAAC,UAAU;gBAAAU,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNjE,OAAA;YAAK2D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B5D,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAAC;gBAAEC,MAAM,EAAE,EAAE;gBAAEC,MAAM,EAAE,EAAE;gBAAEC,IAAI,EAAE;cAAE,CAAC,CAAE;cAC/DoC,SAAS,EAAC,kLAAkL;cAAAC,QAAA,EAC7L;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLpD,SAAS,CAAC,eAAe,CAAC,gBACzBb,OAAA;QAAK2D,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACxC5D,OAAA,CAACJ,cAAc;UAAC4E,IAAI,EAAC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,GACJlD,QAAQ,CAAC0D,MAAM,GAAG,CAAC,gBACrBzE,OAAA;QAAK2D,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC5D5D,OAAA;UAAI2D,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACrC7C,QAAQ,CAAC2D,GAAG,CAAEpB,OAAO,IAAK;YACzB,MAAMqB,WAAW,GAAGtB,cAAc,CAACC,OAAO,CAAC;YAC3C,oBACEtD,OAAA;cAAqB2D,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxC5D,OAAA;gBAAK2D,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD5D,OAAA;kBAAK2D,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5D,OAAA;oBACE4E,GAAG,EAAEtB,OAAO,CAACuB,KAAM;oBACnBC,GAAG,EAAExB,OAAO,CAACyB,IAAK;oBAClBpB,SAAS,EAAC;kBAAmC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACFjE,OAAA;oBAAA4D,QAAA,gBACE5D,OAAA;sBAAI2D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEN,OAAO,CAACyB;oBAAI;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrEjE,OAAA;sBAAG2D,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEN,OAAO,CAAC0B;oBAAQ;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3DjE,OAAA;sBAAK2D,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/C5D,OAAA;wBAAM2D,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,GAAC,QAAC,EAACN,OAAO,CAAC2B,KAAK;sBAAA;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC7EjE,OAAA;wBAAM2D,SAAS,EAAE,4DAA4DgB,WAAW,CAAClB,KAAK,EAAG;wBAAAG,QAAA,EAC9Fe,WAAW,CAACnB;sBAAI;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,eACPjE,OAAA;wBAAM2D,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,SAC/B,EAACN,OAAO,CAACC,cAAc;sBAAA;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjE,OAAA;kBAAK2D,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5D,OAAA,CAACR,IAAI;oBACHqE,EAAE,EAAE,oBAAoBP,OAAO,CAACP,EAAE,OAAQ;oBAC1CY,SAAS,EAAC,+NAA+N;oBAAAC,QAAA,EAC1O;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjE,OAAA,CAACH,aAAa;oBACZ0E,OAAO,EAAEA,CAAA,KAAMjC,mBAAmB,CAACgB,OAAO,CAACP,EAAE,EAAEO,OAAO,CAACyB,IAAI,CAAE;oBAC7DG,OAAO,EAAErE,SAAS,CAAC,kBAAkByC,OAAO,CAACP,EAAE,EAAE,CAAE;oBACnDY,SAAS,EAAC,qNAAqN;oBAAAC,QAAA,EAChO;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GArCCX,OAAO,CAACP,EAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCf,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGJhD,UAAU,CAACkE,KAAK,GAAG,CAAC,iBACnBnF,OAAA;UAAK2D,SAAS,EAAC,uFAAuF;UAAAC,QAAA,gBACpG5D,OAAA;YAAK2D,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5D,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAAClC,UAAU,CAACM,IAAI,GAAG,CAAC,CAAE;cACrD6D,QAAQ,EAAE,CAACnE,UAAU,CAACoE,QAAS;cAC/B1B,SAAS,EAAC,2LAA2L;cAAAC,QAAA,EACtM;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA;cACEuE,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAAClC,UAAU,CAACM,IAAI,GAAG,CAAC,CAAE;cACrD6D,QAAQ,EAAE,CAACnE,UAAU,CAACqE,QAAS;cAC/B3B,SAAS,EAAC,gMAAgM;cAAAC,QAAA,EAC3M;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNjE,OAAA;YAAK2D,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1E5D,OAAA;cAAA4D,QAAA,eACE5D,OAAA;gBAAG2D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,eACtB,eAAA5D,OAAA;kBAAM2D,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE3C,UAAU,CAACM;gBAAI;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,OAAG,EAAC,GAAG,eAC1EjE,OAAA;kBAAM2D,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE3C,UAAU,CAACkE;gBAAK;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,MAAE,EAAChD,UAAU,CAACsE,KAAK,EAAC,kBAC7E;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjE,OAAA;cAAA4D,QAAA,eACE5D,OAAA;gBAAK2D,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBACxE5D,OAAA;kBACEuE,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAAClC,UAAU,CAACM,IAAI,GAAG,CAAC,CAAE;kBACrD6D,QAAQ,EAAE,CAACnE,UAAU,CAACoE,QAAS;kBAC/B1B,SAAS,EAAC,6LAA6L;kBAAAC,QAAA,EACxM;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjE,OAAA;kBACEuE,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAAClC,UAAU,CAACM,IAAI,GAAG,CAAC,CAAE;kBACrD6D,QAAQ,EAAE,CAACnE,UAAU,CAACqE,QAAS;kBAC/B3B,SAAS,EAAC,6LAA6L;kBAAAC,QAAA,EACxM;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENjE,OAAA;QAAK2D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5D,OAAA;UAAK2D,SAAS,EAAC,iCAAiC;UAAAC,QAAA,eAC9C5D,OAAA;YAAKwF,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA9B,QAAA,eACxD5D,OAAA;cAAM2F,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAiE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjE,OAAA;UAAI2D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EjE,OAAA;UAAG2D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtCzC,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACG,MAAM,GAAG,6BAA6B,GAAG;QAA2C;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC,eACJjE,OAAA;UAAK2D,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5D,OAAA,CAACR,IAAI;YACHqE,EAAE,EAAC,sBAAsB;YACzBF,SAAS,EAAC,2NAA2N;YAAAC,QAAA,EACtO;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNjE,OAAA,CAACF,UAAU;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA7SID,cAAc;EAAA,QACDd,WAAW,EACwBE,aAAa,EAC5BC,QAAQ,EACVC,UAAU;AAAA;AAAAoG,EAAA,GAJzC1F,cAAc;AA+SpB,eAAeA,cAAc;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}