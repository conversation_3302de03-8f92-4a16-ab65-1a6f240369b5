.seller-store-page {
    min-height: 100vh;
    background: #f8f9fa;
}

/* Loading and Error States */
.seller-store-loading,
.seller-store-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e0e0e0;
    border-top: 4px solid #27ae60;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.seller-store-error h2 {
    color: #e74c3c;
    margin-bottom: 10px;
}

.seller-store-error p {
    color: #7f8c8d;
}

/* Store Header */
.store-header {
    position: relative;
    background: white;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.store-banner {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    overflow: hidden;
}

.banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.3));
}

.store-info-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.store-main-info {
    display: flex;
    align-items: flex-start;
    gap: 25px;
    padding: 30px 0;
    position: relative;
    top: -80px;
}

.store-logo {
    width: 120px;
    height: 120px;
    border-radius: 20px;
    overflow: hidden;
    background: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 4px solid white;
    flex-shrink: 0;
}

.store-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.store-details {
    flex: 1;
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.store-name {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
    line-height: 1.2;
}

.business-name {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin: 0 0 6px 0;
    font-weight: 500;
}

.contact-person {
    font-size: 1rem;
    color: #95a5a6;
    margin: 0 0 20px 0;
}

.store-stats {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.stat-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

.stat-value {
    font-size: 0.9rem;
    color: #2c3e50;
    font-weight: 600;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    font-size: 16px;
    line-height: 1;
}

.star.filled {
    color: #f39c12;
}

.star.half {
    color: #f39c12;
    opacity: 0.6;
}

.star.empty {
    color: #bdc3c7;
}

.rating-value {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

.verified-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #e8f5e8;
    color: #27ae60;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.verified-icon {
    background: #27ae60;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
}

.store-description {
    color: #5d6d7e;
    line-height: 1.6;
    margin: 0;
    font-size: 1rem;
}

/* Filters and Sorting */
.store-filters {
    max-width: 1200px;
    margin: 0 auto 30px auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
    display: flex;
    gap: 20px;
    align-items: center;
}

.category-filter,
.sort-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.category-filter label,
.sort-filter label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
}

.category-filter select,
.sort-filter select {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    color: #2c3e50;
    font-size: 0.9rem;
    min-width: 150px;
    transition: border-color 0.2s ease;
}

.category-filter select:focus,
.sort-filter select:focus {
    outline: none;
    border-color: #27ae60;
}

.results-info p {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin: 0;
}

/* Products Grid */
.store-products {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.no-products {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-products h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.no-products p {
    color: #7f8c8d;
    font-size: 1rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 40px 0;
}

.pagination-btn,
.page-btn {
    padding: 10px 16px;
    border: 2px solid #e0e0e0;
    background: white;
    color: #2c3e50;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled),
.page-btn:hover {
    border-color: #27ae60;
    background: #27ae60;
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-btn.active {
    background: #27ae60;
    border-color: #27ae60;
    color: white;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .store-banner {
        height: 200px;
    }

    .store-main-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        top: -60px;
        padding: 20px 0;
    }

    .store-logo {
        width: 100px;
        height: 100px;
    }

    .store-name {
        font-size: 2rem;
    }

    .store-stats {
        justify-content: center;
    }

    .store-filters {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .filter-section {
        flex-direction: column;
        gap: 15px;
    }

    .category-filter,
    .sort-filter {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .category-filter select,
    .sort-filter select {
        min-width: auto;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 8px;
    }

    .pagination-btn,
    .page-btn {
        padding: 8px 12px;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .store-info-container {
        padding: 0 15px;
    }

    .store-details {
        padding: 20px 15px;
    }

    .store-name {
        font-size: 1.8rem;
    }

    .store-stats {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}
