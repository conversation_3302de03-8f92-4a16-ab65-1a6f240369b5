import React from 'react';
import { useLoading } from '../contexts/LoadingContext';

// Basic loading spinner
export const LoadingSpinner = ({ size = 'medium', color = 'primary', className = '' }) => {
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-8 h-8',
    large: 'w-12 h-12',
    xlarge: 'w-16 h-16'
  };

  const colorClasses = {
    primary: 'border-green-500',
    secondary: 'border-gray-500',
    white: 'border-white',
    dark: 'border-gray-800'
  };

  return (
    <div className={`inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent ${sizeClasses[size]} ${colorClasses[color]} ${className}`}>
      <span className="sr-only">Loading...</span>
    </div>
  );
};

// Loading overlay for full screen
export const LoadingOverlay = ({ message = 'Loading...', transparent = false }) => {
  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${transparent ? 'bg-black bg-opacity-30' : 'bg-white bg-opacity-90'}`}>
      <div className="text-center">
        <LoadingSpinner size="large" />
        <p className="mt-4 text-lg font-medium text-gray-700">{message}</p>
      </div>
    </div>
  );
};

// Loading skeleton for content
export const LoadingSkeleton = ({ 
  width = '100%', 
  height = '1rem', 
  className = '',
  rounded = true,
  animated = true 
}) => {
  return (
    <div 
      className={`bg-gray-200 ${rounded ? 'rounded' : ''} ${animated ? 'animate-pulse' : ''} ${className}`}
      style={{ width, height }}
    />
  );
};

// Product card skeleton
export const ProductCardSkeleton = () => {
  return (
    <div className="bg-white rounded-lg shadow-md p-4 animate-pulse">
      <LoadingSkeleton height="200px" className="mb-4" />
      <LoadingSkeleton height="1.5rem" className="mb-2" />
      <LoadingSkeleton height="1rem" width="60%" className="mb-2" />
      <LoadingSkeleton height="1.25rem" width="40%" />
    </div>
  );
};

// List item skeleton
export const ListItemSkeleton = ({ showAvatar = false }) => {
  return (
    <div className="flex items-center space-x-4 p-4 animate-pulse">
      {showAvatar && (
        <LoadingSkeleton width="3rem" height="3rem" className="rounded-full" />
      )}
      <div className="flex-1">
        <LoadingSkeleton height="1.25rem" className="mb-2" />
        <LoadingSkeleton height="1rem" width="70%" />
      </div>
    </div>
  );
};

// Button loading state
export const LoadingButton = ({ 
  children, 
  loading = false, 
  disabled = false, 
  className = '',
  loadingText = 'Loading...',
  ...props 
}) => {
  return (
    <button
      {...props}
      disabled={loading || disabled}
      className={`relative ${className} ${loading ? 'cursor-not-allowed' : ''}`}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="small" color="white" />
        </div>
      )}
      <span className={loading ? 'opacity-0' : 'opacity-100'}>
        {loading ? loadingText : children}
      </span>
    </button>
  );
};

// Page loading component
export const PageLoading = ({ message = 'Loading page...' }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4">
          <LoadingSpinner size="medium" color="white" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Please wait</h2>
        <p className="text-gray-600">{message}</p>
      </div>
    </div>
  );
};

// Section loading component
export const SectionLoading = ({ message = 'Loading...', height = 'auto' }) => {
  return (
    <div className="flex items-center justify-center p-8" style={{ minHeight: height }}>
      <div className="text-center">
        <LoadingSpinner size="large" />
        <p className="mt-4 text-gray-600">{message}</p>
      </div>
    </div>
  );
};

// Global loading indicator (shows when any loading is active)
export const GlobalLoadingIndicator = () => {
  const { hasAnyLoading, getActiveLoadingStates } = useLoading();
  
  if (!hasAnyLoading()) return null;
  
  const activeStates = getActiveLoadingStates();
  const primaryState = activeStates[0];
  
  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-green-500 text-white px-4 py-2 text-sm font-medium">
      <div className="flex items-center justify-center space-x-2">
        <LoadingSpinner size="small" color="white" />
        <span>{primaryState?.message || 'Loading...'}</span>
        {activeStates.length > 1 && (
          <span className="text-green-200">
            (+{activeStates.length - 1} more)
          </span>
        )}
      </div>
    </div>
  );
};

// Loading state wrapper component
export const LoadingWrapper = ({ 
  loading, 
  error, 
  children, 
  loadingComponent: LoadingComponent = SectionLoading,
  errorComponent: ErrorComponent = null,
  loadingProps = {},
  errorProps = {}
}) => {
  if (loading) {
    return <LoadingComponent {...loadingProps} />;
  }
  
  if (error && ErrorComponent) {
    return <ErrorComponent error={error} {...errorProps} />;
  }
  
  return children;
};

// Higher-order component for loading states
export const withLoading = (WrappedComponent, loadingKey) => {
  return function LoadingWrappedComponent(props) {
    const { isLoading, getLoadingMessage } = useLoading();
    
    const loading = isLoading(loadingKey);
    const loadingMessage = getLoadingMessage(loadingKey);
    
    if (loading) {
      return <SectionLoading message={loadingMessage} />;
    }
    
    return <WrappedComponent {...props} />;
  };
};

// Lazy loading placeholder
export const LazyLoadingPlaceholder = ({ height = '200px' }) => {
  return (
    <div 
      className="bg-gray-100 animate-pulse flex items-center justify-center"
      style={{ height }}
    >
      <div className="text-center text-gray-400">
        <LoadingSpinner size="medium" color="secondary" />
        <p className="mt-2 text-sm">Loading content...</p>
      </div>
    </div>
  );
};

export default {
  LoadingSpinner,
  LoadingOverlay,
  LoadingSkeleton,
  ProductCardSkeleton,
  ListItemSkeleton,
  LoadingButton,
  PageLoading,
  SectionLoading,
  GlobalLoadingIndicator,
  LoadingWrapper,
  withLoading,
  LazyLoadingPlaceholder
};
