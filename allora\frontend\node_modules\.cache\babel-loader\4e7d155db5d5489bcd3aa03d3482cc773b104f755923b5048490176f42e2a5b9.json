{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\contexts\\\\LoadingContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingContext = /*#__PURE__*/createContext();\nexport const useLoading = () => {\n  _s();\n  const context = useContext(LoadingContext);\n  if (!context) {\n    throw new Error('useLoading must be used within a LoadingProvider');\n  }\n  return context;\n};\n_s(useLoading, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const LoadingProvider = ({\n  children\n}) => {\n  _s2();\n  const [loadingStates, setLoadingStates] = useState({});\n  const [globalLoading, setGlobalLoading] = useState(false);\n\n  // Set loading state for a specific key\n  const setLoading = useCallback((key, isLoading, message = '') => {\n    setLoadingStates(prev => ({\n      ...prev,\n      [key]: isLoading ? {\n        loading: true,\n        message\n      } : undefined\n    }));\n  }, []);\n\n  // Get loading state for a specific key\n  const isLoading = useCallback(key => {\n    var _loadingStates$key;\n    return ((_loadingStates$key = loadingStates[key]) === null || _loadingStates$key === void 0 ? void 0 : _loadingStates$key.loading) || false;\n  }, [loadingStates]);\n\n  // Get loading message for a specific key\n  const getLoadingMessage = useCallback(key => {\n    var _loadingStates$key2;\n    return ((_loadingStates$key2 = loadingStates[key]) === null || _loadingStates$key2 === void 0 ? void 0 : _loadingStates$key2.message) || '';\n  }, [loadingStates]);\n\n  // Check if any loading state is active\n  const hasAnyLoading = useCallback(() => {\n    return Object.values(loadingStates).some(state => state === null || state === void 0 ? void 0 : state.loading) || globalLoading;\n  }, [loadingStates, globalLoading]);\n\n  // Get all active loading states\n  const getActiveLoadingStates = useCallback(() => {\n    return Object.entries(loadingStates).filter(([_, state]) => state === null || state === void 0 ? void 0 : state.loading).map(([key, state]) => ({\n      key,\n      message: state.message\n    }));\n  }, [loadingStates]);\n\n  // Clear specific loading state\n  const clearLoading = useCallback(key => {\n    setLoadingStates(prev => {\n      const newState = {\n        ...prev\n      };\n      delete newState[key];\n      return newState;\n    });\n  }, []);\n\n  // Clear all loading states\n  const clearAllLoading = useCallback(() => {\n    setLoadingStates({});\n    setGlobalLoading(false);\n  }, []);\n\n  // Set global loading state\n  const setGlobalLoadingState = useCallback((isLoading, message = '') => {\n    setGlobalLoading(isLoading);\n    if (isLoading && message) {\n      setLoading('global', true, message);\n    } else if (!isLoading) {\n      clearLoading('global');\n    }\n  }, [setLoading, clearLoading]);\n\n  // Async wrapper that automatically manages loading state\n  const withLoading = useCallback(async (key, asyncFunction, message = 'Loading...') => {\n    try {\n      setLoading(key, true, message);\n      const result = await asyncFunction();\n      return result;\n    } catch (error) {\n      throw error;\n    } finally {\n      clearLoading(key);\n    }\n  }, [setLoading, clearLoading]);\n  const value = {\n    // State getters\n    isLoading,\n    getLoadingMessage,\n    hasAnyLoading,\n    getActiveLoadingStates,\n    globalLoading,\n    // State setters\n    setLoading,\n    clearLoading,\n    clearAllLoading,\n    setGlobalLoadingState,\n    // Utilities\n    withLoading\n  };\n  return /*#__PURE__*/_jsxDEV(LoadingContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n\n// Higher-order component for automatic loading management\n_s2(LoadingProvider, \"1NUsK7tBD3tnL253XfT+lGbxLYE=\");\n_c = LoadingProvider;\nexport const withLoadingState = (WrappedComponent, loadingKey) => {\n  var _s3 = $RefreshSig$();\n  return _s3(function LoadingWrappedComponent(props) {\n    _s3();\n    const {\n      isLoading,\n      setLoading,\n      clearLoading,\n      getLoadingMessage\n    } = useLoading();\n    const loadingProps = {\n      isLoading: isLoading(loadingKey),\n      setLoading: (loading, message) => setLoading(loadingKey, loading, message),\n      clearLoading: () => clearLoading(loadingKey),\n      loadingMessage: getLoadingMessage(loadingKey)\n    };\n    return /*#__PURE__*/_jsxDEV(WrappedComponent, {\n      ...props,\n      ...loadingProps\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 12\n    }, this);\n  }, \"dfpAZRwpeHaTmd80SYGMQAR9l8k=\", false, function () {\n    return [useLoading];\n  });\n};\nexport default LoadingContext;\nvar _c;\n$RefreshReg$(_c, \"LoadingProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useCallback", "jsxDEV", "_jsxDEV", "LoadingContext", "useLoading", "_s", "context", "Error", "LoadingProvider", "children", "_s2", "loadingStates", "setLoadingStates", "globalLoading", "setGlobalLoading", "setLoading", "key", "isLoading", "message", "prev", "loading", "undefined", "_loadingStates$key", "getLoadingMessage", "_loadingStates$key2", "hasAnyLoading", "Object", "values", "some", "state", "getActiveLoadingStates", "entries", "filter", "_", "map", "clearLoading", "newState", "clearAllLoading", "setGlobalLoadingState", "with<PERSON>oa<PERSON>", "asyncFunction", "result", "error", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "withLoadingState", "WrappedComponent", "loadingKey", "_s3", "$RefreshSig$", "LoadingWrappedComponent", "props", "loadingProps", "loadingMessage", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/contexts/LoadingContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useCallback } from 'react';\n\nconst LoadingContext = createContext();\n\nexport const useLoading = () => {\n  const context = useContext(LoadingContext);\n  if (!context) {\n    throw new Error('useLoading must be used within a LoadingProvider');\n  }\n  return context;\n};\n\nexport const LoadingProvider = ({ children }) => {\n  const [loadingStates, setLoadingStates] = useState({});\n  const [globalLoading, setGlobalLoading] = useState(false);\n\n  // Set loading state for a specific key\n  const setLoading = useCallback((key, isLoading, message = '') => {\n    setLoadingStates(prev => ({\n      ...prev,\n      [key]: isLoading ? { loading: true, message } : undefined\n    }));\n  }, []);\n\n  // Get loading state for a specific key\n  const isLoading = useCallback((key) => {\n    return loadingStates[key]?.loading || false;\n  }, [loadingStates]);\n\n  // Get loading message for a specific key\n  const getLoadingMessage = useCallback((key) => {\n    return loadingStates[key]?.message || '';\n  }, [loadingStates]);\n\n  // Check if any loading state is active\n  const hasAnyLoading = useCallback(() => {\n    return Object.values(loadingStates).some(state => state?.loading) || globalLoading;\n  }, [loadingStates, globalLoading]);\n\n  // Get all active loading states\n  const getActiveLoadingStates = useCallback(() => {\n    return Object.entries(loadingStates)\n      .filter(([_, state]) => state?.loading)\n      .map(([key, state]) => ({ key, message: state.message }));\n  }, [loadingStates]);\n\n  // Clear specific loading state\n  const clearLoading = useCallback((key) => {\n    setLoadingStates(prev => {\n      const newState = { ...prev };\n      delete newState[key];\n      return newState;\n    });\n  }, []);\n\n  // Clear all loading states\n  const clearAllLoading = useCallback(() => {\n    setLoadingStates({});\n    setGlobalLoading(false);\n  }, []);\n\n  // Set global loading state\n  const setGlobalLoadingState = useCallback((isLoading, message = '') => {\n    setGlobalLoading(isLoading);\n    if (isLoading && message) {\n      setLoading('global', true, message);\n    } else if (!isLoading) {\n      clearLoading('global');\n    }\n  }, [setLoading, clearLoading]);\n\n  // Async wrapper that automatically manages loading state\n  const withLoading = useCallback(async (key, asyncFunction, message = 'Loading...') => {\n    try {\n      setLoading(key, true, message);\n      const result = await asyncFunction();\n      return result;\n    } catch (error) {\n      throw error;\n    } finally {\n      clearLoading(key);\n    }\n  }, [setLoading, clearLoading]);\n\n  const value = {\n    // State getters\n    isLoading,\n    getLoadingMessage,\n    hasAnyLoading,\n    getActiveLoadingStates,\n    globalLoading,\n    \n    // State setters\n    setLoading,\n    clearLoading,\n    clearAllLoading,\n    setGlobalLoadingState,\n    \n    // Utilities\n    withLoading\n  };\n\n  return (\n    <LoadingContext.Provider value={value}>\n      {children}\n    </LoadingContext.Provider>\n  );\n};\n\n// Higher-order component for automatic loading management\nexport const withLoadingState = (WrappedComponent, loadingKey) => {\n  return function LoadingWrappedComponent(props) {\n    const { isLoading, setLoading, clearLoading, getLoadingMessage } = useLoading();\n    \n    const loadingProps = {\n      isLoading: isLoading(loadingKey),\n      setLoading: (loading, message) => setLoading(loadingKey, loading, message),\n      clearLoading: () => clearLoading(loadingKey),\n      loadingMessage: getLoadingMessage(loadingKey)\n    };\n    \n    return <WrappedComponent {...props} {...loadingProps} />;\n  };\n};\n\nexport default LoadingContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAMC,cAAc,gBAAGN,aAAa,CAAC,CAAC;AAEtC,OAAO,MAAMO,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,OAAO,GAAGR,UAAU,CAACK,cAAc,CAAC;EAC1C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;EACrE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,UAAU;AAQvB,OAAO,MAAMI,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC/C,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAMgB,UAAU,GAAGf,WAAW,CAAC,CAACgB,GAAG,EAAEC,SAAS,EAAEC,OAAO,GAAG,EAAE,KAAK;IAC/DN,gBAAgB,CAACO,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACH,GAAG,GAAGC,SAAS,GAAG;QAAEG,OAAO,EAAE,IAAI;QAAEF;MAAQ,CAAC,GAAGG;IAClD,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMJ,SAAS,GAAGjB,WAAW,CAAEgB,GAAG,IAAK;IAAA,IAAAM,kBAAA;IACrC,OAAO,EAAAA,kBAAA,GAAAX,aAAa,CAACK,GAAG,CAAC,cAAAM,kBAAA,uBAAlBA,kBAAA,CAAoBF,OAAO,KAAI,KAAK;EAC7C,CAAC,EAAE,CAACT,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMY,iBAAiB,GAAGvB,WAAW,CAAEgB,GAAG,IAAK;IAAA,IAAAQ,mBAAA;IAC7C,OAAO,EAAAA,mBAAA,GAAAb,aAAa,CAACK,GAAG,CAAC,cAAAQ,mBAAA,uBAAlBA,mBAAA,CAAoBN,OAAO,KAAI,EAAE;EAC1C,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMc,aAAa,GAAGzB,WAAW,CAAC,MAAM;IACtC,OAAO0B,MAAM,CAACC,MAAM,CAAChB,aAAa,CAAC,CAACiB,IAAI,CAACC,KAAK,IAAIA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAET,OAAO,CAAC,IAAIP,aAAa;EACpF,CAAC,EAAE,CAACF,aAAa,EAAEE,aAAa,CAAC,CAAC;;EAElC;EACA,MAAMiB,sBAAsB,GAAG9B,WAAW,CAAC,MAAM;IAC/C,OAAO0B,MAAM,CAACK,OAAO,CAACpB,aAAa,CAAC,CACjCqB,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEJ,KAAK,CAAC,KAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAET,OAAO,CAAC,CACtCc,GAAG,CAAC,CAAC,CAAClB,GAAG,EAAEa,KAAK,CAAC,MAAM;MAAEb,GAAG;MAAEE,OAAO,EAAEW,KAAK,CAACX;IAAQ,CAAC,CAAC,CAAC;EAC7D,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMwB,YAAY,GAAGnC,WAAW,CAAEgB,GAAG,IAAK;IACxCJ,gBAAgB,CAACO,IAAI,IAAI;MACvB,MAAMiB,QAAQ,GAAG;QAAE,GAAGjB;MAAK,CAAC;MAC5B,OAAOiB,QAAQ,CAACpB,GAAG,CAAC;MACpB,OAAOoB,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,eAAe,GAAGrC,WAAW,CAAC,MAAM;IACxCY,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACpBE,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwB,qBAAqB,GAAGtC,WAAW,CAAC,CAACiB,SAAS,EAAEC,OAAO,GAAG,EAAE,KAAK;IACrEJ,gBAAgB,CAACG,SAAS,CAAC;IAC3B,IAAIA,SAAS,IAAIC,OAAO,EAAE;MACxBH,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAEG,OAAO,CAAC;IACrC,CAAC,MAAM,IAAI,CAACD,SAAS,EAAE;MACrBkB,YAAY,CAAC,QAAQ,CAAC;IACxB;EACF,CAAC,EAAE,CAACpB,UAAU,EAAEoB,YAAY,CAAC,CAAC;;EAE9B;EACA,MAAMI,WAAW,GAAGvC,WAAW,CAAC,OAAOgB,GAAG,EAAEwB,aAAa,EAAEtB,OAAO,GAAG,YAAY,KAAK;IACpF,IAAI;MACFH,UAAU,CAACC,GAAG,EAAE,IAAI,EAAEE,OAAO,CAAC;MAC9B,MAAMuB,MAAM,GAAG,MAAMD,aAAa,CAAC,CAAC;MACpC,OAAOC,MAAM;IACf,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb,CAAC,SAAS;MACRP,YAAY,CAACnB,GAAG,CAAC;IACnB;EACF,CAAC,EAAE,CAACD,UAAU,EAAEoB,YAAY,CAAC,CAAC;EAE9B,MAAMQ,KAAK,GAAG;IACZ;IACA1B,SAAS;IACTM,iBAAiB;IACjBE,aAAa;IACbK,sBAAsB;IACtBjB,aAAa;IAEb;IACAE,UAAU;IACVoB,YAAY;IACZE,eAAe;IACfC,qBAAqB;IAErB;IACAC;EACF,CAAC;EAED,oBACErC,OAAA,CAACC,cAAc,CAACyC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAlC,QAAA,EACnCA;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE9B,CAAC;;AAED;AAAAtC,GAAA,CAjGaF,eAAe;AAAAyC,EAAA,GAAfzC,eAAe;AAkG5B,OAAO,MAAM0C,gBAAgB,GAAGA,CAACC,gBAAgB,EAAEC,UAAU,KAAK;EAAA,IAAAC,GAAA,GAAAC,YAAA;EAChE,OAAAD,GAAA,CAAO,SAASE,uBAAuBA,CAACC,KAAK,EAAE;IAAAH,GAAA;IAC7C,MAAM;MAAEpC,SAAS;MAAEF,UAAU;MAAEoB,YAAY;MAAEZ;IAAkB,CAAC,GAAGnB,UAAU,CAAC,CAAC;IAE/E,MAAMqD,YAAY,GAAG;MACnBxC,SAAS,EAAEA,SAAS,CAACmC,UAAU,CAAC;MAChCrC,UAAU,EAAEA,CAACK,OAAO,EAAEF,OAAO,KAAKH,UAAU,CAACqC,UAAU,EAAEhC,OAAO,EAAEF,OAAO,CAAC;MAC1EiB,YAAY,EAAEA,CAAA,KAAMA,YAAY,CAACiB,UAAU,CAAC;MAC5CM,cAAc,EAAEnC,iBAAiB,CAAC6B,UAAU;IAC9C,CAAC;IAED,oBAAOlD,OAAA,CAACiD,gBAAgB;MAAA,GAAKK,KAAK;MAAA,GAAMC;IAAY;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC1D,CAAC;IAAA,QAVoE5C,UAAU;EAAA;AAWjF,CAAC;AAED,eAAeD,cAAc;AAAC,IAAA8C,EAAA;AAAAU,YAAA,CAAAV,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}