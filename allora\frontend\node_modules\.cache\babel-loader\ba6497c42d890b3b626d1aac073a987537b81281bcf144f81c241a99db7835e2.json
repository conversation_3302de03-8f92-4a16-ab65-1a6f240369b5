{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\components\\\\ModernMinimalistNavbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { AlloraBlackWhiteLogo, AlloraMobileLogo } from './AlloraLogo';\nimport { useCart } from '../contexts/CartContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModernMinimalistNavbar = ({\n  token,\n  user,\n  onLogout\n}) => {\n  _s();\n  var _user$username, _user$username2;\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isScrolled, setIsScrolled] = useState(false);\n  const location = useLocation();\n  const {\n    cartCount\n  } = useCart();\n  const isActivePath = path => location.pathname === path;\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when clicking outside\n  useEffect(() => {\n    const handleClickOutside = () => {\n      if (isMenuOpen) setIsMenuOpen(false);\n    };\n    document.addEventListener('click', handleClickOutside);\n    return () => document.removeEventListener('click', handleClickOutside);\n  }, [isMenuOpen]);\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchTerm.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchTerm)}`;\n    }\n  };\n\n  // Navigation items\n  const navItems = [{\n    path: '/',\n    label: 'Home',\n    icon: '🏠'\n  }, {\n    path: '/search',\n    label: 'Search',\n    icon: '🔍'\n  }, {\n    path: '/categories',\n    label: 'Categories',\n    icon: '📂'\n  }, {\n    path: '/community',\n    label: 'Community',\n    icon: '👥'\n  }];\n  const userNavItems = token ? [{\n    path: '/account',\n    label: 'Account',\n    icon: '👤'\n  }, {\n    path: '/cart',\n    label: 'Cart',\n    icon: '🛒',\n    badge: cartCount\n  }, {\n    path: '/inventory',\n    label: 'Inventory',\n    icon: '📦'\n  }, {\n    path: '/price-trends',\n    label: 'Trends',\n    icon: '📈'\n  }] : [];\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: `sticky top-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-200/50' : 'bg-white/90 backdrop-blur-sm border-b border-gray-100'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 lg:px-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16 lg:h-18\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex items-center justify-between w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = '/',\n              className: \"group flex items-center hover:scale-105 transition-all duration-300\",\n              children: /*#__PURE__*/_jsxDEV(AlloraBlackWhiteLogo, {\n                size: \"medium\",\n                className: \"group-hover:drop-shadow-md transition-all duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-1 bg-gray-50/80 rounded-full p-1 backdrop-blur-sm border border-gray-200/30\",\n            children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = item.path,\n              className: `px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${isActivePath(item.path) ? 'bg-black text-white shadow-lg' : 'text-gray-600 hover:bg-white hover:text-black hover:shadow-md'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), item.label]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSearch,\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"w-64 px-4 py-2 pl-10 bg-gray-50/80 border border-gray-200/50 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 text-sm backdrop-blur-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-4 w-4 text-gray-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => window.location.href = '/cart',\n                className: \"relative p-2 bg-gray-50/80 rounded-full text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200\",\n                title: token ? 'View Cart' : 'View Cart (Guest)',\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 1.5,\n                    d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), cartCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium\",\n                  children: cartCount\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), token ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => window.location.href = '/account',\n                  className: \"flex items-center space-x-2 px-3 py-2 bg-gray-50/80 rounded-full text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-gray-600\",\n                      children: (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()) || 'U'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: (user === null || user === void 0 ? void 0 : user.username) || 'Account'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: onLogout,\n                  className: \"px-3 py-2 bg-red-50 text-red-600 rounded-full text-sm font-medium hover:bg-red-100 transition-colors duration-200\",\n                  children: \"Logout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => window.location.href = '/login',\n                  className: \"px-4 py-2 text-gray-600 hover:text-black transition-colors duration-200 text-sm font-medium\",\n                  children: \"Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => window.location.href = '/sell',\n                  className: \"px-4 py-2 bg-emerald-600 text-white rounded-full text-sm font-medium hover:bg-emerald-700 transition-all duration-200 shadow-sm\",\n                  children: \"Sell\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:hidden flex items-center justify-between w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.href = '/',\n            className: \"group flex items-center hover:scale-110 transition-all duration-300 flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(AlloraMobileLogo, {\n              size: 36,\n              className: \"group-hover:drop-shadow-md transition-all duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSearch,\n            className: \"flex-1 mx-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"w-full px-4 py-2.5 pl-10 bg-gray-50/90 border border-gray-200/50 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 text-sm backdrop-blur-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-4 w-4 text-gray-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              setIsMenuOpen(!isMenuOpen);\n            },\n            className: \"p-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200\",\n            \"aria-label\": \"Toggle menu\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-6 h-6 flex flex-col justify-center items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-5 h-0.5 bg-current transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-1' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-5 h-0.5 bg-current transition-all duration-300 mt-1 ${isMenuOpen ? 'opacity-0' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-5 h-0.5 bg-current transition-all duration-300 mt-1 ${isMenuOpen ? '-rotate-45 -translate-y-1' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), isMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:hidden bg-white/95 backdrop-blur-lg border-t border-gray-200/50 shadow-xl\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 py-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-3 mb-6\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              window.location.href = item.path;\n              setIsMenuOpen(false);\n            },\n            className: `flex items-center justify-center space-x-2 py-4 px-3 rounded-xl text-sm font-medium transition-all duration-300 ${isActivePath(item.path) ? 'bg-black text-white shadow-lg' : 'bg-gray-50/80 text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-4 border-t border-gray-200/50\",\n          children: token ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 p-4 bg-gray-50/80 rounded-xl\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-gray-700\",\n                  children: (user === null || user === void 0 ? void 0 : (_user$username2 = user.username) === null || _user$username2 === void 0 ? void 0 : _user$username2.charAt(0).toUpperCase()) || 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: (user === null || user === void 0 ? void 0 : user.username) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Account Settings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-3\",\n              children: userNavItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  window.location.href = item.path;\n                  setIsMenuOpen(false);\n                },\n                className: \"relative flex items-center justify-center space-x-2 py-3 px-3 bg-gray-50/80 rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-base\",\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: item.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 25\n                }, this), item.badge && item.badge > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium\",\n                  children: item.badge\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 27\n                }, this)]\n              }, item.path, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                onLogout();\n                setIsMenuOpen(false);\n              },\n              className: \"w-full py-3 px-4 bg-red-50 text-red-600 rounded-xl text-sm font-medium hover:bg-red-100 transition-colors duration-200\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                window.location.href = '/cart';\n                setIsMenuOpen(false);\n              },\n              className: \"relative w-full flex items-center justify-center space-x-2 py-3 px-4 bg-gray-50/80 rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 1.5,\n                  d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Cart (Guest)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), cartCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium\",\n                children: cartCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                window.location.href = '/login';\n                setIsMenuOpen(false);\n              },\n              className: \"w-full py-3 px-4 text-center rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200 border border-gray-200/50\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                window.location.href = '/sell';\n                setIsMenuOpen(false);\n              },\n              className: \"w-full py-3 px-4 bg-emerald-600 text-white rounded-xl text-sm font-medium hover:bg-emerald-700 transition-all duration-200 shadow-sm\",\n              children: \"Sell\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernMinimalistNavbar, \"uCC26ZH7YGE8HM/3R23e+OsI6uE=\", false, function () {\n  return [useLocation, useCart];\n});\n_c = ModernMinimalistNavbar;\nexport default ModernMinimalistNavbar;\nvar _c;\n$RefreshReg$(_c, \"ModernMinimalistNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "AlloraBlackWhiteLogo", "AlloraMobile<PERSON>ogo", "useCart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModernMinimalistNavbar", "token", "user", "onLogout", "_s", "_user$username", "_user$username2", "isMenuOpen", "setIsMenuOpen", "searchTerm", "setSearchTerm", "isScrolled", "setIsScrolled", "location", "cartCount", "isActivePath", "path", "pathname", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "handleClickOutside", "document", "handleSearch", "e", "preventDefault", "trim", "href", "encodeURIComponent", "navItems", "label", "icon", "userNavItems", "badge", "className", "children", "onClick", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "onSubmit", "type", "placeholder", "value", "onChange", "target", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "title", "username", "char<PERSON>t", "toUpperCase", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/components/ModernMinimalistNavbar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { AlloraBlackWhiteLogo, AlloraMobileLogo } from './AlloraLogo';\nimport { useCart } from '../contexts/CartContext';\n\nconst ModernMinimalistNavbar = ({ token, user, onLogout }) => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isScrolled, setIsScrolled] = useState(false);\n  const location = useLocation();\n  const { cartCount } = useCart();\n\n  const isActivePath = (path) => location.pathname === path;\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when clicking outside\n  useEffect(() => {\n    const handleClickOutside = () => {\n      if (isMenuOpen) setIsMenuOpen(false);\n    };\n    document.addEventListener('click', handleClickOutside);\n    return () => document.removeEventListener('click', handleClickOutside);\n  }, [isMenuOpen]);\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchTerm.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchTerm)}`;\n    }\n  };\n\n  // Navigation items\n  const navItems = [\n    { path: '/', label: 'Home', icon: '🏠' },\n    { path: '/search', label: 'Search', icon: '🔍' },\n    { path: '/categories', label: 'Categories', icon: '📂' },\n    { path: '/community', label: 'Community', icon: '👥' },\n  ];\n\n  const userNavItems = token ? [\n    { path: '/account', label: 'Account', icon: '👤' },\n    { path: '/cart', label: 'Cart', icon: '🛒', badge: cartCount },\n    { path: '/inventory', label: 'Inventory', icon: '📦' },\n    { path: '/price-trends', label: 'Trends', icon: '📈' },\n  ] : [];\n\n  return (\n    <header className={`sticky top-0 z-50 transition-all duration-300 ${\n      isScrolled\n        ? 'bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-200/50'\n        : 'bg-white/90 backdrop-blur-sm border-b border-gray-100'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-4 lg:px-6\">\n        <div className=\"flex items-center justify-between h-16 lg:h-18\">\n\n          {/* Desktop Layout */}\n          <div className=\"hidden lg:flex items-center justify-between w-full\">\n            {/* Left Section - Logo */}\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => window.location.href = '/'}\n                className=\"group flex items-center hover:scale-105 transition-all duration-300\"\n              >\n                <AlloraBlackWhiteLogo\n                  size=\"medium\"\n                  className=\"group-hover:drop-shadow-md transition-all duration-300\"\n                />\n              </button>\n            </div>\n\n            {/* Center Section - Floating Navigation Pills */}\n            <div className=\"flex items-center space-x-1 bg-gray-50/80 rounded-full p-1 backdrop-blur-sm border border-gray-200/30\">\n              {navItems.map((item) => (\n                <button\n                  key={item.path}\n                  onClick={() => window.location.href = item.path}\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${\n                    isActivePath(item.path)\n                      ? 'bg-black text-white shadow-lg'\n                      : 'text-gray-600 hover:bg-white hover:text-black hover:shadow-md'\n                  }`}\n                >\n                  <span className=\"mr-2\">{item.icon}</span>\n                  {item.label}\n                </button>\n              ))}\n            </div>\n\n            {/* Right Section - Search & User Actions */}\n            <div className=\"flex items-center space-x-3\">\n              {/* Search Bar */}\n              <form onSubmit={handleSearch} className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-64 px-4 py-2 pl-10 bg-gray-50/80 border border-gray-200/50 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 text-sm backdrop-blur-sm\"\n                />\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-4 w-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n              </form>\n\n              {/* User Actions */}\n              <div className=\"flex items-center space-x-2\">\n                {/* Cart Icon - Always visible for both logged-in and guest users */}\n                <button\n                  onClick={() => window.location.href = '/cart'}\n                  className=\"relative p-2 bg-gray-50/80 rounded-full text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200\"\n                  title={token ? 'View Cart' : 'View Cart (Guest)'}\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z\" />\n                  </svg>\n                  {cartCount > 0 && (\n                    <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium\">\n                      {cartCount}\n                    </span>\n                  )}\n                </button>\n\n                {/* User Account Actions - Only for logged-in users */}\n                {token ? (\n                  <>\n                    <button\n                      onClick={() => window.location.href = '/account'}\n                      className=\"flex items-center space-x-2 px-3 py-2 bg-gray-50/80 rounded-full text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200\"\n                    >\n                      <div className=\"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\">\n                        <span className=\"text-xs font-medium text-gray-600\">\n                          {user?.username?.charAt(0).toUpperCase() || 'U'}\n                        </span>\n                      </div>\n                      <span className=\"text-sm font-medium\">{user?.username || 'Account'}</span>\n                    </button>\n                    <button\n                      onClick={onLogout}\n                      className=\"px-3 py-2 bg-red-50 text-red-600 rounded-full text-sm font-medium hover:bg-red-100 transition-colors duration-200\"\n                    >\n                      Logout\n                    </button>\n                  </>\n                ) : (\n                  <>\n                    <button\n                      onClick={() => window.location.href = '/login'}\n                      className=\"px-4 py-2 text-gray-600 hover:text-black transition-colors duration-200 text-sm font-medium\"\n                    >\n                      Login\n                    </button>\n                    <button\n                      onClick={() => window.location.href = '/sell'}\n                      className=\"px-4 py-2 bg-emerald-600 text-white rounded-full text-sm font-medium hover:bg-emerald-700 transition-all duration-200 shadow-sm\"\n                    >\n                      Sell\n                    </button>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Mobile Layout */}\n          <div className=\"lg:hidden flex items-center justify-between w-full\">\n            {/* Mobile Logo */}\n            <button\n              onClick={() => window.location.href = '/'}\n              className=\"group flex items-center hover:scale-110 transition-all duration-300 flex-shrink-0\"\n            >\n              <AlloraMobileLogo\n                size={36}\n                className=\"group-hover:drop-shadow-md transition-all duration-300\"\n              />\n            </button>\n\n            {/* Mobile Search Bar */}\n            <form onSubmit={handleSearch} className=\"flex-1 mx-4\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full px-4 py-2.5 pl-10 bg-gray-50/90 border border-gray-200/50 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 text-sm backdrop-blur-sm\"\n                />\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-4 w-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n              </div>\n            </form>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={(e) => {\n                e.stopPropagation();\n                setIsMenuOpen(!isMenuOpen);\n              }}\n              className=\"p-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200\"\n              aria-label=\"Toggle menu\"\n            >\n              <div className=\"w-6 h-6 flex flex-col justify-center items-center\">\n                <div className={`w-5 h-0.5 bg-current transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-1' : ''}`}></div>\n                <div className={`w-5 h-0.5 bg-current transition-all duration-300 mt-1 ${isMenuOpen ? 'opacity-0' : ''}`}></div>\n                <div className={`w-5 h-0.5 bg-current transition-all duration-300 mt-1 ${isMenuOpen ? '-rotate-45 -translate-y-1' : ''}`}></div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMenuOpen && (\n        <div className=\"lg:hidden bg-white/95 backdrop-blur-lg border-t border-gray-200/50 shadow-xl\">\n          <div className=\"max-w-7xl mx-auto px-4 py-6\">\n            {/* Mobile Navigation Grid */}\n            <div className=\"grid grid-cols-2 gap-3 mb-6\">\n              {navItems.map((item) => (\n                <button\n                  key={item.path}\n                  onClick={() => {\n                    window.location.href = item.path;\n                    setIsMenuOpen(false);\n                  }}\n                  className={`flex items-center justify-center space-x-2 py-4 px-3 rounded-xl text-sm font-medium transition-all duration-300 ${\n                    isActivePath(item.path)\n                      ? 'bg-black text-white shadow-lg'\n                      : 'bg-gray-50/80 text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-md'\n                  }`}\n                >\n                  <span className=\"text-lg\">{item.icon}</span>\n                  <span>{item.label}</span>\n                </button>\n              ))}\n            </div>\n\n            {/* Mobile User Actions */}\n            <div className=\"pt-4 border-t border-gray-200/50\">\n              {token ? (\n                <div className=\"space-y-3\">\n                  {/* User Profile Card */}\n                  <div className=\"flex items-center space-x-3 p-4 bg-gray-50/80 rounded-xl\">\n                    <div className=\"w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-sm\">\n                      <span className=\"text-sm font-semibold text-gray-700\">\n                        {user?.username?.charAt(0).toUpperCase() || 'U'}\n                      </span>\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {user?.username || 'User'}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">Account Settings</p>\n                    </div>\n                  </div>\n\n                  {/* User Action Grid */}\n                  <div className=\"grid grid-cols-2 gap-3\">\n                    {userNavItems.map((item) => (\n                      <button\n                        key={item.path}\n                        onClick={() => {\n                          window.location.href = item.path;\n                          setIsMenuOpen(false);\n                        }}\n                        className=\"relative flex items-center justify-center space-x-2 py-3 px-3 bg-gray-50/80 rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-300\"\n                      >\n                        <span className=\"text-base\">{item.icon}</span>\n                        <span>{item.label}</span>\n                        {item.badge && item.badge > 0 && (\n                          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium\">\n                            {item.badge}\n                          </span>\n                        )}\n                      </button>\n                    ))}\n                  </div>\n\n                  {/* Logout Button */}\n                  <button\n                    onClick={() => {\n                      onLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full py-3 px-4 bg-red-50 text-red-600 rounded-xl text-sm font-medium hover:bg-red-100 transition-colors duration-200\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {/* Guest Cart Button */}\n                  <button\n                    onClick={() => {\n                      window.location.href = '/cart';\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"relative w-full flex items-center justify-center space-x-2 py-3 px-4 bg-gray-50/80 rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200\"\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z\" />\n                    </svg>\n                    <span>Cart (Guest)</span>\n                    {cartCount > 0 && (\n                      <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium\">\n                        {cartCount}\n                      </span>\n                    )}\n                  </button>\n\n                  <button\n                    onClick={() => {\n                      window.location.href = '/login';\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full py-3 px-4 text-center rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200 border border-gray-200/50\"\n                  >\n                    Login\n                  </button>\n                  <button\n                    onClick={() => {\n                      window.location.href = '/sell';\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full py-3 px-4 bg-emerald-600 text-white rounded-xl text-sm font-medium hover:bg-emerald-700 transition-all duration-200 shadow-sm\"\n                  >\n                    Sell\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default ModernMinimalistNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,oBAAoB,EAAEC,gBAAgB,QAAQ,cAAc;AACrE,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,eAAA;EAC5D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMuB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsB;EAAU,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAE/B,MAAMoB,YAAY,GAAIC,IAAI,IAAKH,QAAQ,CAACI,QAAQ,KAAKD,IAAI;;EAEzD;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAGA,CAAA,KAAM;MACzBN,aAAa,CAACO,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IACDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3B,SAAS,CAAC,MAAM;IACd,MAAMgC,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAIhB,UAAU,EAAEC,aAAa,CAAC,KAAK,CAAC;IACtC,CAAC;IACDgB,QAAQ,CAACH,gBAAgB,CAAC,OAAO,EAAEE,kBAAkB,CAAC;IACtD,OAAO,MAAMC,QAAQ,CAACF,mBAAmB,CAAC,OAAO,EAAEC,kBAAkB,CAAC;EACxE,CAAC,EAAE,CAAChB,UAAU,CAAC,CAAC;EAEhB,MAAMkB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIlB,UAAU,CAACmB,IAAI,CAAC,CAAC,EAAE;MACrBT,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,aAAaC,kBAAkB,CAACrB,UAAU,CAAC,EAAE;IACtE;EACF,CAAC;;EAED;EACA,MAAMsB,QAAQ,GAAG,CACf;IAAEf,IAAI,EAAE,GAAG;IAAEgB,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAK,CAAC,EACxC;IAAEjB,IAAI,EAAE,SAAS;IAAEgB,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEjB,IAAI,EAAE,aAAa;IAAEgB,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,EACxD;IAAEjB,IAAI,EAAE,YAAY;IAAEgB,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,CACvD;EAED,MAAMC,YAAY,GAAGjC,KAAK,GAAG,CAC3B;IAAEe,IAAI,EAAE,UAAU;IAAEgB,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAK,CAAC,EAClD;IAAEjB,IAAI,EAAE,OAAO;IAAEgB,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,IAAI;IAAEE,KAAK,EAAErB;EAAU,CAAC,EAC9D;IAAEE,IAAI,EAAE,YAAY;IAAEgB,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,EACtD;IAAEjB,IAAI,EAAE,eAAe;IAAEgB,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAK,CAAC,CACvD,GAAG,EAAE;EAEN,oBACEpC,OAAA;IAAQuC,SAAS,EAAE,iDACjBzB,UAAU,GACN,oEAAoE,GACpE,uDAAuD,EAC1D;IAAA0B,QAAA,gBACDxC,OAAA;MAAKuC,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CxC,OAAA;QAAKuC,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAG7DxC,OAAA;UAAKuC,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBAEjExC,OAAA;YAAKuC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCxC,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAMnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,GAAI;cAC1CO,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAE/ExC,OAAA,CAACJ,oBAAoB;gBACnB8C,IAAI,EAAC,QAAQ;gBACbH,SAAS,EAAC;cAAwD;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN9C,OAAA;YAAKuC,SAAS,EAAC,uGAAuG;YAAAC,QAAA,EACnHN,QAAQ,CAACa,GAAG,CAAEC,IAAI,iBACjBhD,OAAA;cAEEyC,OAAO,EAAEA,CAAA,KAAMnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAGgB,IAAI,CAAC7B,IAAK;cAChDoB,SAAS,EAAE,0EACTrB,YAAY,CAAC8B,IAAI,CAAC7B,IAAI,CAAC,GACnB,+BAA+B,GAC/B,+DAA+D,EAClE;cAAAqB,QAAA,gBAEHxC,OAAA;gBAAMuC,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEQ,IAAI,CAACZ;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACxCE,IAAI,CAACb,KAAK;YAAA,GATNa,IAAI,CAAC7B,IAAI;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN9C,OAAA;YAAKuC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1CxC,OAAA;cAAMiD,QAAQ,EAAErB,YAAa;cAACW,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAChDxC,OAAA;gBACEkD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,WAAW;gBACvBC,KAAK,EAAExC,UAAW;gBAClByC,QAAQ,EAAGxB,CAAC,IAAKhB,aAAa,CAACgB,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE;gBAC/Cb,SAAS,EAAC;cAA6O;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxP,CAAC,eACF9C,OAAA;gBAAKuC,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnFxC,OAAA;kBAAKuC,SAAS,EAAC,uBAAuB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,eAC1FxC,OAAA;oBAAM0D,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA6C;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGP9C,OAAA;cAAKuC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAE1CxC,OAAA;gBACEyC,OAAO,EAAEA,CAAA,KAAMnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,OAAQ;gBAC9CO,SAAS,EAAC,sHAAsH;gBAChIuB,KAAK,EAAE1D,KAAK,GAAG,WAAW,GAAG,mBAAoB;gBAAAoC,QAAA,gBAEjDxC,OAAA;kBAAKuC,SAAS,EAAC,SAAS;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,eAC5ExC,OAAA;oBAAM0D,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,GAAI;oBAACC,CAAC,EAAC;kBAA6C;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpH,CAAC,EACL7B,SAAS,GAAG,CAAC,iBACZjB,OAAA;kBAAMuC,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,EACvIvB;gBAAS;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,EAGR1C,KAAK,gBACJJ,OAAA,CAAAE,SAAA;gBAAAsC,QAAA,gBACExC,OAAA;kBACEyC,OAAO,EAAEA,CAAA,KAAMnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,UAAW;kBACjDO,SAAS,EAAC,+IAA+I;kBAAAC,QAAA,gBAEzJxC,OAAA;oBAAKuC,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFxC,OAAA;sBAAMuC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAChD,CAAAnC,IAAI,aAAJA,IAAI,wBAAAG,cAAA,GAAJH,IAAI,CAAE0D,QAAQ,cAAAvD,cAAA,uBAAdA,cAAA,CAAgBwD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;oBAAG;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN9C,OAAA;oBAAMuC,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAE,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,QAAQ,KAAI;kBAAS;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACT9C,OAAA;kBACEyC,OAAO,EAAEnC,QAAS;kBAClBiC,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,EAC9H;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT,CAAC,gBAEH9C,OAAA,CAAAE,SAAA;gBAAAsC,QAAA,gBACExC,OAAA;kBACEyC,OAAO,EAAEA,CAAA,KAAMnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,QAAS;kBAC/CO,SAAS,EAAC,6FAA6F;kBAAAC,QAAA,EACxG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9C,OAAA;kBACEyC,OAAO,EAAEA,CAAA,KAAMnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,OAAQ;kBAC9CO,SAAS,EAAC,iIAAiI;kBAAAC,QAAA,EAC5I;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9C,OAAA;UAAKuC,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBAEjExC,OAAA;YACEyC,OAAO,EAAEA,CAAA,KAAMnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,GAAI;YAC1CO,SAAS,EAAC,mFAAmF;YAAAC,QAAA,eAE7FxC,OAAA,CAACH,gBAAgB;cACf6C,IAAI,EAAE,EAAG;cACTH,SAAS,EAAC;YAAwD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGT9C,OAAA;YAAMiD,QAAQ,EAAErB,YAAa;YAACW,SAAS,EAAC,aAAa;YAAAC,QAAA,eACnDxC,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBACEkD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,WAAW;gBACvBC,KAAK,EAAExC,UAAW;gBAClByC,QAAQ,EAAGxB,CAAC,IAAKhB,aAAa,CAACgB,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE;gBAC/Cb,SAAS,EAAC;cAAiP;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5P,CAAC,eACF9C,OAAA;gBAAKuC,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnFxC,OAAA;kBAAKuC,SAAS,EAAC,uBAAuB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,eAC1FxC,OAAA;oBAAM0D,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA6C;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGP9C,OAAA;YACEyC,OAAO,EAAGZ,CAAC,IAAK;cACdA,CAAC,CAACqC,eAAe,CAAC,CAAC;cACnBvD,aAAa,CAAC,CAACD,UAAU,CAAC;YAC5B,CAAE;YACF6B,SAAS,EAAC,6FAA6F;YACvG,cAAW,aAAa;YAAAC,QAAA,eAExBxC,OAAA;cAAKuC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAChExC,OAAA;gBAAKuC,SAAS,EAAE,oDAAoD7B,UAAU,GAAG,yBAAyB,GAAG,EAAE;cAAG;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzH9C,OAAA;gBAAKuC,SAAS,EAAE,yDAAyD7B,UAAU,GAAG,WAAW,GAAG,EAAE;cAAG;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChH9C,OAAA;gBAAKuC,SAAS,EAAE,yDAAyD7B,UAAU,GAAG,2BAA2B,GAAG,EAAE;cAAG;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpC,UAAU,iBACTV,OAAA;MAAKuC,SAAS,EAAC,8EAA8E;MAAAC,QAAA,eAC3FxC,OAAA;QAAKuC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CxC,OAAA;UAAKuC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCN,QAAQ,CAACa,GAAG,CAAEC,IAAI,iBACjBhD,OAAA;YAEEyC,OAAO,EAAEA,CAAA,KAAM;cACbnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAGgB,IAAI,CAAC7B,IAAI;cAChCR,aAAa,CAAC,KAAK,CAAC;YACtB,CAAE;YACF4B,SAAS,EAAE,mHACTrB,YAAY,CAAC8B,IAAI,CAAC7B,IAAI,CAAC,GACnB,+BAA+B,GAC/B,gFAAgF,EACnF;YAAAqB,QAAA,gBAEHxC,OAAA;cAAMuC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEQ,IAAI,CAACZ;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5C9C,OAAA;cAAAwC,QAAA,EAAOQ,IAAI,CAACb;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAZpBE,IAAI,CAAC7B,IAAI;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaR,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN9C,OAAA;UAAKuC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAC9CpC,KAAK,gBACJJ,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBxC,OAAA;cAAKuC,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACvExC,OAAA;gBAAKuC,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,eAC5HxC,OAAA;kBAAMuC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAClD,CAAAnC,IAAI,aAAJA,IAAI,wBAAAI,eAAA,GAAJJ,IAAI,CAAE0D,QAAQ,cAAAtD,eAAA,uBAAdA,eAAA,CAAgBuD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;gBAAG;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN9C,OAAA;gBAAKuC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBxC,OAAA;kBAAGuC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC7C,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,QAAQ,KAAI;gBAAM;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACJ9C,OAAA;kBAAGuC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9C,OAAA;cAAKuC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCH,YAAY,CAACU,GAAG,CAAEC,IAAI,iBACrBhD,OAAA;gBAEEyC,OAAO,EAAEA,CAAA,KAAM;kBACbnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAGgB,IAAI,CAAC7B,IAAI;kBAChCR,aAAa,CAAC,KAAK,CAAC;gBACtB,CAAE;gBACF4B,SAAS,EAAC,yLAAyL;gBAAAC,QAAA,gBAEnMxC,OAAA;kBAAMuC,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEQ,IAAI,CAACZ;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9C9C,OAAA;kBAAAwC,QAAA,EAAOQ,IAAI,CAACb;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACxBE,IAAI,CAACV,KAAK,IAAIU,IAAI,CAACV,KAAK,GAAG,CAAC,iBAC3BtC,OAAA;kBAAMuC,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,EACvIQ,IAAI,CAACV;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACP;cAAA,GAbIE,IAAI,CAAC7B,IAAI;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcR,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN9C,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAM;gBACbnC,QAAQ,CAAC,CAAC;gBACVK,aAAa,CAAC,KAAK,CAAC;cACtB,CAAE;cACF4B,SAAS,EAAC,wHAAwH;cAAAC,QAAA,EACnI;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAEN9C,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBxC,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAM;gBACbnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,OAAO;gBAC9BrB,aAAa,CAAC,KAAK,CAAC;cACtB,CAAE;cACF4B,SAAS,EAAC,gMAAgM;cAAAC,QAAA,gBAE1MxC,OAAA;gBAAKuC,SAAS,EAAC,SAAS;gBAACgB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAjB,QAAA,eAC5ExC,OAAA;kBAAM0D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,GAAI;kBAACC,CAAC,EAAC;gBAA6C;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC,eACN9C,OAAA;gBAAAwC,QAAA,EAAM;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACxB7B,SAAS,GAAG,CAAC,iBACZjB,OAAA;gBAAMuC,SAAS,EAAC,0HAA0H;gBAAAC,QAAA,EACvIvB;cAAS;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAET9C,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAM;gBACbnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,QAAQ;gBAC/BrB,aAAa,CAAC,KAAK,CAAC;cACtB,CAAE;cACF4B,SAAS,EAAC,oKAAoK;cAAAC,QAAA,EAC/K;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9C,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAM;gBACbnB,MAAM,CAACN,QAAQ,CAACgB,IAAI,GAAG,OAAO;gBAC9BrB,aAAa,CAAC,KAAK,CAAC;cACtB,CAAE;cACF4B,SAAS,EAAC,sIAAsI;cAAAC,QAAA,EACjJ;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACvC,EAAA,CAtVIJ,sBAAsB;EAAA,QAITR,WAAW,EACNG,OAAO;AAAA;AAAAqE,EAAA,GALzBhE,sBAAsB;AAwV5B,eAAeA,sBAAsB;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}