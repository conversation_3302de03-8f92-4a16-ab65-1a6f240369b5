"""
Recommendation API System
========================

Comprehensive recommendation API with multiple endpoints for different
recommendation types, A/B testing support, and performance monitoring.

Endpoints:
- Personalized recommendations
- Trending products
- Similar products
- Cross-sell recommendations
- Upsell recommendations
- Category-based recommendations
- Recently viewed recommendations

Author: Allora Development Team
Date: 2025-07-06
"""

from flask import Blueprint, request, jsonify, session
from datetime import datetime, timedelta
import json
import time
import uuid
from typing import Dict, List, Optional, Any, Tuple
import logging

# Import recommendation components
from personalization_engine import PersonalizationEngine
from advanced_recommendation_models import RecommendationModelManager
from recommendation_analytics import RecommendationAnalytics
from recommendation_system_architecture import (
    RecommendationRequest, RecommendationResult, RecommendationType,
    UserInteractionType
)

# Import database models
from app import db, User, Product, UserInteractionLog, UserBehaviorProfile

# Create Blueprint
recommendation_api = Blueprint('recommendation_api', __name__)

# Initialize components (will be set up in main app)
personalization_engine = None
model_manager = None
analytics_system = None

def init_recommendation_system(db_session, redis_client):
    """Initialize the recommendation system components"""
    global personalization_engine, model_manager, analytics_system
    personalization_engine = PersonalizationEngine(redis_client, db_session)
    model_manager = RecommendationModelManager()
    analytics_system = RecommendationAnalytics(db_session)

@recommendation_api.route('/api/recommendations/personalized/<int:user_id>', methods=['GET'])
def get_personalized_recommendations(user_id):
    """Get personalized recommendations for a user"""
    try:
        # Get query parameters
        limit = request.args.get('limit', 10, type=int)
        category = request.args.get('category')
        exclude_viewed = request.args.get('exclude_viewed', 'true').lower() == 'true'
        context = request.args.get('context', '{}')
        
        # Parse context
        try:
            context_data = json.loads(context)
        except:
            context_data = {}
        
        # Add request context
        context_data.update({
            'page_type': request.args.get('page_type', 'general'),
            'device_type': request.headers.get('User-Agent', '').lower(),
            'timestamp': datetime.utcnow().isoformat(),
            'session_id': request.args.get('session_id')
        })
        
        # Create recommendation request
        rec_request = RecommendationRequest(
            user_id=str(user_id),
            recommendation_type=RecommendationType.PERSONALIZED,
            limit=limit,
            context=context_data,
            filters={'category': category} if category else {}
        )
        
        # Get recommendations
        if personalization_engine:
            result = personalization_engine.get_personalized_recommendations(rec_request)
        else:
            result = _get_fallback_recommendations(user_id, limit)
        
        # Format response
        recommendations = []
        for product_id, score in result.recommendations:
            product = Product.query.get(product_id)
            if product:
                recommendations.append({
                    'product_id': product_id,
                    'score': score,
                    'product': {
                        'id': product.id,
                        'name': product.name,
                        'price': product.price,
                        'image_url': product.image_url,
                        'category': product.category,
                        'brand': product.brand,
                        'average_rating': product.average_rating
                    },
                    'reason': _generate_recommendation_reason(product, result.algorithm)
                })
        
        # Track recommendation request
        _track_recommendation_request(user_id, 'personalized', len(recommendations))
        
        return jsonify({
            'user_id': user_id,
            'recommendations': recommendations,
            'algorithm': result.algorithm,
            'confidence_score': result.confidence_score,
            'timestamp': result.timestamp.isoformat(),
            'total_count': len(recommendations)
        })
        
    except Exception as e:
        logging.error(f"Error getting personalized recommendations: {e}")
        return jsonify({'error': 'Failed to get recommendations'}), 500

@recommendation_api.route('/api/recommendations/similar/<int:product_id>', methods=['GET'])
def get_similar_products(product_id):
    """Get products similar to a given product"""
    try:
        limit = request.args.get('limit', 10, type=int)
        user_id = request.args.get('user_id', type=int)
        
        # Get the base product
        base_product = Product.query.get(product_id)
        if not base_product:
            return jsonify({'error': 'Product not found'}), 404
        
        # Get similar products using content-based filtering
        if model_manager and hasattr(model_manager.models['content_based'], 'trained'):
            similar_items = model_manager.models['content_based'].get_similar_items(
                product_id, limit
            )
        else:
            # Fallback: get products from same category
            similar_items = _get_similar_products_fallback(base_product, limit)
        
        # Format response
        recommendations = []
        for similar_product_id, similarity_score in similar_items:
            product = Product.query.get(similar_product_id)
            if product:
                recommendations.append({
                    'product_id': similar_product_id,
                    'similarity_score': similarity_score,
                    'product': {
                        'id': product.id,
                        'name': product.name,
                        'price': product.price,
                        'image_url': product.image_url,
                        'category': product.category,
                        'brand': product.brand,
                        'average_rating': product.average_rating
                    },
                    'reason': f"Similar to {base_product.name}"
                })
        
        # Track recommendation request
        if user_id:
            _track_recommendation_request(user_id, 'similar', len(recommendations))
        
        return jsonify({
            'base_product_id': product_id,
            'recommendations': recommendations,
            'algorithm': 'content_based',
            'total_count': len(recommendations)
        })
        
    except Exception as e:
        logging.error(f"Error getting similar products: {e}")
        return jsonify({'error': 'Failed to get similar products'}), 500

@recommendation_api.route('/api/recommendations/trending', methods=['GET'])
def get_trending_products():
    """Get trending products based on recent interactions"""
    try:
        limit = request.args.get('limit', 10, type=int)
        category = request.args.get('category')
        time_window = request.args.get('time_window', 7, type=int)  # days
        
        # Calculate trending products based on recent interactions
        cutoff_date = datetime.utcnow() - timedelta(days=time_window)
        
        # Query for trending products
        trending_query = db.session.query(
            UserInteractionLog.product_id,
            db.func.count(UserInteractionLog.id).label('interaction_count'),
            db.func.count(db.distinct(UserInteractionLog.user_id)).label('unique_users')
        ).filter(
            UserInteractionLog.timestamp >= cutoff_date,
            UserInteractionLog.product_id.isnot(None)
        ).group_by(UserInteractionLog.product_id)
        
        if category:
            trending_query = trending_query.join(Product).filter(Product.category == category)
        
        trending_products = trending_query.order_by(
            db.desc('interaction_count')
        ).limit(limit).all()
        
        # Format response
        recommendations = []
        for product_id, interaction_count, unique_users in trending_products:
            product = Product.query.get(product_id)
            if product:
                # Calculate trending score
                trending_score = (interaction_count * 0.7) + (unique_users * 0.3)
                
                recommendations.append({
                    'product_id': product_id,
                    'trending_score': trending_score,
                    'interaction_count': interaction_count,
                    'unique_users': unique_users,
                    'product': {
                        'id': product.id,
                        'name': product.name,
                        'price': product.price,
                        'image_url': product.image_url,
                        'category': product.category,
                        'brand': product.brand,
                        'average_rating': product.average_rating
                    },
                    'reason': f"Trending with {interaction_count} interactions"
                })
        
        return jsonify({
            'recommendations': recommendations,
            'algorithm': 'trending',
            'time_window_days': time_window,
            'category': category,
            'total_count': len(recommendations)
        })
        
    except Exception as e:
        logging.error(f"Error getting trending products: {e}")
        return jsonify({'error': 'Failed to get trending products'}), 500

@recommendation_api.route('/api/recommendations/cross-sell/<int:product_id>', methods=['GET'])
def get_cross_sell_recommendations(product_id):
    """Get cross-sell recommendations for a product"""
    try:
        limit = request.args.get('limit', 5, type=int)
        user_id = request.args.get('user_id', type=int)
        
        # Get the base product
        base_product = Product.query.get(product_id)
        if not base_product:
            return jsonify({'error': 'Product not found'}), 404
        
        # Find products frequently bought together
        cross_sell_products = _get_cross_sell_products(product_id, limit)
        
        # Format response
        recommendations = []
        for cross_product_id, frequency_score in cross_sell_products:
            product = Product.query.get(cross_product_id)
            if product:
                recommendations.append({
                    'product_id': cross_product_id,
                    'frequency_score': frequency_score,
                    'product': {
                        'id': product.id,
                        'name': product.name,
                        'price': product.price,
                        'image_url': product.image_url,
                        'category': product.category,
                        'brand': product.brand,
                        'average_rating': product.average_rating
                    },
                    'reason': f"Frequently bought with {base_product.name}"
                })
        
        # Track recommendation request
        if user_id:
            _track_recommendation_request(user_id, 'cross_sell', len(recommendations))
        
        return jsonify({
            'base_product_id': product_id,
            'recommendations': recommendations,
            'algorithm': 'cross_sell',
            'total_count': len(recommendations)
        })
        
    except Exception as e:
        logging.error(f"Error getting cross-sell recommendations: {e}")
        return jsonify({'error': 'Failed to get cross-sell recommendations'}), 500

@recommendation_api.route('/api/recommendations/upsell/<int:product_id>', methods=['GET'])
def get_upsell_recommendations(product_id):
    """Get upsell recommendations for a product"""
    try:
        limit = request.args.get('limit', 5, type=int)
        user_id = request.args.get('user_id', type=int)
        
        # Get the base product
        base_product = Product.query.get(product_id)
        if not base_product:
            return jsonify({'error': 'Product not found'}), 404
        
        # Find higher-priced products in same category
        upsell_products = Product.query.filter(
            Product.category == base_product.category,
            Product.price > base_product.price,
            Product.id != product_id
        ).order_by(Product.average_rating.desc(), Product.price.asc()).limit(limit).all()
        
        # Format response
        recommendations = []
        for product in upsell_products:
            price_difference = product.price - base_product.price
            value_score = (product.average_rating or 3.0) / (price_difference / base_product.price + 1)
            
            recommendations.append({
                'product_id': product.id,
                'value_score': value_score,
                'price_difference': price_difference,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'price': product.price,
                    'image_url': product.image_url,
                    'category': product.category,
                    'brand': product.brand,
                    'average_rating': product.average_rating
                },
                'reason': f"Premium alternative to {base_product.name}"
            })
        
        # Track recommendation request
        if user_id:
            _track_recommendation_request(user_id, 'upsell', len(recommendations))
        
        return jsonify({
            'base_product_id': product_id,
            'recommendations': recommendations,
            'algorithm': 'upsell',
            'total_count': len(recommendations)
        })
        
    except Exception as e:
        logging.error(f"Error getting upsell recommendations: {e}")
        return jsonify({'error': 'Failed to get upsell recommendations'}), 500

@recommendation_api.route('/api/recommendations/category/<category>', methods=['GET'])
def get_category_recommendations(category):
    """Get recommendations for a specific category"""
    try:
        limit = request.args.get('limit', 10, type=int)
        user_id = request.args.get('user_id', type=int)
        sort_by = request.args.get('sort_by', 'rating')  # rating, price, popularity
        
        # Base query for category products
        query = Product.query.filter(Product.category == category)
        
        # Apply sorting
        if sort_by == 'rating':
            query = query.order_by(Product.average_rating.desc())
        elif sort_by == 'price':
            query = query.order_by(Product.price.asc())
        elif sort_by == 'popularity':
            # Join with interaction logs to get popularity
            query = query.outerjoin(UserInteractionLog).group_by(Product.id).order_by(
                db.func.count(UserInteractionLog.id).desc()
            )
        
        products = query.limit(limit).all()
        
        # Format response
        recommendations = []
        for i, product in enumerate(products):
            recommendations.append({
                'product_id': product.id,
                'rank': i + 1,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'price': product.price,
                    'image_url': product.image_url,
                    'category': product.category,
                    'brand': product.brand,
                    'average_rating': product.average_rating
                },
                'reason': f"Top {category} product"
            })
        
        # Track recommendation request
        if user_id:
            _track_recommendation_request(user_id, 'category', len(recommendations))
        
        return jsonify({
            'category': category,
            'recommendations': recommendations,
            'algorithm': 'category_based',
            'sort_by': sort_by,
            'total_count': len(recommendations)
        })
        
    except Exception as e:
        logging.error(f"Error getting category recommendations: {e}")
        return jsonify({'error': 'Failed to get category recommendations'}), 500

@recommendation_api.route('/api/recommendations/recently-viewed/<int:user_id>', methods=['GET'])
def get_recently_viewed_recommendations(user_id):
    """Get recommendations based on recently viewed products"""
    try:
        limit = request.args.get('limit', 10, type=int)
        
        # Get recently viewed products
        recent_views = UserInteractionLog.query.filter(
            UserInteractionLog.user_id == user_id,
            UserInteractionLog.interaction_type == 'view',
            UserInteractionLog.product_id.isnot(None)
        ).order_by(UserInteractionLog.timestamp.desc()).limit(20).all()
        
        if not recent_views:
            return jsonify({
                'user_id': user_id,
                'recommendations': [],
                'message': 'No recently viewed products'
            })
        
        # Get similar products for recently viewed items
        all_recommendations = []
        viewed_product_ids = set()
        
        for view in recent_views[:5]:  # Use top 5 recent views
            if view.product_id not in viewed_product_ids:
                viewed_product_ids.add(view.product_id)
                
                # Get similar products
                if model_manager and hasattr(model_manager.models['content_based'], 'trained'):
                    similar_items = model_manager.models['content_based'].get_similar_items(
                        view.product_id, 3
                    )
                    all_recommendations.extend(similar_items)
        
        # Remove duplicates and sort by score
        unique_recommendations = {}
        for product_id, score in all_recommendations:
            if product_id not in viewed_product_ids:  # Exclude already viewed
                if product_id not in unique_recommendations:
                    unique_recommendations[product_id] = score
                else:
                    unique_recommendations[product_id] = max(
                        unique_recommendations[product_id], score
                    )
        
        # Sort and limit
        sorted_recommendations = sorted(
            unique_recommendations.items(), key=lambda x: x[1], reverse=True
        )[:limit]
        
        # Format response
        recommendations = []
        for product_id, score in sorted_recommendations:
            product = Product.query.get(product_id)
            if product:
                recommendations.append({
                    'product_id': product_id,
                    'similarity_score': score,
                    'product': {
                        'id': product.id,
                        'name': product.name,
                        'price': product.price,
                        'image_url': product.image_url,
                        'category': product.category,
                        'brand': product.brand,
                        'average_rating': product.average_rating
                    },
                    'reason': "Based on your recently viewed products"
                })
        
        # Track recommendation request
        _track_recommendation_request(user_id, 'recently_viewed', len(recommendations))
        
        return jsonify({
            'user_id': user_id,
            'recommendations': recommendations,
            'algorithm': 'recently_viewed',
            'total_count': len(recommendations)
        })
        
    except Exception as e:
        logging.error(f"Error getting recently viewed recommendations: {e}")
        return jsonify({'error': 'Failed to get recently viewed recommendations'}), 500

# Helper functions

def _get_fallback_recommendations(user_id: int, limit: int) -> RecommendationResult:
    """Fallback recommendations when personalization engine is not available"""
    # Get popular products
    popular_products = Product.query.order_by(
        Product.average_rating.desc()
    ).limit(limit).all()
    
    recommendations = [(str(p.id), p.average_rating or 3.0) for p in popular_products]
    
    return RecommendationResult(
        user_id=str(user_id),
        recommendations=recommendations,
        algorithm='fallback_popular',
        context={},
        timestamp=datetime.utcnow(),
        confidence_score=0.5
    )

def _generate_recommendation_reason(product: Product, algorithm: str) -> str:
    """Generate human-readable reason for recommendation"""
    reasons = {
        'hybrid': f"Recommended for you based on your preferences",
        'collaborative_user': f"Users like you also liked this",
        'collaborative_item': f"Similar to products you've viewed",
        'content_based': f"Matches your interest in {product.category}",
        'matrix_factorization': f"Personalized recommendation",
        'fallback_popular': f"Popular choice"
    }
    
    return reasons.get(algorithm, "Recommended for you")

def _get_similar_products_fallback(base_product: Product, limit: int) -> List[Tuple[int, float]]:
    """Fallback method to get similar products"""
    similar_products = Product.query.filter(
        Product.category == base_product.category,
        Product.id != base_product.id
    ).order_by(Product.average_rating.desc()).limit(limit).all()
    
    return [(p.id, 0.8) for p in similar_products]

def _get_cross_sell_products(product_id: int, limit: int) -> List[Tuple[int, float]]:
    """Get products frequently bought together"""
    # This is a simplified implementation
    # In a real system, you'd analyze purchase history to find co-purchased items
    
    base_product = Product.query.get(product_id)
    if not base_product:
        return []
    
    # For now, return products from complementary categories
    complementary_categories = {
        'electronics': ['accessories', 'cables'],
        'clothing': ['shoes', 'accessories'],
        'books': ['stationery', 'bookmarks']
    }
    
    target_categories = complementary_categories.get(base_product.category, [base_product.category])
    
    cross_sell_products = Product.query.filter(
        Product.category.in_(target_categories),
        Product.id != product_id
    ).order_by(Product.average_rating.desc()).limit(limit).all()
    
    return [(p.id, 0.7) for p in cross_sell_products]

def _track_recommendation_request(user_id: int, recommendation_type: str, count: int):
    """Track recommendation request for analytics"""
    try:
        # This could be enhanced to store in a separate analytics table
        interaction_log = UserInteractionLog(
            user_id=user_id,
            product_id=None,
            session_id=str(uuid.uuid4()),
            interaction_type='recommendation_request',
            interaction_value=count,
            context_data={
                'recommendation_type': recommendation_type,
                'timestamp': datetime.utcnow().isoformat()
            }
        )
        
        db.session.add(interaction_log)
        db.session.commit()
        
    except Exception as e:
        logging.warning(f"Failed to track recommendation request: {e}")

@recommendation_api.route('/api/recommendations/analytics/performance', methods=['GET'])
def get_performance_analytics():
    """Get recommendation performance analytics"""
    try:
        # Get query parameters
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        algorithm = request.args.get('algorithm')

        # Parse dates
        start_date = None
        end_date = None

        if start_date_str:
            start_date = datetime.fromisoformat(start_date_str.replace('Z', '+00:00'))
        if end_date_str:
            end_date = datetime.fromisoformat(end_date_str.replace('Z', '+00:00'))

        # Get analytics report
        if analytics_system:
            report = analytics_system.get_recommendation_performance_report(
                start_date, end_date, algorithm
            )
        else:
            report = {'error': 'Analytics system not initialized'}

        return jsonify(report)

    except Exception as e:
        logging.error(f"Error getting performance analytics: {e}")
        return jsonify({'error': 'Failed to get performance analytics'}), 500

@recommendation_api.route('/api/recommendations/analytics/ab-test/<test_name>', methods=['GET'])
def get_ab_test_results(test_name):
    """Get A/B test results for recommendation algorithms"""
    try:
        # Get query parameters
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        # Parse dates
        start_date = None
        end_date = None

        if start_date_str:
            start_date = datetime.fromisoformat(start_date_str.replace('Z', '+00:00'))
        if end_date_str:
            end_date = datetime.fromisoformat(end_date_str.replace('Z', '+00:00'))

        # Get A/B test results
        if analytics_system:
            results = analytics_system.get_ab_test_results(test_name, start_date, end_date)
        else:
            results = {'error': 'Analytics system not initialized'}

        return jsonify(results)

    except Exception as e:
        logging.error(f"Error getting A/B test results: {e}")
        return jsonify({'error': 'Failed to get A/B test results'}), 500

@recommendation_api.route('/api/recommendations/track/click', methods=['POST'])
def track_recommendation_click():
    """Track when a user clicks on a recommended product"""
    try:
        data = request.get_json()

        if not data or 'user_id' not in data or 'product_id' not in data:
            return jsonify({'error': 'Missing required fields'}), 400

        # Create interaction log
        interaction_log = UserInteractionLog(
            user_id=data['user_id'],
            product_id=data['product_id'],
            session_id=data.get('session_id', str(uuid.uuid4())),
            interaction_type='click',
            interaction_value=1,
            context_data={
                'recommendation_context': data.get('recommendation_context', {}),
                'algorithm': data.get('algorithm'),
                'position': data.get('position'),
                'timestamp': datetime.utcnow().isoformat()
            }
        )

        db.session.add(interaction_log)
        db.session.commit()

        # Update user profile if personalization engine is available
        if personalization_engine:
            personalization_engine.update_user_profile(
                str(data['user_id']),
                UserInteractionType.CLICK,
                str(data['product_id']),
                data.get('recommendation_context', {})
            )

        return jsonify({'status': 'success', 'message': 'Click tracked successfully'})

    except Exception as e:
        logging.error(f"Error tracking recommendation click: {e}")
        return jsonify({'error': 'Failed to track click'}), 500

@recommendation_api.route('/api/recommendations/track/conversion', methods=['POST'])
def track_recommendation_conversion():
    """Track when a recommended product leads to a conversion"""
    try:
        data = request.get_json()

        if not data or 'user_id' not in data or 'product_id' not in data:
            return jsonify({'error': 'Missing required fields'}), 400

        conversion_type = data.get('conversion_type', 'purchase')
        conversion_value = data.get('conversion_value', 0)

        # Create interaction log
        interaction_log = UserInteractionLog(
            user_id=data['user_id'],
            product_id=data['product_id'],
            session_id=data.get('session_id', str(uuid.uuid4())),
            interaction_type=conversion_type,
            interaction_value=conversion_value,
            context_data={
                'recommendation_context': data.get('recommendation_context', {}),
                'algorithm': data.get('algorithm'),
                'position': data.get('position'),
                'order_id': data.get('order_id'),
                'timestamp': datetime.utcnow().isoformat()
            }
        )

        db.session.add(interaction_log)
        db.session.commit()

        # Update user profile
        if personalization_engine:
            interaction_type = UserInteractionType.PURCHASE if conversion_type == 'purchase' else UserInteractionType.ADD_TO_CART
            personalization_engine.update_user_profile(
                str(data['user_id']),
                interaction_type,
                str(data['product_id']),
                data.get('recommendation_context', {})
            )

        return jsonify({'status': 'success', 'message': 'Conversion tracked successfully'})

    except Exception as e:
        logging.error(f"Error tracking recommendation conversion: {e}")
        return jsonify({'error': 'Failed to track conversion'}), 500

# Export blueprint
__all__ = ['recommendation_api', 'init_recommendation_system']
