"""
Test script for carrier integration system
==========================================

This script tests the carrier integration functionality including:
- Rate calculation from multiple carriers
- Shipment creation and tracking
- Error handling and fallback mechanisms
"""

import sys
import os
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from carrier_integration import (
    CarrierFactory, RateCalculationEngine, ShipmentManager,
    CarrierAPIError, ShippingCarrier
)
from order_fulfillment_architecture import Address, Package

def create_test_addresses():
    """Create test addresses for shipping"""
    origin = Address(
        name="Allora Store",
        company="Allora E-commerce",
        address_line_1="123 Business Park",
        address_line_2="Suite 100",
        city="Mumbai",
        state="Maharashtra",
        postal_code="400001",
        country="India",
        phone="+91-9876543210",
        email="<EMAIL>"
    )
    
    destination = Address(
        name="<PERSON>",
        company=None,
        address_line_1="456 Residential Area",
        address_line_2="Apartment 2B",
        city="Delhi",
        state="Delhi",
        postal_code="110001",
        country="India",
        phone="+91-9876543211",
        email="<EMAIL>"
    )
    
    return origin, destination

def create_test_packages():
    """Create test packages for shipping"""
    packages = [
        Package(
            weight=1.5,  # 1.5 kg
            length=30.0,  # 30 cm
            width=20.0,   # 20 cm
            height=10.0,  # 10 cm
            declared_value=2500.0,  # INR 2500
            description="Sustainable T-shirt"
        ),
        Package(
            weight=0.8,   # 0.8 kg
            length=25.0,  # 25 cm
            width=15.0,   # 15 cm
            height=8.0,   # 8 cm
            declared_value=1800.0,  # INR 1800
            description="Eco-friendly Mug"
        )
    ]
    
    return packages

def test_carrier_factory():
    """Test carrier factory functionality"""
    print("=" * 60)
    print("TESTING CARRIER FACTORY")
    print("=" * 60)
    
    try:
        factory = CarrierFactory()
        
        # Test getting individual carriers
        carriers_to_test = [
            ShippingCarrier.BLUE_DART,
            ShippingCarrier.DELHIVERY,
            ShippingCarrier.FEDEX
        ]
        
        for carrier in carriers_to_test:
            try:
                carrier_api = factory.get_carrier(carrier)
                print(f"✓ Successfully created {carrier.value} API instance")
            except Exception as e:
                print(f"✗ Failed to create {carrier.value} API: {e}")
        
        # Test getting all carriers
        try:
            all_carriers = factory.get_all_carriers()
            print(f"✓ Successfully retrieved {len(all_carriers)} carrier instances")
        except Exception as e:
            print(f"✗ Failed to get all carriers: {e}")
            
    except Exception as e:
        print(f"✗ Carrier factory test failed: {e}")

def test_rate_calculation():
    """Test rate calculation engine"""
    print("\n" + "=" * 60)
    print("TESTING RATE CALCULATION ENGINE")
    print("=" * 60)
    
    try:
        rate_engine = RateCalculationEngine()
        origin, destination = create_test_addresses()
        packages = create_test_packages()
        
        print(f"Origin: {origin.city}, {origin.state}")
        print(f"Destination: {destination.city}, {destination.state}")
        print(f"Packages: {len(packages)} items, Total weight: {sum(p.weight for p in packages)} kg")
        print(f"Total value: INR {sum(p.declared_value for p in packages)}")
        
        # Test rate calculation
        try:
            print("\nCalculating rates from all carriers...")
            rates = rate_engine.calculate_rates(origin, destination, packages)
            
            if rates:
                print(f"✓ Retrieved {len(rates)} shipping rates:")
                for i, rate in enumerate(rates, 1):
                    print(f"  {i}. {rate.carrier.value} - {rate.service_type}")
                    print(f"     Rate: {rate.currency} {rate.rate:.2f}")
                    print(f"     Delivery: {rate.estimated_days} days")
                    print(f"     Guaranteed: {'Yes' if rate.guaranteed else 'No'}")
                    print(f"     Tracking: {'Yes' if rate.tracking_included else 'No'}")
                    print()
            else:
                print("✗ No rates retrieved")
                
        except Exception as e:
            print(f"✗ Rate calculation failed: {e}")
        
        # Test best rate selection
        try:
            print("Finding best rates by criteria...")
            
            criteria_list = ['cheapest', 'fastest', 'most_reliable']
            for criteria in criteria_list:
                try:
                    best_rate = rate_engine.get_best_rate(origin, destination, packages, criteria)
                    print(f"✓ Best {criteria}: {best_rate.carrier.value} - {best_rate.service_type}")
                    print(f"   Rate: {best_rate.currency} {best_rate.rate:.2f}, Days: {best_rate.estimated_days}")
                except Exception as e:
                    print(f"✗ Failed to get best {criteria} rate: {e}")
                    
        except Exception as e:
            print(f"✗ Best rate selection failed: {e}")
            
    except Exception as e:
        print(f"✗ Rate calculation test failed: {e}")

def test_shipment_creation():
    """Test shipment creation (simulation only)"""
    print("\n" + "=" * 60)
    print("TESTING SHIPMENT CREATION (SIMULATION)")
    print("=" * 60)
    
    try:
        shipment_manager = ShipmentManager()
        origin, destination = create_test_addresses()
        packages = create_test_packages()
        
        print("Note: This is a simulation test with demo credentials")
        print("Real shipment creation requires valid carrier API credentials")
        
        # Test with Blue Dart (most likely to work in demo mode)
        try:
            print(f"\nTesting shipment creation with Blue Dart...")
            result = shipment_manager.create_shipment(
                origin=origin,
                destination=destination,
                packages=packages,
                carrier=ShippingCarrier.BLUE_DART,
                service_type='Standard',
                reference_number='TEST-ORDER-001'
            )
            
            if result.get('success'):
                print("✓ Shipment creation successful (simulation)")
                print(f"   Tracking Number: {result.get('tracking_number', 'N/A')}")
                print(f"   Estimated Delivery: {result.get('estimated_delivery', 'N/A')}")
                print(f"   Label URL: {result.get('label_url', 'N/A')}")
            else:
                print("✗ Shipment creation failed (expected with demo credentials)")
                
        except CarrierAPIError as e:
            print(f"✗ Carrier API Error (expected): {e}")
        except Exception as e:
            print(f"✗ Unexpected error: {e}")
            
    except Exception as e:
        print(f"✗ Shipment creation test failed: {e}")

def test_error_handling():
    """Test error handling and edge cases"""
    print("\n" + "=" * 60)
    print("TESTING ERROR HANDLING")
    print("=" * 60)
    
    try:
        factory = CarrierFactory()
        
        # Test invalid carrier
        try:
            invalid_carrier = ShippingCarrier('INVALID_CARRIER')
            print("✗ Should have failed with invalid carrier")
        except ValueError:
            print("✓ Correctly handled invalid carrier enum")
        except Exception as e:
            print(f"✗ Unexpected error with invalid carrier: {e}")
        
        # Test empty packages
        try:
            rate_engine = RateCalculationEngine()
            origin, destination = create_test_addresses()
            empty_packages = []
            
            rates = rate_engine.calculate_rates(origin, destination, empty_packages)
            print("✗ Should have failed with empty packages")
        except Exception as e:
            print(f"✓ Correctly handled empty packages: {type(e).__name__}")
        
        # Test invalid addresses
        try:
            invalid_origin = Address("", "", "", "", "", "", "", "", "", "")
            destination = create_test_addresses()[1]
            packages = create_test_packages()
            
            rates = rate_engine.calculate_rates(invalid_origin, destination, packages)
            print("✗ Should have failed with invalid address")
        except Exception as e:
            print(f"✓ Correctly handled invalid address: {type(e).__name__}")
            
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")

def main():
    """Run all tests"""
    print("ALLORA E-COMMERCE - CARRIER INTEGRATION TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    test_carrier_factory()
    test_rate_calculation()
    test_shipment_creation()
    test_error_handling()
    
    print("\n" + "=" * 60)
    print("TEST SUITE COMPLETED")
    print("=" * 60)
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\nNote: Some tests may show expected failures due to demo credentials.")
    print("For production use, configure real carrier API credentials in environment variables.")

if __name__ == "__main__":
    main()
