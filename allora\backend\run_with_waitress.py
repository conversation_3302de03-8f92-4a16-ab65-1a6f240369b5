#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from waitress import serve
from app import app

if __name__ == '__main__':
    print("🚀 Starting Allora Flask app with Waitress WSGI server...")
    print("📊 Routes registered:", len(list(app.url_map.iter_rules())))
    print("🌐 Server will be available at: http://127.0.0.1:5000")
    print("🔧 Using Waitress instead of Flask development server")
    print("=" * 50)
    
    # Initialize the app context and database
    with app.app_context():
        from app import db, initialize_payment_gateways, initialize_admin_user, initialize_oauth_providers
        try:
            db.create_all()
            print("✅ Database tables created/verified")
            initialize_payment_gateways()
            print("✅ Payment gateways initialized")
            initialize_admin_user()
            print("✅ Admin user initialized")
            initialize_oauth_providers()
            print("✅ OAuth providers initialized")
        except Exception as e:
            print(f"⚠️  Warning during initialization: {e}")
    
    print("🎯 Starting server...")
    # Serve the app with Waitress
    serve(app, host='127.0.0.1', port=5000, threads=6)
