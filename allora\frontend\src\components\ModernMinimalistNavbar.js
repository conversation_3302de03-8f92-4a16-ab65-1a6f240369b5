import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { AlloraBlackWhiteLogo, AlloraMobileLogo } from './AlloraLogo';
import { useCart } from '../contexts/CartContext';

const ModernMinimalistNavbar = ({ token, user, onLogout }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const { cartCount } = useCart();

  const isActivePath = (path) => location.pathname === path;

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isMenuOpen) setIsMenuOpen(false);
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isMenuOpen]);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchTerm)}`;
    }
  };

  // Navigation items
  const navItems = [
    { path: '/', label: 'Home', icon: '🏠' },
    { path: '/search', label: 'Search', icon: '🔍' },
    { path: '/categories', label: 'Categories', icon: '📂' },
    { path: '/community', label: 'Community', icon: '👥' },
    { path: '/track', label: 'Track Order', icon: '📦' },
  ];

  const userNavItems = token ? [
    { path: '/account', label: 'Account', icon: '👤' },
    { path: '/cart', label: 'Cart', icon: '🛒', badge: cartCount },
    { path: '/inventory', label: 'Inventory', icon: '📦' },
    { path: '/price-trends', label: 'Trends', icon: '📈' },
  ] : [];

  return (
    <header className={`sticky top-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-200/50'
        : 'bg-white/90 backdrop-blur-sm border-b border-gray-100'
    }`}>
      <div className="max-w-7xl mx-auto px-4 lg:px-6">
        <div className="flex items-center justify-between h-16 lg:h-18">

          {/* Desktop Layout */}
          <div className="hidden lg:flex items-center justify-between w-full">
            {/* Left Section - Logo */}
            <div className="flex items-center">
              <button
                onClick={() => window.location.href = '/'}
                className="group flex items-center hover:scale-105 transition-all duration-300"
              >
                <AlloraBlackWhiteLogo
                  size="medium"
                  className="group-hover:drop-shadow-md transition-all duration-300"
                />
              </button>
            </div>

            {/* Center Section - Floating Navigation Pills */}
            <div className="flex items-center space-x-1 bg-gray-50/80 rounded-full p-1 backdrop-blur-sm border border-gray-200/30">
              {navItems.map((item) => (
                <button
                  key={item.path}
                  onClick={() => window.location.href = item.path}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    isActivePath(item.path)
                      ? 'bg-black text-white shadow-lg'
                      : 'text-gray-600 hover:bg-white hover:text-black hover:shadow-md'
                  }`}
                >
                  <span className="mr-2">{item.icon}</span>
                  {item.label}
                </button>
              ))}
            </div>

            {/* Right Section - Search & User Actions */}
            <div className="flex items-center space-x-3">
              {/* Search Bar */}
              <form onSubmit={handleSearch} className="relative">
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64 px-4 py-2 pl-10 bg-gray-50/80 border border-gray-200/50 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 text-sm backdrop-blur-sm"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </form>

              {/* User Actions */}
              <div className="flex items-center space-x-2">
                {/* Cart Icon - Always visible for both logged-in and guest users */}
                <button
                  onClick={() => window.location.href = '/cart'}
                  className="relative p-2 bg-gray-50/80 rounded-full text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200"
                  title={token ? 'View Cart' : 'View Cart (Guest)'}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
                  </svg>
                  {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                      {cartCount}
                    </span>
                  )}
                </button>

                {/* User Account Actions - Only for logged-in users */}
                {token ? (
                  <>
                    <button
                      onClick={() => window.location.href = '/account'}
                      className="flex items-center space-x-2 px-3 py-2 bg-gray-50/80 rounded-full text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200"
                    >
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-600">
                          {user?.username?.charAt(0).toUpperCase() || 'U'}
                        </span>
                      </div>
                      <span className="text-sm font-medium">{user?.username || 'Account'}</span>
                    </button>
                    <button
                      onClick={onLogout}
                      className="px-3 py-2 bg-red-50 text-red-600 rounded-full text-sm font-medium hover:bg-red-100 transition-colors duration-200"
                    >
                      Logout
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => window.location.href = '/login'}
                      className="px-4 py-2 text-gray-600 hover:text-black transition-colors duration-200 text-sm font-medium"
                    >
                      Login
                    </button>
                    <button
                      onClick={() => window.location.href = '/sell'}
                      className="px-4 py-2 bg-emerald-600 text-white rounded-full text-sm font-medium hover:bg-emerald-700 transition-all duration-200 shadow-sm"
                    >
                      Sell
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="lg:hidden flex items-center justify-between w-full">
            {/* Mobile Logo */}
            <button
              onClick={() => window.location.href = '/'}
              className="group flex items-center hover:scale-110 transition-all duration-300 flex-shrink-0"
            >
              <AlloraMobileLogo
                size={36}
                className="group-hover:drop-shadow-md transition-all duration-300"
              />
            </button>

            {/* Mobile Search Bar */}
            <form onSubmit={handleSearch} className="flex-1 mx-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2.5 pl-10 bg-gray-50/90 border border-gray-200/50 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 text-sm backdrop-blur-sm"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </form>

            {/* Mobile Menu Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsMenuOpen(!isMenuOpen);
              }}
              className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200"
              aria-label="Toggle menu"
            >
              <div className="w-6 h-6 flex flex-col justify-center items-center">
                <div className={`w-5 h-0.5 bg-current transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-1' : ''}`}></div>
                <div className={`w-5 h-0.5 bg-current transition-all duration-300 mt-1 ${isMenuOpen ? 'opacity-0' : ''}`}></div>
                <div className={`w-5 h-0.5 bg-current transition-all duration-300 mt-1 ${isMenuOpen ? '-rotate-45 -translate-y-1' : ''}`}></div>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white/95 backdrop-blur-lg border-t border-gray-200/50 shadow-xl">
          <div className="max-w-7xl mx-auto px-4 py-6">
            {/* Mobile Navigation Grid */}
            <div className="grid grid-cols-2 gap-3 mb-6">
              {navItems.map((item) => (
                <button
                  key={item.path}
                  onClick={() => {
                    window.location.href = item.path;
                    setIsMenuOpen(false);
                  }}
                  className={`flex items-center justify-center space-x-2 py-4 px-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                    isActivePath(item.path)
                      ? 'bg-black text-white shadow-lg'
                      : 'bg-gray-50/80 text-gray-600 hover:bg-gray-100 hover:text-black hover:shadow-md'
                  }`}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span>{item.label}</span>
                </button>
              ))}
            </div>

            {/* Mobile User Actions */}
            <div className="pt-4 border-t border-gray-200/50">
              {token ? (
                <div className="space-y-3">
                  {/* User Profile Card */}
                  <div className="flex items-center space-x-3 p-4 bg-gray-50/80 rounded-xl">
                    <div className="w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-sm">
                      <span className="text-sm font-semibold text-gray-700">
                        {user?.username?.charAt(0).toUpperCase() || 'U'}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {user?.username || 'User'}
                      </p>
                      <p className="text-xs text-gray-500">Account Settings</p>
                    </div>
                  </div>

                  {/* User Action Grid */}
                  <div className="grid grid-cols-2 gap-3">
                    {userNavItems.map((item) => (
                      <button
                        key={item.path}
                        onClick={() => {
                          window.location.href = item.path;
                          setIsMenuOpen(false);
                        }}
                        className="relative flex items-center justify-center space-x-2 py-3 px-3 bg-gray-50/80 rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-300"
                      >
                        <span className="text-base">{item.icon}</span>
                        <span>{item.label}</span>
                        {item.badge && item.badge > 0 && (
                          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                            {item.badge}
                          </span>
                        )}
                      </button>
                    ))}
                  </div>

                  {/* Logout Button */}
                  <button
                    onClick={() => {
                      onLogout();
                      setIsMenuOpen(false);
                    }}
                    className="w-full py-3 px-4 bg-red-50 text-red-600 rounded-xl text-sm font-medium hover:bg-red-100 transition-colors duration-200"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {/* Guest Cart Button */}
                  <button
                    onClick={() => {
                      window.location.href = '/cart';
                      setIsMenuOpen(false);
                    }}
                    className="relative w-full flex items-center justify-center space-x-2 py-3 px-4 bg-gray-50/80 rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
                    </svg>
                    <span>Cart (Guest)</span>
                    {cartCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                        {cartCount}
                      </span>
                    )}
                  </button>

                  <button
                    onClick={() => {
                      window.location.href = '/login';
                      setIsMenuOpen(false);
                    }}
                    className="w-full py-3 px-4 text-center rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-black transition-all duration-200 border border-gray-200/50"
                  >
                    Login
                  </button>
                  <button
                    onClick={() => {
                      window.location.href = '/sell';
                      setIsMenuOpen(false);
                    }}
                    className="w-full py-3 px-4 bg-emerald-600 text-white rounded-xl text-sm font-medium hover:bg-emerald-700 transition-all duration-200 shadow-sm"
                  >
                    Sell
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default ModernMinimalistNavbar;
