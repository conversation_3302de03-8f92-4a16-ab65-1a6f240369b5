import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useError } from '../contexts/ErrorContext';
import { useLoading } from '../contexts/LoadingContext';
import { LoadingSpinner } from '../components/LoadingComponents';
import { ErrorAlert } from '../components/ErrorComponents';
import ProductCard from '../components/ProductCard';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const PublicStorePage = () => {
  const { storeSlug } = useParams();
  const navigate = useNavigate();
  const { handleApiError } = useError();
  const { isLoading, withLoading } = useLoading();

  const [store, setStore] = useState(null);
  const [products, setProducts] = useState([]);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    category: '',
    sort: 'newest',
    page: 1
  });

  useEffect(() => {
    fetchStoreProfile();
  }, [storeSlug]);

  useEffect(() => {
    if (store) {
      fetchStoreProducts();
    }
  }, [store, filters]);

  const fetchStoreProfile = async () => {
    try {
      await withLoading('store_profile', async () => {
        const response = await fetch(`${API_BASE_URL}/store/${storeSlug}`);

        if (!response.ok) {
          if (response.status === 404) {
            navigate('/404');
            return;
          }
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to load store');
        }

        const data = await response.json();
        setStore(data.data);
      }, 'Loading store...');
    } catch (error) {
      handleApiError(error, { action: 'fetch_store' });
    }
  };

  const fetchStoreProducts = async () => {
    try {
      await withLoading('store_products', async () => {
        const queryParams = new URLSearchParams({
          page: filters.page,
          per_page: 20,
          sort: filters.sort,
          ...(filters.category && { category: filters.category })
        });

        const response = await fetch(`${API_BASE_URL}/store/${storeSlug}/products?${queryParams}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to load products');
        }

        const data = await response.json();
        setProducts(data.data.products);
        setPagination(data.data.pagination);
      }, 'Loading products...');
    } catch (error) {
      handleApiError(error, { action: 'fetch_products' });
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filtering
    }));
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  if (isLoading('store_profile')) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (!store) {
    return null;
  }

  // Get unique categories from products
  const categories = [...new Set(products.map(product => product.category))].filter(Boolean);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Store Header */}
      <div className="bg-white shadow-sm">
        {/* Store Banner */}
        {store.store_banner && (
          <div className="h-48 md:h-64 overflow-hidden">
            <img
              src={store.store_banner}
              alt={`${store.store_name} banner`}
              className="w-full h-full object-cover"
            />
          </div>
        )}

        {/* Store Info */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-start space-x-6">
            {/* Store Logo */}
            {store.store_logo && (
              <div className="flex-shrink-0">
                <img
                  src={store.store_logo}
                  alt={`${store.store_name} logo`}
                  className="h-20 w-20 rounded-lg object-cover border-2 border-white shadow-lg"
                />
              </div>
            )}

            {/* Store Details */}
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900">{store.store_name}</h1>
              <p className="text-gray-600 mt-2">{store.store_description}</p>
              
              {/* Store Stats */}
              <div className="flex items-center space-x-6 mt-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <span className="font-medium text-gray-900">{store.total_products}</span>
                  <span className="ml-1">Products</span>
                </div>
                <div className="flex items-center">
                  <span className="font-medium text-gray-900">{store.total_orders}</span>
                  <span className="ml-1">Orders Completed</span>
                </div>
                {store.rating > 0 && (
                  <div className="flex items-center">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`h-4 w-4 ${i < Math.floor(store.rating) ? 'text-yellow-400' : 'text-gray-300'}`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <span className="ml-1 font-medium text-gray-900">{store.rating.toFixed(1)}</span>
                    <span className="ml-1">({store.total_reviews} reviews)</span>
                  </div>
                )}
              </div>

              {/* Store Policies */}
              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                {store.processing_time && (
                  <div className="flex items-center text-gray-600">
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Processing: {store.processing_time}
                  </div>
                )}
                {store.free_shipping_threshold > 0 && (
                  <div className="flex items-center text-gray-600">
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                    Free shipping on orders ₹{store.free_shipping_threshold}+
                  </div>
                )}
                {store.min_order_amount > 0 && (
                  <div className="flex items-center text-gray-600">
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Min order: ₹{store.min_order_amount}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Products Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort By
              </label>
              <select
                value={filters.sort}
                onChange={(e) => handleFilterChange('sort', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <option value="newest">Newest First</option>
                <option value="price_low">Price: Low to High</option>
                <option value="price_high">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={() => setFilters({ category: '', sort: 'newest', page: 1 })}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        {isLoading('store_products') ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="large" />
          </div>
        ) : products.length > 0 ? (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="mt-8 bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={!pagination.has_prev}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={!pagination.has_next}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing page <span className="font-medium">{pagination.page}</span> of{' '}
                      <span className="font-medium">{pagination.pages}</span> ({pagination.total} total products)
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={!pagination.has_prev}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={!pagination.has_next}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.category || filters.sort !== 'newest' ? 'Try adjusting your filters.' : 'This store hasn\'t added any products yet.'}
            </p>
          </div>
        )}
      </div>

      {/* Store Policies Section */}
      {(store.return_policy || store.shipping_policy || store.terms_conditions) && (
        <div className="bg-white border-t">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Store Policies</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {store.return_policy && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Return Policy</h3>
                  <p className="text-gray-600 whitespace-pre-line">{store.return_policy}</p>
                </div>
              )}
              {store.shipping_policy && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Shipping Policy</h3>
                  <p className="text-gray-600 whitespace-pre-line">{store.shipping_policy}</p>
                </div>
              )}
              {store.terms_conditions && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Terms & Conditions</h3>
                  <p className="text-gray-600 whitespace-pre-line">{store.terms_conditions}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Global Error Display */}
      <ErrorAlert />
    </div>
  );
};

export default PublicStorePage;
