{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\Sell.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { API_BASE_URL } from '../config/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sell = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState({\n    businessName: '',\n    contactPerson: '',\n    email: '',\n    phone: '',\n    businessType: '',\n    category: '',\n    description: '',\n    address: '',\n    gstNumber: '',\n    panNumber: '',\n    bankAccount: '',\n    ifscCode: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const {\n    success,\n    error\n  } = useNotification();\n  const businessTypes = ['Individual Seller', 'Small Business', 'Manufacturer', 'Wholesaler', 'Retailer', 'Brand Owner'];\n  const categories = ['Electronics', 'Fashion & Apparel', 'Home & Garden', 'Health & Beauty', 'Sports & Outdoors', 'Books & Media', 'Toys & Games', 'Automotive', 'Food & Beverages', 'Other'];\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const response = await fetch(`${API_BASE_URL}/seller/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n      const data = await response.json();\n      if (response.ok) {\n        success('Seller application submitted successfully! We will review your application and contact you within 2-3 business days.', {\n          title: 'Application Submitted'\n        });\n        // Reset form\n        setFormData({\n          businessName: '',\n          contactPerson: '',\n          email: '',\n          phone: '',\n          businessType: '',\n          category: '',\n          description: '',\n          address: '',\n          gstNumber: '',\n          panNumber: '',\n          bankAccount: '',\n          ifscCode: ''\n        });\n        setCurrentStep(1);\n      } else {\n        throw new Error(data.error || 'Failed to submit application');\n      }\n    } catch (err) {\n      error(err.message || 'Failed to submit seller application', {\n        title: 'Submission Failed'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const nextStep = () => {\n    if (currentStep < 3) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const isStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return formData.businessName && formData.contactPerson && formData.email && formData.phone;\n      case 2:\n        return formData.businessType && formData.category && formData.description;\n      case 3:\n        return formData.address && formData.gstNumber && formData.panNumber;\n      default:\n        return false;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 py-12\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n          children: [\"Start Selling on \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-emerald-600\",\n            children: \"Allora\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n          children: \"Join thousands of sellers and grow your business with our sustainable marketplace\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-3 gap-8 mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-6 bg-white rounded-2xl shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"Easy Setup\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Get started in minutes with our simple onboarding process\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-6 bg-white rounded-2xl shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"Low Fees\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Competitive commission rates to maximize your profits\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-6 bg-white rounded-2xl shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"Grow Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Reach millions of customers and scale your business\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-3xl shadow-xl p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [1, 2, 3].map(step => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center justify-center w-10 h-10 rounded-full font-semibold ${step <= currentStep ? 'bg-emerald-600 text-white' : 'bg-gray-200 text-gray-600'}`,\n              children: step\n            }, step, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-emerald-600 h-2 rounded-full transition-all duration-300\",\n              style: {\n                width: `${currentStep / 3 * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between mt-2 text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Basic Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Business Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Verification\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [currentStep === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: \"Basic Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Business Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.businessName,\n                  onChange: e => handleInputChange('businessName', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  placeholder: \"Enter your business name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Contact Person *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.contactPerson,\n                  onChange: e => handleInputChange('contactPerson', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  placeholder: \"Your full name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email Address *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  value: formData.email,\n                  onChange: e => handleInputChange('email', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  placeholder: \"<EMAIL>\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Phone Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  value: formData.phone,\n                  onChange: e => handleInputChange('phone', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  placeholder: \"+91 XXXXX XXXXX\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), currentStep === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: \"Business Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Business Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: formData.businessType,\n                  onChange: e => handleInputChange('businessType', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select business type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), businessTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: type,\n                    children: type\n                  }, type, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Primary Category *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: formData.category,\n                  onChange: e => handleInputChange('category', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category,\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Business Description *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.description,\n                onChange: e => handleInputChange('description', e.target.value),\n                rows: 4,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                placeholder: \"Tell us about your business, products, and what makes you unique...\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), currentStep === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-6\",\n              children: \"Verification Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Business Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.address,\n                onChange: e => handleInputChange('address', e.target.value),\n                rows: 3,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                placeholder: \"Enter your complete business address\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"GST Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.gstNumber,\n                  onChange: e => handleInputChange('gstNumber', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  placeholder: \"22AAAAA0000A1Z5\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"PAN Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.panNumber,\n                  onChange: e => handleInputChange('panNumber', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  placeholder: \"**********\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Bank Account Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.bankAccount,\n                  onChange: e => handleInputChange('bankAccount', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  placeholder: \"Account number for payments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"IFSC Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.ifscCode,\n                  onChange: e => handleInputChange('ifscCode', e.target.value),\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\",\n                  placeholder: \"SBIN0001234\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 border border-blue-200 rounded-xl p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-500 text-xl\",\n                    children: \"\\u2139\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-blue-800\",\n                    children: \"Verification Process\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-blue-700 mt-1\",\n                    children: \"We'll verify your business details within 2-3 business days. You'll receive an email with next steps once approved.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between mt-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: prevStep,\n              className: `px-6 py-3 rounded-xl font-medium transition-all duration-200 ${currentStep === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n              disabled: currentStep === 1,\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this), currentStep < 3 ? /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: nextStep,\n              className: `px-6 py-3 rounded-xl font-medium transition-all duration-200 ${isStepValid() ? 'bg-emerald-600 text-white hover:bg-emerald-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,\n              disabled: !isStepValid(),\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: `px-8 py-3 rounded-xl font-medium transition-all duration-200 ${isStepValid() && !loading ? 'bg-emerald-600 text-white hover:bg-emerald-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,\n              disabled: !isStepValid() || loading,\n              children: loading ? 'Submitting...' : 'Submit Application'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(Sell, \"LG8GkdcHH7T10TJt0viQyazGTts=\", false, function () {\n  return [useNotification];\n});\n_c = Sell;\nexport default Sell;\nvar _c;\n$RefreshReg$(_c, \"Sell\");", "map": {"version": 3, "names": ["React", "useState", "useNotification", "API_BASE_URL", "jsxDEV", "_jsxDEV", "<PERSON>ll", "_s", "currentStep", "setCurrentStep", "formData", "setFormData", "businessName", "<PERSON><PERSON><PERSON>", "email", "phone", "businessType", "category", "description", "address", "gstNumber", "panNumber", "bankAccount", "ifscCode", "loading", "setLoading", "success", "error", "businessTypes", "categories", "handleInputChange", "field", "value", "prev", "handleSubmit", "e", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "title", "Error", "err", "message", "nextStep", "prevStep", "isStepValid", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "style", "width", "onSubmit", "type", "onChange", "target", "placeholder", "required", "rows", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/Sell.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { API_BASE_URL } from '../config/api';\n\nconst Sell = () => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const [formData, setFormData] = useState({\n    businessName: '',\n    contactPerson: '',\n    email: '',\n    phone: '',\n    businessType: '',\n    category: '',\n    description: '',\n    address: '',\n    gstNumber: '',\n    panNumber: '',\n    bankAccount: '',\n    ifscCode: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const { success, error } = useNotification();\n\n  const businessTypes = [\n    'Individual Seller',\n    'Small Business',\n    'Manufacturer',\n    'Wholesaler',\n    'Retailer',\n    'Brand Owner'\n  ];\n\n  const categories = [\n    'Electronics',\n    'Fashion & Apparel',\n    'Home & Garden',\n    'Health & Beauty',\n    'Sports & Outdoors',\n    'Books & Media',\n    'Toys & Games',\n    'Automotive',\n    'Food & Beverages',\n    'Other'\n  ];\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const response = await fetch(`${API_BASE_URL}/seller/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        success('Seller application submitted successfully! We will review your application and contact you within 2-3 business days.', {\n          title: 'Application Submitted'\n        });\n        // Reset form\n        setFormData({\n          businessName: '',\n          contactPerson: '',\n          email: '',\n          phone: '',\n          businessType: '',\n          category: '',\n          description: '',\n          address: '',\n          gstNumber: '',\n          panNumber: '',\n          bankAccount: '',\n          ifscCode: ''\n        });\n        setCurrentStep(1);\n      } else {\n        throw new Error(data.error || 'Failed to submit application');\n      }\n    } catch (err) {\n      error(err.message || 'Failed to submit seller application', {\n        title: 'Submission Failed'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const nextStep = () => {\n    if (currentStep < 3) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const isStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return formData.businessName && formData.contactPerson && formData.email && formData.phone;\n      case 2:\n        return formData.businessType && formData.category && formData.description;\n      case 3:\n        return formData.address && formData.gstNumber && formData.panNumber;\n      default:\n        return false;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n            Start Selling on <span className=\"text-emerald-600\">Allora</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Join thousands of sellers and grow your business with our sustainable marketplace\n          </p>\n        </div>\n\n        {/* Benefits Section */}\n        <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n          <div className=\"text-center p-6 bg-white rounded-2xl shadow-lg\">\n            <div className=\"w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">🚀</span>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Easy Setup</h3>\n            <p className=\"text-gray-600\">Get started in minutes with our simple onboarding process</p>\n          </div>\n          <div className=\"text-center p-6 bg-white rounded-2xl shadow-lg\">\n            <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">💰</span>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Low Fees</h3>\n            <p className=\"text-gray-600\">Competitive commission rates to maximize your profits</p>\n          </div>\n          <div className=\"text-center p-6 bg-white rounded-2xl shadow-lg\">\n            <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl\">📈</span>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Grow Sales</h3>\n            <p className=\"text-gray-600\">Reach millions of customers and scale your business</p>\n          </div>\n        </div>\n\n        {/* Application Form */}\n        <div className=\"bg-white rounded-3xl shadow-xl p-8\">\n          {/* Progress Bar */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between mb-4\">\n              {[1, 2, 3].map((step) => (\n                <div\n                  key={step}\n                  className={`flex items-center justify-center w-10 h-10 rounded-full font-semibold ${\n                    step <= currentStep\n                      ? 'bg-emerald-600 text-white'\n                      : 'bg-gray-200 text-gray-600'\n                  }`}\n                >\n                  {step}\n                </div>\n              ))}\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className=\"bg-emerald-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${(currentStep / 3) * 100}%` }}\n              ></div>\n            </div>\n            <div className=\"flex justify-between mt-2 text-sm text-gray-600\">\n              <span>Basic Info</span>\n              <span>Business Details</span>\n              <span>Verification</span>\n            </div>\n          </div>\n\n          <form onSubmit={handleSubmit}>\n            {/* Step 1: Basic Information */}\n            {currentStep === 1 && (\n              <div className=\"space-y-6\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Basic Information</h2>\n                \n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Business Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.businessName}\n                      onChange={(e) => handleInputChange('businessName', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      placeholder=\"Enter your business name\"\n                      required\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Contact Person *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.contactPerson}\n                      onChange={(e) => handleInputChange('contactPerson', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      placeholder=\"Your full name\"\n                      required\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Email Address *\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={formData.email}\n                      onChange={(e) => handleInputChange('email', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      placeholder=\"<EMAIL>\"\n                      required\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Phone Number *\n                    </label>\n                    <input\n                      type=\"tel\"\n                      value={formData.phone}\n                      onChange={(e) => handleInputChange('phone', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      placeholder=\"+91 XXXXX XXXXX\"\n                      required\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Step 2: Business Details */}\n            {currentStep === 2 && (\n              <div className=\"space-y-6\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Business Details</h2>\n                \n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Business Type *\n                    </label>\n                    <select\n                      value={formData.businessType}\n                      onChange={(e) => handleInputChange('businessType', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      required\n                    >\n                      <option value=\"\">Select business type</option>\n                      {businessTypes.map((type) => (\n                        <option key={type} value={type}>{type}</option>\n                      ))}\n                    </select>\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Primary Category *\n                    </label>\n                    <select\n                      value={formData.category}\n                      onChange={(e) => handleInputChange('category', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      required\n                    >\n                      <option value=\"\">Select category</option>\n                      {categories.map((category) => (\n                        <option key={category} value={category}>{category}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Business Description *\n                  </label>\n                  <textarea\n                    value={formData.description}\n                    onChange={(e) => handleInputChange('description', e.target.value)}\n                    rows={4}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                    placeholder=\"Tell us about your business, products, and what makes you unique...\"\n                    required\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Step 3: Verification */}\n            {currentStep === 3 && (\n              <div className=\"space-y-6\">\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Verification Details</h2>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Business Address *\n                  </label>\n                  <textarea\n                    value={formData.address}\n                    onChange={(e) => handleInputChange('address', e.target.value)}\n                    rows={3}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                    placeholder=\"Enter your complete business address\"\n                    required\n                  />\n                </div>\n\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      GST Number *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.gstNumber}\n                      onChange={(e) => handleInputChange('gstNumber', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      placeholder=\"22AAAAA0000A1Z5\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      PAN Number *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.panNumber}\n                      onChange={(e) => handleInputChange('panNumber', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      placeholder=\"**********\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Bank Account Number\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.bankAccount}\n                      onChange={(e) => handleInputChange('bankAccount', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      placeholder=\"Account number for payments\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      IFSC Code\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.ifscCode}\n                      onChange={(e) => handleInputChange('ifscCode', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n                      placeholder=\"SBIN0001234\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"bg-blue-50 border border-blue-200 rounded-xl p-4\">\n                  <div className=\"flex items-start\">\n                    <div className=\"flex-shrink-0\">\n                      <span className=\"text-blue-500 text-xl\">ℹ️</span>\n                    </div>\n                    <div className=\"ml-3\">\n                      <h4 className=\"text-sm font-medium text-blue-800\">Verification Process</h4>\n                      <p className=\"text-sm text-blue-700 mt-1\">\n                        We'll verify your business details within 2-3 business days. You'll receive an email with next steps once approved.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Navigation Buttons */}\n            <div className=\"flex justify-between mt-8\">\n              <button\n                type=\"button\"\n                onClick={prevStep}\n                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${\n                  currentStep === 1\n                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                }`}\n                disabled={currentStep === 1}\n              >\n                Previous\n              </button>\n              \n              {currentStep < 3 ? (\n                <button\n                  type=\"button\"\n                  onClick={nextStep}\n                  className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${\n                    isStepValid()\n                      ? 'bg-emerald-600 text-white hover:bg-emerald-700'\n                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  }`}\n                  disabled={!isStepValid()}\n                >\n                  Next\n                </button>\n              ) : (\n                <button\n                  type=\"submit\"\n                  className={`px-8 py-3 rounded-xl font-medium transition-all duration-200 ${\n                    isStepValid() && !loading\n                      ? 'bg-emerald-600 text-white hover:bg-emerald-700'\n                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  }`}\n                  disabled={!isStepValid() || loading}\n                >\n                  {loading ? 'Submitting...' : 'Submit Application'}\n                </button>\n              )}\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Sell;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,YAAY,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEyB,OAAO;IAAEC;EAAM,CAAC,GAAGzB,eAAe,CAAC,CAAC;EAE5C,MAAM0B,aAAa,GAAG,CACpB,mBAAmB,EACnB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,UAAU,EACV,aAAa,CACd;EAED,MAAMC,UAAU,GAAG,CACjB,aAAa,EACb,mBAAmB,EACnB,eAAe,EACf,iBAAiB,EACjB,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,OAAO,CACR;EAED,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CrB,WAAW,CAACsB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBX,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGnC,YAAY,kBAAkB,EAAE;QAC9DoC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjC,QAAQ;MAC/B,CAAC,CAAC;MAEF,MAAMkC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;QACfpB,OAAO,CAAC,sHAAsH,EAAE;UAC9HqB,KAAK,EAAE;QACT,CAAC,CAAC;QACF;QACApC,WAAW,CAAC;UACVC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE,EAAE;UACjBC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,YAAY,EAAE,EAAE;UAChBC,QAAQ,EAAE,EAAE;UACZC,WAAW,EAAE,EAAE;UACfC,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE,EAAE;UACbC,WAAW,EAAE,EAAE;UACfC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFd,cAAc,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,MAAM,IAAIuC,KAAK,CAACJ,IAAI,CAACjB,KAAK,IAAI,8BAA8B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOsB,GAAG,EAAE;MACZtB,KAAK,CAACsB,GAAG,CAACC,OAAO,IAAI,qCAAqC,EAAE;QAC1DH,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI3C,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAM4C,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI5C,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAM6C,WAAW,GAAGA,CAAA,KAAM;IACxB,QAAQ7C,WAAW;MACjB,KAAK,CAAC;QACJ,OAAOE,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACG,aAAa,IAAIH,QAAQ,CAACI,KAAK,IAAIJ,QAAQ,CAACK,KAAK;MAC5F,KAAK,CAAC;QACJ,OAAOL,QAAQ,CAACM,YAAY,IAAIN,QAAQ,CAACO,QAAQ,IAAIP,QAAQ,CAACQ,WAAW;MAC3E,KAAK,CAAC;QACJ,OAAOR,QAAQ,CAACS,OAAO,IAAIT,QAAQ,CAACU,SAAS,IAAIV,QAAQ,CAACW,SAAS;MACrE;QACE,OAAO,KAAK;IAChB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKiD,SAAS,EAAC,2EAA2E;IAAAC,QAAA,eACxFlD,OAAA;MAAKiD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErClD,OAAA;QAAKiD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClD,OAAA;UAAIiD,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAAC,mBAC/C,eAAAlD,OAAA;YAAMiD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACLtD,OAAA;UAAGiD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtD,OAAA;QAAKiD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9ClD,OAAA;UAAKiD,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DlD,OAAA;YAAKiD,SAAS,EAAC,qFAAqF;YAAAC,QAAA,eAClGlD,OAAA;cAAMiD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNtD,OAAA;YAAIiD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEtD,OAAA;YAAGiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNtD,OAAA;UAAKiD,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DlD,OAAA;YAAKiD,SAAS,EAAC,kFAAkF;YAAAC,QAAA,eAC/FlD,OAAA;cAAMiD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNtD,OAAA;YAAIiD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEtD,OAAA;YAAGiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAqD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC,eACNtD,OAAA;UAAKiD,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DlD,OAAA;YAAKiD,SAAS,EAAC,oFAAoF;YAAAC,QAAA,eACjGlD,OAAA;cAAMiD,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNtD,OAAA;YAAIiD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEtD,OAAA;YAAGiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtD,OAAA;QAAKiD,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBAEjDlD,OAAA;UAAKiD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlD,OAAA;YAAKiD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EACpD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACK,GAAG,CAAEC,IAAI,iBAClBxD,OAAA;cAEEiD,SAAS,EAAE,yEACTO,IAAI,IAAIrD,WAAW,GACf,2BAA2B,GAC3B,2BAA2B,EAC9B;cAAA+C,QAAA,EAEFM;YAAI,GAPAA,IAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQN,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDlD,OAAA;cACEiD,SAAS,EAAC,6DAA6D;cACvEQ,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAIvD,WAAW,GAAG,CAAC,GAAI,GAAG;cAAI;YAAE;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNtD,OAAA;YAAKiD,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DlD,OAAA;cAAAkD,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBtD,OAAA;cAAAkD,QAAA,EAAM;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7BtD,OAAA;cAAAkD,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtD,OAAA;UAAM2D,QAAQ,EAAE9B,YAAa;UAAAqB,QAAA,GAE1B/C,WAAW,KAAK,CAAC,iBAChBH,OAAA;YAAKiD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlD,OAAA;cAAIiD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE5EtD,OAAA;cAAKiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXjC,KAAK,EAAEtB,QAAQ,CAACE,YAAa;kBAC7BsD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,cAAc,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBACnEsB,SAAS,EAAC,oIAAoI;kBAC9Ic,WAAW,EAAC,0BAA0B;kBACtCC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXjC,KAAK,EAAEtB,QAAQ,CAACG,aAAc;kBAC9BqD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,eAAe,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBACpEsB,SAAS,EAAC,oIAAoI;kBAC9Ic,WAAW,EAAC,gBAAgB;kBAC5BC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,IAAI,EAAC,OAAO;kBACZjC,KAAK,EAAEtB,QAAQ,CAACI,KAAM;kBACtBoD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,OAAO,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBAC5DsB,SAAS,EAAC,oIAAoI;kBAC9Ic,WAAW,EAAC,gBAAgB;kBAC5BC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,IAAI,EAAC,KAAK;kBACVjC,KAAK,EAAEtB,QAAQ,CAACK,KAAM;kBACtBmD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,OAAO,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBAC5DsB,SAAS,EAAC,oIAAoI;kBAC9Ic,WAAW,EAAC,iBAAiB;kBAC7BC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAnD,WAAW,KAAK,CAAC,iBAChBH,OAAA;YAAKiD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlD,OAAA;cAAIiD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE3EtD,OAAA;cAAKiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE2B,KAAK,EAAEtB,QAAQ,CAACM,YAAa;kBAC7BkD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,cAAc,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBACnEsB,SAAS,EAAC,oIAAoI;kBAC9Ie,QAAQ;kBAAAd,QAAA,gBAERlD,OAAA;oBAAQ2B,KAAK,EAAC,EAAE;oBAAAuB,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7C/B,aAAa,CAACgC,GAAG,CAAEK,IAAI,iBACtB5D,OAAA;oBAAmB2B,KAAK,EAAEiC,IAAK;oBAAAV,QAAA,EAAEU;kBAAI,GAAxBA,IAAI;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE2B,KAAK,EAAEtB,QAAQ,CAACO,QAAS;kBACzBiD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,UAAU,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBAC/DsB,SAAS,EAAC,oIAAoI;kBAC9Ie,QAAQ;kBAAAd,QAAA,gBAERlD,OAAA;oBAAQ2B,KAAK,EAAC,EAAE;oBAAAuB,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACxC9B,UAAU,CAAC+B,GAAG,CAAE3C,QAAQ,iBACvBZ,OAAA;oBAAuB2B,KAAK,EAAEf,QAAS;oBAAAsC,QAAA,EAAEtC;kBAAQ,GAApCA,QAAQ;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtD,OAAA;cAAAkD,QAAA,gBACElD,OAAA;gBAAOiD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtD,OAAA;gBACE2B,KAAK,EAAEtB,QAAQ,CAACQ,WAAY;gBAC5BgD,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,aAAa,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;gBAClEsC,IAAI,EAAE,CAAE;gBACRhB,SAAS,EAAC,oIAAoI;gBAC9Ic,WAAW,EAAC,qEAAqE;gBACjFC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAnD,WAAW,KAAK,CAAC,iBAChBH,OAAA;YAAKiD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlD,OAAA;cAAIiD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE/EtD,OAAA;cAAAkD,QAAA,gBACElD,OAAA;gBAAOiD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtD,OAAA;gBACE2B,KAAK,EAAEtB,QAAQ,CAACS,OAAQ;gBACxB+C,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,SAAS,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;gBAC9DsC,IAAI,EAAE,CAAE;gBACRhB,SAAS,EAAC,oIAAoI;gBAC9Ic,WAAW,EAAC,sCAAsC;gBAClDC,QAAQ;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXjC,KAAK,EAAEtB,QAAQ,CAACU,SAAU;kBAC1B8C,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,WAAW,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBAChEsB,SAAS,EAAC,oIAAoI;kBAC9Ic,WAAW,EAAC,iBAAiB;kBAC7BC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXjC,KAAK,EAAEtB,QAAQ,CAACW,SAAU;kBAC1B6C,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,WAAW,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBAChEsB,SAAS,EAAC,oIAAoI;kBAC9Ic,WAAW,EAAC,YAAY;kBACxBC,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXjC,KAAK,EAAEtB,QAAQ,CAACY,WAAY;kBAC5B4C,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,aAAa,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBAClEsB,SAAS,EAAC,oIAAoI;kBAC9Ic,WAAW,EAAC;gBAA6B;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAOiD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACXjC,KAAK,EAAEtB,QAAQ,CAACa,QAAS;kBACzB2C,QAAQ,EAAG/B,CAAC,IAAKL,iBAAiB,CAAC,UAAU,EAAEK,CAAC,CAACgC,MAAM,CAACnC,KAAK,CAAE;kBAC/DsB,SAAS,EAAC,oIAAoI;kBAC9Ic,WAAW,EAAC;gBAAa;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,kDAAkD;cAAAC,QAAA,eAC/DlD,OAAA;gBAAKiD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlD,OAAA;kBAAKiD,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BlD,OAAA;oBAAMiD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACNtD,OAAA;kBAAKiD,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBlD,OAAA;oBAAIiD,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3EtD,OAAA;oBAAGiD,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAE1C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDtD,OAAA;YAAKiD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxClD,OAAA;cACE4D,IAAI,EAAC,QAAQ;cACbM,OAAO,EAAEnB,QAAS;cAClBE,SAAS,EAAE,gEACT9C,WAAW,KAAK,CAAC,GACb,8CAA8C,GAC9C,6CAA6C,EAChD;cACHgE,QAAQ,EAAEhE,WAAW,KAAK,CAAE;cAAA+C,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERnD,WAAW,GAAG,CAAC,gBACdH,OAAA;cACE4D,IAAI,EAAC,QAAQ;cACbM,OAAO,EAAEpB,QAAS;cAClBG,SAAS,EAAE,gEACTD,WAAW,CAAC,CAAC,GACT,gDAAgD,GAChD,8CAA8C,EACjD;cACHmB,QAAQ,EAAE,CAACnB,WAAW,CAAC,CAAE;cAAAE,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETtD,OAAA;cACE4D,IAAI,EAAC,QAAQ;cACbX,SAAS,EAAE,gEACTD,WAAW,CAAC,CAAC,IAAI,CAAC7B,OAAO,GACrB,gDAAgD,GAChD,8CAA8C,EACjD;cACHgD,QAAQ,EAAE,CAACnB,WAAW,CAAC,CAAC,IAAI7B,OAAQ;cAAA+B,QAAA,EAEnC/B,OAAO,GAAG,eAAe,GAAG;YAAoB;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CApcID,IAAI;EAAA,QAiBmBJ,eAAe;AAAA;AAAAuE,EAAA,GAjBtCnE,IAAI;AAscV,eAAeA,IAAI;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}