from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Configure CORS
CORS(app, resources={r"/api/*": {"origins": ["http://localhost:3000"]}})

# Database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# Simple Product model
class Product(db.Model):
    __tablename__ = 'products'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    price = db.Column(db.Numeric(10, 2), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())

# Root route
@app.route('/', methods=['GET'])
def home():
    return jsonify({'message': 'Welcome to the Allora API! Use /api endpoints to interact with the backend.'})

# API Endpoints
@app.route('/api/products', methods=['GET'])
def get_products():
    try:
        products = Product.query.all()
        products_data = []
        for product in products:
            products_data.append({
                'id': product.id,
                'name': product.name,
                'price': float(product.price),
                'description': product.description,
                'created_at': product.created_at.isoformat() if product.created_at else None
            })
        
        return jsonify({
            'success': True,
            'data': products_data,
            'total': len(products_data)
        })
    except Exception as e:
        return jsonify({'error': f'Failed to fetch products: {str(e)}'}), 500

@app.route('/api/products/<int:id>', methods=['GET'])
def get_product(id):
    try:
        product = Product.query.get_or_404(id)
        return jsonify({
            'success': True,
            'data': {
                'id': product.id,
                'name': product.name,
                'price': float(product.price),
                'description': product.description,
                'created_at': product.created_at.isoformat() if product.created_at else None
            }
        })
    except Exception as e:
        return jsonify({'error': f'Failed to fetch product: {str(e)}'}), 500

if __name__ == '__main__':
    print("Starting minimal Flask app...")
    with app.app_context():
        print("Creating database tables...")
        db.create_all()
        print("Database tables created successfully!")
        
        # Add a test product if none exist
        if Product.query.count() == 0:
            test_product = Product(
                name='Test Product',
                price=99.99,
                description='This is a test product'
            )
            db.session.add(test_product)
            db.session.commit()
            print("Test product added!")
    
    app.run(debug=True, host='0.0.0.0', port=5003)
