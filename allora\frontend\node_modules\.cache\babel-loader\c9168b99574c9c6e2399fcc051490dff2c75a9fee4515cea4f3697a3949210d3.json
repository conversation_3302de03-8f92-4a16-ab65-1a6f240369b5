{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AdminLayout from '../components/AdminLayout';\nimport './AdminDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _statistics$total_rev, _fulfillmentData$fulf, _fulfillmentData$fulf2, _fulfillmentData$fulf3, _fulfillmentData$trac, _fulfillmentData$trac2, _fulfillmentData$trac3, _fulfillmentData$trac4, _fulfillmentData$trac5, _fulfillmentData$trac6, _fulfillmentData$trac7, _fulfillmentData$fulf4, _fulfillmentData$fulf5, _fulfillmentData$fulf6, _fulfillmentData$trac8;\n  const [dashboardData, setDashboardData] = useState(null);\n  const [fulfillmentData, setFulfillmentData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchDashboardData();\n    fetchFulfillmentData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        navigate('/admin/login');\n        return;\n      }\n      const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setDashboardData(data);\n      } else if (response.status === 401) {\n        localStorage.removeItem('adminToken');\n        localStorage.removeItem('adminUser');\n        navigate('/admin/login');\n      } else {\n        setError('Failed to load dashboard data');\n      }\n    } catch (error) {\n      setError('Network error. Please try again.');\n      console.error('Dashboard fetch error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchFulfillmentData = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      if (!token) return;\n      const response = await fetch('http://localhost:5000/api/fulfillment/dashboard/summary', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setFulfillmentData(data.summary);\n      }\n    } catch (error) {\n      console.error('Fulfillment data fetch error:', error);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchDashboardData,\n          className: \"retry-btn\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this);\n  }\n  const {\n    statistics,\n    recent_orders,\n    low_stock_products\n  } = dashboardData || {};\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-dashboard\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Dashboard Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome to the Allora Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon products\",\n            children: \"\\uD83D\\uDCE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_products) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon orders\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_orders) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon users\",\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_users) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon revenue\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\u20B9\", (statistics === null || statistics === void 0 ? void 0 : (_statistics$total_rev = statistics.total_revenue) === null || _statistics$total_rev === void 0 ? void 0 : _statistics$total_rev.toLocaleString()) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Revenue (30 days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon fulfillment\",\n            children: \"\\uD83D\\uDE9A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (fulfillmentData === null || fulfillmentData === void 0 ? void 0 : (_fulfillmentData$fulf = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf === void 0 ? void 0 : _fulfillmentData$fulf.total_shipments) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Active Shipments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon delivery\",\n            children: \"\\uD83D\\uDCE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (fulfillmentData === null || fulfillmentData === void 0 ? void 0 : (_fulfillmentData$fulf2 = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf2 === void 0 ? void 0 : _fulfillmentData$fulf2.delivered_today) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Delivered Today\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon performance\",\n            children: \"\\u26A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [(fulfillmentData === null || fulfillmentData === void 0 ? void 0 : (_fulfillmentData$fulf3 = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf3 === void 0 ? void 0 : _fulfillmentData$fulf3.on_time_delivery_rate) || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"On-Time Delivery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon exceptions\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (fulfillmentData === null || fulfillmentData === void 0 ? void 0 : (_fulfillmentData$trac = fulfillmentData.tracking_metrics) === null || _fulfillmentData$trac === void 0 ? void 0 : _fulfillmentData$trac.exception_count) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Exceptions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/orders'),\n              className: \"view-all-btn\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"orders-table\",\n            children: recent_orders && recent_orders.length > 0 ? /*#__PURE__*/_jsxDEV(\"table\", {\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Order #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Customer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recent_orders.map(order => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [\"#\", order.order_number]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: order.customer_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status-badge ${order.status}`,\n                      children: order.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [\"\\u20B9\", order.total_amount.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(order.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 49\n                  }, this)]\n                }, order.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 45\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"No recent orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Low Stock Alert\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/inventory'),\n              className: \"view-all-btn\",\n              children: \"View Inventory\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"low-stock-list\",\n            children: low_stock_products && low_stock_products.length > 0 ? low_stock_products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"low-stock-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Stock: \", product.stock_quantity, \" / Threshold: \", product.low_stock_threshold]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stock-level\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stock-bar\",\n                  style: {\n                    width: `${Math.min(product.stock_quantity / product.low_stock_threshold * 100, 100)}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 41\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 37\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"All products are well stocked\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section fulfillment-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Fulfillment Overview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/fulfillment'),\n              className: \"view-all-btn\",\n              children: \"View Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 25\n          }, this), fulfillmentData ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fulfillment-overview\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fulfillment-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Carrier Performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"carrier-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"carrier-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"carrier-name\",\n                    children: \"Blue Dart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"carrier-performance\",\n                    children: [((_fulfillmentData$trac2 = fulfillmentData.tracking_metrics) === null || _fulfillmentData$trac2 === void 0 ? void 0 : (_fulfillmentData$trac3 = _fulfillmentData$trac2.carrier_performance) === null || _fulfillmentData$trac3 === void 0 ? void 0 : _fulfillmentData$trac3.blue_dart) || 'N/A', \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"carrier-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"carrier-name\",\n                    children: \"Delhivery\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"carrier-performance\",\n                    children: [((_fulfillmentData$trac4 = fulfillmentData.tracking_metrics) === null || _fulfillmentData$trac4 === void 0 ? void 0 : (_fulfillmentData$trac5 = _fulfillmentData$trac4.carrier_performance) === null || _fulfillmentData$trac5 === void 0 ? void 0 : _fulfillmentData$trac5.delhivery) || 'N/A', \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"carrier-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"carrier-name\",\n                    children: \"FedEx\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"carrier-performance\",\n                    children: [((_fulfillmentData$trac6 = fulfillmentData.tracking_metrics) === null || _fulfillmentData$trac6 === void 0 ? void 0 : (_fulfillmentData$trac7 = _fulfillmentData$trac6.carrier_performance) === null || _fulfillmentData$trac7 === void 0 ? void 0 : _fulfillmentData$trac7.fedex) || 'N/A', \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fulfillment-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Recent Activity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"activity-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"activity-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"activity-label\",\n                    children: \"Orders Processed Today\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"activity-value\",\n                    children: ((_fulfillmentData$fulf4 = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf4 === void 0 ? void 0 : _fulfillmentData$fulf4.orders_processed_today) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"activity-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"activity-label\",\n                    children: \"Shipments Created\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"activity-value\",\n                    children: ((_fulfillmentData$fulf5 = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf5 === void 0 ? void 0 : _fulfillmentData$fulf5.shipments_created_today) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"activity-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"activity-label\",\n                    children: \"Average Processing Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"activity-value\",\n                    children: ((_fulfillmentData$fulf6 = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf6 === void 0 ? void 0 : _fulfillmentData$fulf6.avg_processing_time) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 33\n            }, this), ((_fulfillmentData$trac8 = fulfillmentData.tracking_metrics) === null || _fulfillmentData$trac8 === void 0 ? void 0 : _fulfillmentData$trac8.exception_count) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fulfillment-alerts\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u26A0\\uFE0F System Alerts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"alert-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"alert-message\",\n                  children: [fulfillmentData.tracking_metrics.exception_count, \" shipment exceptions require attention\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"alert-action-btn\",\n                  onClick: () => navigate('/admin/fulfillment/alerts'),\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fulfillment-loading\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading fulfillment data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 9\n  }, this);\n};\n_s(AdminDashboard, \"IckvB8unvifWDN/lAv5wcCqHmOM=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "AdminLayout", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_statistics$total_rev", "_fulfillmentData$fulf", "_fulfillmentData$fulf2", "_fulfillmentData$fulf3", "_fulfillmentData$trac", "_fulfillmentData$trac2", "_fulfillmentData$trac3", "_fulfillmentData$trac4", "_fulfillmentData$trac5", "_fulfillmentData$trac6", "_fulfillmentData$trac7", "_fulfillmentData$fulf4", "_fulfillmentData$fulf5", "_fulfillmentData$fulf6", "_fulfillmentData$trac8", "dashboardData", "setDashboardData", "fulfillmentData", "setFulfillmentData", "loading", "setLoading", "error", "setError", "navigate", "fetchDashboardData", "fetchFulfillmentData", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "data", "json", "status", "removeItem", "console", "summary", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "statistics", "recent_orders", "low_stock_products", "total_products", "total_orders", "total_users", "total_revenue", "toLocaleString", "fulfillment_metrics", "total_shipments", "delivered_today", "on_time_delivery_rate", "tracking_metrics", "exception_count", "length", "map", "order", "order_number", "customer_email", "total_amount", "Date", "created_at", "toLocaleDateString", "id", "product", "name", "stock_quantity", "low_stock_threshold", "style", "width", "Math", "min", "carrier_performance", "blue_dart", "delhivery", "fedex", "orders_processed_today", "shipments_created_today", "avg_processing_time", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AdminLayout from '../components/AdminLayout';\nimport './AdminDashboard.css';\n\nconst AdminDashboard = () => {\n    const [dashboardData, setDashboardData] = useState(null);\n    const [fulfillmentData, setFulfillmentData] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        fetchDashboardData();\n        fetchFulfillmentData();\n    }, []);\n\n    const fetchDashboardData = async () => {\n        try {\n            const token = localStorage.getItem('adminToken');\n            if (!token) {\n                navigate('/admin/login');\n                return;\n            }\n\n            const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                },\n            });\n\n            if (response.ok) {\n                const data = await response.json();\n                setDashboardData(data);\n            } else if (response.status === 401) {\n                localStorage.removeItem('adminToken');\n                localStorage.removeItem('adminUser');\n                navigate('/admin/login');\n            } else {\n                setError('Failed to load dashboard data');\n            }\n        } catch (error) {\n            setError('Network error. Please try again.');\n            console.error('Dashboard fetch error:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchFulfillmentData = async () => {\n        try {\n            const token = localStorage.getItem('adminToken');\n            if (!token) return;\n\n            const response = await fetch('http://localhost:5000/api/fulfillment/dashboard/summary', {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                },\n            });\n\n            if (response.ok) {\n                const data = await response.json();\n                setFulfillmentData(data.summary);\n            }\n        } catch (error) {\n            console.error('Fulfillment data fetch error:', error);\n        }\n    };\n\n    if (loading) {\n        return (\n            <AdminLayout>\n                <div className=\"admin-loading\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading dashboard...</p>\n                </div>\n            </AdminLayout>\n        );\n    }\n\n    if (error) {\n        return (\n            <AdminLayout>\n                <div className=\"admin-error\">\n                    <p>{error}</p>\n                    <button onClick={fetchDashboardData} className=\"retry-btn\">\n                        Retry\n                    </button>\n                </div>\n            </AdminLayout>\n        );\n    }\n\n    const { statistics, recent_orders, low_stock_products } = dashboardData || {};\n\n    return (\n        <AdminLayout>\n            <div className=\"admin-dashboard\">\n                <div className=\"dashboard-header\">\n                    <h1>Dashboard Overview</h1>\n                    <p>Welcome to the Allora Admin Panel</p>\n                </div>\n\n                {/* Statistics Cards */}\n                <div className=\"stats-grid\">\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon products\">📦</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_products || 0}</h3>\n                            <p>Total Products</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon orders\">🛒</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_orders || 0}</h3>\n                            <p>Total Orders</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon users\">👥</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_users || 0}</h3>\n                            <p>Total Users</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon revenue\">💰</div>\n                        <div className=\"stat-content\">\n                            <h3>₹{statistics?.total_revenue?.toLocaleString() || 0}</h3>\n                            <p>Revenue (30 days)</p>\n                        </div>\n                    </div>\n\n                    {/* Fulfillment Statistics */}\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon fulfillment\">🚚</div>\n                        <div className=\"stat-content\">\n                            <h3>{fulfillmentData?.fulfillment_metrics?.total_shipments || 0}</h3>\n                            <p>Active Shipments</p>\n                        </div>\n                    </div>\n\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon delivery\">📦</div>\n                        <div className=\"stat-content\">\n                            <h3>{fulfillmentData?.fulfillment_metrics?.delivered_today || 0}</h3>\n                            <p>Delivered Today</p>\n                        </div>\n                    </div>\n\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon performance\">⚡</div>\n                        <div className=\"stat-content\">\n                            <h3>{fulfillmentData?.fulfillment_metrics?.on_time_delivery_rate || 0}%</h3>\n                            <p>On-Time Delivery</p>\n                        </div>\n                    </div>\n\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon exceptions\">⚠️</div>\n                        <div className=\"stat-content\">\n                            <h3>{fulfillmentData?.tracking_metrics?.exception_count || 0}</h3>\n                            <p>Exceptions</p>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"dashboard-content\">\n                    {/* Recent Orders */}\n                    <div className=\"dashboard-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Orders</h2>\n                            <button\n                                onClick={() => navigate('/admin/orders')}\n                                className=\"view-all-btn\"\n                            >\n                                View All\n                            </button>\n                        </div>\n                        \n                        <div className=\"orders-table\">\n                            {recent_orders && recent_orders.length > 0 ? (\n                                <table>\n                                    <thead>\n                                        <tr>\n                                            <th>Order #</th>\n                                            <th>Customer</th>\n                                            <th>Status</th>\n                                            <th>Amount</th>\n                                            <th>Date</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        {recent_orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>#{order.order_number}</td>\n                                                <td>{order.customer_email}</td>\n                                                <td>\n                                                    <span className={`status-badge ${order.status}`}>\n                                                        {order.status}\n                                                    </span>\n                                                </td>\n                                                <td>₹{order.total_amount.toLocaleString()}</td>\n                                                <td>{new Date(order.created_at).toLocaleDateString()}</td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </table>\n                            ) : (\n                                <p className=\"no-data\">No recent orders</p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Low Stock Alert */}\n                    <div className=\"dashboard-section\">\n                        <div className=\"section-header\">\n                            <h2>Low Stock Alert</h2>\n                            <button \n                                onClick={() => navigate('/admin/inventory')}\n                                className=\"view-all-btn\"\n                            >\n                                View Inventory\n                            </button>\n                        </div>\n                        \n                        <div className=\"low-stock-list\">\n                            {low_stock_products && low_stock_products.length > 0 ? (\n                                low_stock_products.map(product => (\n                                    <div key={product.id} className=\"low-stock-item\">\n                                        <div className=\"product-info\">\n                                            <h4>{product.name}</h4>\n                                            <p>Stock: {product.stock_quantity} / Threshold: {product.low_stock_threshold}</p>\n                                        </div>\n                                        <div className=\"stock-level\">\n                                            <div \n                                                className=\"stock-bar\"\n                                                style={{\n                                                    width: `${Math.min((product.stock_quantity / product.low_stock_threshold) * 100, 100)}%`\n                                                }}\n                                            ></div>\n                                        </div>\n                                    </div>\n                                ))\n                            ) : (\n                                <p className=\"no-data\">All products are well stocked</p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Fulfillment Monitoring */}\n                    <div className=\"dashboard-section fulfillment-section\">\n                        <div className=\"section-header\">\n                            <h2>Fulfillment Overview</h2>\n                            <button\n                                onClick={() => navigate('/admin/fulfillment')}\n                                className=\"view-all-btn\"\n                            >\n                                View Details\n                            </button>\n                        </div>\n\n                        {fulfillmentData ? (\n                            <div className=\"fulfillment-overview\">\n                                {/* Carrier Performance */}\n                                <div className=\"fulfillment-metric\">\n                                    <h4>Carrier Performance</h4>\n                                    <div className=\"carrier-stats\">\n                                        <div className=\"carrier-stat\">\n                                            <span className=\"carrier-name\">Blue Dart</span>\n                                            <span className=\"carrier-performance\">\n                                                {fulfillmentData.tracking_metrics?.carrier_performance?.blue_dart || 'N/A'}%\n                                            </span>\n                                        </div>\n                                        <div className=\"carrier-stat\">\n                                            <span className=\"carrier-name\">Delhivery</span>\n                                            <span className=\"carrier-performance\">\n                                                {fulfillmentData.tracking_metrics?.carrier_performance?.delhivery || 'N/A'}%\n                                            </span>\n                                        </div>\n                                        <div className=\"carrier-stat\">\n                                            <span className=\"carrier-name\">FedEx</span>\n                                            <span className=\"carrier-performance\">\n                                                {fulfillmentData.tracking_metrics?.carrier_performance?.fedex || 'N/A'}%\n                                            </span>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                {/* Recent Fulfillment Activity */}\n                                <div className=\"fulfillment-metric\">\n                                    <h4>Recent Activity</h4>\n                                    <div className=\"activity-stats\">\n                                        <div className=\"activity-item\">\n                                            <span className=\"activity-label\">Orders Processed Today</span>\n                                            <span className=\"activity-value\">\n                                                {fulfillmentData.fulfillment_metrics?.orders_processed_today || 0}\n                                            </span>\n                                        </div>\n                                        <div className=\"activity-item\">\n                                            <span className=\"activity-label\">Shipments Created</span>\n                                            <span className=\"activity-value\">\n                                                {fulfillmentData.fulfillment_metrics?.shipments_created_today || 0}\n                                            </span>\n                                        </div>\n                                        <div className=\"activity-item\">\n                                            <span className=\"activity-label\">Average Processing Time</span>\n                                            <span className=\"activity-value\">\n                                                {fulfillmentData.fulfillment_metrics?.avg_processing_time || 'N/A'}\n                                            </span>\n                                        </div>\n                                    </div>\n                                </div>\n\n                                {/* System Alerts */}\n                                {fulfillmentData.tracking_metrics?.exception_count > 0 && (\n                                    <div className=\"fulfillment-alerts\">\n                                        <h4>⚠️ System Alerts</h4>\n                                        <div className=\"alert-item\">\n                                            <span className=\"alert-message\">\n                                                {fulfillmentData.tracking_metrics.exception_count} shipment exceptions require attention\n                                            </span>\n                                            <button\n                                                className=\"alert-action-btn\"\n                                                onClick={() => navigate('/admin/fulfillment/alerts')}\n                                            >\n                                                View Details\n                                            </button>\n                                        </div>\n                                    </div>\n                                )}\n                            </div>\n                        ) : (\n                            <div className=\"fulfillment-loading\">\n                                <p>Loading fulfillment data...</p>\n                            </div>\n                        )}\n                    </div>\n                </div>\n            </div>\n        </AdminLayout>\n    );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM+B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZ+B,kBAAkB,CAAC,CAAC;IACpBC,oBAAoB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACF,KAAK,EAAE;QACRH,QAAQ,CAAC,cAAc,CAAC;QACxB;MACJ;MAEA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;QACtEC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUL,KAAK;QACpC;MACJ,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACb,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClClB,gBAAgB,CAACiB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIJ,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;QAChCR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;QACrCT,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;QACpCb,QAAQ,CAAC,cAAc,CAAC;MAC5B,CAAC,MAAM;QACHD,QAAQ,CAAC,+BAA+B,CAAC;MAC7C;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZC,QAAQ,CAAC,kCAAkC,CAAC;MAC5Ce,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACND,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMK,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACF,KAAK,EAAE;MAEZ,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,yDAAyD,EAAE;QACpFC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUL,KAAK;QACpC;MACJ,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACb,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClChB,kBAAkB,CAACe,IAAI,CAACK,OAAO,CAAC;MACpC;IACJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACZgB,OAAO,CAAChB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACzD;EACJ,CAAC;EAED,IAAIF,OAAO,EAAE;IACT,oBACItB,OAAA,CAACF,WAAW;MAAA4C,QAAA,eACR1C,OAAA;QAAK2C,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC1B1C,OAAA;UAAK2C,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC/C,OAAA;UAAA0C,QAAA,EAAG;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEtB;EAEA,IAAIvB,KAAK,EAAE;IACP,oBACIxB,OAAA,CAACF,WAAW;MAAA4C,QAAA,eACR1C,OAAA;QAAK2C,SAAS,EAAC,aAAa;QAAAD,QAAA,gBACxB1C,OAAA;UAAA0C,QAAA,EAAIlB;QAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd/C,OAAA;UAAQgD,OAAO,EAAErB,kBAAmB;UAACgB,SAAS,EAAC,WAAW;UAAAD,QAAA,EAAC;QAE3D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEtB;EAEA,MAAM;IAAEE,UAAU;IAAEC,aAAa;IAAEC;EAAmB,CAAC,GAAGjC,aAAa,IAAI,CAAC,CAAC;EAE7E,oBACIlB,OAAA,CAACF,WAAW;IAAA4C,QAAA,eACR1C,OAAA;MAAK2C,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC5B1C,OAAA;QAAK2C,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7B1C,OAAA;UAAA0C,QAAA,EAAI;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B/C,OAAA;UAAA0C,QAAA,EAAG;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAGN/C,OAAA;QAAK2C,SAAS,EAAC,YAAY;QAAAD,QAAA,gBACvB1C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1C,OAAA;YAAK2C,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5C/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1C,OAAA;cAAA0C,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,cAAc,KAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1C/C,OAAA;cAAA0C,QAAA,EAAG;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1C,OAAA;YAAK2C,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1C,OAAA;cAAA0C,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,YAAY,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxC/C,OAAA;cAAA0C,QAAA,EAAG;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1C,OAAA;YAAK2C,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzC/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1C,OAAA;cAAA0C,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,KAAI;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvC/C,OAAA;cAAA0C,QAAA,EAAG;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1C,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3C/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1C,OAAA;cAAA0C,QAAA,GAAI,QAAC,EAAC,CAAAO,UAAU,aAAVA,UAAU,wBAAA9C,qBAAA,GAAV8C,UAAU,CAAEM,aAAa,cAAApD,qBAAA,uBAAzBA,qBAAA,CAA2BqD,cAAc,CAAC,CAAC,KAAI,CAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5D/C,OAAA;cAAA0C,QAAA,EAAG;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1C,OAAA;YAAK2C,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/C/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1C,OAAA;cAAA0C,QAAA,EAAK,CAAAtB,eAAe,aAAfA,eAAe,wBAAAhB,qBAAA,GAAfgB,eAAe,CAAEqC,mBAAmB,cAAArD,qBAAA,uBAApCA,qBAAA,CAAsCsD,eAAe,KAAI;YAAC;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrE/C,OAAA;cAAA0C,QAAA,EAAG;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1C,OAAA;YAAK2C,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5C/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1C,OAAA;cAAA0C,QAAA,EAAK,CAAAtB,eAAe,aAAfA,eAAe,wBAAAf,sBAAA,GAAfe,eAAe,CAAEqC,mBAAmB,cAAApD,sBAAA,uBAApCA,sBAAA,CAAsCsD,eAAe,KAAI;YAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrE/C,OAAA;cAAA0C,QAAA,EAAG;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1C,OAAA;YAAK2C,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9C/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1C,OAAA;cAAA0C,QAAA,GAAK,CAAAtB,eAAe,aAAfA,eAAe,wBAAAd,sBAAA,GAAfc,eAAe,CAAEqC,mBAAmB,cAAAnD,sBAAA,uBAApCA,sBAAA,CAAsCsD,qBAAqB,KAAI,CAAC,EAAC,GAAC;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5E/C,OAAA;cAAA0C,QAAA,EAAG;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1C,OAAA;YAAK2C,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9C/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1C,OAAA;cAAA0C,QAAA,EAAK,CAAAtB,eAAe,aAAfA,eAAe,wBAAAb,qBAAA,GAAfa,eAAe,CAAEyC,gBAAgB,cAAAtD,qBAAA,uBAAjCA,qBAAA,CAAmCuD,eAAe,KAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClE/C,OAAA;cAAA0C,QAAA,EAAG;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/C,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAE9B1C,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9B1C,OAAA;YAAK2C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B1C,OAAA;cAAA0C,QAAA,EAAI;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtB/C,OAAA;cACIgD,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,eAAe,CAAE;cACzCiB,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEN/C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAD,QAAA,EACxBQ,aAAa,IAAIA,aAAa,CAACa,MAAM,GAAG,CAAC,gBACtC/D,OAAA;cAAA0C,QAAA,gBACI1C,OAAA;gBAAA0C,QAAA,eACI1C,OAAA;kBAAA0C,QAAA,gBACI1C,OAAA;oBAAA0C,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB/C,OAAA;oBAAA0C,QAAA,EAAI;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB/C,OAAA;oBAAA0C,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf/C,OAAA;oBAAA0C,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf/C,OAAA;oBAAA0C,QAAA,EAAI;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACR/C,OAAA;gBAAA0C,QAAA,EACKQ,aAAa,CAACc,GAAG,CAACC,KAAK,iBACpBjE,OAAA;kBAAA0C,QAAA,gBACI1C,OAAA;oBAAA0C,QAAA,GAAI,GAAC,EAACuB,KAAK,CAACC,YAAY;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9B/C,OAAA;oBAAA0C,QAAA,EAAKuB,KAAK,CAACE;kBAAc;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/B/C,OAAA;oBAAA0C,QAAA,eACI1C,OAAA;sBAAM2C,SAAS,EAAE,gBAAgBsB,KAAK,CAAC3B,MAAM,EAAG;sBAAAI,QAAA,EAC3CuB,KAAK,CAAC3B;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACL/C,OAAA;oBAAA0C,QAAA,GAAI,QAAC,EAACuB,KAAK,CAACG,YAAY,CAACZ,cAAc,CAAC,CAAC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/C/C,OAAA;oBAAA0C,QAAA,EAAK,IAAI2B,IAAI,CAACJ,KAAK,CAACK,UAAU,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GATrDkB,KAAK,CAACO,EAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUb,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAER/C,OAAA;cAAG2C,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC7C;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/C,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9B1C,OAAA;YAAK2C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B1C,OAAA;cAAA0C,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB/C,OAAA;cACIgD,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,kBAAkB,CAAE;cAC5CiB,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEN/C,OAAA;YAAK2C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC1BS,kBAAkB,IAAIA,kBAAkB,CAACY,MAAM,GAAG,CAAC,GAChDZ,kBAAkB,CAACa,GAAG,CAACS,OAAO,iBAC1BzE,OAAA;cAAsB2C,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC5C1C,OAAA;gBAAK2C,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB1C,OAAA;kBAAA0C,QAAA,EAAK+B,OAAO,CAACC;gBAAI;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvB/C,OAAA;kBAAA0C,QAAA,GAAG,SAAO,EAAC+B,OAAO,CAACE,cAAc,EAAC,gBAAc,EAACF,OAAO,CAACG,mBAAmB;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACN/C,OAAA;gBAAK2C,SAAS,EAAC,aAAa;gBAAAD,QAAA,eACxB1C,OAAA;kBACI2C,SAAS,EAAC,WAAW;kBACrBkC,KAAK,EAAE;oBACHC,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAEP,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,mBAAmB,GAAI,GAAG,EAAE,GAAG,CAAC;kBACzF;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAZA0B,OAAO,CAACD,EAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaf,CACR,CAAC,gBAEF/C,OAAA;cAAG2C,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC1D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/C,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAD,QAAA,gBAClD1C,OAAA;YAAK2C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B1C,OAAA;cAAA0C,QAAA,EAAI;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B/C,OAAA;cACIgD,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,oBAAoB,CAAE;cAC9CiB,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,EAEL3B,eAAe,gBACZpB,OAAA;YAAK2C,SAAS,EAAC,sBAAsB;YAAAD,QAAA,gBAEjC1C,OAAA;cAAK2C,SAAS,EAAC,oBAAoB;cAAAD,QAAA,gBAC/B1C,OAAA;gBAAA0C,QAAA,EAAI;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5B/C,OAAA;gBAAK2C,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC1B1C,OAAA;kBAAK2C,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBACzB1C,OAAA;oBAAM2C,SAAS,EAAC,cAAc;oBAAAD,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/C/C,OAAA;oBAAM2C,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,GAChC,EAAAlC,sBAAA,GAAAY,eAAe,CAACyC,gBAAgB,cAAArD,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAkCyE,mBAAmB,cAAAxE,sBAAA,uBAArDA,sBAAA,CAAuDyE,SAAS,KAAI,KAAK,EAAC,GAC/E;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN/C,OAAA;kBAAK2C,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBACzB1C,OAAA;oBAAM2C,SAAS,EAAC,cAAc;oBAAAD,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/C/C,OAAA;oBAAM2C,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,GAChC,EAAAhC,sBAAA,GAAAU,eAAe,CAACyC,gBAAgB,cAAAnD,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAkCuE,mBAAmB,cAAAtE,sBAAA,uBAArDA,sBAAA,CAAuDwE,SAAS,KAAI,KAAK,EAAC,GAC/E;kBAAA;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN/C,OAAA;kBAAK2C,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBACzB1C,OAAA;oBAAM2C,SAAS,EAAC,cAAc;oBAAAD,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C/C,OAAA;oBAAM2C,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,GAChC,EAAA9B,sBAAA,GAAAQ,eAAe,CAACyC,gBAAgB,cAAAjD,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAkCqE,mBAAmB,cAAApE,sBAAA,uBAArDA,sBAAA,CAAuDuE,KAAK,KAAI,KAAK,EAAC,GAC3E;kBAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGN/C,OAAA;cAAK2C,SAAS,EAAC,oBAAoB;cAAAD,QAAA,gBAC/B1C,OAAA;gBAAA0C,QAAA,EAAI;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB/C,OAAA;gBAAK2C,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC3B1C,OAAA;kBAAK2C,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC1B1C,OAAA;oBAAM2C,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,EAAC;kBAAsB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9D/C,OAAA;oBAAM2C,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,EAC3B,EAAA5B,sBAAA,GAAAM,eAAe,CAACqC,mBAAmB,cAAA3C,sBAAA,uBAAnCA,sBAAA,CAAqCuE,sBAAsB,KAAI;kBAAC;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN/C,OAAA;kBAAK2C,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC1B1C,OAAA;oBAAM2C,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,EAAC;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzD/C,OAAA;oBAAM2C,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,EAC3B,EAAA3B,sBAAA,GAAAK,eAAe,CAACqC,mBAAmB,cAAA1C,sBAAA,uBAAnCA,sBAAA,CAAqCuE,uBAAuB,KAAI;kBAAC;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN/C,OAAA;kBAAK2C,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC1B1C,OAAA;oBAAM2C,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/D/C,OAAA;oBAAM2C,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,EAC3B,EAAA1B,sBAAA,GAAAI,eAAe,CAACqC,mBAAmB,cAAAzC,sBAAA,uBAAnCA,sBAAA,CAAqCuE,mBAAmB,KAAI;kBAAK;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAGL,EAAA9B,sBAAA,GAAAG,eAAe,CAACyC,gBAAgB,cAAA5C,sBAAA,uBAAhCA,sBAAA,CAAkC6C,eAAe,IAAG,CAAC,iBAClD9D,OAAA;cAAK2C,SAAS,EAAC,oBAAoB;cAAAD,QAAA,gBAC/B1C,OAAA;gBAAA0C,QAAA,EAAI;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzB/C,OAAA;gBAAK2C,SAAS,EAAC,YAAY;gBAAAD,QAAA,gBACvB1C,OAAA;kBAAM2C,SAAS,EAAC,eAAe;kBAAAD,QAAA,GAC1BtB,eAAe,CAACyC,gBAAgB,CAACC,eAAe,EAAC,wCACtD;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACP/C,OAAA;kBACI2C,SAAS,EAAC,kBAAkB;kBAC5BK,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,2BAA2B,CAAE;kBAAAgB,QAAA,EACxD;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,gBAEN/C,OAAA;YAAK2C,SAAS,EAAC,qBAAqB;YAAAD,QAAA,eAChC1C,OAAA;cAAA0C,QAAA,EAAG;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAC7C,EAAA,CArVID,cAAc;EAAA,QAKCJ,WAAW;AAAA;AAAA2F,EAAA,GAL1BvF,cAAc;AAuVpB,eAAeA,cAAc;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}