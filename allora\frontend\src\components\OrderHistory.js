import React, { useState, useEffect } from 'react';
import {
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Calendar,
  CreditCard,
  MapPin,
  ExternalLink
} from 'lucide-react';
import { API_BASE_URL } from '../config/api';
import OrderTracking from './OrderTracking';

const OrderHistory = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showTracking, setShowTracking] = useState(false);
  const [trackingOrderId, setTrackingOrderId] = useState(null);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/orders`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const ordersData = await response.json();
        setOrders(Array.isArray(ordersData) ? ordersData : []);
      } else {
        console.error('Failed to fetch orders');
        setOrders([]);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchOrderDetails = async (orderId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const orderData = await response.json();
        setSelectedOrder(orderData);
        setShowOrderDetails(true);
      } else {
        console.error('Failed to fetch order details');
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
    }
  };

  const handleTrackOrder = (orderId) => {
    setTrackingOrderId(orderId);
    setShowTracking(true);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'confirmed':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      case 'shipped':
        return <Truck className="w-5 h-5 text-purple-500" />;
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Package className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-gray-200 h-24 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (showOrderDetails && selectedOrder) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Order Details</h2>
          <button
            onClick={() => setShowOrderDetails(false)}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            ← Back to Orders
          </button>
        </div>

        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Order Information</h3>
              <p className="text-sm text-gray-600">Order #{selectedOrder.order_number}</p>
              <p className="text-sm text-gray-600">
                Placed on {new Date(selectedOrder.created_at).toLocaleDateString()}
              </p>
              <div className="flex items-center space-x-2 mt-2">
                {getStatusIcon(selectedOrder.status)}
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedOrder.status)}`}>
                  {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                </span>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Shipping Address</h3>
              {selectedOrder.shipping_address && (
                <div className="text-sm text-gray-600">
                  <p>{selectedOrder.shipping_address.full_name}</p>
                  <p>{selectedOrder.shipping_address.address_line_1}</p>
                  {selectedOrder.shipping_address.address_line_2 && (
                    <p>{selectedOrder.shipping_address.address_line_2}</p>
                  )}
                  <p>{selectedOrder.shipping_address.city}, {selectedOrder.shipping_address.state}</p>
                  <p>{selectedOrder.shipping_address.postal_code}</p>
                </div>
              )}
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Payment & Tracking</h3>
              <p className="text-sm text-gray-600">Payment: {selectedOrder.payment_method}</p>
              <p className="text-sm text-gray-600">
                Status: {selectedOrder.payment_status.charAt(0).toUpperCase() + selectedOrder.payment_status.slice(1)}
              </p>
              {selectedOrder.tracking_number && (
                <p className="text-sm text-gray-600">Tracking: {selectedOrder.tracking_number}</p>
              )}
              {selectedOrder.estimated_delivery && (
                <p className="text-sm text-gray-600">
                  Est. delivery: {new Date(selectedOrder.estimated_delivery).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-semibold text-gray-900">Order Items</h3>
          {Array.isArray(selectedOrder.items) && selectedOrder.items.map((item) => (
            <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
              {item.product_image && (
                <img
                  src={item.product_image}
                  alt={item.product_name}
                  className="w-16 h-16 object-cover rounded-lg"
                />
              )}
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{item.product_name}</h4>
                <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                <p className="text-sm text-gray-600">Price: ₹{item.unit_price}</p>
              </div>
              <div className="text-right">
                <p className="font-semibold text-gray-900">₹{item.total_price}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Subtotal:</span>
              <span>₹{selectedOrder.subtotal}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Tax:</span>
              <span>₹{selectedOrder.tax_amount}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Shipping:</span>
              <span>₹{selectedOrder.shipping_amount}</span>
            </div>
            {selectedOrder.discount_amount > 0 && (
              <div className="flex justify-between text-sm text-green-600">
                <span>Discount:</span>
                <span>-₹{selectedOrder.discount_amount}</span>
              </div>
            )}
            <div className="border-t pt-2 flex justify-between font-semibold">
              <span>Total:</span>
              <span>₹{selectedOrder.total_amount}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Order History</h2>
        <div className="text-sm text-gray-600">
          {orders.length} {orders.length === 1 ? 'order' : 'orders'}
        </div>
      </div>

      {orders.length === 0 ? (
        <div className="text-center py-12">
          <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No orders yet</h3>
          <p className="text-gray-600 mb-6">When you place your first order, it will appear here.</p>
          <button
            onClick={() => window.location.href = '/'}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Start Shopping
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {Array.isArray(orders) && orders.map((order) => (
            <div key={order.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(order.status)}
                  <div>
                    <h3 className="font-semibold text-gray-900">Order #{order.order_number}</h3>
                    <p className="text-sm text-gray-600">
                      Placed on {new Date(order.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">₹{order.total_amount}</p>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Package className="w-4 h-4" />
                    <span>{order.item_count} {order.item_count === 1 ? 'item' : 'items'}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <CreditCard className="w-4 h-4" />
                    <span>{order.payment_method}</span>
                  </div>
                  {order.tracking_number && (
                    <div className="flex items-center space-x-1">
                      <Truck className="w-4 h-4" />
                      <span>Tracking: {order.tracking_number}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {order.tracking_number && (
                    <button
                      onClick={() => handleTrackOrder(order.id)}
                      className="flex items-center space-x-2 px-3 py-2 text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <Truck className="w-4 h-4" />
                      <span>Track Order</span>
                    </button>
                  )}
                  <button
                    onClick={() => fetchOrderDetails(order.id)}
                    className="flex items-center space-x-2 px-4 py-2 text-green-600 hover:text-green-700 transition-colors"
                  >
                    <Eye className="w-4 h-4" />
                    <span>View Details</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Order Tracking Modal */}
      {showTracking && (
        <OrderTracking
          orderId={trackingOrderId}
          onClose={() => {
            setShowTracking(false);
            setTrackingOrderId(null);
          }}
        />
      )}
    </div>
  );
};

export default OrderHistory;
