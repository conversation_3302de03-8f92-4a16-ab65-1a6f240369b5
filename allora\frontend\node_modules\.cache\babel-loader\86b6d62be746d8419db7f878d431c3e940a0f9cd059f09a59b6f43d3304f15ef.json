{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\Category.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useSearchParams, Link } from 'react-router-dom';\nimport VirtualizedProductGrid from '../components/VirtualizedProductGrid';\nimport SearchFilters from '../components/SearchFilters';\nimport SortControls from '../components/SortControls';\nimport { API_BASE_URL } from '../config/api';\nimport { Sparkles, Leaf, Award, TrendingUp, Filter, Grid, List, ChevronRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Category = ({\n  token\n}) => {\n  _s();\n  const {\n    categoryName\n  } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // State\n  const [products, setProducts] = useState([]);\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total: 0,\n    pages: 0,\n    has_next: false,\n    has_prev: false\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Filter state (simplified for category page)\n  const [filters, setFilters] = useState({\n    categories: [],\n    brands: [],\n    sellers: [],\n    priceRange: {\n      min: 0,\n      max: 10000\n    },\n    ratingRange: {\n      min: 0,\n      max: 5\n    },\n    sustainabilityRange: {\n      min: 0,\n      max: 100\n    },\n    inStockOnly: false\n  });\n\n  // Sort and view state\n  const [sortBy, setSortBy] = useState('price');\n  const [sortOrder, setSortOrder] = useState('asc');\n  const [viewMode, setViewMode] = useState('grid');\n\n  // Decode category name for display\n  const decodedCategoryName = categoryName ? categoryName.replace('-', ' ').replace('and', '&') : '';\n\n  // Category configuration with real images and modern styling\n  const getCategoryConfig = categoryName => {\n    const configs = {\n      'clothing': {\n        title: 'Sustainable Clothing',\n        subtitle: 'Eco-friendly fashion that makes a statement',\n        heroImage: 'https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-emerald-600/90 via-teal-600/80 to-cyan-600/90',\n        icon: '👗',\n        features: ['Organic Materials', 'Fair Trade', 'Carbon Neutral', 'Recyclable'],\n        description: 'Discover our curated collection of sustainable clothing made from organic and recycled materials.'\n      },\n      'footwear': {\n        title: 'Eco-Friendly Footwear',\n        subtitle: 'Step forward with sustainable style',\n        heroImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-amber-600/90 via-orange-600/80 to-red-600/90',\n        icon: '👟',\n        features: ['Vegan Materials', 'Recycled Soles', 'Comfort Fit', 'Durable'],\n        description: 'Walk confidently in shoes crafted from sustainable materials without compromising on style.'\n      },\n      'accessories': {\n        title: 'Sustainable Accessories',\n        subtitle: 'Complete your look responsibly',\n        heroImage: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-purple-600/90 via-pink-600/80 to-rose-600/90',\n        icon: '👜',\n        features: ['Handcrafted', 'Eco Materials', 'Timeless Design', 'Versatile'],\n        description: 'Elevate your style with accessories that reflect your commitment to sustainability.'\n      },\n      'kitchen-dining': {\n        title: 'Eco Kitchen & Dining',\n        subtitle: 'Sustainable solutions for your home',\n        heroImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-green-600/90 via-emerald-600/80 to-teal-600/90',\n        icon: '🍽️',\n        features: ['BPA Free', 'Reusable', 'Dishwasher Safe', 'Eco-Friendly'],\n        description: 'Transform your kitchen with sustainable products that reduce waste and environmental impact.'\n      },\n      'electronics': {\n        title: 'Green Electronics',\n        subtitle: 'Technology with a conscience',\n        heroImage: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-blue-600/90 via-indigo-600/80 to-purple-600/90',\n        icon: '📱',\n        features: ['Energy Efficient', 'Recyclable', 'Long Lasting', 'Low Impact'],\n        description: 'Discover electronics designed with sustainability in mind, from renewable materials to energy efficiency.'\n      },\n      'sports-fitness': {\n        title: 'Eco Sports & Fitness',\n        subtitle: 'Train sustainably, perform exceptionally',\n        heroImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-cyan-600/90 via-blue-600/80 to-indigo-600/90',\n        icon: '🏃‍♂️',\n        features: ['Natural Materials', 'Performance Grade', 'Eco-Friendly', 'Durable'],\n        description: 'Achieve your fitness goals with gear that supports both your performance and the planet.'\n      }\n    };\n    const normalizedName = categoryName.toLowerCase().replace(/\\s+/g, '-').replace('&', 'and');\n    return configs[normalizedName] || {\n      title: decodedCategoryName,\n      subtitle: `Discover our ${decodedCategoryName.toLowerCase()} collection`,\n      heroImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n      gradient: 'from-gray-600/90 via-slate-600/80 to-zinc-600/90',\n      icon: '📦',\n      features: ['Quality', 'Sustainable', 'Affordable', 'Reliable'],\n      description: `Explore our carefully curated ${decodedCategoryName.toLowerCase()} products.`\n    };\n  };\n  const categoryConfig = getCategoryConfig(decodedCategoryName);\n\n  // Initialize from URL parameters\n  useEffect(() => {\n    const urlSortBy = searchParams.get('sort_by') || 'price';\n    const urlSortOrder = searchParams.get('sort_order') || 'asc';\n    const urlPage = parseInt(searchParams.get('page')) || 1;\n    setSortBy(urlSortBy);\n    setSortOrder(urlSortOrder);\n    setPagination(prev => ({\n      ...prev,\n      page: urlPage\n    }));\n  }, [searchParams]);\n\n  // Fetch products when parameters change\n  useEffect(() => {\n    if (categoryName) {\n      fetchCategoryProducts();\n    }\n  }, [categoryName, filters, sortBy, sortOrder, pagination.page]);\n  const fetchCategoryProducts = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const params = new URLSearchParams();\n      if (filters.brands.length > 0) params.append('brand', filters.brands.join(','));\n      if (filters.priceRange.min > 0) params.append('min_price', filters.priceRange.min);\n      if (filters.priceRange.max < 10000) params.append('max_price', filters.priceRange.max);\n      if (filters.ratingRange.min > 0) params.append('min_rating', filters.ratingRange.min);\n      if (filters.ratingRange.max < 5) params.append('max_rating', filters.ratingRange.max);\n      if (filters.sustainabilityRange.min > 0) params.append('min_sustainability', filters.sustainabilityRange.min);\n      if (filters.sustainabilityRange.max < 100) params.append('max_sustainability', filters.sustainabilityRange.max);\n      if (filters.inStockOnly) params.append('in_stock_only', 'true');\n      params.append('sort_by', sortBy);\n      params.append('sort_order', sortOrder);\n      params.append('page', pagination.page);\n      params.append('per_page', pagination.per_page);\n      const response = await fetch(`${API_BASE_URL}/categories/${categoryName}/products?${params.toString()}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch category products');\n      }\n      const data = await response.json();\n      setProducts(data.products || []);\n      setPagination(data.pagination || {});\n    } catch (err) {\n      setError(err.message);\n      setProducts([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFiltersChange = newFilters => {\n    setFilters(newFilters);\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n\n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', '1');\n    setSearchParams(newParams);\n  };\n  const handleClearFilters = () => {\n    // Reset filters to default state\n    const defaultFilters = {\n      categories: [],\n      brands: [],\n      sellers: [],\n      priceRange: {\n        min: 0,\n        max: 10000\n      },\n      ratingRange: {\n        min: 0,\n        max: 5\n      },\n      sustainabilityRange: {\n        min: 0,\n        max: 100\n      },\n      inStockOnly: false\n    };\n    setFilters(defaultFilters);\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n\n    // Clear filter-related URL parameters while keeping sort and category\n    const newParams = new URLSearchParams(searchParams);\n\n    // Remove filter parameters\n    newParams.delete('brand');\n    newParams.delete('min_price');\n    newParams.delete('max_price');\n    newParams.delete('min_rating');\n    newParams.delete('max_rating');\n    newParams.delete('min_sustainability');\n    newParams.delete('max_sustainability');\n    newParams.delete('in_stock_only');\n\n    // Reset to first page\n    newParams.set('page', '1');\n    setSearchParams(newParams);\n  };\n  const handleSortChange = (newSortBy, newSortOrder) => {\n    setSortBy(newSortBy);\n    setSortOrder(newSortOrder);\n\n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('sort_by', newSortBy);\n    newParams.set('sort_order', newSortOrder);\n    newParams.set('page', '1');\n    setSearchParams(newParams);\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n  };\n  const handlePageChange = newPage => {\n    setPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n\n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', newPage.toString());\n    setSearchParams(newParams);\n\n    // Scroll to top\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white p-8 rounded-2xl shadow-xl max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDE14\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"Oops! Something went wrong\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500 text-lg mb-6\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 font-semibold\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-white to-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-cover bg-center bg-no-repeat\",\n        style: {\n          backgroundImage: `url(${categoryConfig.heroImage})`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute inset-0 bg-gradient-to-r ${categoryConfig.gradient}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 container mx-auto px-4 py-20\",\n        children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex mb-8\",\n          \"aria-label\": \"Breadcrumb\",\n          children: /*#__PURE__*/_jsxDEV(\"ol\", {\n            className: \"inline-flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"inline-flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"inline-flex items-center text-sm font-medium text-white/80 hover:text-white transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4 mr-2\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this), \"Home\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n                className: \"w-4 h-4 text-white/60\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/search\",\n                className: \"text-sm font-medium text-white/80 hover:text-white transition-colors\",\n                children: \"Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n                className: \"w-4 h-4 text-white/60\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              \"aria-current\": \"page\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-white\",\n                children: categoryConfig.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-white max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-6xl mb-6 animate-bounce\",\n            children: categoryConfig.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-5xl md:text-6xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80\",\n            children: categoryConfig.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl md:text-2xl mb-8 text-white/90 font-light\",\n            children: categoryConfig.subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg mb-8 text-white/80 max-w-2xl mx-auto leading-relaxed\",\n            children: categoryConfig.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap justify-center gap-3 mb-8\",\n            children: categoryConfig.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium text-white border border-white/30 hover:bg-white/30 transition-all duration-200\",\n              children: feature\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center space-x-8 text-white/90\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold\",\n                children: pagination.total || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm\",\n                children: \"Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-px h-8 bg-white/30\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Leaf, {\n                className: \"w-5 h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"100% Sustainable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-px h-8 bg-white/30\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Award, {\n                className: \"w-5 h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Premium Quality\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col xl:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"xl:w-80 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden sticky top-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-100\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Filter, {\n                    className: \"w-5 h-5 mr-2 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 21\n                  }, this), \"Filters\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleClearFilters,\n                  className: \"text-sm text-gray-500 hover:text-gray-700 transition-colors\",\n                  children: \"Clear All\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: /*#__PURE__*/_jsxDEV(SearchFilters, {\n                filters: filters,\n                onFiltersChange: handleFiltersChange,\n                onClearFilters: handleClearFilters,\n                isLoading: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: [pagination.total || 0, \" Products Found\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), pagination.total > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                    className: \"w-4 h-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this), \"Sorted by \", sortBy === 'price' ? 'Price' : sortBy === 'name' ? 'Name' : 'Relevance']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: /*#__PURE__*/_jsxDEV(SortControls, {\n                  sortBy: sortBy,\n                  sortOrder: sortOrder,\n                  onSortChange: handleSortChange,\n                  resultCount: pagination.total,\n                  viewMode: viewMode,\n                  onViewModeChange: setViewMode\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg border border-gray-100 p-12\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                children: \"Loading Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: [\"Finding the best \", decodedCategoryName.toLowerCase(), \" for you...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this), !loading && products.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg border border-gray-100 p-6\",\n            children: /*#__PURE__*/_jsxDEV(VirtualizedProductGrid, {\n              products: products,\n              height: 600,\n              itemHeight: viewMode === 'grid' ? 320 : 200,\n              itemWidth: viewMode === 'grid' ? 300 : 800,\n              emptyMessage: `No ${decodedCategoryName.toLowerCase()} products found.`,\n              viewMode: viewMode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this), !loading && products.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg border border-gray-100 p-12\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-8xl mb-6 opacity-50\",\n                children: \"\\uD83D\\uDD0D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-900 mb-3\",\n                children: [\"No products found in \", categoryConfig.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-8 max-w-md mx-auto\",\n                children: \"We couldn't find any products matching your criteria. Try adjusting your filters or explore other categories.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleClearFilters,\n                  className: \"px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200 font-medium\",\n                  children: \"Clear Filters\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/search\",\n                  className: \"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 font-medium\",\n                  children: \"Browse All Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), !loading && products.length > 0 && pagination.pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-8 bg-white rounded-2xl shadow-lg border border-gray-100 p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Showing page \", pagination.page, \" of \", pagination.pages]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePageChange(pagination.page - 1),\n                  disabled: !pagination.has_prev,\n                  className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium\",\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1\",\n                  children: Array.from({\n                    length: Math.min(5, pagination.pages)\n                  }, (_, i) => {\n                    const pageNum = i + 1;\n                    return /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handlePageChange(pageNum),\n                      className: `w-10 h-10 rounded-lg font-medium transition-colors duration-200 ${pageNum === pagination.page ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                      children: pageNum\n                    }, pageNum, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 499,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePageChange(pagination.page + 1),\n                  disabled: !pagination.has_next,\n                  className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium\",\n                  children: \"Next\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n};\n_s(Category, \"pwIJHHZsPOr1gLXIV04aBieLEvE=\", false, function () {\n  return [useParams, useSearchParams];\n});\n_c = Category;\nexport default Category;\nvar _c;\n$RefreshReg$(_c, \"Category\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useSearchParams", "Link", "VirtualizedProductGrid", "SearchFilters", "SortControls", "API_BASE_URL", "<PERSON><PERSON><PERSON>", "Leaf", "Award", "TrendingUp", "Filter", "Grid", "List", "ChevronRight", "jsxDEV", "_jsxDEV", "Category", "token", "_s", "categoryName", "searchParams", "setSearchParams", "products", "setProducts", "pagination", "setPagination", "page", "per_page", "total", "pages", "has_next", "has_prev", "loading", "setLoading", "error", "setError", "filters", "setFilters", "categories", "brands", "sellers", "priceRange", "min", "max", "ratingRange", "sustainabilityRange", "inStockOnly", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "viewMode", "setViewMode", "decodedCategoryName", "replace", "getCategoryConfig", "configs", "title", "subtitle", "heroImage", "gradient", "icon", "features", "description", "normalizedName", "toLowerCase", "categoryConfig", "urlSortBy", "get", "urlSortOrder", "urlPage", "parseInt", "prev", "fetchCategoryProducts", "params", "URLSearchParams", "length", "append", "join", "response", "fetch", "toString", "ok", "Error", "data", "json", "err", "message", "handleFiltersChange", "newFilters", "newParams", "set", "handleClearFilters", "defaultFilters", "delete", "handleSortChange", "newSortBy", "newSortOrder", "handlePageChange", "newPage", "window", "scrollTo", "top", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "location", "reload", "style", "backgroundImage", "to", "fill", "viewBox", "d", "map", "feature", "index", "onFiltersChange", "onClearFilters", "isLoading", "onSortChange", "resultCount", "onViewModeChange", "height", "itemHeight", "itemWidth", "emptyMessage", "disabled", "Array", "from", "Math", "_", "i", "pageNum", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/Category.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useSearchParams, Link } from 'react-router-dom';\nimport VirtualizedProductGrid from '../components/VirtualizedProductGrid';\nimport SearchFilters from '../components/SearchFilters';\nimport SortControls from '../components/SortControls';\nimport { API_BASE_URL } from '../config/api';\nimport { Sparkles, Leaf, Award, TrendingUp, Filter, Grid, List, ChevronRight } from 'lucide-react';\n\nconst Category = ({ token }) => {\n  const { categoryName } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // State\n  const [products, setProducts] = useState([]);\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total: 0,\n    pages: 0,\n    has_next: false,\n    has_prev: false\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Filter state (simplified for category page)\n  const [filters, setFilters] = useState({\n    categories: [],\n    brands: [],\n    sellers: [],\n    priceRange: { min: 0, max: 10000 },\n    ratingRange: { min: 0, max: 5 },\n    sustainabilityRange: { min: 0, max: 100 },\n    inStockOnly: false\n  });\n\n  // Sort and view state\n  const [sortBy, setSortBy] = useState('price');\n  const [sortOrder, setSortOrder] = useState('asc');\n  const [viewMode, setViewMode] = useState('grid');\n\n  // Decode category name for display\n  const decodedCategoryName = categoryName ?\n    categoryName.replace('-', ' ').replace('and', '&') : '';\n\n  // Category configuration with real images and modern styling\n  const getCategoryConfig = (categoryName) => {\n    const configs = {\n      'clothing': {\n        title: 'Sustainable Clothing',\n        subtitle: 'Eco-friendly fashion that makes a statement',\n        heroImage: 'https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-emerald-600/90 via-teal-600/80 to-cyan-600/90',\n        icon: '👗',\n        features: ['Organic Materials', 'Fair Trade', 'Carbon Neutral', 'Recyclable'],\n        description: 'Discover our curated collection of sustainable clothing made from organic and recycled materials.'\n      },\n      'footwear': {\n        title: 'Eco-Friendly Footwear',\n        subtitle: 'Step forward with sustainable style',\n        heroImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-amber-600/90 via-orange-600/80 to-red-600/90',\n        icon: '👟',\n        features: ['Vegan Materials', 'Recycled Soles', 'Comfort Fit', 'Durable'],\n        description: 'Walk confidently in shoes crafted from sustainable materials without compromising on style.'\n      },\n      'accessories': {\n        title: 'Sustainable Accessories',\n        subtitle: 'Complete your look responsibly',\n        heroImage: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-purple-600/90 via-pink-600/80 to-rose-600/90',\n        icon: '👜',\n        features: ['Handcrafted', 'Eco Materials', 'Timeless Design', 'Versatile'],\n        description: 'Elevate your style with accessories that reflect your commitment to sustainability.'\n      },\n      'kitchen-dining': {\n        title: 'Eco Kitchen & Dining',\n        subtitle: 'Sustainable solutions for your home',\n        heroImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-green-600/90 via-emerald-600/80 to-teal-600/90',\n        icon: '🍽️',\n        features: ['BPA Free', 'Reusable', 'Dishwasher Safe', 'Eco-Friendly'],\n        description: 'Transform your kitchen with sustainable products that reduce waste and environmental impact.'\n      },\n      'electronics': {\n        title: 'Green Electronics',\n        subtitle: 'Technology with a conscience',\n        heroImage: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-blue-600/90 via-indigo-600/80 to-purple-600/90',\n        icon: '📱',\n        features: ['Energy Efficient', 'Recyclable', 'Long Lasting', 'Low Impact'],\n        description: 'Discover electronics designed with sustainability in mind, from renewable materials to energy efficiency.'\n      },\n      'sports-fitness': {\n        title: 'Eco Sports & Fitness',\n        subtitle: 'Train sustainably, perform exceptionally',\n        heroImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        gradient: 'from-cyan-600/90 via-blue-600/80 to-indigo-600/90',\n        icon: '🏃‍♂️',\n        features: ['Natural Materials', 'Performance Grade', 'Eco-Friendly', 'Durable'],\n        description: 'Achieve your fitness goals with gear that supports both your performance and the planet.'\n      }\n    };\n\n    const normalizedName = categoryName.toLowerCase().replace(/\\s+/g, '-').replace('&', 'and');\n    return configs[normalizedName] || {\n      title: decodedCategoryName,\n      subtitle: `Discover our ${decodedCategoryName.toLowerCase()} collection`,\n      heroImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n      gradient: 'from-gray-600/90 via-slate-600/80 to-zinc-600/90',\n      icon: '📦',\n      features: ['Quality', 'Sustainable', 'Affordable', 'Reliable'],\n      description: `Explore our carefully curated ${decodedCategoryName.toLowerCase()} products.`\n    };\n  };\n\n  const categoryConfig = getCategoryConfig(decodedCategoryName);\n\n  // Initialize from URL parameters\n  useEffect(() => {\n    const urlSortBy = searchParams.get('sort_by') || 'price';\n    const urlSortOrder = searchParams.get('sort_order') || 'asc';\n    const urlPage = parseInt(searchParams.get('page')) || 1;\n\n    setSortBy(urlSortBy);\n    setSortOrder(urlSortOrder);\n    setPagination(prev => ({ ...prev, page: urlPage }));\n  }, [searchParams]);\n\n  // Fetch products when parameters change\n  useEffect(() => {\n    if (categoryName) {\n      fetchCategoryProducts();\n    }\n  }, [categoryName, filters, sortBy, sortOrder, pagination.page]);\n\n  const fetchCategoryProducts = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const params = new URLSearchParams();\n\n      if (filters.brands.length > 0) params.append('brand', filters.brands.join(','));\n      if (filters.priceRange.min > 0) params.append('min_price', filters.priceRange.min);\n      if (filters.priceRange.max < 10000) params.append('max_price', filters.priceRange.max);\n      if (filters.ratingRange.min > 0) params.append('min_rating', filters.ratingRange.min);\n      if (filters.ratingRange.max < 5) params.append('max_rating', filters.ratingRange.max);\n      if (filters.sustainabilityRange.min > 0) params.append('min_sustainability', filters.sustainabilityRange.min);\n      if (filters.sustainabilityRange.max < 100) params.append('max_sustainability', filters.sustainabilityRange.max);\n      if (filters.inStockOnly) params.append('in_stock_only', 'true');\n\n      params.append('sort_by', sortBy);\n      params.append('sort_order', sortOrder);\n      params.append('page', pagination.page);\n      params.append('per_page', pagination.per_page);\n\n      const response = await fetch(`${API_BASE_URL}/categories/${categoryName}/products?${params.toString()}`);\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch category products');\n      }\n\n      const data = await response.json();\n      setProducts(data.products || []);\n      setPagination(data.pagination || {});\n\n    } catch (err) {\n      setError(err.message);\n      setProducts([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFiltersChange = (newFilters) => {\n    setFilters(newFilters);\n    setPagination(prev => ({ ...prev, page: 1 }));\n    \n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', '1');\n    setSearchParams(newParams);\n  };\n\n  const handleClearFilters = () => {\n    // Reset filters to default state\n    const defaultFilters = {\n      categories: [],\n      brands: [],\n      sellers: [],\n      priceRange: { min: 0, max: 10000 },\n      ratingRange: { min: 0, max: 5 },\n      sustainabilityRange: { min: 0, max: 100 },\n      inStockOnly: false\n    };\n\n    setFilters(defaultFilters);\n    setPagination(prev => ({ ...prev, page: 1 }));\n\n    // Clear filter-related URL parameters while keeping sort and category\n    const newParams = new URLSearchParams(searchParams);\n\n    // Remove filter parameters\n    newParams.delete('brand');\n    newParams.delete('min_price');\n    newParams.delete('max_price');\n    newParams.delete('min_rating');\n    newParams.delete('max_rating');\n    newParams.delete('min_sustainability');\n    newParams.delete('max_sustainability');\n    newParams.delete('in_stock_only');\n\n    // Reset to first page\n    newParams.set('page', '1');\n\n    setSearchParams(newParams);\n  };\n\n  const handleSortChange = (newSortBy, newSortOrder) => {\n    setSortBy(newSortBy);\n    setSortOrder(newSortOrder);\n    \n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('sort_by', newSortBy);\n    newParams.set('sort_order', newSortOrder);\n    newParams.set('page', '1');\n    setSearchParams(newParams);\n    setPagination(prev => ({ ...prev, page: 1 }));\n  };\n\n  const handlePageChange = (newPage) => {\n    setPagination(prev => ({ ...prev, page: newPage }));\n    \n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', newPage.toString());\n    setSearchParams(newParams);\n    \n    // Scroll to top\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center\">\n        <div className=\"text-center bg-white p-8 rounded-2xl shadow-xl max-w-md mx-4\">\n          <div className=\"text-6xl mb-4\">😔</div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Oops! Something went wrong</h2>\n          <p className=\"text-red-500 text-lg mb-6\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"bg-gradient-to-r from-red-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 font-semibold\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-gray-50\">\n\n      {/* Hero Section */}\n      <div className=\"relative overflow-hidden\">\n        <div\n          className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n          style={{ backgroundImage: `url(${categoryConfig.heroImage})` }}\n        />\n        <div className={`absolute inset-0 bg-gradient-to-r ${categoryConfig.gradient}`} />\n\n        {/* Animated Background Elements */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse\" />\n          <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000\" />\n        </div>\n\n        <div className=\"relative z-10 container mx-auto px-4 py-20\">\n          {/* Breadcrumb Navigation */}\n          <nav className=\"flex mb-8\" aria-label=\"Breadcrumb\">\n            <ol className=\"inline-flex items-center space-x-2\">\n              <li className=\"inline-flex items-center\">\n                <Link to=\"/\" className=\"inline-flex items-center text-sm font-medium text-white/80 hover:text-white transition-colors\">\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\" />\n                  </svg>\n                  Home\n                </Link>\n              </li>\n              <li>\n                <ChevronRight className=\"w-4 h-4 text-white/60\" />\n              </li>\n              <li>\n                <Link to=\"/search\" className=\"text-sm font-medium text-white/80 hover:text-white transition-colors\">\n                  Categories\n                </Link>\n              </li>\n              <li>\n                <ChevronRight className=\"w-4 h-4 text-white/60\" />\n              </li>\n              <li aria-current=\"page\">\n                <span className=\"text-sm font-medium text-white\">\n                  {categoryConfig.title}\n                </span>\n              </li>\n            </ol>\n          </nav>\n\n          {/* Hero Content */}\n          <div className=\"text-center text-white max-w-4xl mx-auto\">\n            <div className=\"text-6xl mb-6 animate-bounce\">\n              {categoryConfig.icon}\n            </div>\n            <h1 className=\"text-5xl md:text-6xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80\">\n              {categoryConfig.title}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 text-white/90 font-light\">\n              {categoryConfig.subtitle}\n            </p>\n            <p className=\"text-lg mb-8 text-white/80 max-w-2xl mx-auto leading-relaxed\">\n              {categoryConfig.description}\n            </p>\n\n            {/* Feature Tags */}\n            <div className=\"flex flex-wrap justify-center gap-3 mb-8\">\n              {categoryConfig.features.map((feature, index) => (\n                <span\n                  key={index}\n                  className=\"px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium text-white border border-white/30 hover:bg-white/30 transition-all duration-200\"\n                >\n                  {feature}\n                </span>\n              ))}\n            </div>\n\n            {/* Stats */}\n            <div className=\"flex justify-center items-center space-x-8 text-white/90\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold\">{pagination.total || 0}</div>\n                <div className=\"text-sm\">Products</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/30\" />\n              <div className=\"text-center flex items-center\">\n                <Leaf className=\"w-5 h-5 mr-2\" />\n                <span className=\"text-sm\">100% Sustainable</span>\n              </div>\n              <div className=\"w-px h-8 bg-white/30\" />\n              <div className=\"text-center flex items-center\">\n                <Award className=\"w-5 h-5 mr-2\" />\n                <span className=\"text-sm\">Premium Quality</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex flex-col xl:flex-row gap-8\">\n\n          {/* Modern Filters Sidebar */}\n          <div className=\"xl:w-80 flex-shrink-0\">\n            <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden sticky top-4\">\n              <div className=\"bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-100\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n                    <Filter className=\"w-5 h-5 mr-2 text-gray-600\" />\n                    Filters\n                  </h3>\n                  <button\n                    onClick={handleClearFilters}\n                    className=\"text-sm text-gray-500 hover:text-gray-700 transition-colors\"\n                  >\n                    Clear All\n                  </button>\n                </div>\n              </div>\n              <div className=\"p-6\">\n                <SearchFilters\n                  filters={filters}\n                  onFiltersChange={handleFiltersChange}\n                  onClearFilters={handleClearFilters}\n                  isLoading={loading}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Products Section */}\n          <div className=\"flex-1\">\n\n            {/* Modern Sort Controls */}\n            <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n                <div className=\"flex items-center space-x-4\">\n                  <h2 className=\"text-lg font-semibold text-gray-900\">\n                    {pagination.total || 0} Products Found\n                  </h2>\n                  {pagination.total > 0 && (\n                    <div className=\"flex items-center text-sm text-gray-500\">\n                      <TrendingUp className=\"w-4 h-4 mr-1\" />\n                      Sorted by {sortBy === 'price' ? 'Price' : sortBy === 'name' ? 'Name' : 'Relevance'}\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"flex items-center space-x-4\">\n                  <SortControls\n                    sortBy={sortBy}\n                    sortOrder={sortOrder}\n                    onSortChange={handleSortChange}\n                    resultCount={pagination.total}\n                    viewMode={viewMode}\n                    onViewModeChange={setViewMode}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Loading State */}\n            {loading && (\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-12\">\n                <div className=\"text-center\">\n                  <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mb-4\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent\"></div>\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Loading Products</h3>\n                  <p className=\"text-gray-600\">Finding the best {decodedCategoryName.toLowerCase()} for you...</p>\n                </div>\n              </div>\n            )}\n\n            {/* Products Grid */}\n            {!loading && products.length > 0 && (\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-6\">\n                <VirtualizedProductGrid\n                  products={products}\n                  height={600}\n                  itemHeight={viewMode === 'grid' ? 320 : 200}\n                  itemWidth={viewMode === 'grid' ? 300 : 800}\n                  emptyMessage={`No ${decodedCategoryName.toLowerCase()} products found.`}\n                  viewMode={viewMode}\n                />\n              </div>\n            )}\n\n            {/* No Results */}\n            {!loading && products.length === 0 && (\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-12\">\n                <div className=\"text-center\">\n                  <div className=\"text-8xl mb-6 opacity-50\">🔍</div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">\n                    No products found in {categoryConfig.title}\n                  </h3>\n                  <p className=\"text-gray-600 mb-8 max-w-md mx-auto\">\n                    We couldn't find any products matching your criteria. Try adjusting your filters or explore other categories.\n                  </p>\n                  <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                    <button\n                      onClick={handleClearFilters}\n                      className=\"px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200 font-medium\"\n                    >\n                      Clear Filters\n                    </button>\n                    <Link\n                      to=\"/search\"\n                      className=\"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 font-medium\"\n                    >\n                      Browse All Products\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Modern Pagination */}\n            {!loading && products.length > 0 && pagination.pages > 1 && (\n              <div className=\"mt-8 bg-white rounded-2xl shadow-lg border border-gray-100 p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-gray-600\">\n                    Showing page {pagination.page} of {pagination.pages}\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      onClick={() => handlePageChange(pagination.page - 1)}\n                      disabled={!pagination.has_prev}\n                      className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium\"\n                    >\n                      Previous\n                    </button>\n\n                    <div className=\"flex items-center space-x-1\">\n                      {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {\n                        const pageNum = i + 1;\n                        return (\n                          <button\n                            key={pageNum}\n                            onClick={() => handlePageChange(pageNum)}\n                            className={`w-10 h-10 rounded-lg font-medium transition-colors duration-200 ${\n                              pageNum === pagination.page\n                                ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'\n                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                            }`}\n                          >\n                            {pageNum}\n                          </button>\n                        );\n                      })}\n                    </div>\n\n                    <button\n                      onClick={() => handlePageChange(pagination.page + 1)}\n                      disabled={!pagination.has_next}\n                      className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium\"\n                    >\n                      Next\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Category;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,eAAe,EAAEC,IAAI,QAAQ,kBAAkB;AACnE,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAa,CAAC,GAAGpB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC;IAC3C6B,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC;IACrCyC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAM,CAAC;IAClCC,WAAW,EAAE;MAAEF,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IAC/BE,mBAAmB,EAAE;MAAEH,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAI,CAAC;IACzCG,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnD,QAAQ,CAAC,OAAO,CAAC;EAC7C,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,MAAM,CAAC;;EAEhD;EACA,MAAMwD,mBAAmB,GAAGlC,YAAY,GACtCA,YAAY,CAACmC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,EAAE;;EAEzD;EACA,MAAMC,iBAAiB,GAAIpC,YAAY,IAAK;IAC1C,MAAMqC,OAAO,GAAG;MACd,UAAU,EAAE;QACVC,KAAK,EAAE,sBAAsB;QAC7BC,QAAQ,EAAE,6CAA6C;QACvDC,SAAS,EAAE,8GAA8G;QACzHC,QAAQ,EAAE,oDAAoD;QAC9DC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,YAAY,EAAE,gBAAgB,EAAE,YAAY,CAAC;QAC7EC,WAAW,EAAE;MACf,CAAC;MACD,UAAU,EAAE;QACVN,KAAK,EAAE,uBAAuB;QAC9BC,QAAQ,EAAE,qCAAqC;QAC/CC,SAAS,EAAE,2GAA2G;QACtHC,QAAQ,EAAE,mDAAmD;QAC7DC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,CAAC;QACzEC,WAAW,EAAE;MACf,CAAC;MACD,aAAa,EAAE;QACbN,KAAK,EAAE,yBAAyB;QAChCC,QAAQ,EAAE,gCAAgC;QAC1CC,SAAS,EAAE,2GAA2G;QACtHC,QAAQ,EAAE,mDAAmD;QAC7DC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,iBAAiB,EAAE,WAAW,CAAC;QAC1EC,WAAW,EAAE;MACf,CAAC;MACD,gBAAgB,EAAE;QAChBN,KAAK,EAAE,sBAAsB;QAC7BC,QAAQ,EAAE,qCAAqC;QAC/CC,SAAS,EAAE,2GAA2G;QACtHC,QAAQ,EAAE,qDAAqD;QAC/DC,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,cAAc,CAAC;QACrEC,WAAW,EAAE;MACf,CAAC;MACD,aAAa,EAAE;QACbN,KAAK,EAAE,mBAAmB;QAC1BC,QAAQ,EAAE,8BAA8B;QACxCC,SAAS,EAAE,8GAA8G;QACzHC,QAAQ,EAAE,qDAAqD;QAC/DC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC;QAC1EC,WAAW,EAAE;MACf,CAAC;MACD,gBAAgB,EAAE;QAChBN,KAAK,EAAE,sBAAsB;QAC7BC,QAAQ,EAAE,0CAA0C;QACpDC,SAAS,EAAE,8GAA8G;QACzHC,QAAQ,EAAE,mDAAmD;QAC7DC,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,cAAc,EAAE,SAAS,CAAC;QAC/EC,WAAW,EAAE;MACf;IACF,CAAC;IAED,MAAMC,cAAc,GAAG7C,YAAY,CAAC8C,WAAW,CAAC,CAAC,CAACX,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;IAC1F,OAAOE,OAAO,CAACQ,cAAc,CAAC,IAAI;MAChCP,KAAK,EAAEJ,mBAAmB;MAC1BK,QAAQ,EAAE,gBAAgBL,mBAAmB,CAACY,WAAW,CAAC,CAAC,aAAa;MACxEN,SAAS,EAAE,8GAA8G;MACzHC,QAAQ,EAAE,kDAAkD;MAC5DC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,CAAC;MAC9DC,WAAW,EAAE,iCAAiCV,mBAAmB,CAACY,WAAW,CAAC,CAAC;IACjF,CAAC;EACH,CAAC;EAED,MAAMC,cAAc,GAAGX,iBAAiB,CAACF,mBAAmB,CAAC;;EAE7D;EACAvD,SAAS,CAAC,MAAM;IACd,MAAMqE,SAAS,GAAG/C,YAAY,CAACgD,GAAG,CAAC,SAAS,CAAC,IAAI,OAAO;IACxD,MAAMC,YAAY,GAAGjD,YAAY,CAACgD,GAAG,CAAC,YAAY,CAAC,IAAI,KAAK;IAC5D,MAAME,OAAO,GAAGC,QAAQ,CAACnD,YAAY,CAACgD,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;IAEvDpB,SAAS,CAACmB,SAAS,CAAC;IACpBjB,YAAY,CAACmB,YAAY,CAAC;IAC1B5C,aAAa,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,IAAI,EAAE4C;IAAQ,CAAC,CAAC,CAAC;EACrD,CAAC,EAAE,CAAClD,YAAY,CAAC,CAAC;;EAElB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIqB,YAAY,EAAE;MAChBsD,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACtD,YAAY,EAAEiB,OAAO,EAAEW,MAAM,EAAEE,SAAS,EAAEzB,UAAU,CAACE,IAAI,CAAC,CAAC;EAE/D,MAAM+C,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCxC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMuC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEpC,IAAIvC,OAAO,CAACG,MAAM,CAACqC,MAAM,GAAG,CAAC,EAAEF,MAAM,CAACG,MAAM,CAAC,OAAO,EAAEzC,OAAO,CAACG,MAAM,CAACuC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC/E,IAAI1C,OAAO,CAACK,UAAU,CAACC,GAAG,GAAG,CAAC,EAAEgC,MAAM,CAACG,MAAM,CAAC,WAAW,EAAEzC,OAAO,CAACK,UAAU,CAACC,GAAG,CAAC;MAClF,IAAIN,OAAO,CAACK,UAAU,CAACE,GAAG,GAAG,KAAK,EAAE+B,MAAM,CAACG,MAAM,CAAC,WAAW,EAAEzC,OAAO,CAACK,UAAU,CAACE,GAAG,CAAC;MACtF,IAAIP,OAAO,CAACQ,WAAW,CAACF,GAAG,GAAG,CAAC,EAAEgC,MAAM,CAACG,MAAM,CAAC,YAAY,EAAEzC,OAAO,CAACQ,WAAW,CAACF,GAAG,CAAC;MACrF,IAAIN,OAAO,CAACQ,WAAW,CAACD,GAAG,GAAG,CAAC,EAAE+B,MAAM,CAACG,MAAM,CAAC,YAAY,EAAEzC,OAAO,CAACQ,WAAW,CAACD,GAAG,CAAC;MACrF,IAAIP,OAAO,CAACS,mBAAmB,CAACH,GAAG,GAAG,CAAC,EAAEgC,MAAM,CAACG,MAAM,CAAC,oBAAoB,EAAEzC,OAAO,CAACS,mBAAmB,CAACH,GAAG,CAAC;MAC7G,IAAIN,OAAO,CAACS,mBAAmB,CAACF,GAAG,GAAG,GAAG,EAAE+B,MAAM,CAACG,MAAM,CAAC,oBAAoB,EAAEzC,OAAO,CAACS,mBAAmB,CAACF,GAAG,CAAC;MAC/G,IAAIP,OAAO,CAACU,WAAW,EAAE4B,MAAM,CAACG,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC;MAE/DH,MAAM,CAACG,MAAM,CAAC,SAAS,EAAE9B,MAAM,CAAC;MAChC2B,MAAM,CAACG,MAAM,CAAC,YAAY,EAAE5B,SAAS,CAAC;MACtCyB,MAAM,CAACG,MAAM,CAAC,MAAM,EAAErD,UAAU,CAACE,IAAI,CAAC;MACtCgD,MAAM,CAACG,MAAM,CAAC,UAAU,EAAErD,UAAU,CAACG,QAAQ,CAAC;MAE9C,MAAMoD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG3E,YAAY,eAAec,YAAY,aAAauD,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC;MAExG,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC9D,WAAW,CAAC6D,IAAI,CAAC9D,QAAQ,IAAI,EAAE,CAAC;MAChCG,aAAa,CAAC2D,IAAI,CAAC5D,UAAU,IAAI,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAO8D,GAAG,EAAE;MACZnD,QAAQ,CAACmD,GAAG,CAACC,OAAO,CAAC;MACrBhE,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,SAAS;MACRU,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuD,mBAAmB,GAAIC,UAAU,IAAK;IAC1CpD,UAAU,CAACoD,UAAU,CAAC;IACtBhE,aAAa,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;;IAE7C;IACA,MAAMgE,SAAS,GAAG,IAAIf,eAAe,CAACvD,YAAY,CAAC;IACnDsE,SAAS,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;IAC1BtE,eAAe,CAACqE,SAAS,CAAC;EAC5B,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,cAAc,GAAG;MACrBvD,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAM,CAAC;MAClCC,WAAW,EAAE;QAAEF,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MAC/BE,mBAAmB,EAAE;QAAEH,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAI,CAAC;MACzCG,WAAW,EAAE;IACf,CAAC;IAEDT,UAAU,CAACwD,cAAc,CAAC;IAC1BpE,aAAa,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;;IAE7C;IACA,MAAMgE,SAAS,GAAG,IAAIf,eAAe,CAACvD,YAAY,CAAC;;IAEnD;IACAsE,SAAS,CAACI,MAAM,CAAC,OAAO,CAAC;IACzBJ,SAAS,CAACI,MAAM,CAAC,WAAW,CAAC;IAC7BJ,SAAS,CAACI,MAAM,CAAC,WAAW,CAAC;IAC7BJ,SAAS,CAACI,MAAM,CAAC,YAAY,CAAC;IAC9BJ,SAAS,CAACI,MAAM,CAAC,YAAY,CAAC;IAC9BJ,SAAS,CAACI,MAAM,CAAC,oBAAoB,CAAC;IACtCJ,SAAS,CAACI,MAAM,CAAC,oBAAoB,CAAC;IACtCJ,SAAS,CAACI,MAAM,CAAC,eAAe,CAAC;;IAEjC;IACAJ,SAAS,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;IAE1BtE,eAAe,CAACqE,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMK,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,YAAY,KAAK;IACpDjD,SAAS,CAACgD,SAAS,CAAC;IACpB9C,YAAY,CAAC+C,YAAY,CAAC;;IAE1B;IACA,MAAMP,SAAS,GAAG,IAAIf,eAAe,CAACvD,YAAY,CAAC;IACnDsE,SAAS,CAACC,GAAG,CAAC,SAAS,EAAEK,SAAS,CAAC;IACnCN,SAAS,CAACC,GAAG,CAAC,YAAY,EAAEM,YAAY,CAAC;IACzCP,SAAS,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;IAC1BtE,eAAe,CAACqE,SAAS,CAAC;IAC1BjE,aAAa,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMwE,gBAAgB,GAAIC,OAAO,IAAK;IACpC1E,aAAa,CAAC+C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE9C,IAAI,EAAEyE;IAAQ,CAAC,CAAC,CAAC;;IAEnD;IACA,MAAMT,SAAS,GAAG,IAAIf,eAAe,CAACvD,YAAY,CAAC;IACnDsE,SAAS,CAACC,GAAG,CAAC,MAAM,EAAEQ,OAAO,CAAClB,QAAQ,CAAC,CAAC,CAAC;IACzC5D,eAAe,CAACqE,SAAS,CAAC;;IAE1B;IACAU,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,IAAIrE,KAAK,EAAE;IACT,oBACEnB,OAAA;MAAKyF,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrG1F,OAAA;QAAKyF,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3E1F,OAAA;UAAKyF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC9F,OAAA;UAAIyF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrF9F,OAAA;UAAGyF,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAEvE;QAAK;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD9F,OAAA;UACE+F,OAAO,EAAEA,CAAA,KAAMV,MAAM,CAACW,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCR,SAAS,EAAC,0JAA0J;UAAAC,QAAA,EACrK;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9F,OAAA;IAAKyF,SAAS,EAAC,mEAAmE;IAAAC,QAAA,gBAGhF1F,OAAA;MAAKyF,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvC1F,OAAA;QACEyF,SAAS,EAAC,kDAAkD;QAC5DS,KAAK,EAAE;UAAEC,eAAe,EAAE,OAAOhD,cAAc,CAACP,SAAS;QAAI;MAAE;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACF9F,OAAA;QAAKyF,SAAS,EAAE,qCAAqCtC,cAAc,CAACN,QAAQ;MAAG;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGlF9F,OAAA;QAAKyF,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C1F,OAAA;UAAKyF,SAAS,EAAC;QAAsF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxG9F,OAAA;UAAKyF,SAAS,EAAC;QAAmG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClH,CAAC,eAEN9F,OAAA;QAAKyF,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBAEzD1F,OAAA;UAAKyF,SAAS,EAAC,WAAW;UAAC,cAAW,YAAY;UAAAC,QAAA,eAChD1F,OAAA;YAAIyF,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBAChD1F,OAAA;cAAIyF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACtC1F,OAAA,CAACd,IAAI;gBAACkH,EAAE,EAAC,GAAG;gBAACX,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,gBACpH1F,OAAA;kBAAKyF,SAAS,EAAC,cAAc;kBAACY,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAZ,QAAA,eACnE1F,OAAA;oBAAMuG,CAAC,EAAC;kBAAkM;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1M,CAAC,QAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACL9F,OAAA;cAAA0F,QAAA,eACE1F,OAAA,CAACF,YAAY;gBAAC2F,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACL9F,OAAA;cAAA0F,QAAA,eACE1F,OAAA,CAACd,IAAI;gBAACkH,EAAE,EAAC,SAAS;gBAACX,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EAAC;cAEpG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACL9F,OAAA;cAAA0F,QAAA,eACE1F,OAAA,CAACF,YAAY;gBAAC2F,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACL9F,OAAA;cAAI,gBAAa,MAAM;cAAA0F,QAAA,eACrB1F,OAAA;gBAAMyF,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAC7CvC,cAAc,CAACT;cAAK;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGN9F,OAAA;UAAKyF,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBACvD1F,OAAA;YAAKyF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAC1CvC,cAAc,CAACL;UAAI;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACN9F,OAAA;YAAIyF,SAAS,EAAC,2GAA2G;YAAAC,QAAA,EACtHvC,cAAc,CAACT;UAAK;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACL9F,OAAA;YAAGyF,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAC7DvC,cAAc,CAACR;UAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACJ9F,OAAA;YAAGyF,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EACxEvC,cAAc,CAACH;UAAW;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAGJ9F,OAAA;YAAKyF,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EACtDvC,cAAc,CAACJ,QAAQ,CAACyD,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC1C1G,OAAA;cAEEyF,SAAS,EAAC,yJAAyJ;cAAAC,QAAA,EAElKe;YAAO,GAHHC,KAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIN,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN9F,OAAA;YAAKyF,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACvE1F,OAAA;cAAKyF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1F,OAAA;gBAAKyF,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEjF,UAAU,CAACI,KAAK,IAAI;cAAC;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjE9F,OAAA;gBAAKyF,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACN9F,OAAA;cAAKyF,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxC9F,OAAA;cAAKyF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5C1F,OAAA,CAACR,IAAI;gBAACiG,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjC9F,OAAA;gBAAMyF,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN9F,OAAA;cAAKyF,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxC9F,OAAA;cAAKyF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5C1F,OAAA,CAACP,KAAK;gBAACgG,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClC9F,OAAA;gBAAMyF,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9F,OAAA;MAAKyF,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C1F,OAAA;QAAKyF,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAG9C1F,OAAA;UAAKyF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC1F,OAAA;YAAKyF,SAAS,EAAC,oFAAoF;YAAAC,QAAA,gBACjG1F,OAAA;cAAKyF,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClF1F,OAAA;gBAAKyF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD1F,OAAA;kBAAIyF,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,gBACnE1F,OAAA,CAACL,MAAM;oBAAC8F,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEnD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9F,OAAA;kBACE+F,OAAO,EAAElB,kBAAmB;kBAC5BY,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9F,OAAA;cAAKyF,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClB1F,OAAA,CAACZ,aAAa;gBACZiC,OAAO,EAAEA,OAAQ;gBACjBsF,eAAe,EAAElC,mBAAoB;gBACrCmC,cAAc,EAAE/B,kBAAmB;gBACnCgC,SAAS,EAAE5F;cAAQ;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9F,OAAA;UAAKyF,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBAGrB1F,OAAA;YAAKyF,SAAS,EAAC,gEAAgE;YAAAC,QAAA,eAC7E1F,OAAA;cAAKyF,SAAS,EAAC,oEAAoE;cAAAC,QAAA,gBACjF1F,OAAA;gBAAKyF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C1F,OAAA;kBAAIyF,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,GAChDjF,UAAU,CAACI,KAAK,IAAI,CAAC,EAAC,iBACzB;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACJrF,UAAU,CAACI,KAAK,GAAG,CAAC,iBACnBb,OAAA;kBAAKyF,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtD1F,OAAA,CAACN,UAAU;oBAAC+F,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAC7B,EAAC9D,MAAM,KAAK,OAAO,GAAG,OAAO,GAAGA,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG,WAAW;gBAAA;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9F,OAAA;gBAAKyF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eAC1C1F,OAAA,CAACX,YAAY;kBACX2C,MAAM,EAAEA,MAAO;kBACfE,SAAS,EAAEA,SAAU;kBACrB4E,YAAY,EAAE9B,gBAAiB;kBAC/B+B,WAAW,EAAEtG,UAAU,CAACI,KAAM;kBAC9BuB,QAAQ,EAAEA,QAAS;kBACnB4E,gBAAgB,EAAE3E;gBAAY;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL7E,OAAO,iBACNjB,OAAA;YAAKyF,SAAS,EAAC,4DAA4D;YAAAC,QAAA,eACzE1F,OAAA;cAAKyF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1F,OAAA;gBAAKyF,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,eAC/H1F,OAAA;kBAAKyF,SAAS,EAAC;gBAA8E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CAAC,eACN9F,OAAA;gBAAIyF,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9E9F,OAAA;gBAAGyF,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,mBAAiB,EAACpD,mBAAmB,CAACY,WAAW,CAAC,CAAC,EAAC,aAAW;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAC7E,OAAO,IAAIV,QAAQ,CAACsD,MAAM,GAAG,CAAC,iBAC9B7D,OAAA;YAAKyF,SAAS,EAAC,2DAA2D;YAAAC,QAAA,eACxE1F,OAAA,CAACb,sBAAsB;cACrBoB,QAAQ,EAAEA,QAAS;cACnB0G,MAAM,EAAE,GAAI;cACZC,UAAU,EAAE9E,QAAQ,KAAK,MAAM,GAAG,GAAG,GAAG,GAAI;cAC5C+E,SAAS,EAAE/E,QAAQ,KAAK,MAAM,GAAG,GAAG,GAAG,GAAI;cAC3CgF,YAAY,EAAE,MAAM9E,mBAAmB,CAACY,WAAW,CAAC,CAAC,kBAAmB;cACxEd,QAAQ,EAAEA;YAAS;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGA,CAAC7E,OAAO,IAAIV,QAAQ,CAACsD,MAAM,KAAK,CAAC,iBAChC7D,OAAA;YAAKyF,SAAS,EAAC,4DAA4D;YAAAC,QAAA,eACzE1F,OAAA;cAAKyF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1F,OAAA;gBAAKyF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClD9F,OAAA;gBAAIyF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,GAAC,uBAC/B,EAACvC,cAAc,CAACT,KAAK;cAAA;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACL9F,OAAA;gBAAGyF,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ9F,OAAA;gBAAKyF,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,gBAC7D1F,OAAA;kBACE+F,OAAO,EAAElB,kBAAmB;kBAC5BY,SAAS,EAAC,6GAA6G;kBAAAC,QAAA,EACxH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9F,OAAA,CAACd,IAAI;kBACHkH,EAAE,EAAC,SAAS;kBACZX,SAAS,EAAC,8JAA8J;kBAAAC,QAAA,EACzK;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAC7E,OAAO,IAAIV,QAAQ,CAACsD,MAAM,GAAG,CAAC,IAAIpD,UAAU,CAACK,KAAK,GAAG,CAAC,iBACtDd,OAAA;YAAKyF,SAAS,EAAC,gEAAgE;YAAAC,QAAA,eAC7E1F,OAAA;cAAKyF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD1F,OAAA;gBAAKyF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,eACxB,EAACjF,UAAU,CAACE,IAAI,EAAC,MAAI,EAACF,UAAU,CAACK,KAAK;cAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eAEN9F,OAAA;gBAAKyF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C1F,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAMZ,gBAAgB,CAAC1E,UAAU,CAACE,IAAI,GAAG,CAAC,CAAE;kBACrD0G,QAAQ,EAAE,CAAC5G,UAAU,CAACO,QAAS;kBAC/ByE,SAAS,EAAC,6JAA6J;kBAAAC,QAAA,EACxK;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAET9F,OAAA;kBAAKyF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EACzC4B,KAAK,CAACC,IAAI,CAAC;oBAAE1D,MAAM,EAAE2D,IAAI,CAAC7F,GAAG,CAAC,CAAC,EAAElB,UAAU,CAACK,KAAK;kBAAE,CAAC,EAAE,CAAC2G,CAAC,EAAEC,CAAC,KAAK;oBAC/D,MAAMC,OAAO,GAAGD,CAAC,GAAG,CAAC;oBACrB,oBACE1H,OAAA;sBAEE+F,OAAO,EAAEA,CAAA,KAAMZ,gBAAgB,CAACwC,OAAO,CAAE;sBACzClC,SAAS,EAAE,mEACTkC,OAAO,KAAKlH,UAAU,CAACE,IAAI,GACvB,yDAAyD,GACzD,6CAA6C,EAChD;sBAAA+E,QAAA,EAEFiC;oBAAO,GARHA,OAAO;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OASN,CAAC;kBAEb,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN9F,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAMZ,gBAAgB,CAAC1E,UAAU,CAACE,IAAI,GAAG,CAAC,CAAE;kBACrD0G,QAAQ,EAAE,CAAC5G,UAAU,CAACM,QAAS;kBAC/B0E,SAAS,EAAC,6JAA6J;kBAAAC,QAAA,EACxK;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3F,EAAA,CA1gBIF,QAAQ;EAAA,QACajB,SAAS,EACMC,eAAe;AAAA;AAAA2I,EAAA,GAFnD3H,QAAQ;AA4gBd,eAAeA,QAAQ;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}