{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\Search.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport VirtualizedProductGrid from '../components/VirtualizedProductGrid';\nimport SearchFilters from '../components/SearchFilters';\nimport SearchSuggestions from '../components/SearchSuggestions';\nimport SortControls from '../components/SortControls';\nimport Pagination from '../components/Pagination';\nimport VisualSearch from '../components/VisualSearch';\nimport { ErrorDisplay, LoadingSpinner } from '../components/ErrorBoundary';\nimport { SearchSEO } from '../components/SEOHead';\nimport { useAuth } from '../contexts/AuthContext';\nimport { API_BASE_URL } from '../config/api';\nimport { useDebounce } from '../hooks/useDebounce';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Search = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  token\n}) => {\n  _s();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const searchInputRef = useRef(null);\n\n  // Search state\n  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [showVisualSearch, setShowVisualSearch] = useState(false);\n  const debouncedSearchQuery = useDebounce(searchQuery, 300);\n  const {\n    user\n  } = useAuth();\n  const {\n    success,\n    error\n  } = useNotification();\n\n  // Function to log search queries for analytics\n  const logSearchQuery = async (query, resultsCount = 0, filters = {}) => {\n    try {\n      const token = localStorage.getItem('token');\n      const guestSessionId = localStorage.getItem('guest_session_id');\n      const searchData = {\n        query: query.trim(),\n        type: 'text',\n        results_count: resultsCount,\n        filters: filters,\n        guest_session_id: guestSessionId,\n        session_id: sessionStorage.getItem('session_id') || Date.now().toString()\n      };\n      await fetch(`${API_BASE_URL}/analytics/search`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...(token && {\n            'Authorization': `Bearer ${token}`\n          }),\n          ...(guestSessionId && {\n            'X-Guest-Session-ID': guestSessionId\n          })\n        },\n        body: JSON.stringify(searchData)\n      });\n    } catch (error) {\n      console.error('Failed to log search query:', error);\n      // Don't show error to user - analytics logging should be silent\n    }\n  };\n\n  // Products and pagination state\n  const [products, setProducts] = useState([]);\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 20,\n    total: 0,\n    pages: 0,\n    has_next: false,\n    has_prev: false\n  });\n  const [loading, setLoading] = useState(false);\n  const [errorState, setErrorState] = useState(null);\n\n  // Filter state\n  const [filters, setFilters] = useState({\n    categories: [],\n    brands: [],\n    sellers: [],\n    priceRange: {\n      min: 0,\n      max: 10000\n    },\n    ratingRange: {\n      min: 0,\n      max: 5\n    },\n    sustainabilityRange: {\n      min: 0,\n      max: 100\n    },\n    inStockOnly: false\n  });\n\n  // Sort and view state\n  const [sortBy, setSortBy] = useState('price');\n  const [sortOrder, setSortOrder] = useState('asc');\n  const [viewMode, setViewMode] = useState('grid');\n  const [sortLoading, setSortLoading] = useState(false);\n\n  // Initialize from URL parameters\n  useEffect(() => {\n    const urlQuery = searchParams.get('q') || '';\n    const urlCategory = searchParams.get('category');\n    const urlBrand = searchParams.get('brand');\n\n    // Get sort preferences from URL or localStorage, with fallback to default\n    const savedSortBy = localStorage.getItem('allora_preferred_sort_by') || 'price';\n    const savedSortOrder = localStorage.getItem('allora_preferred_sort_order') || 'asc';\n    const urlSortBy = searchParams.get('sort_by') || savedSortBy;\n    const urlSortOrder = searchParams.get('sort_order') || savedSortOrder;\n    const urlPage = parseInt(searchParams.get('page')) || 1;\n    setSearchQuery(urlQuery);\n    setSortBy(urlSortBy);\n    setSortOrder(urlSortOrder);\n    setPagination(prev => ({\n      ...prev,\n      page: urlPage\n    }));\n\n    // Set filters from URL\n    if (urlCategory || urlBrand) {\n      setFilters(prev => ({\n        ...prev,\n        categories: urlCategory ? [urlCategory] : [],\n        brands: urlBrand ? [urlBrand] : []\n      }));\n    }\n  }, [searchParams]);\n\n  // Initial load of products\n  useEffect(() => {\n    fetchProducts();\n  }, []); // Run once on mount\n\n  // Fetch products when search parameters change\n  useEffect(() => {\n    // Always fetch products - show all products by default, filtered by search/filters if provided\n    fetchProducts();\n  }, [debouncedSearchQuery, filters, sortBy, sortOrder, pagination.page]);\n  const fetchProducts = async () => {\n    setLoading(true);\n    setErrorState(null);\n    try {\n      const params = new URLSearchParams();\n      if (debouncedSearchQuery) params.append('search', debouncedSearchQuery);\n      if (filters.categories.length > 0) params.append('category', filters.categories.join(','));\n      if (filters.brands.length > 0) params.append('brand', filters.brands.join(','));\n      if (filters.sellers && filters.sellers.length > 0) params.append('seller_id', filters.sellers.join(','));\n      if (filters.priceRange.min > 0) params.append('min_price', filters.priceRange.min);\n      if (filters.priceRange.max < 10000) params.append('max_price', filters.priceRange.max);\n      if (filters.ratingRange.min > 0) params.append('min_rating', filters.ratingRange.min);\n      if (filters.ratingRange.max < 5) params.append('max_rating', filters.ratingRange.max);\n      if (filters.sustainabilityRange.min > 0) params.append('min_sustainability', filters.sustainabilityRange.min);\n      if (filters.sustainabilityRange.max < 100) params.append('max_sustainability', filters.sustainabilityRange.max);\n      if (filters.inStockOnly) params.append('in_stock_only', 'true');\n      params.append('sort_by', sortBy);\n      params.append('sort_order', sortOrder);\n      params.append('page', pagination.page);\n      params.append('per_page', pagination.per_page);\n      console.log('Fetching products with params:', params.toString());\n      const response = await fetch(`${API_BASE_URL}/products?${params.toString()}`);\n      if (!response.ok) {\n        if (response.status === 429) {\n          const retryAfter = response.headers.get('Retry-After') || '60';\n          throw new Error(`Rate limit exceeded. Please wait ${retryAfter} seconds before trying again.`);\n        }\n        throw new Error(`Failed to fetch products (Status: ${response.status})`);\n      }\n      const data = await response.json();\n      console.log('Products API response:', data);\n\n      // Validate and clean product data\n      const validProducts = (data.products || []).filter(product => {\n        if (!product) {\n          console.warn('Invalid product found:', product);\n          return false;\n        }\n        if (!product.name) {\n          console.warn('Product missing name:', product);\n        }\n        if (product.price === null || product.price === undefined) {\n          console.warn('Product missing price:', product);\n          product.price = 0; // Set default price\n        }\n        if (!product.image) {\n          console.warn('Product missing image:', product);\n        }\n        return true;\n      });\n      console.log(`Processed ${validProducts.length} valid products from ${(data.products || []).length} total`);\n      setProducts(validProducts);\n      setPagination(data.pagination || {\n        page: 1,\n        per_page: 20,\n        total: 0,\n        pages: 0,\n        has_next: false,\n        has_prev: false\n      });\n\n      // Log search query for analytics (only if there's a search query)\n      if (debouncedSearchQuery && debouncedSearchQuery.trim()) {\n        logSearchQuery(debouncedSearchQuery, validProducts.length, {\n          category: filters.category,\n          brand: filters.brand,\n          min_price: filters.minPrice,\n          max_price: filters.maxPrice,\n          sort_by: sortBy,\n          sort_order: sortOrder\n        });\n      }\n    } catch (err) {\n      console.error('Search products error:', err);\n\n      // Handle rate limiting gracefully\n      if (err.message.includes('Rate limit exceeded')) {\n        setErrorState('Too many requests. Please wait a moment and try again.');\n      } else if (err.message.includes('429')) {\n        setErrorState('Server is busy. Please try again in a few moments.');\n      } else {\n        setErrorState(err.message);\n      }\n\n      // Don't clear products on rate limit - keep showing existing results\n      if (!err.message.includes('Rate limit') && !err.message.includes('429')) {\n        setProducts([]);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearchChange = value => {\n    setSearchQuery(value);\n    setShowSuggestions(value.length >= 2);\n\n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    if (value) {\n      newParams.set('q', value);\n    } else {\n      newParams.delete('q');\n    }\n    newParams.set('page', '1'); // Reset to first page\n    setSearchParams(newParams);\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n  };\n  const handleSuggestionSelect = suggestion => {\n    if (suggestion.type === 'product') {\n      setSearchQuery(suggestion.text);\n    } else if (suggestion.type === 'category') {\n      navigate(`/category/${suggestion.text.toLowerCase().replace(' ', '-')}`);\n      return;\n    } else if (suggestion.type === 'brand') {\n      setFilters(prev => ({\n        ...prev,\n        brands: [suggestion.text]\n      }));\n    }\n    setShowSuggestions(false);\n  };\n  const handleFiltersChange = newFilters => {\n    setFilters(newFilters);\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n\n    // Update URL with filter parameters\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', '1');\n    setSearchParams(newParams);\n  };\n  const handleClearFilters = () => {\n    // Reset filters to default state\n    const defaultFilters = {\n      categories: [],\n      brands: [],\n      sellers: [],\n      priceRange: {\n        min: 0,\n        max: 10000\n      },\n      ratingRange: {\n        min: 0,\n        max: 5\n      },\n      sustainabilityRange: {\n        min: 0,\n        max: 100\n      },\n      inStockOnly: false\n    };\n    setFilters(defaultFilters);\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n\n    // Clear filter-related URL parameters while keeping search query and sort\n    const newParams = new URLSearchParams(searchParams);\n\n    // Remove filter parameters\n    newParams.delete('category');\n    newParams.delete('brand');\n    newParams.delete('min_price');\n    newParams.delete('max_price');\n    newParams.delete('min_rating');\n    newParams.delete('max_rating');\n    newParams.delete('min_sustainability');\n    newParams.delete('max_sustainability');\n    newParams.delete('in_stock_only');\n\n    // Reset to first page\n    newParams.set('page', '1');\n    setSearchParams(newParams);\n  };\n  const handleSortChange = (newSortBy, newSortOrder) => {\n    setSortLoading(true);\n    setSortBy(newSortBy);\n    setSortOrder(newSortOrder);\n\n    // Save user's sort preference to localStorage\n    localStorage.setItem('allora_preferred_sort_by', newSortBy);\n    localStorage.setItem('allora_preferred_sort_order', newSortOrder);\n\n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('sort_by', newSortBy);\n    newParams.set('sort_order', newSortOrder);\n    newParams.set('page', '1');\n    setSearchParams(newParams);\n    setPagination(prev => ({\n      ...prev,\n      page: 1\n    }));\n\n    // Clear sort loading after a short delay\n    setTimeout(() => setSortLoading(false), 500);\n  };\n  const handlePageChange = newPage => {\n    setPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n\n    // Update URL\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', newPage.toString());\n    setSearchParams(newParams);\n\n    // Scroll to top\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  if (errorState) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-safe py-6\",\n      children: /*#__PURE__*/_jsxDEV(ErrorDisplay, {\n        error: errorState,\n        onRetry: () => {\n          setErrorState(null);\n          fetchProducts();\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SearchSEO, {\n      query: searchQuery,\n      results: products\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative mb-8 p-8 bg-gradient-to-r from-green-600 via-green-500 to-emerald-500 rounded-2xl shadow-xl overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-black/10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-4xl font-bold text-white mb-3 tracking-tight\",\n                  children: searchQuery ? '🔍 Search Results' : '🛍️ Discover Products'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-green-100 text-lg\",\n                  children: searchQuery ? `Found ${pagination.total} results for \"${searchQuery}\"` : 'Explore our sustainable collection - use search and filters to find exactly what you need'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-32 h-32 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-6xl\",\n                    children: \"\\uD83C\\uDF31\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -top-4 -right-4 w-24 h-24 bg-white/5 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -bottom-6 -left-6 w-32 h-32 bg-white/5 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col lg:flex-row gap-4 items-stretch lg:items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  ref: searchInputRef,\n                  type: \"text\",\n                  placeholder: \"Search for sustainable products, eco-friendly brands, categories...\",\n                  value: searchQuery,\n                  onChange: e => handleSearchChange(e.target.value),\n                  onFocus: () => setShowSuggestions(searchQuery.length >= 2),\n                  onBlur: () => setTimeout(() => setShowSuggestions(false), 200),\n                  className: \"w-full px-6 py-4 pl-14 pr-6 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-green-500/20 focus:border-green-500 text-lg placeholder-gray-400 transition-all duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-6 w-6 text-gray-400\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 17\n                }, this), searchQuery && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleSearchChange(''),\n                  className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-5 w-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowVisualSearch(!showVisualSearch),\n                className: `px-6 py-4 rounded-xl border-2 transition-all duration-300 flex items-center gap-3 font-medium ${showVisualSearch ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-500 shadow-lg transform scale-105' : 'bg-white text-gray-700 border-gray-300 hover:border-green-500 hover:text-green-600 hover:shadow-md'}`,\n                title: \"AI Visual Search - Upload an image to find similar products\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-6 w-6\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden sm:inline\",\n                  children: \"AI Visual Search\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sm:hidden\",\n                  children: \"\\uD83D\\uDCF7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(SearchSuggestions, {\n            query: searchQuery,\n            onSuggestionSelect: handleSuggestionSelect,\n            onClose: () => setShowSuggestions(false),\n            isVisible: showSuggestions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 9\n        }, this), showVisualSearch && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-200 shadow-lg overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-6 w-6 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-xl font-bold text-gray-900\",\n                    children: \"\\uD83E\\uDD16 AI Visual Search\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Upload an image to find similar sustainable products\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowVisualSearch(false),\n                className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-lg transition-all duration-200\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-6 w-6\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(VisualSearch, {\n              token: token\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col xl:flex-row gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"xl:w-80 flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sticky top-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-gray-900 flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\uD83C\\uDFAF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 21\n                    }, this), \"Refine Your Search\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 mt-1\",\n                    children: \"Find exactly what you're looking for\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: /*#__PURE__*/_jsxDEV(SearchFilters, {\n                    filters: filters,\n                    onFiltersChange: handleFiltersChange,\n                    onClearFilters: handleClearFilters,\n                    isLoading: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\",\n                children: /*#__PURE__*/_jsxDEV(SortControls, {\n                  sortBy: sortBy,\n                  sortOrder: sortOrder,\n                  onSortChange: handleSortChange,\n                  resultCount: pagination.total,\n                  viewMode: viewMode,\n                  onViewModeChange: setViewMode,\n                  isLoading: sortLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 13\n            }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-gray-900 mb-2\",\n                  children: \"Searching Products...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Finding the best sustainable options for you\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this), !loading && products.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-white text-sm font-bold\",\n                        children: \"\\u2713\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 545,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-green-800\",\n                        children: [\"Showing \", products.length, \" products sorted by\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold\",\n                          children: sortBy === 'price' ? 'Price' : sortBy === 'rating' ? 'Rating' : sortBy === 'sustainability' ? 'Sustainability' : sortBy === 'popularity' ? 'Popularity' : sortBy === 'newest' ? 'Newest' : 'Name'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 550,\n                          columnNumber: 27\n                        }, this), ' ', \"(\", sortOrder === 'asc' ? 'Low to High' : 'High to Low', \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 548,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-green-600\",\n                        children: [sortBy === 'price' && sortOrder === 'asc' && '💰 Best deals first', sortBy === 'price' && sortOrder === 'desc' && '💎 Premium products first', sortBy === 'rating' && sortOrder === 'desc' && '⭐ Highest rated first', sortBy === 'sustainability' && sortOrder === 'desc' && '🌱 Most eco-friendly first', sortBy === 'popularity' && sortOrder === 'desc' && '🔥 Most popular first', sortBy === 'newest' && sortOrder === 'desc' && '🆕 Latest arrivals first']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 559,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-6\",\n                  children: /*#__PURE__*/_jsxDEV(VirtualizedProductGrid, {\n                    products: products,\n                    emptyMessage: \"No products found matching your search criteria.\",\n                    className: \"min-h-[400px]\",\n                    viewMode: viewMode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\",\n                children: /*#__PURE__*/_jsxDEV(Pagination, {\n                  currentPage: pagination.page,\n                  totalPages: pagination.pages,\n                  hasNext: pagination.has_next,\n                  hasPrev: pagination.has_prev,\n                  onPageChange: handlePageChange,\n                  loading: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 15\n            }, this), !loading && products.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg border border-gray-200 p-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center max-w-md mx-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-24 h-24 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-4xl\",\n                    children: \"\\uD83D\\uDD0D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-gray-900 mb-3\",\n                  children: searchQuery || filters.categories.length > 0 || filters.brands.length > 0 ? 'No Products Found' : 'No Products Available'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-8 leading-relaxed\",\n                  children: searchQuery || filters.categories.length > 0 || filters.brands.length > 0 ? \"We couldn't find any sustainable products matching your search criteria. Try adjusting your search or filters.\" : \"There are currently no products available in our sustainable catalog. Check back soon!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 19\n                }, this), (searchQuery || filters.categories.length > 0 || filters.brands.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-xl p-6 mb-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900 mb-3\",\n                    children: \"\\uD83D\\uDCA1 Search Tips:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"text-sm text-gray-600 space-y-2 text-left\",\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-center gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"w-1.5 h-1.5 bg-green-500 rounded-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 622,\n                        columnNumber: 27\n                      }, this), \"Try different or more general keywords\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-center gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"w-1.5 h-1.5 bg-green-500 rounded-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 626,\n                        columnNumber: 27\n                      }, this), \"Check your spelling and try synonyms\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-center gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"w-1.5 h-1.5 bg-green-500 rounded-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 27\n                      }, this), \"Remove some filters to broaden results\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: \"flex items-center gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"w-1.5 h-1.5 bg-green-500 rounded-full\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 27\n                      }, this), \"Try our AI Visual Search feature\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleClearFilters,\n                    className: \"px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                    children: \"\\uD83D\\uDD04 Clear All Filters\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowVisualSearch(true),\n                    className: \"px-6 py-3 bg-white text-gray-700 border-2 border-gray-300 rounded-xl hover:border-green-500 hover:text-green-600 transition-all duration-200 font-medium\",\n                    children: \"\\uD83D\\uDCF7 Try Visual Search\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}, \"B/ocVkBIaehHAB+NnXLejD58CPY=\", false, function () {\n  return [useSearchParams, useNavigate, useDebounce, useAuth, useNotification];\n})), \"B/ocVkBIaehHAB+NnXLejD58CPY=\", false, function () {\n  return [useSearchParams, useNavigate, useDebounce, useAuth, useNotification];\n});\n_c2 = Search;\nexport default Search;\nvar _c, _c2;\n$RefreshReg$(_c, \"Search$React.memo\");\n$RefreshReg$(_c2, \"Search\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useSearchParams", "useNavigate", "VirtualizedProductGrid", "SearchFilters", "SearchSuggestions", "SortControls", "Pagination", "VisualSearch", "ErrorDisplay", "LoadingSpinner", "SearchSEO", "useAuth", "API_BASE_URL", "useDebounce", "useNotification", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Search", "_s", "memo", "_c", "token", "searchParams", "setSearchParams", "navigate", "searchInputRef", "searchQuery", "setSearch<PERSON>uery", "get", "showSuggestions", "setShowSuggestions", "showVisualSearch", "setShowVisualSearch", "debounced<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "user", "success", "error", "logSearchQuery", "query", "resultsCount", "filters", "localStorage", "getItem", "guestSessionId", "searchData", "trim", "type", "results_count", "guest_session_id", "session_id", "sessionStorage", "Date", "now", "toString", "fetch", "method", "headers", "body", "JSON", "stringify", "console", "products", "setProducts", "pagination", "setPagination", "page", "per_page", "total", "pages", "has_next", "has_prev", "loading", "setLoading", "errorState", "setErrorState", "setFilters", "categories", "brands", "sellers", "priceRange", "min", "max", "ratingRange", "sustainabilityRange", "inStockOnly", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "viewMode", "setViewMode", "sortLoading", "setSortLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "urlCategory", "<PERSON><PERSON><PERSON><PERSON>", "savedSortBy", "savedSortOrder", "urlSortBy", "urlSortOrder", "urlPage", "parseInt", "prev", "fetchProducts", "params", "URLSearchParams", "append", "length", "join", "log", "response", "ok", "status", "retryAfter", "Error", "data", "json", "validProducts", "filter", "product", "warn", "name", "price", "undefined", "image", "category", "brand", "min_price", "minPrice", "max_price", "maxPrice", "sort_by", "sort_order", "err", "message", "includes", "handleSearchChange", "value", "newParams", "set", "delete", "handleSuggestionSelect", "suggestion", "text", "toLowerCase", "replace", "handleFiltersChange", "newFilters", "handleClearFilters", "defaultFilters", "handleSortChange", "newSortBy", "newSortOrder", "setItem", "setTimeout", "handlePageChange", "newPage", "window", "scrollTo", "top", "behavior", "className", "children", "onRetry", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "results", "ref", "placeholder", "onChange", "e", "target", "onFocus", "onBlur", "fill", "viewBox", "fillRule", "d", "clipRule", "onClick", "title", "onSuggestionSelect", "onClose", "isVisible", "onFiltersChange", "onClearFilters", "isLoading", "onSortChange", "resultCount", "onViewModeChange", "emptyMessage", "currentPage", "totalPages", "hasNext", "has<PERSON>rev", "onPageChange", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/Search.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { useSearchParams, useNavigate } from 'react-router-dom';\r\nimport VirtualizedProductGrid from '../components/VirtualizedProductGrid';\r\nimport SearchFilters from '../components/SearchFilters';\r\nimport SearchSuggestions from '../components/SearchSuggestions';\r\nimport SortControls from '../components/SortControls';\r\nimport Pagination from '../components/Pagination';\r\nimport VisualSearch from '../components/VisualSearch';\r\nimport { ErrorDisplay, LoadingSpinner } from '../components/ErrorBoundary';\r\nimport { SearchSEO } from '../components/SEOHead';\r\nimport { useAuth } from '../contexts/AuthContext';\r\nimport { API_BASE_URL } from '../config/api';\r\nimport { useDebounce } from '../hooks/useDebounce';\r\nimport { useNotification } from '../contexts/NotificationContext';\r\n\r\nconst Search = React.memo(({ token }) => {\r\n  const [searchParams, setSearchParams] = useSearchParams();\r\n  const navigate = useNavigate();\r\n  const searchInputRef = useRef(null);\r\n\r\n  // Search state\r\n  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');\r\n  const [showSuggestions, setShowSuggestions] = useState(false);\r\n  const [showVisualSearch, setShowVisualSearch] = useState(false);\r\n  const debouncedSearchQuery = useDebounce(searchQuery, 300);\r\n  const { user } = useAuth();\r\n  const { success, error } = useNotification();\r\n\r\n  // Function to log search queries for analytics\r\n  const logSearchQuery = async (query, resultsCount = 0, filters = {}) => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      const guestSessionId = localStorage.getItem('guest_session_id');\r\n\r\n      const searchData = {\r\n        query: query.trim(),\r\n        type: 'text',\r\n        results_count: resultsCount,\r\n        filters: filters,\r\n        guest_session_id: guestSessionId,\r\n        session_id: sessionStorage.getItem('session_id') || Date.now().toString()\r\n      };\r\n\r\n      await fetch(`${API_BASE_URL}/analytics/search`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          ...(token && { 'Authorization': `Bearer ${token}` }),\r\n          ...(guestSessionId && { 'X-Guest-Session-ID': guestSessionId })\r\n        },\r\n        body: JSON.stringify(searchData)\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to log search query:', error);\r\n      // Don't show error to user - analytics logging should be silent\r\n    }\r\n  };\r\n\r\n  // Products and pagination state\r\n  const [products, setProducts] = useState([]);\r\n  const [pagination, setPagination] = useState({\r\n    page: 1,\r\n    per_page: 20,\r\n    total: 0,\r\n    pages: 0,\r\n    has_next: false,\r\n    has_prev: false\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [errorState, setErrorState] = useState(null);\r\n\r\n  // Filter state\r\n  const [filters, setFilters] = useState({\r\n    categories: [],\r\n    brands: [],\r\n    sellers: [],\r\n    priceRange: { min: 0, max: 10000 },\r\n    ratingRange: { min: 0, max: 5 },\r\n    sustainabilityRange: { min: 0, max: 100 },\r\n    inStockOnly: false\r\n  });\r\n\r\n  // Sort and view state\r\n  const [sortBy, setSortBy] = useState('price');\r\n  const [sortOrder, setSortOrder] = useState('asc');\r\n  const [viewMode, setViewMode] = useState('grid');\r\n  const [sortLoading, setSortLoading] = useState(false);\r\n\r\n  // Initialize from URL parameters\r\n  useEffect(() => {\r\n    const urlQuery = searchParams.get('q') || '';\r\n    const urlCategory = searchParams.get('category');\r\n    const urlBrand = searchParams.get('brand');\r\n\r\n    // Get sort preferences from URL or localStorage, with fallback to default\r\n    const savedSortBy = localStorage.getItem('allora_preferred_sort_by') || 'price';\r\n    const savedSortOrder = localStorage.getItem('allora_preferred_sort_order') || 'asc';\r\n    const urlSortBy = searchParams.get('sort_by') || savedSortBy;\r\n    const urlSortOrder = searchParams.get('sort_order') || savedSortOrder;\r\n    const urlPage = parseInt(searchParams.get('page')) || 1;\r\n\r\n    setSearchQuery(urlQuery);\r\n    setSortBy(urlSortBy);\r\n    setSortOrder(urlSortOrder);\r\n    setPagination(prev => ({ ...prev, page: urlPage }));\r\n\r\n    // Set filters from URL\r\n    if (urlCategory || urlBrand) {\r\n      setFilters(prev => ({\r\n        ...prev,\r\n        categories: urlCategory ? [urlCategory] : [],\r\n        brands: urlBrand ? [urlBrand] : []\r\n      }));\r\n    }\r\n  }, [searchParams]);\r\n\r\n  // Initial load of products\r\n  useEffect(() => {\r\n    fetchProducts();\r\n  }, []); // Run once on mount\r\n\r\n  // Fetch products when search parameters change\r\n  useEffect(() => {\r\n    // Always fetch products - show all products by default, filtered by search/filters if provided\r\n    fetchProducts();\r\n  }, [debouncedSearchQuery, filters, sortBy, sortOrder, pagination.page]);\r\n\r\n  const fetchProducts = async () => {\r\n    setLoading(true);\r\n    setErrorState(null);\r\n\r\n    try {\r\n      const params = new URLSearchParams();\r\n\r\n      if (debouncedSearchQuery) params.append('search', debouncedSearchQuery);\r\n      if (filters.categories.length > 0) params.append('category', filters.categories.join(','));\r\n      if (filters.brands.length > 0) params.append('brand', filters.brands.join(','));\r\n      if (filters.sellers && filters.sellers.length > 0) params.append('seller_id', filters.sellers.join(','));\r\n      if (filters.priceRange.min > 0) params.append('min_price', filters.priceRange.min);\r\n      if (filters.priceRange.max < 10000) params.append('max_price', filters.priceRange.max);\r\n      if (filters.ratingRange.min > 0) params.append('min_rating', filters.ratingRange.min);\r\n      if (filters.ratingRange.max < 5) params.append('max_rating', filters.ratingRange.max);\r\n      if (filters.sustainabilityRange.min > 0) params.append('min_sustainability', filters.sustainabilityRange.min);\r\n      if (filters.sustainabilityRange.max < 100) params.append('max_sustainability', filters.sustainabilityRange.max);\r\n      if (filters.inStockOnly) params.append('in_stock_only', 'true');\r\n\r\n      params.append('sort_by', sortBy);\r\n      params.append('sort_order', sortOrder);\r\n      params.append('page', pagination.page);\r\n      params.append('per_page', pagination.per_page);\r\n\r\n      console.log('Fetching products with params:', params.toString());\r\n      const response = await fetch(`${API_BASE_URL}/products?${params.toString()}`);\r\n\r\n      if (!response.ok) {\r\n        if (response.status === 429) {\r\n          const retryAfter = response.headers.get('Retry-After') || '60';\r\n          throw new Error(`Rate limit exceeded. Please wait ${retryAfter} seconds before trying again.`);\r\n        }\r\n        throw new Error(`Failed to fetch products (Status: ${response.status})`);\r\n      }\r\n\r\n      const data = await response.json();\r\n      console.log('Products API response:', data);\r\n\r\n      // Validate and clean product data\r\n      const validProducts = (data.products || []).filter(product => {\r\n        if (!product) {\r\n          console.warn('Invalid product found:', product);\r\n          return false;\r\n        }\r\n        if (!product.name) {\r\n          console.warn('Product missing name:', product);\r\n        }\r\n        if (product.price === null || product.price === undefined) {\r\n          console.warn('Product missing price:', product);\r\n          product.price = 0; // Set default price\r\n        }\r\n        if (!product.image) {\r\n          console.warn('Product missing image:', product);\r\n        }\r\n        return true;\r\n      });\r\n\r\n      console.log(`Processed ${validProducts.length} valid products from ${(data.products || []).length} total`);\r\n\r\n      setProducts(validProducts);\r\n      setPagination(data.pagination || {\r\n        page: 1,\r\n        per_page: 20,\r\n        total: 0,\r\n        pages: 0,\r\n        has_next: false,\r\n        has_prev: false\r\n      });\r\n\r\n      // Log search query for analytics (only if there's a search query)\r\n      if (debouncedSearchQuery && debouncedSearchQuery.trim()) {\r\n        logSearchQuery(\r\n          debouncedSearchQuery,\r\n          validProducts.length,\r\n          {\r\n            category: filters.category,\r\n            brand: filters.brand,\r\n            min_price: filters.minPrice,\r\n            max_price: filters.maxPrice,\r\n            sort_by: sortBy,\r\n            sort_order: sortOrder\r\n          }\r\n        );\r\n      }\r\n\r\n    } catch (err) {\r\n      console.error('Search products error:', err);\r\n\r\n      // Handle rate limiting gracefully\r\n      if (err.message.includes('Rate limit exceeded')) {\r\n        setErrorState('Too many requests. Please wait a moment and try again.');\r\n      } else if (err.message.includes('429')) {\r\n        setErrorState('Server is busy. Please try again in a few moments.');\r\n      } else {\r\n        setErrorState(err.message);\r\n      }\r\n\r\n      // Don't clear products on rate limit - keep showing existing results\r\n      if (!err.message.includes('Rate limit') && !err.message.includes('429')) {\r\n        setProducts([]);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSearchChange = (value) => {\r\n    setSearchQuery(value);\r\n    setShowSuggestions(value.length >= 2);\r\n\r\n    // Update URL\r\n    const newParams = new URLSearchParams(searchParams);\r\n    if (value) {\r\n      newParams.set('q', value);\r\n    } else {\r\n      newParams.delete('q');\r\n    }\r\n    newParams.set('page', '1'); // Reset to first page\r\n    setSearchParams(newParams);\r\n    setPagination(prev => ({ ...prev, page: 1 }));\r\n  };\r\n\r\n  const handleSuggestionSelect = (suggestion) => {\r\n    if (suggestion.type === 'product') {\r\n      setSearchQuery(suggestion.text);\r\n    } else if (suggestion.type === 'category') {\r\n      navigate(`/category/${suggestion.text.toLowerCase().replace(' ', '-')}`);\r\n      return;\r\n    } else if (suggestion.type === 'brand') {\r\n      setFilters(prev => ({\r\n        ...prev,\r\n        brands: [suggestion.text]\r\n      }));\r\n    }\r\n    setShowSuggestions(false);\r\n  };\r\n\r\n  const handleFiltersChange = (newFilters) => {\r\n    setFilters(newFilters);\r\n    setPagination(prev => ({ ...prev, page: 1 }));\r\n\r\n    // Update URL with filter parameters\r\n    const newParams = new URLSearchParams(searchParams);\r\n    newParams.set('page', '1');\r\n    setSearchParams(newParams);\r\n  };\r\n\r\n  const handleClearFilters = () => {\r\n    // Reset filters to default state\r\n    const defaultFilters = {\r\n      categories: [],\r\n      brands: [],\r\n      sellers: [],\r\n      priceRange: { min: 0, max: 10000 },\r\n      ratingRange: { min: 0, max: 5 },\r\n      sustainabilityRange: { min: 0, max: 100 },\r\n      inStockOnly: false\r\n    };\r\n\r\n    setFilters(defaultFilters);\r\n    setPagination(prev => ({ ...prev, page: 1 }));\r\n\r\n    // Clear filter-related URL parameters while keeping search query and sort\r\n    const newParams = new URLSearchParams(searchParams);\r\n\r\n    // Remove filter parameters\r\n    newParams.delete('category');\r\n    newParams.delete('brand');\r\n    newParams.delete('min_price');\r\n    newParams.delete('max_price');\r\n    newParams.delete('min_rating');\r\n    newParams.delete('max_rating');\r\n    newParams.delete('min_sustainability');\r\n    newParams.delete('max_sustainability');\r\n    newParams.delete('in_stock_only');\r\n\r\n    // Reset to first page\r\n    newParams.set('page', '1');\r\n\r\n    setSearchParams(newParams);\r\n  };\r\n\r\n  const handleSortChange = (newSortBy, newSortOrder) => {\r\n    setSortLoading(true);\r\n    setSortBy(newSortBy);\r\n    setSortOrder(newSortOrder);\r\n\r\n    // Save user's sort preference to localStorage\r\n    localStorage.setItem('allora_preferred_sort_by', newSortBy);\r\n    localStorage.setItem('allora_preferred_sort_order', newSortOrder);\r\n\r\n    // Update URL\r\n    const newParams = new URLSearchParams(searchParams);\r\n    newParams.set('sort_by', newSortBy);\r\n    newParams.set('sort_order', newSortOrder);\r\n    newParams.set('page', '1');\r\n    setSearchParams(newParams);\r\n    setPagination(prev => ({ ...prev, page: 1 }));\r\n\r\n    // Clear sort loading after a short delay\r\n    setTimeout(() => setSortLoading(false), 500);\r\n  };\r\n\r\n  const handlePageChange = (newPage) => {\r\n    setPagination(prev => ({ ...prev, page: newPage }));\r\n\r\n    // Update URL\r\n    const newParams = new URLSearchParams(searchParams);\r\n    newParams.set('page', newPage.toString());\r\n    setSearchParams(newParams);\r\n\r\n    // Scroll to top\r\n    window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  };\r\n\r\n  if (errorState) {\r\n    return (\r\n      <div className=\"container-safe py-6\">\r\n        <ErrorDisplay\r\n          error={errorState}\r\n          onRetry={() => {\r\n            setErrorState(null);\r\n            fetchProducts();\r\n          }}\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <SearchSEO query={searchQuery} results={products} />\r\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n\r\n        {/* Modern Header with Gradient Background */}\r\n        <div className=\"relative mb-8 p-8 bg-gradient-to-r from-green-600 via-green-500 to-emerald-500 rounded-2xl shadow-xl overflow-hidden\">\r\n          <div className=\"absolute inset-0 bg-black/10\"></div>\r\n          <div className=\"relative z-10\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <h1 className=\"text-4xl font-bold text-white mb-3 tracking-tight\">\r\n                  {searchQuery ? '🔍 Search Results' : '🛍️ Discover Products'}\r\n                </h1>\r\n                <p className=\"text-green-100 text-lg\">\r\n                  {searchQuery\r\n                    ? `Found ${pagination.total} results for \"${searchQuery}\"`\r\n                    : 'Explore our sustainable collection - use search and filters to find exactly what you need'\r\n                  }\r\n                </p>\r\n              </div>\r\n              <div className=\"hidden lg:block\">\r\n                <div className=\"w-32 h-32 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\">\r\n                  <span className=\"text-6xl\">🌱</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          {/* Decorative Elements */}\r\n          <div className=\"absolute -top-4 -right-4 w-24 h-24 bg-white/5 rounded-full\"></div>\r\n          <div className=\"absolute -bottom-6 -left-6 w-32 h-32 bg-white/5 rounded-full\"></div>\r\n        </div>\r\n\r\n        {/* Enhanced Search Bar */}\r\n        <div className=\"relative mb-8\">\r\n          <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\">\r\n            <div className=\"flex flex-col lg:flex-row gap-4 items-stretch lg:items-center\">\r\n              <div className=\"relative flex-1\">\r\n                <input\r\n                  ref={searchInputRef}\r\n                  type=\"text\"\r\n                  placeholder=\"Search for sustainable products, eco-friendly brands, categories...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => handleSearchChange(e.target.value)}\r\n                  onFocus={() => setShowSuggestions(searchQuery.length >= 2)}\r\n                  onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}\r\n                  className=\"w-full px-6 py-4 pl-14 pr-6 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-green-500/20 focus:border-green-500 text-lg placeholder-gray-400 transition-all duration-200\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none\">\r\n                  <svg className=\"h-6 w-6 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n                {searchQuery && (\r\n                  <button\r\n                    onClick={() => handleSearchChange('')}\r\n                    className=\"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors\"\r\n                  >\r\n                    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                      <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </button>\r\n                )}\r\n              </div>\r\n\r\n              {/* Enhanced Visual Search Button */}\r\n              <button\r\n                onClick={() => setShowVisualSearch(!showVisualSearch)}\r\n                className={`px-6 py-4 rounded-xl border-2 transition-all duration-300 flex items-center gap-3 font-medium ${\r\n                  showVisualSearch\r\n                    ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-500 shadow-lg transform scale-105'\r\n                    : 'bg-white text-gray-700 border-gray-300 hover:border-green-500 hover:text-green-600 hover:shadow-md'\r\n                }`}\r\n                title=\"AI Visual Search - Upload an image to find similar products\"\r\n              >\r\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n                <span className=\"hidden sm:inline\">AI Visual Search</span>\r\n                <span className=\"sm:hidden\">📷</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <SearchSuggestions\r\n            query={searchQuery}\r\n            onSuggestionSelect={handleSuggestionSelect}\r\n            onClose={() => setShowSuggestions(false)}\r\n            isVisible={showSuggestions}\r\n          />\r\n        </div>\r\n\r\n        {/* Enhanced Visual Search Component */}\r\n        {showVisualSearch && (\r\n          <div className=\"mb-8 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-200 shadow-lg overflow-hidden\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center\">\r\n                    <svg className=\"h-6 w-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                      <path fillRule=\"evenodd\" d=\"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-xl font-bold text-gray-900\">🤖 AI Visual Search</h3>\r\n                    <p className=\"text-sm text-gray-600\">Upload an image to find similar sustainable products</p>\r\n                  </div>\r\n                </div>\r\n                <button\r\n                  onClick={() => setShowVisualSearch(false)}\r\n                  className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-lg transition-all duration-200\"\r\n                >\r\n                  <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n              <VisualSearch token={token} />\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Main Content with Modern Layout */}\r\n        <div className=\"flex flex-col xl:flex-row gap-8\">\r\n\r\n          {/* Enhanced Filters Sidebar */}\r\n          <div className=\"xl:w-80 flex-shrink-0\">\r\n            <div className=\"sticky top-8\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden\">\r\n                <div className=\"p-6 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200\">\r\n                  <h2 className=\"text-xl font-bold text-gray-900 flex items-center gap-2\">\r\n                    <span>🎯</span>\r\n                    Refine Your Search\r\n                  </h2>\r\n                  <p className=\"text-sm text-gray-600 mt-1\">Find exactly what you're looking for</p>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <SearchFilters\r\n                    filters={filters}\r\n                    onFiltersChange={handleFiltersChange}\r\n                    onClearFilters={handleClearFilters}\r\n                    isLoading={loading}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Products Section */}\r\n          <div className=\"flex-1 min-w-0\">\r\n\r\n            {/* Enhanced Sort Controls */}\r\n            <div className=\"mb-8\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\">\r\n                <SortControls\r\n                  sortBy={sortBy}\r\n                  sortOrder={sortOrder}\r\n                  onSortChange={handleSortChange}\r\n                  resultCount={pagination.total}\r\n                  viewMode={viewMode}\r\n                  onViewModeChange={setViewMode}\r\n                  isLoading={sortLoading}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Enhanced Loading State */}\r\n            {loading && (\r\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-12\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4\">\r\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"></div>\r\n                  </div>\r\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Searching Products...</h3>\r\n                  <p className=\"text-gray-600\">Finding the best sustainable options for you</p>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Enhanced Products Grid */}\r\n            {!loading && products.length > 0 && (\r\n              <div className=\"space-y-8\">\r\n                {/* Sort Status Indicator */}\r\n                <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\r\n                        <span className=\"text-white text-sm font-bold\">✓</span>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-green-800\">\r\n                          Showing {products.length} products sorted by{' '}\r\n                          <span className=\"font-semibold\">\r\n                            {sortBy === 'price' ? 'Price' :\r\n                             sortBy === 'rating' ? 'Rating' :\r\n                             sortBy === 'sustainability' ? 'Sustainability' :\r\n                             sortBy === 'popularity' ? 'Popularity' :\r\n                             sortBy === 'newest' ? 'Newest' : 'Name'}\r\n                          </span>\r\n                          {' '}({sortOrder === 'asc' ? 'Low to High' : 'High to Low'})\r\n                        </p>\r\n                        <p className=\"text-xs text-green-600\">\r\n                          {sortBy === 'price' && sortOrder === 'asc' && '💰 Best deals first'}\r\n                          {sortBy === 'price' && sortOrder === 'desc' && '💎 Premium products first'}\r\n                          {sortBy === 'rating' && sortOrder === 'desc' && '⭐ Highest rated first'}\r\n                          {sortBy === 'sustainability' && sortOrder === 'desc' && '🌱 Most eco-friendly first'}\r\n                          {sortBy === 'popularity' && sortOrder === 'desc' && '🔥 Most popular first'}\r\n                          {sortBy === 'newest' && sortOrder === 'desc' && '🆕 Latest arrivals first'}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden\">\r\n                  <div className=\"p-6\">\r\n                    <VirtualizedProductGrid\r\n                      products={products}\r\n                      emptyMessage=\"No products found matching your search criteria.\"\r\n                      className=\"min-h-[400px]\"\r\n                      viewMode={viewMode}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Enhanced Pagination */}\r\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-6\">\r\n                  <Pagination\r\n                    currentPage={pagination.page}\r\n                    totalPages={pagination.pages}\r\n                    hasNext={pagination.has_next}\r\n                    hasPrev={pagination.has_prev}\r\n                    onPageChange={handlePageChange}\r\n                    loading={loading}\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Enhanced No Results */}\r\n            {!loading && products.length === 0 && (\r\n              <div className=\"bg-white rounded-2xl shadow-lg border border-gray-200 p-12\">\r\n                <div className=\"text-center max-w-md mx-auto\">\r\n                  <div className=\"w-24 h-24 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n                    <span className=\"text-4xl\">🔍</span>\r\n                  </div>\r\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">\r\n                    {searchQuery || filters.categories.length > 0 || filters.brands.length > 0\r\n                      ? 'No Products Found'\r\n                      : 'No Products Available'\r\n                    }\r\n                  </h3>\r\n                  <p className=\"text-gray-600 mb-8 leading-relaxed\">\r\n                    {searchQuery || filters.categories.length > 0 || filters.brands.length > 0\r\n                      ? \"We couldn't find any sustainable products matching your search criteria. Try adjusting your search or filters.\"\r\n                      : \"There are currently no products available in our sustainable catalog. Check back soon!\"\r\n                    }\r\n                  </p>\r\n\r\n                  {(searchQuery || filters.categories.length > 0 || filters.brands.length > 0) && (\r\n                    <div className=\"bg-gray-50 rounded-xl p-6 mb-8\">\r\n                      <h4 className=\"font-semibold text-gray-900 mb-3\">💡 Search Tips:</h4>\r\n                      <ul className=\"text-sm text-gray-600 space-y-2 text-left\">\r\n                        <li className=\"flex items-center gap-2\">\r\n                          <span className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></span>\r\n                          Try different or more general keywords\r\n                        </li>\r\n                        <li className=\"flex items-center gap-2\">\r\n                          <span className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></span>\r\n                          Check your spelling and try synonyms\r\n                        </li>\r\n                        <li className=\"flex items-center gap-2\">\r\n                          <span className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></span>\r\n                          Remove some filters to broaden results\r\n                        </li>\r\n                        <li className=\"flex items-center gap-2\">\r\n                          <span className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></span>\r\n                          Try our AI Visual Search feature\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\r\n                    <button\r\n                      onClick={handleClearFilters}\r\n                      className=\"px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    >\r\n                      🔄 Clear All Filters\r\n                    </button>\r\n                    <button\r\n                      onClick={() => setShowVisualSearch(true)}\r\n                      className=\"px-6 py-3 bg-white text-gray-700 border-2 border-gray-300 rounded-xl hover:border-green-500 hover:text-green-600 transition-all duration-200 font-medium\"\r\n                    >\r\n                      📷 Try Visual Search\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n});\r\n\r\nexport default Search;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,YAAY,EAAEC,cAAc,QAAQ,6BAA6B;AAC1E,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,eAAe,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,MAAM,gBAAAC,EAAA,cAAGxB,KAAK,CAACyB,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG;AAAM,CAAC,KAAK;EAAAH,EAAA;EACvC,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAGzB,eAAe,CAAC,CAAC;EACzD,MAAM0B,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC2B,YAAY,CAACM,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;EAC3E,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMsC,oBAAoB,GAAGtB,WAAW,CAACe,WAAW,EAAE,GAAG,CAAC;EAC1D,MAAM;IAAEQ;EAAK,CAAC,GAAGzB,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAE0B,OAAO;IAAEC;EAAM,CAAC,GAAGxB,eAAe,CAAC,CAAC;;EAE5C;EACA,MAAMyB,cAAc,GAAG,MAAAA,CAAOC,KAAK,EAAEC,YAAY,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IACtE,IAAI;MACF,MAAMnB,KAAK,GAAGoB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;MAE/D,MAAME,UAAU,GAAG;QACjBN,KAAK,EAAEA,KAAK,CAACO,IAAI,CAAC,CAAC;QACnBC,IAAI,EAAE,MAAM;QACZC,aAAa,EAAER,YAAY;QAC3BC,OAAO,EAAEA,OAAO;QAChBQ,gBAAgB,EAAEL,cAAc;QAChCM,UAAU,EAAEC,cAAc,CAACR,OAAO,CAAC,YAAY,CAAC,IAAIS,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC;MAC1E,CAAC;MAED,MAAMC,KAAK,CAAC,GAAG5C,YAAY,mBAAmB,EAAE;QAC9C6C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,IAAInC,KAAK,IAAI;YAAE,eAAe,EAAE,UAAUA,KAAK;UAAG,CAAC,CAAC;UACpD,IAAIsB,cAAc,IAAI;YAAE,oBAAoB,EAAEA;UAAe,CAAC;QAChE,CAAC;QACDc,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACf,UAAU;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC;;EAED;EACA,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC;IAC3CsE,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC6C,OAAO,EAAEmC,UAAU,CAAC,GAAGhF,QAAQ,CAAC;IACrCiF,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAM,CAAC;IAClCC,WAAW,EAAE;MAAEF,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IAC/BE,mBAAmB,EAAE;MAAEH,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAI,CAAC;IACzCG,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3F,QAAQ,CAAC,OAAO,CAAC;EAC7C,MAAM,CAAC4F,SAAS,EAAEC,YAAY,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAC,MAAM,CAAC;EAChD,MAAM,CAACgG,WAAW,EAAEC,cAAc,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiG,QAAQ,GAAGvE,YAAY,CAACM,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE;IAC5C,MAAMkE,WAAW,GAAGxE,YAAY,CAACM,GAAG,CAAC,UAAU,CAAC;IAChD,MAAMmE,QAAQ,GAAGzE,YAAY,CAACM,GAAG,CAAC,OAAO,CAAC;;IAE1C;IACA,MAAMoE,WAAW,GAAGvD,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,IAAI,OAAO;IAC/E,MAAMuD,cAAc,GAAGxD,YAAY,CAACC,OAAO,CAAC,6BAA6B,CAAC,IAAI,KAAK;IACnF,MAAMwD,SAAS,GAAG5E,YAAY,CAACM,GAAG,CAAC,SAAS,CAAC,IAAIoE,WAAW;IAC5D,MAAMG,YAAY,GAAG7E,YAAY,CAACM,GAAG,CAAC,YAAY,CAAC,IAAIqE,cAAc;IACrE,MAAMG,OAAO,GAAGC,QAAQ,CAAC/E,YAAY,CAACM,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;IAEvDD,cAAc,CAACkE,QAAQ,CAAC;IACxBP,SAAS,CAACY,SAAS,CAAC;IACpBV,YAAY,CAACW,YAAY,CAAC;IAC1BnC,aAAa,CAACsC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,IAAI,EAAEmC;IAAQ,CAAC,CAAC,CAAC;;IAEnD;IACA,IAAIN,WAAW,IAAIC,QAAQ,EAAE;MAC3BpB,UAAU,CAAC2B,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP1B,UAAU,EAAEkB,WAAW,GAAG,CAACA,WAAW,CAAC,GAAG,EAAE;QAC5CjB,MAAM,EAAEkB,QAAQ,GAAG,CAACA,QAAQ,CAAC,GAAG;MAClC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACzE,YAAY,CAAC,CAAC;;EAElB;EACA1B,SAAS,CAAC,MAAM;IACd2G,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA3G,SAAS,CAAC,MAAM;IACd;IACA2G,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACtE,oBAAoB,EAAEO,OAAO,EAAE6C,MAAM,EAAEE,SAAS,EAAExB,UAAU,CAACE,IAAI,CAAC,CAAC;EAEvE,MAAMsC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC/B,UAAU,CAAC,IAAI,CAAC;IAChBE,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAM8B,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEpC,IAAIxE,oBAAoB,EAAEuE,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEzE,oBAAoB,CAAC;MACvE,IAAIO,OAAO,CAACoC,UAAU,CAAC+B,MAAM,GAAG,CAAC,EAAEH,MAAM,CAACE,MAAM,CAAC,UAAU,EAAElE,OAAO,CAACoC,UAAU,CAACgC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC1F,IAAIpE,OAAO,CAACqC,MAAM,CAAC8B,MAAM,GAAG,CAAC,EAAEH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAElE,OAAO,CAACqC,MAAM,CAAC+B,IAAI,CAAC,GAAG,CAAC,CAAC;MAC/E,IAAIpE,OAAO,CAACsC,OAAO,IAAItC,OAAO,CAACsC,OAAO,CAAC6B,MAAM,GAAG,CAAC,EAAEH,MAAM,CAACE,MAAM,CAAC,WAAW,EAAElE,OAAO,CAACsC,OAAO,CAAC8B,IAAI,CAAC,GAAG,CAAC,CAAC;MACxG,IAAIpE,OAAO,CAACuC,UAAU,CAACC,GAAG,GAAG,CAAC,EAAEwB,MAAM,CAACE,MAAM,CAAC,WAAW,EAAElE,OAAO,CAACuC,UAAU,CAACC,GAAG,CAAC;MAClF,IAAIxC,OAAO,CAACuC,UAAU,CAACE,GAAG,GAAG,KAAK,EAAEuB,MAAM,CAACE,MAAM,CAAC,WAAW,EAAElE,OAAO,CAACuC,UAAU,CAACE,GAAG,CAAC;MACtF,IAAIzC,OAAO,CAAC0C,WAAW,CAACF,GAAG,GAAG,CAAC,EAAEwB,MAAM,CAACE,MAAM,CAAC,YAAY,EAAElE,OAAO,CAAC0C,WAAW,CAACF,GAAG,CAAC;MACrF,IAAIxC,OAAO,CAAC0C,WAAW,CAACD,GAAG,GAAG,CAAC,EAAEuB,MAAM,CAACE,MAAM,CAAC,YAAY,EAAElE,OAAO,CAAC0C,WAAW,CAACD,GAAG,CAAC;MACrF,IAAIzC,OAAO,CAAC2C,mBAAmB,CAACH,GAAG,GAAG,CAAC,EAAEwB,MAAM,CAACE,MAAM,CAAC,oBAAoB,EAAElE,OAAO,CAAC2C,mBAAmB,CAACH,GAAG,CAAC;MAC7G,IAAIxC,OAAO,CAAC2C,mBAAmB,CAACF,GAAG,GAAG,GAAG,EAAEuB,MAAM,CAACE,MAAM,CAAC,oBAAoB,EAAElE,OAAO,CAAC2C,mBAAmB,CAACF,GAAG,CAAC;MAC/G,IAAIzC,OAAO,CAAC4C,WAAW,EAAEoB,MAAM,CAACE,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC;MAE/DF,MAAM,CAACE,MAAM,CAAC,SAAS,EAAErB,MAAM,CAAC;MAChCmB,MAAM,CAACE,MAAM,CAAC,YAAY,EAAEnB,SAAS,CAAC;MACtCiB,MAAM,CAACE,MAAM,CAAC,MAAM,EAAE3C,UAAU,CAACE,IAAI,CAAC;MACtCuC,MAAM,CAACE,MAAM,CAAC,UAAU,EAAE3C,UAAU,CAACG,QAAQ,CAAC;MAE9CN,OAAO,CAACiD,GAAG,CAAC,gCAAgC,EAAEL,MAAM,CAACnD,QAAQ,CAAC,CAAC,CAAC;MAChE,MAAMyD,QAAQ,GAAG,MAAMxD,KAAK,CAAC,GAAG5C,YAAY,aAAa8F,MAAM,CAACnD,QAAQ,CAAC,CAAC,EAAE,CAAC;MAE7E,IAAI,CAACyD,QAAQ,CAACC,EAAE,EAAE;QAChB,IAAID,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;UAC3B,MAAMC,UAAU,GAAGH,QAAQ,CAACtD,OAAO,CAAC5B,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI;UAC9D,MAAM,IAAIsF,KAAK,CAAC,oCAAoCD,UAAU,+BAA+B,CAAC;QAChG;QACA,MAAM,IAAIC,KAAK,CAAC,qCAAqCJ,QAAQ,CAACE,MAAM,GAAG,CAAC;MAC1E;MAEA,MAAMG,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCxD,OAAO,CAACiD,GAAG,CAAC,wBAAwB,EAAEM,IAAI,CAAC;;MAE3C;MACA,MAAME,aAAa,GAAG,CAACF,IAAI,CAACtD,QAAQ,IAAI,EAAE,EAAEyD,MAAM,CAACC,OAAO,IAAI;QAC5D,IAAI,CAACA,OAAO,EAAE;UACZ3D,OAAO,CAAC4D,IAAI,CAAC,wBAAwB,EAAED,OAAO,CAAC;UAC/C,OAAO,KAAK;QACd;QACA,IAAI,CAACA,OAAO,CAACE,IAAI,EAAE;UACjB7D,OAAO,CAAC4D,IAAI,CAAC,uBAAuB,EAAED,OAAO,CAAC;QAChD;QACA,IAAIA,OAAO,CAACG,KAAK,KAAK,IAAI,IAAIH,OAAO,CAACG,KAAK,KAAKC,SAAS,EAAE;UACzD/D,OAAO,CAAC4D,IAAI,CAAC,wBAAwB,EAAED,OAAO,CAAC;UAC/CA,OAAO,CAACG,KAAK,GAAG,CAAC,CAAC,CAAC;QACrB;QACA,IAAI,CAACH,OAAO,CAACK,KAAK,EAAE;UAClBhE,OAAO,CAAC4D,IAAI,CAAC,wBAAwB,EAAED,OAAO,CAAC;QACjD;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MAEF3D,OAAO,CAACiD,GAAG,CAAC,aAAaQ,aAAa,CAACV,MAAM,wBAAwB,CAACQ,IAAI,CAACtD,QAAQ,IAAI,EAAE,EAAE8C,MAAM,QAAQ,CAAC;MAE1G7C,WAAW,CAACuD,aAAa,CAAC;MAC1BrD,aAAa,CAACmD,IAAI,CAACpD,UAAU,IAAI;QAC/BE,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIrC,oBAAoB,IAAIA,oBAAoB,CAACY,IAAI,CAAC,CAAC,EAAE;QACvDR,cAAc,CACZJ,oBAAoB,EACpBoF,aAAa,CAACV,MAAM,EACpB;UACEkB,QAAQ,EAAErF,OAAO,CAACqF,QAAQ;UAC1BC,KAAK,EAAEtF,OAAO,CAACsF,KAAK;UACpBC,SAAS,EAAEvF,OAAO,CAACwF,QAAQ;UAC3BC,SAAS,EAAEzF,OAAO,CAAC0F,QAAQ;UAC3BC,OAAO,EAAE9C,MAAM;UACf+C,UAAU,EAAE7C;QACd,CACF,CAAC;MACH;IAEF,CAAC,CAAC,OAAO8C,GAAG,EAAE;MACZzE,OAAO,CAACxB,KAAK,CAAC,wBAAwB,EAAEiG,GAAG,CAAC;;MAE5C;MACA,IAAIA,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;QAC/C7D,aAAa,CAAC,wDAAwD,CAAC;MACzE,CAAC,MAAM,IAAI2D,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACtC7D,aAAa,CAAC,oDAAoD,CAAC;MACrE,CAAC,MAAM;QACLA,aAAa,CAAC2D,GAAG,CAACC,OAAO,CAAC;MAC5B;;MAEA;MACA,IAAI,CAACD,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAACF,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACvEzE,WAAW,CAAC,EAAE,CAAC;MACjB;IACF,CAAC,SAAS;MACRU,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgE,kBAAkB,GAAIC,KAAK,IAAK;IACpC9G,cAAc,CAAC8G,KAAK,CAAC;IACrB3G,kBAAkB,CAAC2G,KAAK,CAAC9B,MAAM,IAAI,CAAC,CAAC;;IAErC;IACA,MAAM+B,SAAS,GAAG,IAAIjC,eAAe,CAACnF,YAAY,CAAC;IACnD,IAAImH,KAAK,EAAE;MACTC,SAAS,CAACC,GAAG,CAAC,GAAG,EAAEF,KAAK,CAAC;IAC3B,CAAC,MAAM;MACLC,SAAS,CAACE,MAAM,CAAC,GAAG,CAAC;IACvB;IACAF,SAAS,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5BpH,eAAe,CAACmH,SAAS,CAAC;IAC1B1E,aAAa,CAACsC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAM4E,sBAAsB,GAAIC,UAAU,IAAK;IAC7C,IAAIA,UAAU,CAAChG,IAAI,KAAK,SAAS,EAAE;MACjCnB,cAAc,CAACmH,UAAU,CAACC,IAAI,CAAC;IACjC,CAAC,MAAM,IAAID,UAAU,CAAChG,IAAI,KAAK,UAAU,EAAE;MACzCtB,QAAQ,CAAC,aAAasH,UAAU,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;MACxE;IACF,CAAC,MAAM,IAAIH,UAAU,CAAChG,IAAI,KAAK,OAAO,EAAE;MACtC6B,UAAU,CAAC2B,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPzB,MAAM,EAAE,CAACiE,UAAU,CAACC,IAAI;MAC1B,CAAC,CAAC,CAAC;IACL;IACAjH,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMoH,mBAAmB,GAAIC,UAAU,IAAK;IAC1CxE,UAAU,CAACwE,UAAU,CAAC;IACtBnF,aAAa,CAACsC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;;IAE7C;IACA,MAAMyE,SAAS,GAAG,IAAIjC,eAAe,CAACnF,YAAY,CAAC;IACnDoH,SAAS,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;IAC1BpH,eAAe,CAACmH,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,cAAc,GAAG;MACrBzE,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAM,CAAC;MAClCC,WAAW,EAAE;QAAEF,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MAC/BE,mBAAmB,EAAE;QAAEH,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAI,CAAC;MACzCG,WAAW,EAAE;IACf,CAAC;IAEDT,UAAU,CAAC0E,cAAc,CAAC;IAC1BrF,aAAa,CAACsC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;;IAE7C;IACA,MAAMyE,SAAS,GAAG,IAAIjC,eAAe,CAACnF,YAAY,CAAC;;IAEnD;IACAoH,SAAS,CAACE,MAAM,CAAC,UAAU,CAAC;IAC5BF,SAAS,CAACE,MAAM,CAAC,OAAO,CAAC;IACzBF,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;IAC7BF,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;IAC7BF,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;IAC9BF,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;IAC9BF,SAAS,CAACE,MAAM,CAAC,oBAAoB,CAAC;IACtCF,SAAS,CAACE,MAAM,CAAC,oBAAoB,CAAC;IACtCF,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;;IAEjC;IACAF,SAAS,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;IAE1BpH,eAAe,CAACmH,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMY,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,YAAY,KAAK;IACpD5D,cAAc,CAAC,IAAI,CAAC;IACpBN,SAAS,CAACiE,SAAS,CAAC;IACpB/D,YAAY,CAACgE,YAAY,CAAC;;IAE1B;IACA/G,YAAY,CAACgH,OAAO,CAAC,0BAA0B,EAAEF,SAAS,CAAC;IAC3D9G,YAAY,CAACgH,OAAO,CAAC,6BAA6B,EAAED,YAAY,CAAC;;IAEjE;IACA,MAAMd,SAAS,GAAG,IAAIjC,eAAe,CAACnF,YAAY,CAAC;IACnDoH,SAAS,CAACC,GAAG,CAAC,SAAS,EAAEY,SAAS,CAAC;IACnCb,SAAS,CAACC,GAAG,CAAC,YAAY,EAAEa,YAAY,CAAC;IACzCd,SAAS,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;IAC1BpH,eAAe,CAACmH,SAAS,CAAC;IAC1B1E,aAAa,CAACsC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC;;IAE7C;IACAyF,UAAU,CAAC,MAAM9D,cAAc,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;EAC9C,CAAC;EAED,MAAM+D,gBAAgB,GAAIC,OAAO,IAAK;IACpC5F,aAAa,CAACsC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,IAAI,EAAE2F;IAAQ,CAAC,CAAC,CAAC;;IAEnD;IACA,MAAMlB,SAAS,GAAG,IAAIjC,eAAe,CAACnF,YAAY,CAAC;IACnDoH,SAAS,CAACC,GAAG,CAAC,MAAM,EAAEiB,OAAO,CAACvG,QAAQ,CAAC,CAAC,CAAC;IACzC9B,eAAe,CAACmH,SAAS,CAAC;;IAE1B;IACAmB,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,IAAIvF,UAAU,EAAE;IACd,oBACE3D,OAAA;MAAKmJ,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCpJ,OAAA,CAACR,YAAY;QACX8B,KAAK,EAAEqC,UAAW;QAClB0F,OAAO,EAAEA,CAAA,KAAM;UACbzF,aAAa,CAAC,IAAI,CAAC;UACnB6B,aAAa,CAAC,CAAC;QACjB;MAAE;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACEzJ,OAAA,CAAAE,SAAA;IAAAkJ,QAAA,gBACEpJ,OAAA,CAACN,SAAS;MAAC8B,KAAK,EAAEZ,WAAY;MAAC8I,OAAO,EAAE3G;IAAS;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpDzJ,OAAA;MAAKmJ,SAAS,EAAC,mEAAmE;MAAAC,QAAA,eAChFpJ,OAAA;QAAKmJ,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAG5DpJ,OAAA;UAAKmJ,SAAS,EAAC,sHAAsH;UAAAC,QAAA,gBACnIpJ,OAAA;YAAKmJ,SAAS,EAAC;UAA8B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDzJ,OAAA;YAAKmJ,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BpJ,OAAA;cAAKmJ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDpJ,OAAA;gBAAAoJ,QAAA,gBACEpJ,OAAA;kBAAImJ,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DxI,WAAW,GAAG,mBAAmB,GAAG;gBAAuB;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACLzJ,OAAA;kBAAGmJ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAClCxI,WAAW,GACR,SAASqC,UAAU,CAACI,KAAK,iBAAiBzC,WAAW,GAAG,GACxD;gBAA2F;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE9F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNzJ,OAAA;gBAAKmJ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9BpJ,OAAA;kBAAKmJ,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,eACnGpJ,OAAA;oBAAMmJ,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzJ,OAAA;YAAKmJ,SAAS,EAAC;UAA4D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClFzJ,OAAA;YAAKmJ,SAAS,EAAC;UAA8D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAGNzJ,OAAA;UAAKmJ,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpJ,OAAA;YAAKmJ,SAAS,EAAC,2DAA2D;YAAAC,QAAA,eACxEpJ,OAAA;cAAKmJ,SAAS,EAAC,+DAA+D;cAAAC,QAAA,gBAC5EpJ,OAAA;gBAAKmJ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BpJ,OAAA;kBACE2J,GAAG,EAAEhJ,cAAe;kBACpBqB,IAAI,EAAC,MAAM;kBACX4H,WAAW,EAAC,qEAAqE;kBACjFjC,KAAK,EAAE/G,WAAY;kBACnBiJ,QAAQ,EAAGC,CAAC,IAAKpC,kBAAkB,CAACoC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;kBACpDqC,OAAO,EAAEA,CAAA,KAAMhJ,kBAAkB,CAACJ,WAAW,CAACiF,MAAM,IAAI,CAAC,CAAE;kBAC3DoE,MAAM,EAAEA,CAAA,KAAMrB,UAAU,CAAC,MAAM5H,kBAAkB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAE;kBAC/DmI,SAAS,EAAC;gBAAyM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpN,CAAC,eACFzJ,OAAA;kBAAKmJ,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnFpJ,OAAA;oBAAKmJ,SAAS,EAAC,uBAAuB;oBAACe,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAf,QAAA,eAC5EpJ,OAAA;sBAAMoK,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,kHAAkH;sBAACC,QAAQ,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACL7I,WAAW,iBACVZ,OAAA;kBACEuK,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC,EAAE,CAAE;kBACtCyB,SAAS,EAAC,uGAAuG;kBAAAC,QAAA,eAEjHpJ,OAAA;oBAAKmJ,SAAS,EAAC,SAAS;oBAACe,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAf,QAAA,eAC9DpJ,OAAA;sBAAMoK,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,oMAAoM;sBAACC,QAAQ,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNzJ,OAAA;gBACEuK,OAAO,EAAEA,CAAA,KAAMrJ,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;gBACtDkI,SAAS,EAAE,iGACTlI,gBAAgB,GACZ,0GAA0G,GAC1G,oGAAoG,EACvG;gBACHuJ,KAAK,EAAC,6DAA6D;gBAAApB,QAAA,gBAEnEpJ,OAAA;kBAAKmJ,SAAS,EAAC,SAAS;kBAACe,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAf,QAAA,eAC9DpJ,OAAA;oBAAMoK,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,4FAA4F;oBAACC,QAAQ,EAAC;kBAAS;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I,CAAC,eACNzJ,OAAA;kBAAMmJ,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DzJ,OAAA;kBAAMmJ,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzJ,OAAA,CAACZ,iBAAiB;YAChBoC,KAAK,EAAEZ,WAAY;YACnB6J,kBAAkB,EAAE1C,sBAAuB;YAC3C2C,OAAO,EAAEA,CAAA,KAAM1J,kBAAkB,CAAC,KAAK,CAAE;YACzC2J,SAAS,EAAE5J;UAAgB;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGLxI,gBAAgB,iBACfjB,OAAA;UAAKmJ,SAAS,EAAC,+GAA+G;UAAAC,QAAA,eAC5HpJ,OAAA;YAAKmJ,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBpJ,OAAA;cAAKmJ,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpJ,OAAA;gBAAKmJ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCpJ,OAAA;kBAAKmJ,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,eACjHpJ,OAAA;oBAAKmJ,SAAS,EAAC,oBAAoB;oBAACe,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAf,QAAA,eACzEpJ,OAAA;sBAAMoK,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,4FAA4F;sBAACC,QAAQ,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1I;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzJ,OAAA;kBAAAoJ,QAAA,gBACEpJ,OAAA;oBAAImJ,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxEzJ,OAAA;oBAAGmJ,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAoD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzJ,OAAA;gBACEuK,OAAO,EAAEA,CAAA,KAAMrJ,mBAAmB,CAAC,KAAK,CAAE;gBAC1CiI,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,eAE1GpJ,OAAA;kBAAKmJ,SAAS,EAAC,SAAS;kBAACe,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAf,QAAA,eAC9DpJ,OAAA;oBAAMoK,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oMAAoM;oBAACC,QAAQ,EAAC;kBAAS;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNzJ,OAAA,CAACT,YAAY;cAACgB,KAAK,EAAEA;YAAM;cAAA+I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDzJ,OAAA;UAAKmJ,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAG9CpJ,OAAA;YAAKmJ,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCpJ,OAAA;cAAKmJ,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BpJ,OAAA;gBAAKmJ,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBACpFpJ,OAAA;kBAAKmJ,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBACrFpJ,OAAA;oBAAImJ,SAAS,EAAC,yDAAyD;oBAAAC,QAAA,gBACrEpJ,OAAA;sBAAAoJ,QAAA,EAAM;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,sBAEjB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLzJ,OAAA;oBAAGmJ,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAoC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACNzJ,OAAA;kBAAKmJ,SAAS,EAAC,KAAK;kBAAAC,QAAA,eAClBpJ,OAAA,CAACb,aAAa;oBACZuC,OAAO,EAAEA,OAAQ;oBACjBkJ,eAAe,EAAExC,mBAAoB;oBACrCyC,cAAc,EAAEvC,kBAAmB;oBACnCwC,SAAS,EAAErH;kBAAQ;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzJ,OAAA;YAAKmJ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAG7BpJ,OAAA;cAAKmJ,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBpJ,OAAA;gBAAKmJ,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,eACxEpJ,OAAA,CAACX,YAAY;kBACXkF,MAAM,EAAEA,MAAO;kBACfE,SAAS,EAAEA,SAAU;kBACrBsG,YAAY,EAAEvC,gBAAiB;kBAC/BwC,WAAW,EAAE/H,UAAU,CAACI,KAAM;kBAC9BsB,QAAQ,EAAEA,QAAS;kBACnBsG,gBAAgB,EAAErG,WAAY;kBAC9BkG,SAAS,EAAEjG;gBAAY;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLhG,OAAO,iBACNzD,OAAA;cAAKmJ,SAAS,EAAC,4DAA4D;cAAAC,QAAA,eACzEpJ,OAAA;gBAAKmJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpJ,OAAA;kBAAKmJ,SAAS,EAAC,oHAAoH;kBAAAC,QAAA,eACjIpJ,OAAA;oBAAKmJ,SAAS,EAAC;kBAA2D;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACNzJ,OAAA;kBAAImJ,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFzJ,OAAA;kBAAGmJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA4C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGA,CAAChG,OAAO,IAAIV,QAAQ,CAAC8C,MAAM,GAAG,CAAC,iBAC9B7F,OAAA;cAAKmJ,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAExBpJ,OAAA;gBAAKmJ,SAAS,EAAC,qFAAqF;gBAAAC,QAAA,eAClGpJ,OAAA;kBAAKmJ,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,eAChDpJ,OAAA;oBAAKmJ,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCpJ,OAAA;sBAAKmJ,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,eACjFpJ,OAAA;wBAAMmJ,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNzJ,OAAA;sBAAAoJ,QAAA,gBACEpJ,OAAA;wBAAGmJ,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,GAAC,UACxC,EAACrG,QAAQ,CAAC8C,MAAM,EAAC,qBAAmB,EAAC,GAAG,eAChD7F,OAAA;0BAAMmJ,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAC5B7E,MAAM,KAAK,OAAO,GAAG,OAAO,GAC5BA,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAC9BA,MAAM,KAAK,gBAAgB,GAAG,gBAAgB,GAC9CA,MAAM,KAAK,YAAY,GAAG,YAAY,GACtCA,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAG;wBAAM;0BAAA+E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpC,CAAC,EACN,GAAG,EAAC,GAAC,EAAChF,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,aAAa,EAAC,GAC7D;sBAAA;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACJzJ,OAAA;wBAAGmJ,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,GAClC7E,MAAM,KAAK,OAAO,IAAIE,SAAS,KAAK,KAAK,IAAI,qBAAqB,EAClEF,MAAM,KAAK,OAAO,IAAIE,SAAS,KAAK,MAAM,IAAI,2BAA2B,EACzEF,MAAM,KAAK,QAAQ,IAAIE,SAAS,KAAK,MAAM,IAAI,uBAAuB,EACtEF,MAAM,KAAK,gBAAgB,IAAIE,SAAS,KAAK,MAAM,IAAI,4BAA4B,EACnFF,MAAM,KAAK,YAAY,IAAIE,SAAS,KAAK,MAAM,IAAI,uBAAuB,EAC1EF,MAAM,KAAK,QAAQ,IAAIE,SAAS,KAAK,MAAM,IAAI,0BAA0B;sBAAA;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzJ,OAAA;gBAAKmJ,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,eACpFpJ,OAAA;kBAAKmJ,SAAS,EAAC,KAAK;kBAAAC,QAAA,eAClBpJ,OAAA,CAACd,sBAAsB;oBACrB6D,QAAQ,EAAEA,QAAS;oBACnBmI,YAAY,EAAC,kDAAkD;oBAC/D/B,SAAS,EAAC,eAAe;oBACzBxE,QAAQ,EAAEA;kBAAS;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzJ,OAAA;gBAAKmJ,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,eACxEpJ,OAAA,CAACV,UAAU;kBACT6L,WAAW,EAAElI,UAAU,CAACE,IAAK;kBAC7BiI,UAAU,EAAEnI,UAAU,CAACK,KAAM;kBAC7B+H,OAAO,EAAEpI,UAAU,CAACM,QAAS;kBAC7B+H,OAAO,EAAErI,UAAU,CAACO,QAAS;kBAC7B+H,YAAY,EAAE1C,gBAAiB;kBAC/BpF,OAAO,EAAEA;gBAAQ;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGA,CAAChG,OAAO,IAAIV,QAAQ,CAAC8C,MAAM,KAAK,CAAC,iBAChC7F,OAAA;cAAKmJ,SAAS,EAAC,4DAA4D;cAAAC,QAAA,eACzEpJ,OAAA;gBAAKmJ,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CpJ,OAAA;kBAAKmJ,SAAS,EAAC,iHAAiH;kBAAAC,QAAA,eAC9HpJ,OAAA;oBAAMmJ,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACNzJ,OAAA;kBAAImJ,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClDxI,WAAW,IAAIc,OAAO,CAACoC,UAAU,CAAC+B,MAAM,GAAG,CAAC,IAAInE,OAAO,CAACqC,MAAM,CAAC8B,MAAM,GAAG,CAAC,GACtE,mBAAmB,GACnB;gBAAuB;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzB,CAAC,eACLzJ,OAAA;kBAAGmJ,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAC9CxI,WAAW,IAAIc,OAAO,CAACoC,UAAU,CAAC+B,MAAM,GAAG,CAAC,IAAInE,OAAO,CAACqC,MAAM,CAAC8B,MAAM,GAAG,CAAC,GACtE,gHAAgH,GAChH;gBAAwF;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3F,CAAC,EAEH,CAAC7I,WAAW,IAAIc,OAAO,CAACoC,UAAU,CAAC+B,MAAM,GAAG,CAAC,IAAInE,OAAO,CAACqC,MAAM,CAAC8B,MAAM,GAAG,CAAC,kBACzE7F,OAAA;kBAAKmJ,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpJ,OAAA;oBAAImJ,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrEzJ,OAAA;oBAAImJ,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,gBACvDpJ,OAAA;sBAAImJ,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACrCpJ,OAAA;wBAAMmJ,SAAS,EAAC;sBAAuC;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,0CAEjE;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLzJ,OAAA;sBAAImJ,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACrCpJ,OAAA;wBAAMmJ,SAAS,EAAC;sBAAuC;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,wCAEjE;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLzJ,OAAA;sBAAImJ,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACrCpJ,OAAA;wBAAMmJ,SAAS,EAAC;sBAAuC;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,0CAEjE;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLzJ,OAAA;sBAAImJ,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACrCpJ,OAAA;wBAAMmJ,SAAS,EAAC;sBAAuC;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,oCAEjE;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,eAEDzJ,OAAA;kBAAKmJ,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,gBAC7DpJ,OAAA;oBACEuK,OAAO,EAAEjC,kBAAmB;oBAC5Ba,SAAS,EAAC,6NAA6N;oBAAAC,QAAA,EACxO;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTzJ,OAAA;oBACEuK,OAAO,EAAEA,CAAA,KAAMrJ,mBAAmB,CAAC,IAAI,CAAE;oBACzCiI,SAAS,EAAC,0JAA0J;oBAAAC,QAAA,EACrK;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;EAAA,QAxoByCzK,eAAe,EACtCC,WAAW,EAOCY,WAAW,EACvBF,OAAO,EACGG,eAAe;AAAA,EA8nB3C,CAAC;EAAA,QAxoBwCd,eAAe,EACtCC,WAAW,EAOCY,WAAW,EACvBF,OAAO,EACGG,eAAe;AAAA,EA8nB1C;AAAC0L,GAAA,GAzoBGrL,MAAM;AA2oBZ,eAAeA,MAAM;AAAC,IAAAG,EAAA,EAAAkL,GAAA;AAAAC,YAAA,CAAAnL,EAAA;AAAAmL,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}