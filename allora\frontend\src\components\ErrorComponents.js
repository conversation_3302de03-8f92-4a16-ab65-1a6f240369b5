import React from 'react';
import { useError } from '../contexts/ErrorContext';
import { AlertTriangle, X, RefreshCw, Info, AlertCircle, XCircle, CheckCircle } from 'lucide-react';

// Error severity icons and colors
const ERROR_STYLES = {
  low: {
    icon: Info,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
    iconColor: 'text-blue-500'
  },
  medium: {
    icon: AlertCircle,
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-800',
    iconColor: 'text-yellow-500'
  },
  high: {
    icon: AlertTriangle,
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    textColor: 'text-orange-800',
    iconColor: 'text-orange-500'
  },
  critical: {
    icon: XCircle,
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    iconColor: 'text-red-500'
  }
};

// Individual error display component
export const ErrorAlert = ({ 
  error, 
  onDismiss, 
  onRetry, 
  showDetails = false,
  className = '' 
}) => {
  const { severity = 'medium', type, message, retryable } = error;
  const style = ERROR_STYLES[severity];
  const Icon = style.icon;

  return (
    <div className={`${style.bgColor} ${style.borderColor} border rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <Icon className={`${style.iconColor} w-5 h-5 mt-0.5 mr-3 flex-shrink-0`} />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className={`${style.textColor} font-medium text-sm`}>
              {type ? `${type.charAt(0).toUpperCase() + type.slice(1)} Error` : 'Error'}
            </h3>
            
            <div className="flex items-center space-x-2">
              {retryable && onRetry && (
                <button
                  onClick={onRetry}
                  className={`${style.textColor} hover:opacity-75 transition-opacity`}
                  title="Retry"
                >
                  <RefreshCw className="w-4 h-4" />
                </button>
              )}
              
              {onDismiss && (
                <button
                  onClick={onDismiss}
                  className={`${style.textColor} hover:opacity-75 transition-opacity`}
                  title="Dismiss"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
          
          <p className={`${style.textColor} text-sm mt-1`}>
            {message}
          </p>
          
          {showDetails && error.context && (
            <details className="mt-2">
              <summary className={`${style.textColor} text-xs cursor-pointer hover:underline`}>
                Show details
              </summary>
              <pre className={`${style.textColor} text-xs mt-1 bg-white bg-opacity-50 p-2 rounded overflow-auto`}>
                {JSON.stringify(error.context, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    </div>
  );
};

// Global error display component
export const GlobalErrorDisplay = ({ 
  position = 'top-right',
  maxErrors = 5,
  autoHide = true,
  hideDelay = 5000 
}) => {
  const { errors, dismissError, retryError } = useError();
  
  const visibleErrors = errors.slice(0, maxErrors);
  
  if (visibleErrors.length === 0) return null;
  
  const positionClasses = {
    'top-right': 'fixed top-4 right-4 z-50',
    'top-left': 'fixed top-4 left-4 z-50',
    'bottom-right': 'fixed bottom-4 right-4 z-50',
    'bottom-left': 'fixed bottom-4 left-4 z-50',
    'top-center': 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50',
    'bottom-center': 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50'
  };
  
  return (
    <div className={`${positionClasses[position]} space-y-2 max-w-md`}>
      {visibleErrors.map((error) => (
        <ErrorAlert
          key={error.id}
          error={error}
          onDismiss={() => dismissError(error.id)}
          onRetry={error.retryable ? () => retryError(error.id) : null}
          className="animate-slide-in-right shadow-lg"
        />
      ))}
      
      {errors.length > maxErrors && (
        <div className="bg-gray-100 border border-gray-200 rounded-lg p-2 text-center">
          <span className="text-gray-600 text-sm">
            +{errors.length - maxErrors} more errors
          </span>
        </div>
      )}
    </div>
  );
};

// Error boundary fallback component
export const ErrorBoundaryFallback = ({ 
  error, 
  resetError, 
  componentStack 
}) => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        <div className="text-center">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-bold text-gray-900 mb-2">
            Something went wrong
          </h1>
          <p className="text-gray-600 mb-6">
            An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
          </p>
          
          <div className="space-y-3">
            <button
              onClick={resetError}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
            
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Refresh Page
            </button>
          </div>
          
          {(error || componentStack) && (
            <details className="mt-6 text-left">
              <summary className="text-sm text-gray-500 cursor-pointer hover:underline">
                Technical Details
              </summary>
              <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono overflow-auto max-h-32">
                {error && (
                  <div className="text-red-600 mb-2">{error.message || 'Unknown error occurred'}</div>
                )}
                {componentStack && (
                  <div className="text-gray-600">{componentStack}</div>
                )}
              </div>
            </details>
          )}
        </div>
      </div>
    </div>
  );
};

// Page-level error component
export const PageError = ({ 
  error, 
  onRetry, 
  title = 'Something went wrong',
  description = 'An error occurred while loading this page.' 
}) => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="text-center max-w-md">
        <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>
        <p className="text-gray-600 mb-6">{description}</p>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
            <p className="text-red-800 text-sm">{error.message || 'Unknown error occurred'}</p>
          </div>
        )}
        
        <div className="space-y-3">
          {onRetry && (
            <button
              onClick={onRetry}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </button>
          )}
          
          <button
            onClick={() => window.history.back()}
            className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
};

// Inline error component for forms and sections
export const InlineError = ({ 
  error, 
  className = '',
  showIcon = true 
}) => {
  if (!error) return null;
  
  return (
    <div className={`flex items-center text-red-600 text-sm mt-1 ${className}`}>
      {showIcon && <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />}
      <span>{error.message || error}</span>
    </div>
  );
};

// Success message component for consistency
export const SuccessAlert = ({ 
  message, 
  onDismiss, 
  className = '' 
}) => {
  return (
    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <CheckCircle className="text-green-500 w-5 h-5 mt-0.5 mr-3 flex-shrink-0" />
        
        <div className="flex-1 min-w-0">
          <p className="text-green-800 text-sm">{message}</p>
        </div>
        
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-green-800 hover:opacity-75 transition-opacity ml-3"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default {
  ErrorAlert,
  GlobalErrorDisplay,
  ErrorBoundaryFallback,
  PageError,
  InlineError,
  SuccessAlert
};
