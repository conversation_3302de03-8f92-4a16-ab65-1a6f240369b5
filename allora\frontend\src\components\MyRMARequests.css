.my-rma-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

/* Header */
.my-rma-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.my-rma-header h1 {
    color: #2c3e50;
    font-size: 2.2rem;
    font-weight: 600;
    margin: 0;
}

/* Filters */
.rma-filters {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.search-filter {
    flex: 1;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.status-filters {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid #e0e0e0;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    color: #666;
}

.filter-btn:hover {
    border-color: #3498db;
    color: #3498db;
}

.filter-btn.active {
    background: #3498db;
    border-color: #3498db;
    color: white;
}

/* RMA Requests List */
.rma-requests-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rma-request-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.rma-request-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.rma-info h3 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 1.3rem;
    font-weight: 600;
}

.rma-info p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-body {
    padding: 25px 30px;
}

.request-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item .label {
    color: #666;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item .value {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.detail-item .value.highlight {
    color: #27ae60;
    font-size: 1.1rem;
}

.card-actions {
    padding: 20px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5rem;
    font-weight: 600;
}

.empty-state p {
    color: #666;
    margin-bottom: 30px;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 40px;
    padding: 20px;
}

.pagination-btn {
    padding: 10px 20px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    color: #666;
}

.pagination-btn:hover:not(:disabled) {
    border-color: #3498db;
    color: #3498db;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-number:hover {
    border-color: #3498db;
    color: #3498db;
}

.page-number.active {
    background: #3498db;
    border-color: #3498db;
    color: white;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading State */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: #666;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .my-rma-container {
        padding: 15px;
    }
    
    .my-rma-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 20px;
    }
    
    .my-rma-header h1 {
        font-size: 1.8rem;
    }
    
    .rma-filters {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }
    
    .status-filters {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .card-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 20px;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .request-details {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .card-actions {
        flex-direction: column;
        gap: 10px;
        padding: 20px;
    }
    
    .btn {
        width: 100%;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .pagination-btn {
        padding: 8px 16px;
        font-size: 0.8rem;
    }
    
    .page-number {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }
}
