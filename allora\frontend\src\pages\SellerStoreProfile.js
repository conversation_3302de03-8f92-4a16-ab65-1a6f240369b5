import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useSellerAuth } from '../contexts/SellerAuthContext';
import { useError } from '../contexts/ErrorContext';
import { useLoading } from '../contexts/LoadingContext';
import { LoadingButton } from '../components/LoadingComponents';
import { ErrorAlert } from '../components/ErrorComponents';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const SellerStoreProfile = () => {
  const navigate = useNavigate();
  const { seller, isAuthenticated, getAuthHeaders } = useSellerAuth();
  const { addError, handleApiError } = useError();
  const { isLoading, withLoading } = useLoading();

  const [formData, setFormData] = useState({
    store_name: '',
    store_description: '',
    store_logo: '',
    store_banner: '',
    return_policy: '',
    shipping_policy: '',
    terms_conditions: '',
    min_order_amount: '0',
    free_shipping_threshold: '500',
    processing_time: '1-2 business days'
  });

  const [errors, setErrors] = useState({});
  const [storeSlug, setStoreSlug] = useState('');

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated()) {
      navigate('/seller/login');
      return;
    }
    
    fetchStoreProfile();
  }, [isAuthenticated, navigate]);

  const fetchStoreProfile = async () => {
    try {
      await withLoading('fetch_store', async () => {
        const response = await fetch(`${API_BASE_URL}/seller/store`, {
          headers: {
            'Content-Type': 'application/json',
            ...getAuthHeaders()
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to load store profile');
        }

        const data = await response.json();
        const store = data.data;
        
        setFormData({
          store_name: store.store_name || '',
          store_description: store.store_description || '',
          store_logo: store.store_logo || '',
          store_banner: store.store_banner || '',
          return_policy: store.return_policy || '',
          shipping_policy: store.shipping_policy || '',
          terms_conditions: store.terms_conditions || '',
          min_order_amount: store.min_order_amount?.toString() || '0',
          free_shipping_threshold: store.free_shipping_threshold?.toString() || '500',
          processing_time: store.processing_time || '1-2 business days'
        });
        
        setStoreSlug(store.store_slug || '');
      }, 'Loading store profile...');
    } catch (error) {
      handleApiError(error, { action: 'fetch_store' });
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.store_name.trim()) {
      newErrors.store_name = 'Store name is required';
    }

    if (!formData.store_description.trim()) {
      newErrors.store_description = 'Store description is required';
    }

    if (formData.min_order_amount && (isNaN(formData.min_order_amount) || parseFloat(formData.min_order_amount) < 0)) {
      newErrors.min_order_amount = 'Minimum order amount must be a valid number (0 or greater)';
    }

    if (formData.free_shipping_threshold && (isNaN(formData.free_shipping_threshold) || parseFloat(formData.free_shipping_threshold) < 0)) {
      newErrors.free_shipping_threshold = 'Free shipping threshold must be a valid number (0 or greater)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await withLoading('save_store', async () => {
        const response = await fetch(`${API_BASE_URL}/seller/store`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...getAuthHeaders()
          },
          body: JSON.stringify({
            store_name: formData.store_name.trim(),
            store_description: formData.store_description.trim(),
            store_logo: formData.store_logo.trim(),
            store_banner: formData.store_banner.trim(),
            return_policy: formData.return_policy.trim(),
            shipping_policy: formData.shipping_policy.trim(),
            terms_conditions: formData.terms_conditions.trim(),
            min_order_amount: parseFloat(formData.min_order_amount) || 0,
            free_shipping_threshold: parseFloat(formData.free_shipping_threshold) || 500,
            processing_time: formData.processing_time.trim()
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update store profile');
        }

        const data = await response.json();
        setStoreSlug(data.data.store_slug);
      }, 'Updating store profile...');
    } catch (error) {
      handleApiError(error, { action: 'save_store' });
    }
  };

  if (!isAuthenticated()) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <Link to="/seller/dashboard" className="text-gray-500 hover:text-gray-700">
                ← Back to Dashboard
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">Store Profile</h1>
            </div>
            {storeSlug && (
              <a
                href={`/store/${storeSlug}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                View Public Store
              </a>
            )}
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="space-y-8 p-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Store Name *
                  </label>
                  <input
                    type="text"
                    value={formData.store_name}
                    onChange={(e) => handleInputChange('store_name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                      errors.store_name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter your store name"
                  />
                  {errors.store_name && <p className="mt-1 text-sm text-red-600">{errors.store_name}</p>}
                  {storeSlug && (
                    <p className="mt-1 text-sm text-gray-500">
                      Store URL: <span className="font-mono">/store/{storeSlug}</span>
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Store Description *
                  </label>
                  <textarea
                    rows={4}
                    value={formData.store_description}
                    onChange={(e) => handleInputChange('store_description', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                      errors.store_description ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Describe your store and what makes it special..."
                  />
                  {errors.store_description && <p className="mt-1 text-sm text-red-600">{errors.store_description}</p>}
                </div>
              </div>
            </div>

            {/* Branding */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Branding</h3>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Store Logo URL
                  </label>
                  <input
                    type="url"
                    value={formData.store_logo}
                    onChange={(e) => handleInputChange('store_logo', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="https://example.com/logo.png"
                  />
                  {formData.store_logo && (
                    <div className="mt-2">
                      <img
                        src={formData.store_logo}
                        alt="Store logo preview"
                        className="h-16 w-16 object-cover rounded-lg border"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Store Banner URL
                  </label>
                  <input
                    type="url"
                    value={formData.store_banner}
                    onChange={(e) => handleInputChange('store_banner', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="https://example.com/banner.jpg"
                  />
                  {formData.store_banner && (
                    <div className="mt-2">
                      <img
                        src={formData.store_banner}
                        alt="Store banner preview"
                        className="h-32 w-full object-cover rounded-lg border"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Store Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Store Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Order Amount (₹)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.min_order_amount}
                    onChange={(e) => handleInputChange('min_order_amount', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                      errors.min_order_amount ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="0"
                  />
                  {errors.min_order_amount && <p className="mt-1 text-sm text-red-600">{errors.min_order_amount}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Free Shipping Threshold (₹)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.free_shipping_threshold}
                    onChange={(e) => handleInputChange('free_shipping_threshold', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                      errors.free_shipping_threshold ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="500"
                  />
                  {errors.free_shipping_threshold && <p className="mt-1 text-sm text-red-600">{errors.free_shipping_threshold}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Processing Time
                  </label>
                  <select
                    value={formData.processing_time}
                    onChange={(e) => handleInputChange('processing_time', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="Same day">Same day</option>
                    <option value="1-2 business days">1-2 business days</option>
                    <option value="3-5 business days">3-5 business days</option>
                    <option value="1 week">1 week</option>
                    <option value="2 weeks">2 weeks</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Policies */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Store Policies</h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Return Policy
                  </label>
                  <textarea
                    rows={4}
                    value={formData.return_policy}
                    onChange={(e) => handleInputChange('return_policy', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Describe your return and refund policy..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Shipping Policy
                  </label>
                  <textarea
                    rows={4}
                    value={formData.shipping_policy}
                    onChange={(e) => handleInputChange('shipping_policy', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Describe your shipping methods and delivery times..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Terms & Conditions
                  </label>
                  <textarea
                    rows={4}
                    value={formData.terms_conditions}
                    onChange={(e) => handleInputChange('terms_conditions', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Enter your terms and conditions..."
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t">
              <LoadingButton
                type="submit"
                loading={isLoading('save_store')}
                className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Save Store Profile
              </LoadingButton>
            </div>
          </form>
        </div>
      </div>

      {/* Global Error Display */}
      <ErrorAlert />
    </div>
  );
};

export default SellerStoreProfile;
