import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import './RMARequest.css';

const RMARequest = () => {
    const { orderId } = useParams();
    const navigate = useNavigate();
    
    const [order, setOrder] = useState(null);
    const [selectedItems, setSelectedItems] = useState([]);
    const [returnReasons, setReturnReasons] = useState([]);
    const [rmaConfig, setRmaConfig] = useState({});
    const [customerInfo, setCustomerInfo] = useState({
        email: '',
        phone: '',
        notes: ''
    });
    const [rmaType, setRmaType] = useState('return_refund');
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [uploadedPhotos, setUploadedPhotos] = useState({});

    useEffect(() => {
        fetchOrderDetails();
        fetchReturnReasons();
        fetchRMAConfig();
    }, [orderId]);

    const fetchOrderDetails = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/orders/${orderId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setOrder(data.order);
                
                // Pre-fill customer info if available
                if (data.order.user) {
                    setCustomerInfo(prev => ({
                        ...prev,
                        email: data.order.user.email || '',
                        phone: data.order.user.phone || ''
                    }));
                }
            } else {
                toast.error('Failed to fetch order details');
                navigate('/orders');
            }
        } catch (error) {
            console.error('Error fetching order:', error);
            toast.error('Error loading order details');
        } finally {
            setLoading(false);
        }
    };

    const fetchReturnReasons = async () => {
        try {
            const response = await fetch('/api/rma/return-reasons');
            if (response.ok) {
                const data = await response.json();
                setReturnReasons(data.return_reasons);
            }
        } catch (error) {
            console.error('Error fetching return reasons:', error);
        }
    };

    const fetchRMAConfig = async () => {
        try {
            const response = await fetch('/api/rma/config');
            if (response.ok) {
                const data = await response.json();
                setRmaConfig(data.configuration);
            }
        } catch (error) {
            console.error('Error fetching RMA config:', error);
        }
    };

    const handleItemSelection = (orderItem, isSelected) => {
        if (isSelected) {
            setSelectedItems(prev => [...prev, {
                order_item_id: orderItem.id,
                product_id: orderItem.product_id,
                quantity: 1,
                max_quantity: orderItem.quantity,
                return_reason: '',
                condition_notes: '',
                photos: []
            }]);
        } else {
            setSelectedItems(prev => prev.filter(item => item.order_item_id !== orderItem.id));
        }
    };

    const updateSelectedItem = (orderItemId, field, value) => {
        setSelectedItems(prev => prev.map(item => 
            item.order_item_id === orderItemId 
                ? { ...item, [field]: value }
                : item
        ));
    };

    const handlePhotoUpload = async (orderItemId, files) => {
        const formData = new FormData();
        Array.from(files).forEach(file => {
            formData.append('photos', file);
        });

        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/upload/rma-photos', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });

            if (response.ok) {
                const data = await response.json();
                const photoUrls = data.photo_urls;
                
                // Update selected item with photo URLs
                updateSelectedItem(orderItemId, 'photos', photoUrls);
                
                // Store uploaded photos for display
                setUploadedPhotos(prev => ({
                    ...prev,
                    [orderItemId]: photoUrls
                }));
                
                toast.success('Photos uploaded successfully');
            } else {
                toast.error('Failed to upload photos');
            }
        } catch (error) {
            console.error('Error uploading photos:', error);
            toast.error('Error uploading photos');
        }
    };

    const calculateRefundAmount = async () => {
        if (selectedItems.length === 0) return 0;

        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/rma/calculate-refund', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    items: selectedItems.map(item => {
                        const orderItem = order.order_items.find(oi => oi.id === item.order_item_id);
                        return {
                            total_price: orderItem.unit_price * item.quantity
                        };
                    })
                })
            });

            if (response.ok) {
                const data = await response.json();
                return data.refund_calculation;
            }
        } catch (error) {
            console.error('Error calculating refund:', error);
        }
        
        return null;
    };

    const validateForm = () => {
        if (selectedItems.length === 0) {
            toast.error('Please select at least one item to return');
            return false;
        }

        for (const item of selectedItems) {
            if (!item.return_reason) {
                toast.error('Please select a return reason for all items');
                return false;
            }
            
            if (item.quantity <= 0 || item.quantity > item.max_quantity) {
                toast.error('Please enter valid quantities for all items');
                return false;
            }

            // Check if photos are required for certain return reasons
            if (rmaConfig.require_photos && 
                ['defective', 'damaged_shipping', 'not_as_described'].includes(item.return_reason) &&
                (!item.photos || item.photos.length === 0)) {
                toast.error('Photos are required for the selected return reason');
                return false;
            }
        }

        if (!customerInfo.email) {
            toast.error('Email address is required');
            return false;
        }

        return true;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) return;

        setSubmitting(true);

        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/rma/create', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    order_id: parseInt(orderId),
                    items: selectedItems,
                    customer_info: customerInfo,
                    rma_type: rmaType
                })
            });

            if (response.ok) {
                const data = await response.json();
                toast.success('RMA request created successfully!');
                navigate(`/rma/${data.rma_number}`);
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || 'Failed to create RMA request');
            }
        } catch (error) {
            console.error('Error creating RMA request:', error);
            toast.error('Error creating RMA request');
        } finally {
            setSubmitting(false);
        }
    };

    if (loading) {
        return (
            <div className="rma-request-container">
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>Loading order details...</p>
                </div>
            </div>
        );
    }

    if (!order) {
        return (
            <div className="rma-request-container">
                <div className="error-message">
                    <h2>Order Not Found</h2>
                    <p>The requested order could not be found or you don't have access to it.</p>
                    <button onClick={() => navigate('/orders')} className="btn btn-primary">
                        Back to Orders
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="rma-request-container">
            <div className="rma-request-header">
                <h1>Return/Exchange Request</h1>
                <div className="order-info">
                    <h3>Order #{order.order_number}</h3>
                    <p>Order Date: {new Date(order.created_at).toLocaleDateString()}</p>
                    <p>Total Amount: ₹{order.total_amount}</p>
                </div>
            </div>

            <form onSubmit={handleSubmit} className="rma-request-form">
                {/* RMA Type Selection */}
                <div className="form-section">
                    <h3>Request Type</h3>
                    <div className="rma-type-selection">
                        <label className="radio-option">
                            <input
                                type="radio"
                                value="return_refund"
                                checked={rmaType === 'return_refund'}
                                onChange={(e) => setRmaType(e.target.value)}
                            />
                            <span>Return for Refund</span>
                        </label>
                        <label className="radio-option">
                            <input
                                type="radio"
                                value="exchange_same"
                                checked={rmaType === 'exchange_same'}
                                onChange={(e) => setRmaType(e.target.value)}
                            />
                            <span>Exchange (Same Product)</span>
                        </label>
                        <label className="radio-option">
                            <input
                                type="radio"
                                value="exchange_different"
                                checked={rmaType === 'exchange_different'}
                                onChange={(e) => setRmaType(e.target.value)}
                            />
                            <span>Exchange (Different Product)</span>
                        </label>
                    </div>
                </div>

                {/* Item Selection */}
                <div className="form-section">
                    <h3>Select Items to Return/Exchange</h3>
                    <div className="order-items">
                        {order.order_items.map(orderItem => (
                            <div key={orderItem.id} className="order-item">
                                <div className="item-checkbox">
                                    <input
                                        type="checkbox"
                                        id={`item-${orderItem.id}`}
                                        onChange={(e) => handleItemSelection(orderItem, e.target.checked)}
                                    />
                                </div>
                                <div className="item-image">
                                    <img 
                                        src={orderItem.product?.image_url || '/placeholder-product.jpg'} 
                                        alt={orderItem.product?.name || 'Product'}
                                    />
                                </div>
                                <div className="item-details">
                                    <h4>{orderItem.product?.name || 'Product'}</h4>
                                    <p>Quantity Ordered: {orderItem.quantity}</p>
                                    <p>Unit Price: ₹{orderItem.unit_price}</p>
                                    <p>Total: ₹{orderItem.unit_price * orderItem.quantity}</p>
                                </div>
                                
                                {selectedItems.find(item => item.order_item_id === orderItem.id) && (
                                    <div className="item-return-details">
                                        <div className="quantity-input">
                                            <label>Return Quantity:</label>
                                            <input
                                                type="number"
                                                min="1"
                                                max={orderItem.quantity}
                                                value={selectedItems.find(item => item.order_item_id === orderItem.id)?.quantity || 1}
                                                onChange={(e) => updateSelectedItem(orderItem.id, 'quantity', parseInt(e.target.value))}
                                            />
                                        </div>
                                        
                                        <div className="reason-select">
                                            <label>Return Reason:</label>
                                            <select
                                                value={selectedItems.find(item => item.order_item_id === orderItem.id)?.return_reason || ''}
                                                onChange={(e) => updateSelectedItem(orderItem.id, 'return_reason', e.target.value)}
                                                required
                                            >
                                                <option value="">Select a reason</option>
                                                {returnReasons.map(reason => (
                                                    <option key={reason.value} value={reason.value}>
                                                        {reason.label}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                        
                                        <div className="condition-notes">
                                            <label>Condition Notes:</label>
                                            <textarea
                                                placeholder="Describe the condition of the item..."
                                                value={selectedItems.find(item => item.order_item_id === orderItem.id)?.condition_notes || ''}
                                                onChange={(e) => updateSelectedItem(orderItem.id, 'condition_notes', e.target.value)}
                                            />
                                        </div>
                                        
                                        {rmaConfig.require_photos && (
                                            <div className="photo-upload">
                                                <label>Upload Photos:</label>
                                                <input
                                                    type="file"
                                                    multiple
                                                    accept="image/*"
                                                    onChange={(e) => handlePhotoUpload(orderItem.id, e.target.files)}
                                                />
                                                {uploadedPhotos[orderItem.id] && (
                                                    <div className="uploaded-photos">
                                                        {uploadedPhotos[orderItem.id].map((photoUrl, index) => (
                                                            <img key={index} src={photoUrl} alt={`Upload ${index + 1}`} />
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Customer Information */}
                <div className="form-section">
                    <h3>Contact Information</h3>
                    <div className="customer-info">
                        <div className="form-group">
                            <label>Email Address *</label>
                            <input
                                type="email"
                                value={customerInfo.email}
                                onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                                required
                            />
                        </div>
                        <div className="form-group">
                            <label>Phone Number</label>
                            <input
                                type="tel"
                                value={customerInfo.phone}
                                onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                            />
                        </div>
                        <div className="form-group">
                            <label>Additional Notes</label>
                            <textarea
                                placeholder="Any additional information about your return request..."
                                value={customerInfo.notes}
                                onChange={(e) => setCustomerInfo(prev => ({ ...prev, notes: e.target.value }))}
                            />
                        </div>
                    </div>
                </div>

                {/* Submit Button */}
                <div className="form-actions">
                    <button 
                        type="button" 
                        onClick={() => navigate('/orders')}
                        className="btn btn-secondary"
                    >
                        Cancel
                    </button>
                    <button 
                        type="submit" 
                        disabled={submitting || selectedItems.length === 0}
                        className="btn btn-primary"
                    >
                        {submitting ? 'Creating Request...' : 'Create RMA Request'}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default RMARequest;
