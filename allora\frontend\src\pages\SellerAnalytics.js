import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './SellerAnalytics.css';

const SellerAnalytics = () => {
    const [analyticsData, setAnalyticsData] = useState(null);
    const [productAnalytics, setProductAnalytics] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [selectedPeriod, setSelectedPeriod] = useState(30);
    const [activeTab, setActiveTab] = useState('overview');
    const navigate = useNavigate();

    useEffect(() => {
        fetchAnalyticsData();
    }, [selectedPeriod]);

    const fetchAnalyticsData = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('sellerToken');
            
            if (!token) {
                navigate('/seller/login');
                return;
            }

            // Fetch overview analytics
            const overviewResponse = await fetch(`/api/seller/analytics/overview?days=${selectedPeriod}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!overviewResponse.ok) {
                throw new Error('Failed to fetch analytics data');
            }

            const overviewData = await overviewResponse.json();
            setAnalyticsData(overviewData.data);

            // Fetch product analytics
            const productResponse = await fetch(`/api/seller/analytics/products?days=${selectedPeriod}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!productResponse.ok) {
                throw new Error('Failed to fetch product analytics');
            }

            const productData = await productResponse.json();
            setProductAnalytics(productData.data);

        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    const formatPercentage = (value) => {
        const sign = value >= 0 ? '+' : '';
        return `${sign}${value.toFixed(1)}%`;
    };

    const getGrowthColor = (value) => {
        return value >= 0 ? '#10b981' : '#ef4444';
    };

    if (loading) {
        return (
            <div className="seller-analytics">
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Loading analytics...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="seller-analytics">
                <div className="error-container">
                    <h3>Error Loading Analytics</h3>
                    <p>{error}</p>
                    <button onClick={fetchAnalyticsData} className="retry-btn">
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="seller-analytics">
            <div className="analytics-header">
                <h1>Analytics Dashboard</h1>
                <div className="period-selector">
                    <select 
                        value={selectedPeriod} 
                        onChange={(e) => setSelectedPeriod(Number(e.target.value))}
                        className="period-select"
                    >
                        <option value={7}>Last 7 days</option>
                        <option value={30}>Last 30 days</option>
                        <option value={90}>Last 90 days</option>
                        <option value={365}>Last year</option>
                    </select>
                </div>
            </div>

            <div className="analytics-tabs">
                <button 
                    className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
                    onClick={() => setActiveTab('overview')}
                >
                    Overview
                </button>
                <button 
                    className={`tab-btn ${activeTab === 'products' ? 'active' : ''}`}
                    onClick={() => setActiveTab('products')}
                >
                    Product Performance
                </button>
            </div>

            {activeTab === 'overview' && analyticsData && (
                <div className="overview-tab">
                    {/* Key Metrics Cards */}
                    <div className="metrics-grid">
                        <div className="metric-card">
                            <div className="metric-header">
                                <h3>Total Revenue</h3>
                                <span 
                                    className="growth-indicator"
                                    style={{ color: getGrowthColor(analyticsData.overview.revenue_growth) }}
                                >
                                    {formatPercentage(analyticsData.overview.revenue_growth)}
                                </span>
                            </div>
                            <div className="metric-value">
                                {formatCurrency(analyticsData.overview.total_revenue)}
                            </div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-header">
                                <h3>Total Orders</h3>
                                <span 
                                    className="growth-indicator"
                                    style={{ color: getGrowthColor(analyticsData.overview.orders_growth) }}
                                >
                                    {formatPercentage(analyticsData.overview.orders_growth)}
                                </span>
                            </div>
                            <div className="metric-value">
                                {analyticsData.overview.total_orders}
                            </div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-header">
                                <h3>Your Earnings</h3>
                            </div>
                            <div className="metric-value">
                                {formatCurrency(analyticsData.overview.total_earnings)}
                            </div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-header">
                                <h3>Avg Order Value</h3>
                            </div>
                            <div className="metric-value">
                                {formatCurrency(analyticsData.overview.avg_order_value)}
                            </div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-header">
                                <h3>Unique Customers</h3>
                            </div>
                            <div className="metric-value">
                                {analyticsData.overview.unique_customers}
                            </div>
                        </div>

                        <div className="metric-card">
                            <div className="metric-header">
                                <h3>Commission Paid</h3>
                            </div>
                            <div className="metric-value">
                                {formatCurrency(analyticsData.overview.total_commission)}
                            </div>
                        </div>
                    </div>

                    {/* Top Products */}
                    <div className="top-products-section">
                        <h2>Top Performing Products</h2>
                        <div className="products-table">
                            <div className="table-header">
                                <div>Product Name</div>
                                <div>Units Sold</div>
                                <div>Revenue</div>
                                <div>Orders</div>
                            </div>
                            {analyticsData.top_products.map((product) => (
                                <div key={product.id} className="table-row">
                                    <div className="product-name">{product.name}</div>
                                    <div>{product.total_sold}</div>
                                    <div>{formatCurrency(product.total_revenue)}</div>
                                    <div>{product.order_count}</div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Sales Chart */}
                    <div className="sales-chart-section">
                        <h2>Daily Sales Trend</h2>
                        <div className="chart-container">
                            <div className="chart-placeholder">
                                <p>Sales chart visualization would go here</p>
                                <div className="chart-data">
                                    {analyticsData.daily_sales.map((day, index) => (
                                        <div key={index} className="chart-bar">
                                            <div className="bar-label">{new Date(day.date).toLocaleDateString()}</div>
                                            <div className="bar-value">{formatCurrency(day.revenue)}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {activeTab === 'products' && productAnalytics && (
                <div className="products-tab">
                    <h2>Product Performance Analysis</h2>
                    <div className="products-analytics-table">
                        <div className="table-header">
                            <div>Product</div>
                            <div>Category</div>
                            <div>Price</div>
                            <div>Stock</div>
                            <div>Sold</div>
                            <div>Revenue</div>
                            <div>Rating</div>
                            <div>Performance</div>
                        </div>
                        {productAnalytics.products.map((product) => (
                            <div key={product.id} className="table-row">
                                <div className="product-info">
                                    <div className="product-name">{product.name}</div>
                                </div>
                                <div>{product.category}</div>
                                <div>{formatCurrency(product.price)}</div>
                                <div className={product.stock_quantity < 10 ? 'low-stock' : ''}>
                                    {product.stock_quantity}
                                </div>
                                <div>{product.total_sold}</div>
                                <div>{formatCurrency(product.total_revenue)}</div>
                                <div className="rating">
                                    {product.avg_rating > 0 ? (
                                        <>
                                            ⭐ {product.avg_rating} ({product.review_count})
                                        </>
                                    ) : (
                                        'No reviews'
                                    )}
                                </div>
                                <div className="performance-score">
                                    <div className="score-bar">
                                        <div 
                                            className="score-fill" 
                                            style={{ width: `${product.performance_score}%` }}
                                        ></div>
                                    </div>
                                    <span>{product.performance_score}/100</span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            <div className="analytics-actions">
                <button onClick={() => navigate('/seller/dashboard')} className="back-btn">
                    Back to Dashboard
                </button>
            </div>
        </div>
    );
};

export default SellerAnalytics;
