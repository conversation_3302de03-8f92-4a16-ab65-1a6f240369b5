[{"C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ErrorBoundary.js": "4", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\CartContext.js": "5", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernMinimalistNavbar.js": "6", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernMinimalistFooter.js": "7", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Profile.js": "8", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ProductDetails.js": "9", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Cart.js": "10", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Community.js": "11", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Checkout.js": "12", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Categories.js": "13", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\CartRecovery.js": "14", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Search.js": "15", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Category.js": "16", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Account.js": "17", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\InventoryAssistant.js": "18", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\OrderConfirmation.js": "19", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SupportHub.js": "20", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\PriceTrends.js": "21", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ReturnPolicy.js": "22", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\FAQ.js": "23", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ContactUs.js": "24", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SupportTickets.js": "25", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\AdminProducts.js": "26", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\AdminLogin.js": "27", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\NotFound.js": "28", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\AdminDashboard.js": "29", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ServerError.js": "30", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\utils\\seo.js": "31", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\RecentlyViewed.js": "32", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\Pagination.js": "33", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AlloraLogo.js": "34", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ShippingCalculator.js": "35", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\VirtualizedProductGrid.js": "36", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\TaxCalculator.js": "37", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CouponManager.js": "38", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\utils\\imageUtils.js": "39", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductVariants.js": "40", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\utils\\currency.js": "41", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductImageGallery.js": "42", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\useProducts.js": "43", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\config\\api.js": "44", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductReviews.js": "45", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SavedCartManager.js": "46", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductComparison.js": "47", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AvailabilityNotification.js": "48", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\VirtualizedPostList.js": "49", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\useCommunity.js": "50", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SmartBundles.js": "51", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AddressBook.js": "52", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SearchFilters.js": "53", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SortControls.js": "54", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SearchSuggestions.js": "55", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\WishlistManager.js": "56", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\OrderHistory.js": "57", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\useDebounce.js": "58", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AddressManager.js": "59", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\PaymentMethodManager.js": "60", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AdminLayout.js": "61", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\utils\\responsive.js": "62", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernMinimalistProductCard.js": "63", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\useApi.js": "64", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\VisualSearch.js": "65", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ImageTest.js": "66", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductCard.js": "67", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AmazonStyleProductCard.js": "68", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\About.js": "69", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Sustainability.js": "70", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Careers.js": "71", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Press.js": "72", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ShippingInfo.js": "73", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\CommunityPage.js": "74", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SEOHead.js": "75", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\MinimalistProductCard.js": "76", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernPostCard.js": "77", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CreatePostModal.js": "78", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\DynamicTitle.js": "79", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\DialogContext.js": "80", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernDialog.js": "81", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\usePostInteractions.js": "82", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductListCard.js": "83", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\CookieContext.js": "84", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CookieConsentBanner.js": "85", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CookiePreferenceCenter.js": "86", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\NotificationContext.js": "87", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\PrivacyPolicy.js": "88", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CookiePolicy.js": "89", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\Login.js": "90", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\TermsOfService.js": "91", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\GuestCheckout.js": "92", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\TestPostCard.js": "93", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SimpleHome.js": "94", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Sell.js": "95", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\WebSocketContext.js": "96", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\LoadingContext.js": "97", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\ErrorContext.js": "98", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\ImageContext.js": "99", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\EnhancedImage.js": "100", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\LoadingComponents.js": "101", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ErrorComponents.js": "102", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\SellerAuthContext.js": "103", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerLogin.js": "104", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerDashboard.js": "105", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerProducts.js": "106", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerProductForm.js": "107", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerOrders.js": "108", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerStoreProfile.js": "109", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\PublicStorePage.js": "110", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerEarnings.js": "111", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerAnalytics.js": "112", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\AdminSellerManagement.js": "113", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SellerInfo.js": "114", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerStorePage.js": "115", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\OrderTracking.js": "116", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\TrackOrder.js": "117", "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\TrackingWidget.js": "118"}, {"size": 263, "mtime": 1751285919797, "results": "119", "hashOfConfig": "120"}, {"size": 10585, "mtime": 1751810288981, "results": "121", "hashOfConfig": "120"}, {"size": 2997, "mtime": 1751649456955, "results": "122", "hashOfConfig": "120"}, {"size": 11801, "mtime": 1751790522795, "results": "123", "hashOfConfig": "120"}, {"size": 10459, "mtime": 1751789749125, "results": "124", "hashOfConfig": "120"}, {"size": 16455, "mtime": 1751810161738, "results": "125", "hashOfConfig": "120"}, {"size": 24145, "mtime": 1751658269890, "results": "126", "hashOfConfig": "120"}, {"size": 4384, "mtime": 1751352056550, "results": "127", "hashOfConfig": "120"}, {"size": 60380, "mtime": 1751795067845, "results": "128", "hashOfConfig": "120"}, {"size": 38329, "mtime": 1751790378021, "results": "129", "hashOfConfig": "120"}, {"size": 13428, "mtime": 1751705222568, "results": "130", "hashOfConfig": "120"}, {"size": 22933, "mtime": 1751454893428, "results": "131", "hashOfConfig": "120"}, {"size": 15320, "mtime": 1751681872773, "results": "132", "hashOfConfig": "120"}, {"size": 9163, "mtime": 1751362586984, "results": "133", "hashOfConfig": "120"}, {"size": 29534, "mtime": 1751795550603, "results": "134", "hashOfConfig": "120"}, {"size": 23088, "mtime": 1751795578267, "results": "135", "hashOfConfig": "120"}, {"size": 13235, "mtime": 1751423173938, "results": "136", "hashOfConfig": "120"}, {"size": 1582, "mtime": 1751293429534, "results": "137", "hashOfConfig": "120"}, {"size": 9184, "mtime": 1751371785682, "results": "138", "hashOfConfig": "120"}, {"size": 12322, "mtime": 1751379909901, "results": "139", "hashOfConfig": "120"}, {"size": 1541, "mtime": 1751285919804, "results": "140", "hashOfConfig": "120"}, {"size": 27291, "mtime": 1751710576781, "results": "141", "hashOfConfig": "120"}, {"size": 19306, "mtime": 1751710872868, "results": "142", "hashOfConfig": "120"}, {"size": 22892, "mtime": 1751379416477, "results": "143", "hashOfConfig": "120"}, {"size": 15118, "mtime": 1751710025960, "results": "144", "hashOfConfig": "120"}, {"size": 22351, "mtime": 1751690861539, "results": "145", "hashOfConfig": "120"}, {"size": 3999, "mtime": 1751385199719, "results": "146", "hashOfConfig": "120"}, {"size": 7395, "mtime": 1751389181036, "results": "147", "hashOfConfig": "120"}, {"size": 16644, "mtime": 1751809304669, "results": "148", "hashOfConfig": "120"}, {"size": 8991, "mtime": 1751389279083, "results": "149", "hashOfConfig": "120"}, {"size": 9442, "mtime": 1751454304432, "results": "150", "hashOfConfig": "120"}, {"size": 3250, "mtime": 1751444612704, "results": "151", "hashOfConfig": "120"}, {"size": 3965, "mtime": 1751435847095, "results": "152", "hashOfConfig": "120"}, {"size": 9750, "mtime": 1751644163941, "results": "153", "hashOfConfig": "120"}, {"size": 8952, "mtime": 1751361554867, "results": "154", "hashOfConfig": "120"}, {"size": 2410, "mtime": 1751712654296, "results": "155", "hashOfConfig": "120"}, {"size": 8486, "mtime": 1751362043931, "results": "156", "hashOfConfig": "120"}, {"size": 11032, "mtime": 1751362113611, "results": "157", "hashOfConfig": "120"}, {"size": 5013, "mtime": 1751655813009, "results": "158", "hashOfConfig": "120"}, {"size": 9527, "mtime": 1751354490361, "results": "159", "hashOfConfig": "120"}, {"size": 2207, "mtime": 1751453575375, "results": "160", "hashOfConfig": "120"}, {"size": 8567, "mtime": 1751524522581, "results": "161", "hashOfConfig": "120"}, {"size": 8521, "mtime": 1751649866807, "results": "162", "hashOfConfig": "120"}, {"size": 459, "mtime": 1751475511987, "results": "163", "hashOfConfig": "120"}, {"size": 11504, "mtime": 1751359048524, "results": "164", "hashOfConfig": "120"}, {"size": 9460, "mtime": 1751724145209, "results": "165", "hashOfConfig": "120"}, {"size": 11114, "mtime": 1751723767853, "results": "166", "hashOfConfig": "120"}, {"size": 11135, "mtime": 1751358725286, "results": "167", "hashOfConfig": "120"}, {"size": 1477, "mtime": 1751285919790, "results": "168", "hashOfConfig": "120"}, {"size": 2566, "mtime": 1751705237210, "results": "169", "hashOfConfig": "120"}, {"size": 7603, "mtime": 1751359133582, "results": "170", "hashOfConfig": "120"}, {"size": 14671, "mtime": 1751690374191, "results": "171", "hashOfConfig": "120"}, {"size": 23119, "mtime": 1751795254655, "results": "172", "hashOfConfig": "120"}, {"size": 11432, "mtime": 1751716038964, "results": "173", "hashOfConfig": "120"}, {"size": 5589, "mtime": 1751373044966, "results": "174", "hashOfConfig": "120"}, {"size": 12804, "mtime": 1751724053282, "results": "175", "hashOfConfig": "120"}, {"size": 12801, "mtime": 1751809919533, "results": "176", "hashOfConfig": "120"}, {"size": 608, "mtime": 1751285919793, "results": "177", "hashOfConfig": "120"}, {"size": 15641, "mtime": 1751723825995, "results": "178", "hashOfConfig": "120"}, {"size": 16757, "mtime": 1751690465613, "results": "179", "hashOfConfig": "120"}, {"size": 5732, "mtime": 1751809320316, "results": "180", "hashOfConfig": "120"}, {"size": 8003, "mtime": 1751472770529, "results": "181", "hashOfConfig": "120"}, {"size": 9027, "mtime": 1751794940098, "results": "182", "hashOfConfig": "120"}, {"size": 5106, "mtime": 1751817437570, "results": "183", "hashOfConfig": "120"}, {"size": 6721, "mtime": 1751740406027, "results": "184", "hashOfConfig": "120"}, {"size": 6485, "mtime": 1751709810492, "results": "185", "hashOfConfig": "120"}, {"size": 2869, "mtime": 1751524488818, "results": "186", "hashOfConfig": "120"}, {"size": 6802, "mtime": 1751737711167, "results": "187", "hashOfConfig": "120"}, {"size": 12370, "mtime": 1751636696665, "results": "188", "hashOfConfig": "120"}, {"size": 12830, "mtime": 1751636852917, "results": "189", "hashOfConfig": "120"}, {"size": 13597, "mtime": 1751636937154, "results": "190", "hashOfConfig": "120"}, {"size": 12653, "mtime": 1751637060307, "results": "191", "hashOfConfig": "120"}, {"size": 19998, "mtime": 1751710776615, "results": "192", "hashOfConfig": "120"}, {"size": 16792, "mtime": 1751637219440, "results": "193", "hashOfConfig": "120"}, {"size": 9155, "mtime": 1751655006829, "results": "194", "hashOfConfig": "120"}, {"size": 4829, "mtime": 1751524647556, "results": "195", "hashOfConfig": "120"}, {"size": 18986, "mtime": 1751723999519, "results": "196", "hashOfConfig": "120"}, {"size": 12436, "mtime": 1751703417679, "results": "197", "hashOfConfig": "120"}, {"size": 2891, "mtime": 1751689328701, "results": "198", "hashOfConfig": "120"}, {"size": 5581, "mtime": 1751690093568, "results": "199", "hashOfConfig": "120"}, {"size": 7137, "mtime": 1751690068943, "results": "200", "hashOfConfig": "120"}, {"size": 3759, "mtime": 1751706028430, "results": "201", "hashOfConfig": "120"}, {"size": 6180, "mtime": 1751737531922, "results": "202", "hashOfConfig": "120"}, {"size": 15534, "mtime": 1751729534946, "results": "203", "hashOfConfig": "120"}, {"size": 6562, "mtime": 1751725448417, "results": "204", "hashOfConfig": "120"}, {"size": 12593, "mtime": 1751725501273, "results": "205", "hashOfConfig": "120"}, {"size": 4963, "mtime": 1751733903574, "results": "206", "hashOfConfig": "120"}, {"size": 14217, "mtime": 1751777937719, "results": "207", "hashOfConfig": "120"}, {"size": 15428, "mtime": 1751778223933, "results": "208", "hashOfConfig": "120"}, {"size": 46628, "mtime": 1751777535737, "results": "209", "hashOfConfig": "120"}, {"size": 16629, "mtime": 1751778117183, "results": "210", "hashOfConfig": "120"}, {"size": 25006, "mtime": 1751785394837, "results": "211", "hashOfConfig": "120"}, {"size": 1321, "mtime": 1751778444026, "results": "212", "hashOfConfig": "120"}, {"size": 35604, "mtime": 1751810225109, "results": "213", "hashOfConfig": "120"}, {"size": 18876, "mtime": 1751791835217, "results": "214", "hashOfConfig": "120"}, {"size": 7088, "mtime": 1751789706739, "results": "215", "hashOfConfig": "120"}, {"size": 3633, "mtime": 1751789776175, "results": "216", "hashOfConfig": "120"}, {"size": 6398, "mtime": 1751789806666, "results": "217", "hashOfConfig": "120"}, {"size": 7711, "mtime": 1751789842620, "results": "218", "hashOfConfig": "120"}, {"size": 7133, "mtime": 1751790071480, "results": "219", "hashOfConfig": "120"}, {"size": 6436, "mtime": 1751790030449, "results": "220", "hashOfConfig": "120"}, {"size": 9248, "mtime": 1751818190069, "results": "221", "hashOfConfig": "120"}, {"size": 2128, "mtime": 1751791535731, "results": "222", "hashOfConfig": "120"}, {"size": 7138, "mtime": 1751791567924, "results": "223", "hashOfConfig": "120"}, {"size": 13527, "mtime": 1751794080785, "results": "224", "hashOfConfig": "120"}, {"size": 13767, "mtime": 1751792513025, "results": "225", "hashOfConfig": "120"}, {"size": 15516, "mtime": 1751792652538, "results": "226", "hashOfConfig": "120"}, {"size": 15120, "mtime": 1751792830135, "results": "227", "hashOfConfig": "120"}, {"size": 17133, "mtime": 1751792983808, "results": "228", "hashOfConfig": "120"}, {"size": 15753, "mtime": 1751793120331, "results": "229", "hashOfConfig": "120"}, {"size": 19990, "mtime": 1751793369024, "results": "230", "hashOfConfig": "120"}, {"size": 13203, "mtime": 1751793942807, "results": "231", "hashOfConfig": "120"}, {"size": 17945, "mtime": 1751794344357, "results": "232", "hashOfConfig": "120"}, {"size": 5229, "mtime": 1751794855011, "results": "233", "hashOfConfig": "120"}, {"size": 11882, "mtime": 1751795115044, "results": "234", "hashOfConfig": "120"}, {"size": 12927, "mtime": 1751809783297, "results": "235", "hashOfConfig": "120"}, {"size": 10829, "mtime": 1751809830157, "results": "236", "hashOfConfig": "120"}, {"size": 2205, "mtime": 1751810182410, "results": "237", "hashOfConfig": "120"}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1flqrc", {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\App.js", ["592"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ErrorBoundary.js", ["593"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\CartContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernMinimalistNavbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernMinimalistFooter.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ProductDetails.js", ["594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Cart.js", ["606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Community.js", ["619"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Checkout.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Categories.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\CartRecovery.js", ["620"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Search.js", ["621", "622", "623", "624", "625", "626"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Category.js", ["627", "628", "629", "630"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Account.js", ["631", "632", "633"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\InventoryAssistant.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\OrderConfirmation.js", ["634"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SupportHub.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\PriceTrends.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ReturnPolicy.js", ["635", "636"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\FAQ.js", ["637", "638", "639"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ContactUs.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SupportTickets.js", ["640"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\AdminProducts.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\AdminLogin.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\AdminDashboard.js", ["641"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ServerError.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\utils\\seo.js", ["642"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\RecentlyViewed.js", ["643"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\Pagination.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AlloraLogo.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ShippingCalculator.js", ["644"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\VirtualizedProductGrid.js", ["645"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\TaxCalculator.js", ["646"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CouponManager.js", ["647"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\utils\\imageUtils.js", ["648"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductVariants.js", ["649"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\utils\\currency.js", ["650"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductImageGallery.js", ["651"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\useProducts.js", ["652", "653", "654", "655"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\config\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductReviews.js", ["656"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SavedCartManager.js", ["657"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductComparison.js", ["658", "659"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AvailabilityNotification.js", ["660", "661"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\VirtualizedPostList.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\useCommunity.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SmartBundles.js", ["662"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AddressBook.js", ["663"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SearchFilters.js", ["664"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SortControls.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SearchSuggestions.js", ["665"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\WishlistManager.js", ["666", "667"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\OrderHistory.js", ["668", "669", "670"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\useDebounce.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AddressManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\PaymentMethodManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\utils\\responsive.js", ["671"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernMinimalistProductCard.js", ["672"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\useApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\VisualSearch.js", ["673"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ImageTest.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductCard.js", ["674"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\AmazonStyleProductCard.js", ["675", "676"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Sustainability.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Careers.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Press.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\ShippingInfo.js", ["677", "678"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\CommunityPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SEOHead.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\MinimalistProductCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernPostCard.js", ["679", "680", "681", "682"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CreatePostModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\DynamicTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\DialogContext.js", ["683", "684", "685", "686", "687", "688"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ModernDialog.js", ["689", "690", "691"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\hooks\\usePostInteractions.js", ["692", "693"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ProductListCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\CookieContext.js", ["694"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CookieConsentBanner.js", ["695"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CookiePreferenceCenter.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\NotificationContext.js", ["696"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\PrivacyPolicy.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\CookiePolicy.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\TermsOfService.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\GuestCheckout.js", ["697", "698", "699", "700", "701"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\TestPostCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SimpleHome.js", ["702"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\Sell.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\WebSocketContext.js", ["703"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\LoadingContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\ErrorContext.js", ["704", "705"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\ImageContext.js", ["706", "707", "708"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\EnhancedImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\LoadingComponents.js", ["709"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\ErrorComponents.js", ["710"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\contexts\\SellerAuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerLogin.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerDashboard.js", ["711", "712", "713", "714", "715"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerProducts.js", ["716", "717", "718"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerProductForm.js", ["719", "720", "721"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerOrders.js", ["722", "723"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerStoreProfile.js", ["724", "725", "726"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\PublicStorePage.js", ["727", "728"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerEarnings.js", ["729", "730"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerAnalytics.js", ["731"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\AdminSellerManagement.js", ["732"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\SellerInfo.js", [], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\SellerStorePage.js", ["733"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\OrderTracking.js", ["734", "735"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\pages\\TrackOrder.js", ["736", "737", "738"], [], "C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\frontend\\src\\components\\TrackingWidget.js", [], [], {"ruleId": "739", "severity": 1, "message": "740", "line": 58, "column": 8, "nodeType": "741", "messageId": "742", "endLine": 58, "endColumn": 23}, {"ruleId": "739", "severity": 1, "message": "743", "line": 110, "column": 7, "nodeType": "741", "messageId": "742", "endLine": 110, "endColumn": 20}, {"ruleId": "739", "severity": 1, "message": "744", "line": 8, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 8, "endColumn": 22}, {"ruleId": "739", "severity": 1, "message": "745", "line": 9, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 24}, {"ruleId": "739", "severity": 1, "message": "746", "line": 9, "column": 26, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 40}, {"ruleId": "739", "severity": 1, "message": "747", "line": 9, "column": 42, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 55}, {"ruleId": "739", "severity": 1, "message": "748", "line": 10, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 10, "endColumn": 19}, {"ruleId": "749", "severity": 2, "message": "750", "line": 59, "column": 12, "nodeType": "751", "messageId": "752", "endLine": 59, "endColumn": 25}, {"ruleId": "753", "severity": 2, "message": "754", "line": 60, "column": 18, "nodeType": "741", "messageId": "755", "endLine": 60, "endColumn": 36}, {"ruleId": "753", "severity": 2, "message": "756", "line": 72, "column": 29, "nodeType": "741", "messageId": "755", "endLine": 72, "endColumn": 45}, {"ruleId": "739", "severity": 1, "message": "757", "line": 151, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 151, "endColumn": 19}, {"ruleId": "739", "severity": 1, "message": "758", "line": 151, "column": 21, "nodeType": "741", "messageId": "742", "endLine": 151, "endColumn": 35}, {"ruleId": "739", "severity": 1, "message": "759", "line": 152, "column": 23, "nodeType": "741", "messageId": "742", "endLine": 152, "endColumn": 39}, {"ruleId": "739", "severity": 1, "message": "760", "line": 152, "column": 41, "nodeType": "741", "messageId": "742", "endLine": 152, "endColumn": 52}, {"ruleId": "739", "severity": 1, "message": "744", "line": 11, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 11, "endColumn": 22}, {"ruleId": "739", "severity": 1, "message": "745", "line": 12, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 12, "endColumn": 24}, {"ruleId": "739", "severity": 1, "message": "747", "line": 12, "column": 26, "nodeType": "741", "messageId": "742", "endLine": 12, "endColumn": 39}, {"ruleId": "739", "severity": 1, "message": "761", "line": 25, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 25, "endColumn": 13}, {"ruleId": "739", "severity": 1, "message": "762", "line": 37, "column": 39, "nodeType": "741", "messageId": "742", "endLine": 37, "endColumn": 50}, {"ruleId": "739", "severity": 1, "message": "763", "line": 38, "column": 21, "nodeType": "741", "messageId": "742", "endLine": 38, "endColumn": 31}, {"ruleId": "739", "severity": 1, "message": "759", "line": 39, "column": 23, "nodeType": "741", "messageId": "742", "endLine": 39, "endColumn": 39}, {"ruleId": "739", "severity": 1, "message": "764", "line": 39, "column": 41, "nodeType": "741", "messageId": "742", "endLine": 39, "endColumn": 53}, {"ruleId": "739", "severity": 1, "message": "765", "line": 40, "column": 17, "nodeType": "741", "messageId": "742", "endLine": 40, "endColumn": 25}, {"ruleId": "739", "severity": 1, "message": "766", "line": 47, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 47, "endColumn": 23}, {"ruleId": "767", "severity": 1, "message": "768", "line": 57, "column": 6, "nodeType": "769", "endLine": 57, "endColumn": 26, "suggestions": "770"}, {"ruleId": "753", "severity": 2, "message": "754", "line": 506, "column": 34, "nodeType": "741", "messageId": "755", "endLine": 506, "endColumn": 52}, {"ruleId": "753", "severity": 2, "message": "756", "line": 509, "column": 45, "nodeType": "741", "messageId": "755", "endLine": 509, "endColumn": 61}, {"ruleId": "739", "severity": 1, "message": "771", "line": 40, "column": 9, "nodeType": "741", "messageId": "742", "endLine": 40, "endColumn": 18}, {"ruleId": "767", "severity": 1, "message": "772", "line": 17, "column": 6, "nodeType": "769", "endLine": 17, "endColumn": 21, "suggestions": "773"}, {"ruleId": "739", "severity": 1, "message": "774", "line": 9, "column": 24, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 38}, {"ruleId": "739", "severity": 1, "message": "775", "line": 26, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 26, "endColumn": 15}, {"ruleId": "739", "severity": 1, "message": "776", "line": 27, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 27, "endColumn": 18}, {"ruleId": "739", "severity": 1, "message": "777", "line": 27, "column": 20, "nodeType": "741", "messageId": "742", "endLine": 27, "endColumn": 25}, {"ruleId": "767", "severity": 1, "message": "778", "line": 120, "column": 6, "nodeType": "769", "endLine": 120, "endColumn": 8, "suggestions": "779"}, {"ruleId": "767", "severity": 1, "message": "778", "line": 126, "column": 6, "nodeType": "769", "endLine": 126, "endColumn": 73, "suggestions": "780"}, {"ruleId": "739", "severity": 1, "message": "781", "line": 7, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 7, "endColumn": 18}, {"ruleId": "739", "severity": 1, "message": "782", "line": 7, "column": 53, "nodeType": "741", "messageId": "742", "endLine": 7, "endColumn": 57}, {"ruleId": "739", "severity": 1, "message": "783", "line": 7, "column": 59, "nodeType": "741", "messageId": "742", "endLine": 7, "endColumn": 63}, {"ruleId": "767", "severity": 1, "message": "784", "line": 135, "column": 6, "nodeType": "769", "endLine": 135, "endColumn": 65, "suggestions": "785"}, {"ruleId": "739", "severity": 1, "message": "786", "line": 11, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 11, "endColumn": 7}, {"ruleId": "739", "severity": 1, "message": "787", "line": 15, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 15, "endColumn": 8}, {"ruleId": "767", "severity": 1, "message": "788", "line": 41, "column": 6, "nodeType": "769", "endLine": 41, "endColumn": 23, "suggestions": "789"}, {"ruleId": "767", "severity": 1, "message": "790", "line": 16, "column": 6, "nodeType": "769", "endLine": 16, "endColumn": 19, "suggestions": "791"}, {"ruleId": "739", "severity": 1, "message": "765", "line": 9, "column": 17, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 25}, {"ruleId": "767", "severity": 1, "message": "792", "line": 90, "column": 6, "nodeType": "769", "endLine": 90, "endColumn": 8, "suggestions": "793"}, {"ruleId": "739", "severity": 1, "message": "765", "line": 11, "column": 17, "nodeType": "741", "messageId": "742", "endLine": 11, "endColumn": 25}, {"ruleId": "767", "severity": 1, "message": "794", "line": 149, "column": 6, "nodeType": "769", "endLine": 149, "endColumn": 8, "suggestions": "795"}, {"ruleId": "767", "severity": 1, "message": "796", "line": 208, "column": 6, "nodeType": "769", "endLine": 208, "endColumn": 37, "suggestions": "797"}, {"ruleId": "767", "severity": 1, "message": "798", "line": 16, "column": 6, "nodeType": "769", "endLine": 16, "endColumn": 13, "suggestions": "799"}, {"ruleId": "767", "severity": 1, "message": "800", "line": 16, "column": 8, "nodeType": "769", "endLine": 16, "endColumn": 10, "suggestions": "801"}, {"ruleId": "802", "severity": 1, "message": "803", "line": 316, "column": 1, "nodeType": "804", "endLine": 330, "endColumn": 3}, {"ruleId": "767", "severity": 1, "message": "805", "line": 13, "column": 6, "nodeType": "769", "endLine": 13, "endColumn": 13, "suggestions": "806"}, {"ruleId": "767", "severity": 1, "message": "807", "line": 14, "column": 6, "nodeType": "769", "endLine": 14, "endColumn": 34, "suggestions": "808"}, {"ruleId": "739", "severity": 1, "message": "809", "line": 1, "column": 17, "nodeType": "741", "messageId": "742", "endLine": 1, "endColumn": 24}, {"ruleId": "767", "severity": 1, "message": "810", "line": 13, "column": 6, "nodeType": "769", "endLine": 13, "endColumn": 34, "suggestions": "811"}, {"ruleId": "767", "severity": 1, "message": "812", "line": 21, "column": 6, "nodeType": "769", "endLine": 21, "endColumn": 17, "suggestions": "813"}, {"ruleId": "802", "severity": 1, "message": "803", "line": 157, "column": 1, "nodeType": "804", "endLine": 166, "endColumn": 3}, {"ruleId": "767", "severity": 1, "message": "814", "line": 13, "column": 6, "nodeType": "769", "endLine": 13, "endColumn": 17, "suggestions": "815"}, {"ruleId": "802", "severity": 1, "message": "803", "line": 72, "column": 1, "nodeType": "804", "endLine": 78, "endColumn": 3}, {"ruleId": "767", "severity": 1, "message": "816", "line": 15, "column": 6, "nodeType": "769", "endLine": 15, "endColumn": 17, "suggestions": "817"}, {"ruleId": "739", "severity": 1, "message": "818", "line": 2, "column": 8, "nodeType": "741", "messageId": "742", "endLine": 2, "endColumn": 14}, {"ruleId": "767", "severity": 1, "message": "778", "line": 129, "column": 6, "nodeType": "769", "endLine": 129, "endColumn": 8, "suggestions": "819"}, {"ruleId": "767", "severity": 1, "message": "820", "line": 146, "column": 6, "nodeType": "769", "endLine": 146, "endColumn": 49, "suggestions": "821"}, {"ruleId": "767", "severity": 1, "message": "822", "line": 169, "column": 6, "nodeType": "769", "endLine": 169, "endColumn": 27, "suggestions": "823"}, {"ruleId": "767", "severity": 1, "message": "824", "line": 21, "column": 6, "nodeType": "769", "endLine": 21, "endColumn": 25, "suggestions": "825"}, {"ruleId": "767", "severity": 1, "message": "826", "line": 16, "column": 6, "nodeType": "769", "endLine": 16, "endColumn": 13, "suggestions": "827"}, {"ruleId": "739", "severity": 1, "message": "828", "line": 2, "column": 26, "nodeType": "741", "messageId": "742", "endLine": 2, "endColumn": 33}, {"ruleId": "767", "severity": 1, "message": "829", "line": 18, "column": 6, "nodeType": "769", "endLine": 18, "endColumn": 18, "suggestions": "830"}, {"ruleId": "739", "severity": 1, "message": "831", "line": 7, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 7, "endColumn": 17}, {"ruleId": "767", "severity": 1, "message": "832", "line": 21, "column": 6, "nodeType": "769", "endLine": 21, "endColumn": 17, "suggestions": "833"}, {"ruleId": "767", "severity": 1, "message": "834", "line": 14, "column": 6, "nodeType": "769", "endLine": 14, "endColumn": 13, "suggestions": "835"}, {"ruleId": "767", "severity": 1, "message": "836", "line": 27, "column": 6, "nodeType": "769", "endLine": 27, "endColumn": 13, "suggestions": "837"}, {"ruleId": "739", "severity": 1, "message": "838", "line": 85, "column": 9, "nodeType": "741", "messageId": "742", "endLine": 85, "endColumn": 32}, {"ruleId": "767", "severity": 1, "message": "839", "line": 89, "column": 6, "nodeType": "769", "endLine": 89, "endColumn": 45, "suggestions": "840"}, {"ruleId": "739", "severity": 1, "message": "841", "line": 6, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 6, "endColumn": 7}, {"ruleId": "739", "severity": 1, "message": "842", "line": 9, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 8}, {"ruleId": "739", "severity": 1, "message": "843", "line": 9, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 11}, {"ruleId": "739", "severity": 1, "message": "844", "line": 11, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 11, "endColumn": 9}, {"ruleId": "739", "severity": 1, "message": "845", "line": 12, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 12, "endColumn": 15}, {"ruleId": "802", "severity": 1, "message": "803", "line": 249, "column": 1, "nodeType": "804", "endLine": 263, "endColumn": 3}, {"ruleId": "739", "severity": 1, "message": "846", "line": 5, "column": 30, "nodeType": "741", "messageId": "742", "endLine": 5, "endColumn": 51}, {"ruleId": "739", "severity": 1, "message": "847", "line": 3, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 3, "endColumn": 23}, {"ruleId": "739", "severity": 1, "message": "846", "line": 5, "column": 30, "nodeType": "741", "messageId": "742", "endLine": 5, "endColumn": 51}, {"ruleId": "739", "severity": 1, "message": "848", "line": 6, "column": 23, "nodeType": "741", "messageId": "742", "endLine": 6, "endColumn": 50}, {"ruleId": "739", "severity": 1, "message": "846", "line": 7, "column": 30, "nodeType": "741", "messageId": "742", "endLine": 7, "endColumn": 51}, {"ruleId": "739", "severity": 1, "message": "765", "line": 8, "column": 17, "nodeType": "741", "messageId": "742", "endLine": 8, "endColumn": 25}, {"ruleId": "767", "severity": 1, "message": "849", "line": 98, "column": 6, "nodeType": "769", "endLine": 98, "endColumn": 8, "suggestions": "850"}, {"ruleId": "739", "severity": 1, "message": "851", "line": 8, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 8, "endColumn": 11}, {"ruleId": "739", "severity": 1, "message": "852", "line": 9, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 8}, {"ruleId": "739", "severity": 1, "message": "853", "line": 10, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 10, "endColumn": 8}, {"ruleId": "739", "severity": 1, "message": "854", "line": 25, "column": 7, "nodeType": "741", "messageId": "742", "endLine": 25, "endColumn": 17}, {"ruleId": "767", "severity": 1, "message": "855", "line": 40, "column": 6, "nodeType": "769", "endLine": 40, "endColumn": 8, "suggestions": "856"}, {"ruleId": "767", "severity": 1, "message": "855", "line": 60, "column": 6, "nodeType": "769", "endLine": 60, "endColumn": 8, "suggestions": "857"}, {"ruleId": "767", "severity": 1, "message": "855", "line": 80, "column": 6, "nodeType": "769", "endLine": 80, "endColumn": 8, "suggestions": "858"}, {"ruleId": "767", "severity": 1, "message": "855", "line": 100, "column": 6, "nodeType": "769", "endLine": 100, "endColumn": 8, "suggestions": "859"}, {"ruleId": "767", "severity": 1, "message": "855", "line": 128, "column": 6, "nodeType": "769", "endLine": 128, "endColumn": 8, "suggestions": "860"}, {"ruleId": "767", "severity": 1, "message": "855", "line": 148, "column": 6, "nodeType": "769", "endLine": 148, "endColumn": 8, "suggestions": "861"}, {"ruleId": "739", "severity": 1, "message": "862", "line": 8, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 8, "endColumn": 9}, {"ruleId": "739", "severity": 1, "message": "863", "line": 9, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 9, "endColumn": 7}, {"ruleId": "739", "severity": 1, "message": "864", "line": 10, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 10, "endColumn": 8}, {"ruleId": "739", "severity": 1, "message": "865", "line": 5, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 5, "endColumn": 15}, {"ruleId": "739", "severity": 1, "message": "866", "line": 5, "column": 17, "nodeType": "741", "messageId": "742", "endLine": 5, "endColumn": 20}, {"ruleId": "767", "severity": 1, "message": "867", "line": 171, "column": 6, "nodeType": "769", "endLine": 171, "endColumn": 8, "suggestions": "868"}, {"ruleId": "739", "severity": 1, "message": "869", "line": 3, "column": 36, "nodeType": "741", "messageId": "742", "endLine": 3, "endColumn": 37}, {"ruleId": "767", "severity": 1, "message": "870", "line": 38, "column": 6, "nodeType": "769", "endLine": 38, "endColumn": 8, "suggestions": "871"}, {"ruleId": "739", "severity": 1, "message": "872", "line": 34, "column": 26, "nodeType": "741", "messageId": "742", "endLine": 34, "endColumn": 43}, {"ruleId": "739", "severity": 1, "message": "873", "line": 35, "column": 24, "nodeType": "741", "messageId": "742", "endLine": 35, "endColumn": 39}, {"ruleId": "739", "severity": 1, "message": "874", "line": 36, "column": 21, "nodeType": "741", "messageId": "742", "endLine": 36, "endColumn": 33}, {"ruleId": "739", "severity": 1, "message": "875", "line": 37, "column": 25, "nodeType": "741", "messageId": "742", "endLine": 37, "endColumn": 41}, {"ruleId": "767", "severity": 1, "message": "876", "line": 52, "column": 6, "nodeType": "769", "endLine": 52, "endColumn": 8, "suggestions": "877"}, {"ruleId": "739", "severity": 1, "message": "745", "line": 8, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 8, "endColumn": 24}, {"ruleId": "739", "severity": 1, "message": "878", "line": 23, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 23, "endColumn": 15}, {"ruleId": "767", "severity": 1, "message": "879", "line": 83, "column": 6, "nodeType": "769", "endLine": 83, "endColumn": 24, "suggestions": "880"}, {"ruleId": "739", "severity": 1, "message": "881", "line": 147, "column": 7, "nodeType": "741", "messageId": "742", "endLine": 147, "endColumn": 14}, {"ruleId": "767", "severity": 1, "message": "882", "line": 26, "column": 9, "nodeType": "883", "endLine": 32, "endColumn": 4}, {"ruleId": "884", "severity": 1, "message": "885", "line": 156, "column": 24, "nodeType": "886", "messageId": "887", "endLine": 167, "endColumn": 11}, {"ruleId": "884", "severity": 1, "message": "885", "line": 177, "column": 29, "nodeType": "886", "messageId": "887", "endLine": 177, "endColumn": 82}, {"ruleId": "802", "severity": 1, "message": "803", "line": 212, "column": 1, "nodeType": "804", "endLine": 225, "endColumn": 3}, {"ruleId": "802", "severity": 1, "message": "803", "line": 290, "column": 1, "nodeType": "804", "endLine": 297, "endColumn": 3}, {"ruleId": "739", "severity": 1, "message": "888", "line": 13, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 13, "endColumn": 17}, {"ruleId": "739", "severity": 1, "message": "757", "line": 14, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 14, "endColumn": 19}, {"ruleId": "739", "severity": 1, "message": "889", "line": 18, "column": 10, "nodeType": "741", "messageId": "742", "endLine": 18, "endColumn": 19}, {"ruleId": "739", "severity": 1, "message": "890", "line": 18, "column": 21, "nodeType": "741", "messageId": "742", "endLine": 18, "endColumn": 33}, {"ruleId": "767", "severity": 1, "message": "800", "line": 28, "column": 6, "nodeType": "769", "endLine": 28, "endColumn": 33, "suggestions": "891"}, {"ruleId": "739", "severity": 1, "message": "888", "line": 13, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 13, "endColumn": 17}, {"ruleId": "739", "severity": 1, "message": "757", "line": 14, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 14, "endColumn": 19}, {"ruleId": "767", "severity": 1, "message": "778", "line": 33, "column": 6, "nodeType": "769", "endLine": 33, "endColumn": 42, "suggestions": "892"}, {"ruleId": "739", "severity": 1, "message": "888", "line": 15, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 15, "endColumn": 17}, {"ruleId": "739", "severity": 1, "message": "757", "line": 16, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 16, "endColumn": 19}, {"ruleId": "767", "severity": 1, "message": "893", "line": 55, "column": 6, "nodeType": "769", "endLine": 55, "endColumn": 56, "suggestions": "894"}, {"ruleId": "739", "severity": 1, "message": "757", "line": 14, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 14, "endColumn": 19}, {"ruleId": "767", "severity": 1, "message": "895", "line": 32, "column": 6, "nodeType": "769", "endLine": 32, "endColumn": 42, "suggestions": "896"}, {"ruleId": "739", "severity": 1, "message": "888", "line": 13, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 13, "endColumn": 17}, {"ruleId": "739", "severity": 1, "message": "757", "line": 14, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 14, "endColumn": 19}, {"ruleId": "767", "severity": 1, "message": "897", "line": 41, "column": 6, "nodeType": "769", "endLine": 41, "endColumn": 33, "suggestions": "898"}, {"ruleId": "767", "severity": 1, "message": "897", "line": 28, "column": 6, "nodeType": "769", "endLine": 28, "endColumn": 17, "suggestions": "899"}, {"ruleId": "767", "severity": 1, "message": "900", "line": 34, "column": 6, "nodeType": "769", "endLine": 34, "endColumn": 22, "suggestions": "901"}, {"ruleId": "739", "severity": 1, "message": "888", "line": 13, "column": 11, "nodeType": "741", "messageId": "742", "endLine": 13, "endColumn": 17}, {"ruleId": "767", "severity": 1, "message": "902", "line": 34, "column": 6, "nodeType": "769", "endLine": 34, "endColumn": 33, "suggestions": "903"}, {"ruleId": "767", "severity": 1, "message": "904", "line": 16, "column": 8, "nodeType": "769", "endLine": 16, "endColumn": 24, "suggestions": "905"}, {"ruleId": "767", "severity": 1, "message": "906", "line": 23, "column": 8, "nodeType": "769", "endLine": 23, "endColumn": 17, "suggestions": "907"}, {"ruleId": "767", "severity": 1, "message": "908", "line": 35, "column": 8, "nodeType": "769", "endLine": 35, "endColumn": 61, "suggestions": "909"}, {"ruleId": "739", "severity": 1, "message": "845", "line": 10, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 10, "endColumn": 15}, {"ruleId": "767", "severity": 1, "message": "910", "line": 27, "column": 6, "nodeType": "769", "endLine": 27, "endColumn": 31, "suggestions": "911"}, {"ruleId": "739", "severity": 1, "message": "845", "line": 8, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 8, "endColumn": 15}, {"ruleId": "739", "severity": 1, "message": "844", "line": 12, "column": 3, "nodeType": "741", "messageId": "742", "endLine": 12, "endColumn": 9}, {"ruleId": "739", "severity": 1, "message": "912", "line": 25, "column": 19, "nodeType": "741", "messageId": "742", "endLine": 25, "endColumn": 29}, "no-unused-vars", "'PublicStorePage' is defined but never used.", "Identifier", "unusedVar", "'Error<PERSON>allback' is assigned a value but never used.", "'ProductImage' is defined but never used.", "'LoadingWrapper' is defined but never used.", "'SectionLoading' is defined but never used.", "'LoadingButton' is defined but never used.", "'PageError' is defined but never used.", "react/jsx-no-undef", "'LazyLoadImage' is not defined.", "JSXIdentifier", "undefined", "no-undef", "'getProductImageUrl' is not defined.", "undef", "'handleImageError' is not defined.", "'addError' is assigned a value but never used.", "'handleApiError' is assigned a value but never used.", "'setGlobalLoading' is assigned a value but never used.", "'withLoading' is assigned a value but never used.", "'TrendingUp' is defined but never used.", "'showSuccess' is assigned a value but never used.", "'clearError' is assigned a value but never used.", "'clearLoading' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isCheckingOut' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDefaultAddress'. Either include it or remove the dependency array.", "ArrayExpression", ["913"], "'postTypes' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'recoverCart'. Either include it or remove the dependency array.", ["914"], "'LoadingSpinner' is defined but never used.", "'user' is assigned a value but never used.", "'success' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["915"], ["916"], "'Sparkles' is defined but never used.", "'Grid' is defined but never used.", "'List' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchCategoryProducts'. Either include it or remove the dependency array.", ["917"], "'Plus' is defined but never used.", "'Globe' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", ["918"], "React Hook useEffect has a missing dependency: 'fetchOrderDetails'. Either include it or remove the dependency array.", ["919"], "React Hook useEffect has a missing dependency: 'fetchPolicyData'. Either include it or remove the dependency array.", ["920"], "React Hook useEffect has a missing dependency: 'fetchFaqData'. Either include it or remove the dependency array.", ["921"], "React Hook useMemo has a missing dependency: 'currentFaqData'. Either include it or remove the dependency array.", ["922"], "React Hook useEffect has a missing dependency: 'fetchTickets'. Either include it or remove the dependency array.", ["923"], "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["924"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'fetchRecentlyViewed'. Either include it or remove the dependency array.", ["925"], "React Hook useEffect has a missing dependency: 'calculateShipping'. Either include it or remove the dependency array.", ["926"], "'useMemo' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateTax'. Either include it or remove the dependency array.", ["927"], "React Hook useEffect has a missing dependency: 'fetchAvailableCoupons'. Either include it or remove the dependency array.", ["928"], "React Hook useEffect has a missing dependency: 'fetchProductVariants'. Either include it or remove the dependency array.", ["929"], "React Hook useEffect has a missing dependency: 'fetchProductImages'. Either include it or remove the dependency array.", ["930"], "'useApi' is defined but never used.", ["931"], "React Hook useEffect has missing dependencies: 'fetchProducts' and 'searchTerm'. Either include them or remove the dependency array.", ["932"], "React Hook useCallback has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["933"], "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["934"], "React Hook useEffect has missing dependencies: 'fetchCurrentCart' and 'fetchSavedCarts'. Either include them or remove the dependency array.", ["935"], "'Package' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchProductDetails'. Either include it or remove the dependency array.", ["936"], "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["937"], "React Hook useEffect has a missing dependency: 'fetchSmartBundles'. Either include it or remove the dependency array.", ["938"], "React Hook useEffect has a missing dependency: 'fetchAddresses'. Either include it or remove the dependency array.", ["939"], "'handleRatingRangeChange' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleKeyDown'. Either include it or remove the dependency array.", ["940"], "'Star' is defined but never used.", "'Minus' is defined but never used.", "'Calendar' is defined but never used.", "'MapPin' is defined but never used.", "'ExternalLink' is defined but never used.", "'getLoadingPlaceholder' is defined but never used.", "'LazyLoadImage' is defined but never used.", "'calculateDiscountPercentage' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchShippingData'. Either include it or remove the dependency array.", ["941"], "'ThumbsUp' is defined but never used.", "'Flame' is defined but never used.", "'Smile' is defined but never used.", "'formatDate' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'closeDialog' and 'generateId'. Either include them or remove the dependency array.", ["942"], ["943"], ["944"], ["945"], ["946"], ["947"], "'Trash2' is defined but never used.", "'Save' is defined but never used.", "'Edit3' is defined but never used.", "'post' is assigned a value but never used.", "'get' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedPreferences'. Either include it or remove the dependency array.", ["948"], "'X' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["949"], "'setCouponDiscount' is assigned a value but never used.", "'setShippingCost' is assigned a value but never used.", "'setTaxAmount' is assigned a value but never used.", "'setAppliedCoupon' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'createGuestSession'. Either include it or remove the dependency array.", ["950"], "'info' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'clearError' and 'reportError'. Either include them or remove the dependency array.", ["951"], "'message' is assigned a value but never used.", "The 'defaultFallbacks' object makes the dependencies of useCallback Hook (at line 227) change on every render. To fix this, wrap the initialization of 'defaultFallbacks' in its own useMemo() Hook.", "VariableDeclarator", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'attempts'.", "ArrowFunctionExpression", "unsafeRefs", "'seller' is assigned a value but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", ["952"], ["953"], "React Hook useEffect has a missing dependency: 'fetchProduct'. Either include it or remove the dependency array.", ["954"], "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["955"], "React Hook useEffect has a missing dependency: 'fetchStoreProfile'. Either include it or remove the dependency array.", ["956"], ["957"], "React Hook useEffect has a missing dependency: 'fetchStoreProducts'. Either include it or remove the dependency array.", ["958"], "React Hook useEffect has a missing dependency: 'fetchEarnings'. Either include it or remove the dependency array.", ["959"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["960"], "React Hook useEffect has a missing dependency: 'fetchSellers'. Either include it or remove the dependency array.", ["961"], "React Hook useEffect has a missing dependency: 'fetchSellerStore'. Either include it or remove the dependency array.", ["962"], "React Hook useEffect has a missing dependency: 'fetchTrackingData'. Either include it or remove the dependency array.", ["963"], "'setLoading' is assigned a value but never used.", {"desc": "964", "fix": "965"}, {"desc": "966", "fix": "967"}, {"desc": "968", "fix": "969"}, {"desc": "970", "fix": "971"}, {"desc": "972", "fix": "973"}, {"desc": "974", "fix": "975"}, {"desc": "976", "fix": "977"}, {"desc": "978", "fix": "979"}, {"desc": "980", "fix": "981"}, {"desc": "982", "fix": "983"}, {"desc": "984", "fix": "985"}, {"desc": "986", "fix": "987"}, {"desc": "988", "fix": "989"}, {"desc": "990", "fix": "991"}, {"desc": "992", "fix": "993"}, {"desc": "994", "fix": "995"}, {"desc": "996", "fix": "997"}, {"desc": "998", "fix": "999"}, {"desc": "968", "fix": "1000"}, {"desc": "1001", "fix": "1002"}, {"desc": "1003", "fix": "1004"}, {"desc": "1005", "fix": "1006"}, {"desc": "1007", "fix": "1008"}, {"desc": "1009", "fix": "1010"}, {"desc": "1011", "fix": "1012"}, {"desc": "1013", "fix": "1014"}, {"desc": "1015", "fix": "1016"}, {"desc": "1017", "fix": "1018"}, {"desc": "1019", "fix": "1020"}, {"desc": "1021", "fix": "1022"}, {"desc": "1021", "fix": "1023"}, {"desc": "1021", "fix": "1024"}, {"desc": "1021", "fix": "1025"}, {"desc": "1021", "fix": "1026"}, {"desc": "1021", "fix": "1027"}, {"desc": "1028", "fix": "1029"}, {"desc": "1030", "fix": "1031"}, {"desc": "1032", "fix": "1033"}, {"desc": "1034", "fix": "1035"}, {"desc": "1036", "fix": "1037"}, {"desc": "1038", "fix": "1039"}, {"desc": "1040", "fix": "1041"}, {"desc": "1042", "fix": "1043"}, {"desc": "1044", "fix": "1045"}, {"desc": "1046", "fix": "1047"}, {"desc": "1048", "fix": "1049"}, {"desc": "1050", "fix": "1051"}, {"desc": "1052", "fix": "1053"}, {"desc": "1054", "fix": "1055"}, {"desc": "1056", "fix": "1057"}, {"desc": "1058", "fix": "1059"}, "Update the dependencies array to be: [token, cartLoading, fetchDefaultAddress]", {"range": "1060", "text": "1061"}, "Update the dependencies array to be: [recoverCart, recoveryToken]", {"range": "1062", "text": "1063"}, "Update the dependencies array to be: [fetchProducts]", {"range": "1064", "text": "1065"}, "Update the dependencies array to be: [debouncedSearchQuery, filters, sortBy, sortOrder, pagination.page, fetchProducts]", {"range": "1066", "text": "1067"}, "Update the dependencies array to be: [categoryName, filters, sortBy, sortOrder, pagination.page, fetchCategoryProducts]", {"range": "1068", "text": "1069"}, "Update the dependencies array to be: [fetchUserProfile, location.search]", {"range": "1070", "text": "1071"}, "Update the dependencies array to be: [fetchOrderDetails, orderNumber]", {"range": "1072", "text": "1073"}, "Update the dependencies array to be: [fetchPolicyData]", {"range": "1074", "text": "1075"}, "Update the dependencies array to be: [fetchFaqData]", {"range": "1076", "text": "1077"}, "Update the dependencies array to be: [currentFaqData, searchQuery, selectedCategory]", {"range": "1078", "text": "1079"}, "Update the dependencies array to be: [fetchTickets, token]", {"range": "1080", "text": "1081"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1082", "text": "1083"}, "Update the dependencies array to be: [fetchRecentlyViewed, limit]", {"range": "1084", "text": "1085"}, "Update the dependencies array to be: [selectedAddress, cartItems, calculateShipping]", {"range": "1086", "text": "1087"}, "Update the dependencies array to be: [selectedAddress, cartItems, calculateTax]", {"range": "1088", "text": "1089"}, "Update the dependencies array to be: [cartTotal, fetchAvailableCoupons]", {"range": "1090", "text": "1091"}, "Update the dependencies array to be: [fetchProductVariants, productId]", {"range": "1092", "text": "1093"}, "Update the dependencies array to be: [fetchProductImages, productId]", {"range": "1094", "text": "1095"}, {"range": "1096", "text": "1065"}, "Update the dependencies array to be: [debouncedSearchTerm, fetchProducts, isVisualSearchActive, searchTerm]", {"range": "1097", "text": "1098"}, "Update the dependencies array to be: [debouncedSearchTerm, fetchProducts]", {"range": "1099", "text": "1100"}, "Update the dependencies array to be: [fetchReviews, productId, sortBy]", {"range": "1101", "text": "1102"}, "Update the dependencies array to be: [fetchCurrentCart, fetchSavedCarts, token]", {"range": "1103", "text": "1104"}, "Update the dependencies array to be: [fetchProductDetails, productIds]", {"range": "1105", "text": "1106"}, "Update the dependencies array to be: [fetchNotifications, productId]", {"range": "1107", "text": "1108"}, "Update the dependencies array to be: [fetchSmartBundles, token]", {"range": "1109", "text": "1110"}, "Update the dependencies array to be: [fetchAddresses, token]", {"range": "1111", "text": "1112"}, "Update the dependencies array to be: [isVisible, suggestions, selectedIndex, handleKeyDown]", {"range": "1113", "text": "1114"}, "Update the dependencies array to be: [fetchShippingData]", {"range": "1115", "text": "1116"}, "Update the dependencies array to be: [closeDialog, generateId]", {"range": "1117", "text": "1118"}, {"range": "1119", "text": "1118"}, {"range": "1120", "text": "1118"}, {"range": "1121", "text": "1118"}, {"range": "1122", "text": "1118"}, {"range": "1123", "text": "1118"}, "Update the dependencies array to be: [loadSavedPreferences]", {"range": "1124", "text": "1125"}, "Update the dependencies array to be: [removeNotification]", {"range": "1126", "text": "1127"}, "Update the dependencies array to be: [createGuestSession]", {"range": "1128", "text": "1129"}, "Update the dependencies array to be: [clearError, reportError, showNotification]", {"range": "1130", "text": "1131"}, "Update the dependencies array to be: [fetchDashboardData, isAuthenticated, navigate]", {"range": "1132", "text": "1133"}, "Update the dependencies array to be: [isAuthenticated, navigate, filters, fetchProducts]", {"range": "1134", "text": "1135"}, "Update the dependencies array to be: [isAuthenticated, navigate, productId, isEditMode, fetchProduct]", {"range": "1136", "text": "1137"}, "Update the dependencies array to be: [isAuthenticated, navigate, filters, fetchOrders]", {"range": "1138", "text": "1139"}, "Update the dependencies array to be: [fetchStoreProfile, isAuthenticated, navigate]", {"range": "1140", "text": "1141"}, "Update the dependencies array to be: [fetchStoreProfile, storeSlug]", {"range": "1142", "text": "1143"}, "Update the dependencies array to be: [store, filters, fetchStoreProducts]", {"range": "1144", "text": "1145"}, "Update the dependencies array to be: [fetchEarnings, isAuthenticated, navigate]", {"range": "1146", "text": "1147"}, "Update the dependencies array to be: [fetchAnalyticsData, selectedPeriod]", {"range": "1148", "text": "1149"}, "Update the dependencies array to be: [fetchSellers, filters]", {"range": "1150", "text": "1151"}, "Update the dependencies array to be: [storeSlug, currentPage, category, sortBy, sortOrder, fetchSellerStore]", {"range": "1152", "text": "1153"}, "Update the dependencies array to be: [fetchTrackingData, orderId, trackingNumber]", {"range": "1154", "text": "1155"}, [1983, 2003], "[token, cart<PERSON>oading, fetchDefaultAddress]", [533, 548], "[recoverCart, recoveryToken]", [4516, 4518], "[fetchProducts]", [4745, 4812], "[debouncedSearchQuery, filters, sortBy, sortOrder, pagination.page, fetchProducts]", [6197, 6256], "[categoryName, filters, sortBy, sortOrder, pagination.page, fetchCategoryProducts]", [1111, 1128], "[fetchUserProfile, location.search]", [483, 496], "[fetch<PERSON><PERSON>rDetails, orderNumber]", [3423, 3425], "[fetchPolicyData]", [7508, 7510], "[fetchFaqData]", [9226, 9257], "[currentFaqData, searchQuery, selectedCategory]", [469, 476], "[fetchTickets, token]", [566, 568], "[fetchDashboardData]", [466, 473], "[fetchRecentlyViewed, limit]", [518, 546], "[selected<PERSON><PERSON><PERSON>, cartItems, calculateShipping]", [420, 448], "[selected<PERSON><PERSON><PERSON>, cartItems, calculateTax]", [664, 675], "[cartTotal, fetchAvailableCoupons]", [488, 499], "[fetchProductVariants, productId]", [642, 653], "[fetchProductImages, productId]", [4444, 4446], [4962, 5005], "[debouncedSearchTerm, fetchProducts, isVisualSearchActive, searchTerm]", [5747, 5768], "[debouncedSearchTerm, fetchProducts]", [729, 748], "[fetchReviews, productId, sortBy]", [596, 603], "[fetchCur<PERSON>Cart, fetchSavedCarts, token]", [687, 699], "[fetchProductDetails, productIds]", [839, 850], "[fetchNotifications, productId]", [527, 534], "[fetchSmartBundles, token]", [816, 823], "[fetchAddresses, token]", [2431, 2470], "[isVisible, suggestions, selectedIndex, handleKeyDown]", [2808, 2810], "[fetchShippingData]", [1107, 1109], "[closeDialog, generateId]", [1594, 1596], [2073, 2075], [2560, 2562], [3343, 3345], [3800, 3802], [4501, 4503], "[loadSavedPreferences]", [1157, 1159], "[removeNotification]", [1680, 1682], "[createGuestSession]", [2314, 2332], "[clearError, reportError, showNotification]", [1027, 1054], "[fetchDashboard<PERSON><PERSON>, isAuthenticated, navigate]", [1109, 1145], "[isAuthenticated, navigate, filters, fetchProducts]", [1517, 1567], "[isAuthenticated, navigate, productId, isEditMode, fetchProduct]", [1085, 1121], "[isAuthenticated, navigate, filters, fetchOrders]", [1319, 1346], "[fetchStoreProfile, isAuthenticated, navigate]", [975, 986], "[fetchStoreProfile, storeSlug]", [1066, 1082], "[store, filters, fetchStoreProducts]", [1173, 1200], "[fetchEarnings, isAuthenticated, navigate]", [607, 623], "[fetchAnalyticsData, selectedPeriod]", [806, 815], "[fetchSellers, filters]", [1398, 1451], "[storeSlug, currentPage, category, sortBy, sortOrder, fetchSellerStore]", [629, 654], "[fetchTrackingData, orderId, trackingNumber]"]