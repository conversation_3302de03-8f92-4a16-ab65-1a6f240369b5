import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import AdvancedSearch from './AdvancedSearch';
import SearchResults from './SearchResults';
import SearchAnalytics from './SearchAnalytics';
import SearchFilters from './SearchFilters';
import { useSearchHistory } from './SearchAnalytics';

const SearchPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { searchHistory, addToHistory } = useSearchHistory();
  
  const [searchResults, setSearchResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [currentQuery, setCurrentQuery] = useState('');
  const [currentFilters, setCurrentFilters] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Parse URL parameters on mount and when location changes
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const query = params.get('q') || '';
    const page = parseInt(params.get('page')) || 1;
    
    // Parse filters from URL
    const filters = {};
    params.forEach((value, key) => {
      if (key !== 'q' && key !== 'page') {
        if (key.includes(',')) {
          filters[key] = value.split(',');
        } else {
          filters[key] = value;
        }
      }
    });

    setCurrentQuery(query);
    setCurrentFilters(filters);
    setCurrentPage(page);

    // Perform search if there's a query or filters
    if (query || Object.keys(filters).length > 0) {
      performSearch(query, filters, page);
    }
  }, [location.search]);

  const updateURL = (query, filters, page = 1) => {
    const params = new URLSearchParams();
    
    if (query) {
      params.set('q', query);
    }
    
    if (page > 1) {
      params.set('page', page.toString());
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','));
        } else if (!Array.isArray(value)) {
          params.set(key, value.toString());
        }
      }
    });

    const newSearch = params.toString();
    const newPath = newSearch ? `/search?${newSearch}` : '/search';
    
    if (location.pathname + location.search !== newPath) {
      navigate(newPath, { replace: true });
    }
  };

  const performSearch = async (query = currentQuery, filters = currentFilters, page = 1) => {
    setLoading(true);
    setCurrentQuery(query);
    setCurrentFilters(filters);
    setCurrentPage(page);

    try {
      // This will be handled by the AdvancedSearch component
      // We just need to update the URL
      updateURL(query, filters, page);
      
      // Add to search history if there's a meaningful query
      if (query.trim()) {
        addToHistory(query, filters);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults({ products: [], total: 0, error: 'Search failed' });
    } finally {
      setLoading(false);
    }
  };

  const handleSearchResults = (results) => {
    setSearchResults(results);
    setLoading(false);
  };

  const handleSortChange = (sortBy, sortOrder) => {
    const newFilters = { ...currentFilters, sort_by: sortBy, sort_order: sortOrder };
    performSearch(currentQuery, newFilters, 1);
  };

  const handlePageChange = (page) => {
    performSearch(currentQuery, currentFilters, page);
  };

  const handleFiltersChange = (newFilters) => {
    performSearch(currentQuery, newFilters, 1);
  };

  const clearAllFilters = () => {
    performSearch(currentQuery, {}, 1);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Product Search</h1>
          <p className="text-gray-600">
            Find sustainable products with our advanced search capabilities
          </p>
        </div>

        {/* Advanced Search Interface */}
        <AdvancedSearch
          onSearchResults={handleSearchResults}
          initialQuery={currentQuery}
          initialFilters={currentFilters}
          showFilters={true}
          showComplexSearch={true}
        />

        {/* Search Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                showFilters
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              <svg className="h-4 w-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
              </svg>
              Filters
            </button>

            <button
              onClick={() => setShowAnalytics(!showAnalytics)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                showAnalytics
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              <svg className="h-4 w-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Analytics
            </button>

            {Object.keys(currentFilters).length > 0 && (
              <button
                onClick={clearAllFilters}
                className="px-4 py-2 text-sm text-red-600 hover:text-red-700 border border-red-600 rounded-md hover:bg-red-50 transition-colors"
              >
                Clear All Filters
              </button>
            )}
          </div>

          {/* Search History */}
          {searchHistory.length > 0 && (
            <div className="relative">
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    const historyItem = JSON.parse(e.target.value);
                    performSearch(historyItem.query, historyItem.filters, 1);
                  }
                }}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
                defaultValue=""
              >
                <option value="">Recent Searches</option>
                {searchHistory.map((item, index) => (
                  <option key={index} value={JSON.stringify(item)}>
                    {item.query || 'Filtered Search'} - {new Date(item.timestamp).toLocaleDateString()}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          {showFilters && (
            <div className="lg:w-1/4">
              <div className="sticky top-8">
                <SearchFilters
                  filters={currentFilters}
                  onFiltersChange={handleFiltersChange}
                  onClearFilters={clearAllFilters}
                  isLoading={loading}
                />
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className={`${showFilters ? 'lg:w-3/4' : 'w-full'}`}>
            {/* Search Analytics */}
            {showAnalytics && searchResults && (
              <SearchAnalytics
                searchResults={searchResults}
                searchQuery={currentQuery}
                searchFilters={currentFilters}
                showDetailedAnalytics={true}
              />
            )}

            {/* Search Results */}
            <SearchResults
              results={searchResults}
              loading={loading}
              onSortChange={handleSortChange}
              onPageChange={handlePageChange}
              currentPage={currentPage}
              showAnalytics={!showAnalytics} // Show basic analytics if detailed analytics are hidden
            />

            {/* No Search State */}
            {!searchResults && !loading && (
              <div className="text-center py-12">
                <div className="text-gray-500 mb-4">
                  <svg className="h-16 w-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Start Your Search</h3>
                <p className="text-gray-600 mb-6">
                  Enter keywords above to find sustainable products, or use our advanced search features.
                </p>
                
                {/* Search Suggestions */}
                <div className="max-w-md mx-auto">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Popular Searches:</h4>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {['organic cotton', 'bamboo products', 'eco-friendly', 'sustainable fashion', 'zero waste'].map((suggestion) => (
                      <button
                        key={suggestion}
                        onClick={() => performSearch(suggestion, {}, 1)}
                        className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Search Tips */}
        <div className="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Search Tips</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Simple Search</h4>
              <p className="text-sm text-gray-600">
                Enter keywords to find products. Use quotes for exact phrases: "organic cotton shirt"
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Advanced Search</h4>
              <p className="text-sm text-gray-600">
                Use filters to narrow down results by category, price, rating, and sustainability features.
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Complex Boolean</h4>
              <p className="text-sm text-gray-600">
                Combine multiple search terms with MUST, SHOULD, and MUST NOT operators for precise results.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
