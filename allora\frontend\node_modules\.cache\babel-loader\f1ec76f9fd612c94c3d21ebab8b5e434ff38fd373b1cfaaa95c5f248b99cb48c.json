{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\contexts\\\\SellerAuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SellerAuthContext = /*#__PURE__*/createContext();\nexport const useSellerAuth = () => {\n  _s();\n  const context = useContext(SellerAuthContext);\n  if (!context) {\n    throw new Error('useSellerAuth must be used within a SellerAuthProvider');\n  }\n  return context;\n};\n_s(useSellerAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const SellerAuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [seller, setSeller] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const savedToken = localStorage.getItem('sellerToken');\n    const savedSeller = localStorage.getItem('sellerData');\n    if (savedToken && savedSeller) {\n      try {\n        setToken(savedToken);\n        setSeller(JSON.parse(savedSeller));\n      } catch (error) {\n        console.error('Error parsing saved seller data:', error);\n        localStorage.removeItem('sellerToken');\n        localStorage.removeItem('sellerData');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = (sellerData, authToken) => {\n    setSeller(sellerData);\n    setToken(authToken);\n    localStorage.setItem('sellerToken', authToken);\n    localStorage.setItem('sellerData', JSON.stringify(sellerData));\n  };\n  const logout = () => {\n    setSeller(null);\n    setToken(null);\n    localStorage.removeItem('sellerToken');\n    localStorage.removeItem('sellerData');\n  };\n  const updateSeller = updatedData => {\n    const newSellerData = {\n      ...seller,\n      ...updatedData\n    };\n    setSeller(newSellerData);\n    localStorage.setItem('sellerData', JSON.stringify(newSellerData));\n  };\n  const isAuthenticated = () => {\n    return !!(seller && token);\n  };\n  const getAuthHeaders = () => {\n    return token ? {\n      'Authorization': `Bearer ${token}`\n    } : {};\n  };\n  const value = {\n    seller,\n    token,\n    loading,\n    login,\n    logout,\n    updateSeller,\n    isAuthenticated,\n    getAuthHeaders\n  };\n  return /*#__PURE__*/_jsxDEV(SellerAuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s2(SellerAuthProvider, \"Yn0r4HDuU0zUEKd9ZtSaTsIxWpg=\");\n_c = SellerAuthProvider;\nexport default SellerAuthContext;\nvar _c;\n$RefreshReg$(_c, \"SellerAuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "SellerAuthContext", "useSellerAuth", "_s", "context", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s2", "seller", "setSeller", "token", "setToken", "loading", "setLoading", "savedToken", "localStorage", "getItem", "savedSeller", "JSON", "parse", "error", "console", "removeItem", "login", "sellerData", "authToken", "setItem", "stringify", "logout", "updateSeller", "updatedData", "newSellerData", "isAuthenticated", "getAuthHeaders", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/contexts/SellerAuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst SellerAuthContext = createContext();\n\nexport const useSellerAuth = () => {\n  const context = useContext(SellerAuthContext);\n  if (!context) {\n    throw new Error('useSellerAuth must be used within a SellerAuthProvider');\n  }\n  return context;\n};\n\nexport const SellerAuthProvider = ({ children }) => {\n  const [seller, setSeller] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const savedToken = localStorage.getItem('sellerToken');\n    const savedSeller = localStorage.getItem('sellerData');\n    \n    if (savedToken && savedSeller) {\n      try {\n        setToken(savedToken);\n        setSeller(JSON.parse(savedSeller));\n      } catch (error) {\n        console.error('Error parsing saved seller data:', error);\n        localStorage.removeItem('sellerToken');\n        localStorage.removeItem('sellerData');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const login = (sellerData, authToken) => {\n    setSeller(sellerData);\n    setToken(authToken);\n    localStorage.setItem('sellerToken', authToken);\n    localStorage.setItem('sellerData', JSON.stringify(sellerData));\n  };\n\n  const logout = () => {\n    setSeller(null);\n    setToken(null);\n    localStorage.removeItem('sellerToken');\n    localStorage.removeItem('sellerData');\n  };\n\n  const updateSeller = (updatedData) => {\n    const newSellerData = { ...seller, ...updatedData };\n    setSeller(newSellerData);\n    localStorage.setItem('sellerData', JSON.stringify(newSellerData));\n  };\n\n  const isAuthenticated = () => {\n    return !!(seller && token);\n  };\n\n  const getAuthHeaders = () => {\n    return token ? { 'Authorization': `Bearer ${token}` } : {};\n  };\n\n  const value = {\n    seller,\n    token,\n    loading,\n    login,\n    logout,\n    updateSeller,\n    isAuthenticated,\n    getAuthHeaders\n  };\n\n  return (\n    <SellerAuthContext.Provider value={value}>\n      {children}\n    </SellerAuthContext.Provider>\n  );\n};\n\nexport default SellerAuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,iBAAiB,gBAAGN,aAAa,CAAC,CAAC;AAEzC,OAAO,MAAMO,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,OAAO,GAAGR,UAAU,CAACK,iBAAiB,CAAC;EAC7C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,wDAAwD,CAAC;EAC3E;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,aAAa;AAQ1B,OAAO,MAAMI,kBAAkB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAClD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACtD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAEtD,IAAIF,UAAU,IAAIG,WAAW,EAAE;MAC7B,IAAI;QACFN,QAAQ,CAACG,UAAU,CAAC;QACpBL,SAAS,CAACS,IAAI,CAACC,KAAK,CAACF,WAAW,CAAC,CAAC;MACpC,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDL,YAAY,CAACO,UAAU,CAAC,aAAa,CAAC;QACtCP,YAAY,CAACO,UAAU,CAAC,YAAY,CAAC;MACvC;IACF;IACAT,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,KAAK,GAAGA,CAACC,UAAU,EAAEC,SAAS,KAAK;IACvChB,SAAS,CAACe,UAAU,CAAC;IACrBb,QAAQ,CAACc,SAAS,CAAC;IACnBV,YAAY,CAACW,OAAO,CAAC,aAAa,EAAED,SAAS,CAAC;IAC9CV,YAAY,CAACW,OAAO,CAAC,YAAY,EAAER,IAAI,CAACS,SAAS,CAACH,UAAU,CAAC,CAAC;EAChE,CAAC;EAED,MAAMI,MAAM,GAAGA,CAAA,KAAM;IACnBnB,SAAS,CAAC,IAAI,CAAC;IACfE,QAAQ,CAAC,IAAI,CAAC;IACdI,YAAY,CAACO,UAAU,CAAC,aAAa,CAAC;IACtCP,YAAY,CAACO,UAAU,CAAC,YAAY,CAAC;EACvC,CAAC;EAED,MAAMO,YAAY,GAAIC,WAAW,IAAK;IACpC,MAAMC,aAAa,GAAG;MAAE,GAAGvB,MAAM;MAAE,GAAGsB;IAAY,CAAC;IACnDrB,SAAS,CAACsB,aAAa,CAAC;IACxBhB,YAAY,CAACW,OAAO,CAAC,YAAY,EAAER,IAAI,CAACS,SAAS,CAACI,aAAa,CAAC,CAAC;EACnE,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,EAAExB,MAAM,IAAIE,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMuB,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOvB,KAAK,GAAG;MAAE,eAAe,EAAE,UAAUA,KAAK;IAAG,CAAC,GAAG,CAAC,CAAC;EAC5D,CAAC;EAED,MAAMwB,KAAK,GAAG;IACZ1B,MAAM;IACNE,KAAK;IACLE,OAAO;IACPW,KAAK;IACLK,MAAM;IACNC,YAAY;IACZG,eAAe;IACfC;EACF,CAAC;EAED,oBACElC,OAAA,CAACC,iBAAiB,CAACmC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA5B,QAAA,EACtCA;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB,CAAC;AAEjC,CAAC;AAAChC,GAAA,CAnEWF,kBAAkB;AAAAmC,EAAA,GAAlBnC,kBAAkB;AAqE/B,eAAeL,iBAAiB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}