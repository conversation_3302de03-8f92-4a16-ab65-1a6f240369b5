import React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react';
import { useNotification } from './NotificationContext';

const WebSocketContext = createContext();

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

export const WebSocketProvider = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [lastMessage, setLastMessage] = useState(null);
  const [messageHistory, setMessageHistory] = useState([]);
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const { info, error: showError } = useNotification();

  // Event listeners for different message types
  const eventListeners = useRef({
    cart_updated: [],
    inventory_updated: [],
    order_status_changed: [],
    notification: [],
    user_activity: []
  });

  const getWebSocketUrl = useCallback(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const token = localStorage.getItem('token');
    const guestSession = localStorage.getItem('guest_session_id');
    
    let url = `${protocol}//${host}/ws`;
    const params = new URLSearchParams();
    
    if (token) {
      params.append('token', token);
    } else if (guestSession) {
      params.append('guest_session', guestSession);
    }
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    return url;
  }, []);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      setConnectionStatus('connecting');
      const wsUrl = getWebSocketUrl();
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttempts.current = 0;
        
        // Send initial connection message
        const connectionMessage = {
          type: 'connection',
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent
        };
        
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify(connectionMessage));
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          setLastMessage(message);
          setMessageHistory(prev => [...prev.slice(-99), message]); // Keep last 100 messages
          
          // Trigger event listeners
          const messageType = message.type || message.event;
          if (eventListeners.current[messageType]) {
            eventListeners.current[messageType].forEach(listener => {
              try {
                listener(message);
              } catch (error) {
                console.error('Error in WebSocket event listener:', error);
              }
            });
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setConnectionStatus('disconnected');
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          setConnectionStatus('reconnecting');
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        } else if (reconnectAttempts.current >= maxReconnectAttempts) {
          setConnectionStatus('failed');
          showError('Connection lost. Please refresh the page to reconnect.');
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionStatus('error');
    }
  }, [getWebSocketUrl, showError]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected');
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setConnectionStatus('disconnected');
  }, []);

  const sendMessage = useCallback((message) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        const messageWithTimestamp = {
          ...message,
          timestamp: new Date().toISOString()
        };
        wsRef.current.send(JSON.stringify(messageWithTimestamp));
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        return false;
      }
    } else {
      console.warn('WebSocket is not connected');
      return false;
    }
  }, []);

  const addEventListener = useCallback((eventType, listener) => {
    if (!eventListeners.current[eventType]) {
      eventListeners.current[eventType] = [];
    }
    eventListeners.current[eventType].push(listener);
    
    // Return cleanup function
    return () => {
      eventListeners.current[eventType] = eventListeners.current[eventType].filter(
        l => l !== listener
      );
    };
  }, []);

  const removeEventListener = useCallback((eventType, listener) => {
    if (eventListeners.current[eventType]) {
      eventListeners.current[eventType] = eventListeners.current[eventType].filter(
        l => l !== listener
      );
    }
  }, []);

  // Auto-connect on mount and auth changes
  useEffect(() => {
    connect();
    
    // Listen for auth changes
    const handleStorageChange = (e) => {
      if (e.key === 'token' || e.key === 'guest_session_id') {
        disconnect();
        setTimeout(connect, 1000); // Reconnect with new credentials
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  const value = {
    isConnected,
    connectionStatus,
    lastMessage,
    messageHistory,
    connect,
    disconnect,
    sendMessage,
    addEventListener,
    removeEventListener
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

export default WebSocketContext;
