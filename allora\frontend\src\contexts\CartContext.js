import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { API_BASE_URL } from '../config/api';
import { useWebSocket } from './WebSocketContext';

const CartContext = createContext();

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);
  const [cartCount, setCartCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [guestSession, setGuestSession] = useState(null);
  const { addEventListener, isConnected } = useWebSocket();

  // Define createGuestSession first
  const createGuestSession = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/guest/session`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });

      if (response.ok) {
        const data = await response.json();
        setGuestSession(data.session_id);
        localStorage.setItem('guest_session_id', data.session_id);
        return data.session_id;
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create guest session');
      }
    } catch (error) {
      console.error('Failed to create guest session:', error);
      throw error;
    }
  }, []);

  // Initialize guest session
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      const existingSession = localStorage.getItem('guest_session_id');
      if (existingSession) {
        setGuestSession(existingSession);
      } else {
        createGuestSession();
      }
    }
  }, [createGuestSession]);

  // Fetch cart items and update count
  const fetchCart = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      let response;

      if (token) {
        // Authenticated user cart
        response = await fetch(`${API_BASE_URL}/cart`, {
          headers: { 'Authorization': token },
        });
      } else if (guestSession) {
        // Guest user cart
        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`);
      } else {
        setLoading(false);
        return;
      }

      if (response.ok) {
        const data = await response.json();
        // Handle both old format (direct array) and new format (with items property)
        const items = Array.isArray(data) ? data : (data.items || []);
        setCartItems(items);

        // Calculate total quantity for cart count
        const totalCount = items.reduce((total, item) => total + (item.quantity || 0), 0);
        setCartCount(totalCount);
      } else {
        setCartItems([]);
        setCartCount(0);
      }
    } catch (error) {
      console.error('Failed to fetch cart:', error);
      setCartItems([]);
      setCartCount(0);
    } finally {
      setLoading(false);
    }
  }, [guestSession]);

  // Add item to cart
  const addToCart = async (productId, quantity = 1) => {
    try {
      // Validate input parameters
      if (productId === null || productId === undefined || productId === '' || productId === 0) {
        throw new Error('Product ID is required');
      }

      // Convert to string first, then parse to handle various input types
      const productIdStr = String(productId).trim();
      const quantityStr = String(quantity).trim();

      // Ensure productId and quantity are integers
      const validProductId = parseInt(productIdStr, 10);
      const validQuantity = parseInt(quantityStr, 10);

      if (isNaN(validProductId) || validProductId <= 0) {
        throw new Error(`Invalid product ID: ${productId}`);
      }

      if (isNaN(validQuantity) || validQuantity <= 0) {
        throw new Error(`Invalid quantity: ${quantity}`);
      }

      const token = localStorage.getItem('token');
      let response;

      if (token) {
        // Authenticated user
        response = await fetch(`${API_BASE_URL}/cart`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token,
          },
          body: JSON.stringify({ product_id: validProductId, quantity: validQuantity }),
        });
      } else {
        // Guest user - ensure we have a session
        let currentGuestSession = guestSession || localStorage.getItem('guest_session_id');

        if (!currentGuestSession) {
          // Create guest session if it doesn't exist
          currentGuestSession = await createGuestSession();
        }

        if (currentGuestSession) {
          response = await fetch(`${API_BASE_URL}/guest/cart/${currentGuestSession}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ product_id: validProductId, quantity: validQuantity }),
          });
        } else {
          throw new Error('Failed to create guest session');
        }
      }

      if (response && response.ok) {
        // Refresh cart after adding
        await fetchCart();
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to add item to cart');
      }
    } catch (error) {
      console.error('Failed to add to cart:', error);
      throw error; // Re-throw to let the calling component handle it
    }
  };

  // Update cart item quantity
  const updateCartItem = async (cartItemId, quantity) => {
    try {
      const token = localStorage.getItem('token');
      let response;

      if (token) {
        response = await fetch(`${API_BASE_URL}/cart`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token,
          },
          body: JSON.stringify({ cart_item_id: cartItemId, quantity }),
        });
      } else if (guestSession) {
        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ cart_item_id: cartItemId, quantity }),
        });
      }

      if (response && response.ok) {
        await fetchCart();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to update cart item:', error);
      return false;
    }
  };

  // Remove item from cart
  const removeFromCart = async (cartItemId) => {
    try {
      const token = localStorage.getItem('token');
      let response;

      if (token) {
        response = await fetch(`${API_BASE_URL}/cart`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token,
          },
          body: JSON.stringify({ cart_item_id: cartItemId }),
        });
      } else if (guestSession) {
        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`, {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ cart_item_id: cartItemId }),
        });
      }

      if (response && response.ok) {
        await fetchCart();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to remove from cart:', error);
      return false;
    }
  };

  // Clear entire cart
  const clearCart = () => {
    setCartItems([]);
    setCartCount(0);
  };

  // Fetch cart on mount and when authentication changes
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token || guestSession) {
      fetchCart();
    }
  }, [guestSession, fetchCart]);

  // Listen for storage changes (login/logout)
  useEffect(() => {
    const handleStorageChange = () => {
      fetchCart();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [fetchCart]);

  // Listen for real-time cart updates via WebSocket
  useEffect(() => {
    if (!isConnected) return;

    const handleCartUpdate = (message) => {
      console.log('Real-time cart update received:', message);

      if (message.data) {
        const { action, cart_items, cart_count, item } = message.data;

        switch (action) {
          case 'item_added':
            setCartItems(prev => {
              const existingIndex = prev.findIndex(cartItem => cartItem.id === item.id);
              if (existingIndex >= 0) {
                const updated = [...prev];
                updated[existingIndex] = { ...updated[existingIndex], quantity: item.quantity };
                return updated;
              }
              return [...prev, item];
            });
            setCartCount(prev => prev + (item.quantity || 1));
            break;

          case 'item_updated':
            setCartItems(prev => prev.map(cartItem =>
              cartItem.id === item.id ? { ...cartItem, ...item } : cartItem
            ));
            if (cart_count !== undefined) {
              setCartCount(cart_count);
            }
            break;

          case 'item_removed':
            setCartItems(prev => prev.filter(cartItem => cartItem.id !== item.id));
            setCartCount(prev => Math.max(0, prev - (item.quantity || 1)));
            break;

          case 'cart_cleared':
            setCartItems([]);
            setCartCount(0);
            break;

          case 'full_update':
            if (cart_items) {
              setCartItems(cart_items);
              const totalCount = cart_items.reduce((total, cartItem) => total + (cartItem.quantity || 0), 0);
              setCartCount(totalCount);
            }
            break;

          default:
            // Fallback to full cart refresh
            fetchCart();
        }
      }
    };

    const cleanup = addEventListener('cart_updated', handleCartUpdate);
    return cleanup;
  }, [isConnected, addEventListener, fetchCart]);

  const value = {
    cartItems,
    cartCount,
    loading,
    guestSession,
    fetchCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
