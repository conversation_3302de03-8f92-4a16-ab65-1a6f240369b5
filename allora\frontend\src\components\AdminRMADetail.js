import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import './AdminRMADetail.css';

const AdminRMADetail = () => {
    const { rmaNumber } = useParams();
    const navigate = useNavigate();
    
    const [rmaStatus, setRmaStatus] = useState(null);
    const [loading, setLoading] = useState(true);
    const [processing, setProcessing] = useState(false);
    const [showProcessModal, setShowProcessModal] = useState(false);
    const [processData, setProcessData] = useState({
        type: '', // 'approve', 'reject', 'process_return', 'complete_refund'
        notes: '',
        receivedItems: [],
        refundDetails: {}
    });

    useEffect(() => {
        fetchRMAStatus();
    }, [rmaNumber]);

    const fetchRMAStatus = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/rma/${rmaNumber}/status`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setRmaStatus(data.rma_status);
            } else if (response.status === 404) {
                toast.error('RMA request not found');
                navigate('/admin/rma');
            } else {
                toast.error('Failed to fetch RMA details');
            }
        } catch (error) {
            console.error('Error fetching RMA status:', error);
            toast.error('Error loading RMA details');
        } finally {
            setLoading(false);
        }
    };

    const handleProcess = async () => {
        if (!processData.type) return;

        setProcessing(true);
        try {
            const token = localStorage.getItem('token');
            let endpoint = '';
            let body = {};

            switch (processData.type) {
                case 'approve':
                    endpoint = `approve`;
                    body = { notes: processData.notes };
                    break;
                case 'reject':
                    endpoint = `reject`;
                    body = { reason: processData.notes };
                    break;
                case 'process_return':
                    endpoint = `process-return`;
                    body = { received_items: processData.receivedItems };
                    break;
                case 'complete_refund':
                    endpoint = `complete-refund`;
                    body = processData.refundDetails;
                    break;
                default:
                    return;
            }

            const response = await fetch(`/api/rma/admin/${rmaNumber}/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(body)
            });

            if (response.ok) {
                toast.success(`RMA ${processData.type.replace('_', ' ')} completed successfully`);
                setShowProcessModal(false);
                setProcessData({ type: '', notes: '', receivedItems: [], refundDetails: {} });
                fetchRMAStatus();
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || `Failed to ${processData.type.replace('_', ' ')}`);
            }
        } catch (error) {
            console.error('Error processing RMA:', error);
            toast.error('Error processing RMA');
        } finally {
            setProcessing(false);
        }
    };

    const getStatusColor = (status) => {
        const statusColors = {
            'pending': '#f39c12',
            'approved': '#27ae60',
            'rejected': '#e74c3c',
            'return_shipped': '#3498db',
            'return_received': '#9b59b6',
            'refund_completed': '#2ecc71',
            'cancelled': '#95a5a6'
        };
        return statusColors[status] || '#95a5a6';
    };

    const getStatusLabel = (status) => {
        const statusLabels = {
            'pending': 'Pending Review',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'return_shipped': 'Return Shipped',
            'return_received': 'Return Received',
            'refund_completed': 'Refund Completed',
            'cancelled': 'Cancelled'
        };
        return statusLabels[status] || status;
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString();
    };

    const formatCurrency = (amount) => {
        return `₹${parseFloat(amount).toFixed(2)}`;
    };

    const getAvailableActions = () => {
        if (!rmaStatus) return [];
        
        const actions = [];
        
        switch (rmaStatus.status) {
            case 'pending':
                actions.push(
                    { type: 'approve', label: 'Approve Request', color: '#27ae60' },
                    { type: 'reject', label: 'Reject Request', color: '#e74c3c' }
                );
                break;
            case 'return_received':
                actions.push(
                    { type: 'process_return', label: 'Process Return', color: '#9b59b6' }
                );
                break;
            case 'approved':
                if (rmaStatus.rma_type === 'return_refund') {
                    actions.push(
                        { type: 'complete_refund', label: 'Complete Refund', color: '#2ecc71' }
                    );
                }
                break;
        }
        
        return actions;
    };

    if (loading) {
        return (
            <div className="admin-rma-detail-container">
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>Loading RMA details...</p>
                </div>
            </div>
        );
    }

    if (!rmaStatus) {
        return (
            <div className="admin-rma-detail-container">
                <div className="error-message">
                    <h2>RMA Request Not Found</h2>
                    <p>The requested RMA could not be found.</p>
                    <button onClick={() => navigate('/admin/rma')} className="btn btn-primary">
                        Back to Dashboard
                    </button>
                </div>
            </div>
        );
    }

    const availableActions = getAvailableActions();

    return (
        <div className="admin-rma-detail-container">
            {/* Header */}
            <div className="admin-rma-detail-header">
                <div className="header-content">
                    <h1>RMA Request Details</h1>
                    <div className="rma-number">#{rmaStatus.rma_number}</div>
                </div>
                <div className="header-actions">
                    <button 
                        onClick={() => navigate('/admin/rma')}
                        className="btn btn-secondary"
                    >
                        Back to Dashboard
                    </button>
                    <button 
                        onClick={fetchRMAStatus}
                        className="btn btn-primary"
                    >
                        Refresh
                    </button>
                </div>
            </div>

            {/* Status and Actions */}
            <div className="status-actions-section">
                <div className="current-status">
                    <h3>Current Status</h3>
                    <div className="status-badge large" style={{ backgroundColor: getStatusColor(rmaStatus.status) }}>
                        {getStatusLabel(rmaStatus.status)}
                    </div>
                </div>
                
                {availableActions.length > 0 && (
                    <div className="available-actions">
                        <h3>Available Actions</h3>
                        <div className="action-buttons">
                            {availableActions.map(action => (
                                <button
                                    key={action.type}
                                    onClick={() => {
                                        setProcessData(prev => ({ ...prev, type: action.type }));
                                        setShowProcessModal(true);
                                    }}
                                    className="btn action-btn"
                                    style={{ backgroundColor: action.color }}
                                >
                                    {action.label}
                                </button>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {/* RMA Information */}
            <div className="rma-information">
                <h3>Request Information</h3>
                <div className="info-grid">
                    <div className="info-item">
                        <span className="label">Request Type:</span>
                        <span className="value">{rmaStatus.rma_type?.replace('_', ' ').toUpperCase()}</span>
                    </div>
                    <div className="info-item">
                        <span className="label">Customer Email:</span>
                        <span className="value">{rmaStatus.customer_email}</span>
                    </div>
                    <div className="info-item">
                        <span className="label">Order ID:</span>
                        <span className="value">#{rmaStatus.order?.order_number}</span>
                    </div>
                    <div className="info-item">
                        <span className="label">Created:</span>
                        <span className="value">{formatDate(rmaStatus.created_at)}</span>
                    </div>
                    <div className="info-item">
                        <span className="label">Last Updated:</span>
                        <span className="value">{formatDate(rmaStatus.updated_at)}</span>
                    </div>
                    <div className="info-item">
                        <span className="label">Total Refund:</span>
                        <span className="value highlight">{formatCurrency(rmaStatus.total_refund_amount)}</span>
                    </div>
                </div>
            </div>

            {/* Return Items */}
            <div className="return-items-section">
                <h3>Return Items</h3>
                <div className="items-list">
                    {rmaStatus.items?.map((item, index) => (
                        <div key={index} className="return-item-card">
                            <div className="item-image">
                                <img 
                                    src={item.product?.image_url || '/placeholder-product.jpg'} 
                                    alt={item.product?.name || 'Product'}
                                />
                            </div>
                            <div className="item-details">
                                <h4>{item.product?.name || 'Product'}</h4>
                                <div className="item-info">
                                    <p><strong>Quantity:</strong> {item.quantity}</p>
                                    <p><strong>Return Reason:</strong> {item.return_reason?.replace('_', ' ')}</p>
                                    <p><strong>Unit Price:</strong> {formatCurrency(item.unit_price)}</p>
                                    <p><strong>Total:</strong> {formatCurrency(item.unit_price * item.quantity)}</p>
                                </div>
                                {item.condition_notes && (
                                    <div className="condition-notes">
                                        <strong>Condition Notes:</strong>
                                        <p>{item.condition_notes}</p>
                                    </div>
                                )}
                            </div>
                            <div className="item-status">
                                <span className="status-badge small" style={{ backgroundColor: getStatusColor(item.status) }}>
                                    {getStatusLabel(item.status)}
                                </span>
                                {item.refund_amount && (
                                    <div className="refund-amount">
                                        Refund: {formatCurrency(item.refund_amount)}
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Timeline */}
            {rmaStatus.timeline && rmaStatus.timeline.length > 0 && (
                <div className="timeline-section">
                    <h3>Request Timeline</h3>
                    <div className="timeline">
                        {rmaStatus.timeline.map((event, index) => (
                            <div key={index} className="timeline-event">
                                <div className="timeline-marker"></div>
                                <div className="timeline-content">
                                    <div className="event-title">{event.event_type?.replace('_', ' ').toUpperCase()}</div>
                                    <div className="event-description">{event.event_description}</div>
                                    <div className="event-meta">
                                        <span className="event-date">{formatDate(event.created_at)}</span>
                                        {event.actor_type && (
                                            <span className="event-actor">by {event.actor_type}</span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Process Modal */}
            {showProcessModal && (
                <div className="modal-overlay">
                    <div className="modal">
                        <div className="modal-header">
                            <h3>
                                {processData.type === 'approve' && 'Approve RMA Request'}
                                {processData.type === 'reject' && 'Reject RMA Request'}
                                {processData.type === 'process_return' && 'Process Return Items'}
                                {processData.type === 'complete_refund' && 'Complete Refund'}
                            </h3>
                            <button 
                                onClick={() => setShowProcessModal(false)}
                                className="close-btn"
                            >
                                ×
                            </button>
                        </div>
                        
                        <div className="modal-body">
                            {(processData.type === 'approve' || processData.type === 'reject') && (
                                <div className="form-group">
                                    <label>
                                        {processData.type === 'approve' ? 'Approval Notes:' : 'Rejection Reason:'}
                                    </label>
                                    <textarea
                                        value={processData.notes}
                                        onChange={(e) => setProcessData(prev => ({ ...prev, notes: e.target.value }))}
                                        placeholder={processData.type === 'approve' 
                                            ? 'Add any notes for approval...' 
                                            : 'Please provide reason for rejection...'
                                        }
                                        className="form-control"
                                        rows="4"
                                        required={processData.type === 'reject'}
                                    />
                                </div>
                            )}
                            
                            {processData.type === 'complete_refund' && (
                                <div className="refund-form">
                                    <div className="form-group">
                                        <label>Refund Method:</label>
                                        <select
                                            value={processData.refundDetails.method || 'original_payment'}
                                            onChange={(e) => setProcessData(prev => ({
                                                ...prev,
                                                refundDetails: { ...prev.refundDetails, method: e.target.value }
                                            }))}
                                            className="form-control"
                                        >
                                            <option value="original_payment">Original Payment Method</option>
                                            <option value="bank_transfer">Bank Transfer</option>
                                            <option value="store_credit">Store Credit</option>
                                        </select>
                                    </div>
                                    
                                    <div className="form-group">
                                        <label>Gateway Refund ID:</label>
                                        <input
                                            type="text"
                                            value={processData.refundDetails.gateway_refund_id || ''}
                                            onChange={(e) => setProcessData(prev => ({
                                                ...prev,
                                                refundDetails: { ...prev.refundDetails, gateway_refund_id: e.target.value }
                                            }))}
                                            className="form-control"
                                            placeholder="Enter gateway refund ID..."
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                        
                        <div className="modal-footer">
                            <button 
                                onClick={() => setShowProcessModal(false)}
                                className="btn btn-secondary"
                                disabled={processing}
                            >
                                Cancel
                            </button>
                            <button 
                                onClick={handleProcess}
                                disabled={processing || (processData.type === 'reject' && !processData.notes)}
                                className="btn btn-primary"
                            >
                                {processing ? 'Processing...' : 'Confirm'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AdminRMADetail;
