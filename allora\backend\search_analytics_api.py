"""
Search Analytics API
REST API endpoints for search analytics, performance monitoring, and optimization
"""

import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify
from dataclasses import asdict
from search_analytics_tracker import search_tracker, SearchEvent

search_analytics_bp = Blueprint('search_analytics', __name__)

@search_analytics_bp.route('/api/search/analytics', methods=['POST'])
def track_search_analytics():
    """Track search analytics event"""
    try:
        data = request.get_json()
        
        # Create search event
        search_event = SearchEvent(
            event_id=str(uuid.uuid4()),
            user_id=data.get('user_id'),
            session_id=data.get('session_id', str(uuid.uuid4())),
            query=data.get('query', ''),
            filters=data.get('filters', {}),
            search_type=data.get('search_type', 'simple'),
            timestamp=datetime.utcnow(),
            results_count=data.get('results_count', 0),
            response_time_ms=data.get('response_time_ms', 0),
            elasticsearch_time_ms=data.get('elasticsearch_time_ms', 0),
            clicked_results=data.get('clicked_results', []),
            conversion_events=data.get('conversion_events', []),
            user_agent=request.headers.get('User-Agent'),
            ip_address=request.remote_addr
        )
        
        # Track the event
        event_id = search_tracker.track_search(search_event)
        
        if event_id:
            return jsonify({
                'success': True,
                'event_id': event_id,
                'message': 'Search analytics tracked successfully'
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to track search analytics'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error tracking search analytics: {str(e)}'
        }), 500

@search_analytics_bp.route('/api/search/analytics/click', methods=['POST'])
def track_search_click():
    """Track search result click"""
    try:
        data = request.get_json()
        
        search_id = data.get('search_id')
        product_id = data.get('product_id')
        position = data.get('position', 0)
        user_id = data.get('user_id')
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not search_id or not product_id:
            return jsonify({
                'success': False,
                'message': 'search_id and product_id are required'
            }), 400
        
        # Track the click
        click_id = search_tracker.track_click(
            search_id=search_id,
            product_id=product_id,
            position=position,
            user_id=user_id,
            session_id=session_id
        )
        
        if click_id:
            return jsonify({
                'success': True,
                'click_id': click_id,
                'message': 'Search click tracked successfully'
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to track search click'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error tracking search click: {str(e)}'
        }), 500

@search_analytics_bp.route('/api/search/analytics/conversion', methods=['POST'])
def track_search_conversion():
    """Track search conversion"""
    try:
        data = request.get_json()
        
        search_id = data.get('search_id')
        product_id = data.get('product_id')
        conversion_type = data.get('conversion_type')  # 'add_to_cart', 'purchase', 'wishlist'
        conversion_value = data.get('conversion_value')
        user_id = data.get('user_id')
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not search_id or not product_id or not conversion_type:
            return jsonify({
                'success': False,
                'message': 'search_id, product_id, and conversion_type are required'
            }), 400
        
        # Track the conversion
        conversion_id = search_tracker.track_conversion(
            search_id=search_id,
            product_id=product_id,
            conversion_type=conversion_type,
            conversion_value=conversion_value,
            user_id=user_id,
            session_id=session_id
        )
        
        if conversion_id:
            return jsonify({
                'success': True,
                'conversion_id': conversion_id,
                'message': 'Search conversion tracked successfully'
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to track search conversion'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error tracking search conversion: {str(e)}'
        }), 500

@search_analytics_bp.route('/api/search/analytics/performance', methods=['GET'])
def get_search_performance():
    """Get search performance metrics"""
    try:
        time_range = request.args.get('time_range', '24h')
        
        # Validate time range
        valid_ranges = ['1h', '24h', '7d', '30d']
        if time_range not in valid_ranges:
            return jsonify({
                'success': False,
                'message': f'Invalid time_range. Must be one of: {valid_ranges}'
            }), 400
        
        # Get performance metrics
        metrics = search_tracker.get_search_performance(time_range)
        
        return jsonify({
            'success': True,
            'data': asdict(metrics),
            'time_range': time_range
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error getting search performance: {str(e)}'
        }), 500

@search_analytics_bp.route('/api/search/analytics/suggestions', methods=['GET'])
def get_query_suggestions():
    """Get query suggestions based on popular searches"""
    try:
        partial_query = request.args.get('q', '')
        limit = int(request.args.get('limit', 10))
        
        if not partial_query:
            return jsonify({
                'success': False,
                'message': 'Query parameter q is required'
            }), 400
        
        # Get suggestions
        suggestions = search_tracker.get_query_suggestions(partial_query, limit)
        
        return jsonify({
            'success': True,
            'suggestions': suggestions,
            'query': partial_query
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error getting query suggestions: {str(e)}'
        }), 500

@search_analytics_bp.route('/api/search/analytics/optimization', methods=['GET'])
def get_search_optimization():
    """Get search optimization recommendations"""
    try:
        # Get optimization analysis
        optimization_data = search_tracker.optimize_search_performance()
        
        return jsonify({
            'success': True,
            'data': optimization_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error getting search optimization: {str(e)}'
        }), 500

@search_analytics_bp.route('/api/search/analytics/dashboard', methods=['GET'])
def get_analytics_dashboard():
    """Get comprehensive analytics dashboard data"""
    try:
        time_range = request.args.get('time_range', '24h')
        
        # Get performance metrics
        performance = search_tracker.get_search_performance(time_range)
        
        # Get optimization recommendations
        optimization = search_tracker.optimize_search_performance()
        
        # Combine dashboard data
        dashboard_data = {
            'performance': asdict(performance),
            'optimization': optimization,
            'time_range': time_range,
            'last_updated': datetime.utcnow().isoformat()
        }
        
        return jsonify({
            'success': True,
            'data': dashboard_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error getting analytics dashboard: {str(e)}'
        }), 500

@search_analytics_bp.route('/api/search/analytics/realtime', methods=['GET'])
def get_realtime_analytics():
    """Get real-time search analytics"""
    try:
        # Get current hour metrics from Redis
        current_hour = datetime.utcnow().strftime('%Y-%m-%d-%H')
        
        realtime_data = {
            'current_hour': current_hour,
            'searches_this_hour': 0,
            'avg_response_time': 0,
            'success_rate': 0,
            'active_searches': 0
        }
        
        if search_tracker.redis_client:
            try:
                metrics = search_tracker.redis_client.hgetall(f"search_metrics:{current_hour}")
                
                if metrics:
                    total_searches = int(metrics.get(b'total_searches', 0))
                    total_response_time = int(metrics.get(b'total_response_time', 0))
                    successful_searches = int(metrics.get(b'successful_searches', 0))
                    
                    realtime_data.update({
                        'searches_this_hour': total_searches,
                        'avg_response_time': total_response_time / total_searches if total_searches > 0 else 0,
                        'success_rate': successful_searches / total_searches if total_searches > 0 else 0,
                        'active_searches': total_searches
                    })
                
                # Get popular queries from Redis
                popular_queries = search_tracker.redis_client.zrevrange('popular_queries', 0, 4, withscores=True)
                realtime_data['popular_queries'] = [
                    {'query': query.decode('utf-8'), 'count': int(score)}
                    for query, score in popular_queries
                ]
                
            except Exception as redis_error:
                print(f"Redis error in realtime analytics: {redis_error}")
        
        return jsonify({
            'success': True,
            'data': realtime_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error getting real-time analytics: {str(e)}'
        }), 500

@search_analytics_bp.route('/api/search/analytics/export', methods=['GET'])
def export_search_analytics():
    """Export search analytics data"""
    try:
        time_range = request.args.get('time_range', '7d')
        format_type = request.args.get('format', 'json')  # json, csv
        
        # Get performance data
        performance = search_tracker.get_search_performance(time_range)
        
        if format_type == 'csv':
            # Convert to CSV format
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write headers
            writer.writerow([
                'Metric', 'Value'
            ])
            
            # Write data
            writer.writerow(['Total Searches', performance.total_searches])
            writer.writerow(['Average Response Time (ms)', performance.avg_response_time])
            writer.writerow(['Average Elasticsearch Time (ms)', performance.avg_elasticsearch_time])
            writer.writerow(['Average Results Count', performance.avg_results_count])
            writer.writerow(['Success Rate', performance.success_rate])
            writer.writerow(['Click Through Rate', performance.click_through_rate])
            writer.writerow(['Conversion Rate', performance.conversion_rate])
            
            # Popular queries
            writer.writerow(['', ''])
            writer.writerow(['Popular Queries', 'Count'])
            for query_data in performance.popular_queries:
                writer.writerow([query_data['query'], query_data['count']])
            
            csv_data = output.getvalue()
            output.close()
            
            return csv_data, 200, {
                'Content-Type': 'text/csv',
                'Content-Disposition': f'attachment; filename=search_analytics_{time_range}.csv'
            }
        
        else:
            # Return JSON format
            return jsonify({
                'success': True,
                'data': asdict(performance),
                'time_range': time_range,
                'exported_at': datetime.utcnow().isoformat()
            }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error exporting search analytics: {str(e)}'
        }), 500

# Error handlers
@search_analytics_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': 'Analytics endpoint not found'
    }), 404

@search_analytics_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'message': 'Internal server error in analytics'
    }), 500
