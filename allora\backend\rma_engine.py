"""
RMA Processing Engine
====================

Core business logic engine for processing Return Merchandise Authorization (RMA) requests.
Handles workflow automation, validation, approval processes, and integration with
existing systems.

Key Features:
1. RMA Request Validation
2. Automated Workflow Processing
3. Business Rules Engine
4. Approval Workflow Management
5. Integration with Order/Inventory Systems
6. Timeline and Audit Trail
7. Notification Management
8. Analytics and Reporting

Integration Points:
- Order/OrderItem models
- Inventory Management
- Payment/Refund System
- Shipping/Fulfillment System
- Notification Service
"""

import logging
import uuid
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass

# Flask and database imports
from flask import current_app
from sqlalchemy import and_, or_, desc
from sqlalchemy.orm import joinedload

# Import RMA architecture
from rma_architecture import (
    RMAStatus, RMAType, ReturnReason, InspectionResult, RefundMethod,
    RMAConfiguration, RMAItem as RMAItemData, RMARequest as RMARequestData,
    RMAWorkflowRule, DEFAULT_RMA_RULES, RMA_CONFIG
)

logger = logging.getLogger(__name__)

class RMAProcessingError(Exception):
    """Custom exception for RMA processing errors"""
    pass

class RMAValidationError(Exception):
    """Custom exception for RMA validation errors"""
    pass

class RMAEngine:
    """Main RMA processing engine"""
    
    def __init__(self, db_session=None):
        self.db = db_session
        self.config = RMA_CONFIG
        self.workflow_rules = DEFAULT_RMA_RULES
        
    def create_rma_request(self, order_id: int, items: List[Dict], 
                          customer_info: Dict, rma_type: str = "return_refund") -> str:
        """Create a new RMA request"""
        try:
            from app import db, Order, OrderItem, RMARequest, RMAItem, RMATimeline
            
            # Validate order exists and is eligible
            order = db.session.query(Order).filter(Order.id == order_id).first()
            if not order:
                raise RMAValidationError(f"Order {order_id} not found")
            
            # Check if order is eligible for returns
            if not self._is_order_eligible_for_return(order):
                raise RMAValidationError("Order is not eligible for returns")
            
            # Generate RMA number
            rma_number = self._generate_rma_number()
            
            # Calculate deadline
            deadline = datetime.utcnow() + timedelta(days=self.config.return_window_days)
            
            # Create RMA request
            rma_request = RMARequest(
                rma_number=rma_number,
                order_id=order_id,
                user_id=order.user_id,
                rma_type=rma_type,
                status=RMAStatus.PENDING.value,
                customer_email=customer_info.get('email', order.user.email if order.user else ''),
                customer_phone=customer_info.get('phone'),
                customer_notes=customer_info.get('notes'),
                deadline=deadline,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.session.add(rma_request)
            db.session.flush()  # Get the ID
            
            # Create RMA items
            total_refund = 0.0
            for item_data in items:
                order_item = db.session.query(OrderItem).filter(
                    OrderItem.id == item_data['order_item_id']
                ).first()
                
                if not order_item:
                    raise RMAValidationError(f"Order item {item_data['order_item_id']} not found")
                
                # Validate quantity
                if item_data['quantity'] > order_item.quantity:
                    raise RMAValidationError(f"Return quantity exceeds ordered quantity")
                
                rma_item = RMAItem(
                    rma_request_id=rma_request.id,
                    order_item_id=order_item.id,
                    product_id=order_item.product_id,
                    quantity=item_data['quantity'],
                    unit_price=order_item.unit_price,
                    total_price=item_data['quantity'] * order_item.unit_price,
                    return_reason=item_data['return_reason'],
                    condition_notes=item_data.get('condition_notes'),
                    photos=item_data.get('photos', []),
                    exchange_product_id=item_data.get('exchange_product_id'),
                    exchange_quantity=item_data.get('exchange_quantity'),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                db.session.add(rma_item)
                total_refund += rma_item.total_price
            
            # Update total refund amount
            rma_request.total_refund_amount = total_refund
            
            # Apply business rules
            self._apply_workflow_rules(rma_request)
            
            # Create timeline entry
            self._add_timeline_event(
                rma_request.id,
                'created',
                f'RMA request created for order {order_id}',
                'customer',
                str(order.user_id) if order.user_id else 'guest'
            )
            
            db.session.commit()
            
            # Send notifications
            self._send_rma_notification(rma_request, 'created')
            
            logger.info(f"RMA request {rma_number} created successfully")
            return rma_number
            
        except Exception as e:
            if self.db:
                self.db.rollback()
            logger.error(f"Error creating RMA request: {str(e)}")
            raise RMAProcessingError(f"Failed to create RMA request: {str(e)}")
    
    def process_rma_approval(self, rma_number: str, decision: str, 
                           approver_info: Dict, notes: str = None) -> bool:
        """Process RMA approval decision"""
        try:
            from app import db, RMARequest, RMAApproval, RMATimeline
            
            rma_request = db.session.query(RMARequest).filter(
                RMARequest.rma_number == rma_number
            ).first()
            
            if not rma_request:
                raise RMAValidationError(f"RMA request {rma_number} not found")
            
            if rma_request.status != RMAStatus.PENDING.value:
                raise RMAValidationError(f"RMA request is not in pending status")
            
            # Create approval record
            approval = RMAApproval(
                rma_request_id=rma_request.id,
                approver_type=approver_info.get('type', 'admin'),
                approver_id=approver_info.get('id'),
                approver_name=approver_info.get('name'),
                decision=decision,
                decision_reason=notes,
                decided_at=datetime.utcnow()
            )
            
            db.session.add(approval)
            
            # Update RMA status
            old_status = rma_request.status
            if decision == 'approved':
                rma_request.status = RMAStatus.APPROVED.value
                rma_request.approved_by = approver_info.get('name')
                rma_request.approved_at = datetime.utcnow()
                
                # Generate return label if needed
                if self.config.free_return_shipping:
                    self._generate_return_label(rma_request)
                    
            elif decision == 'rejected':
                rma_request.status = RMAStatus.REJECTED.value
                rma_request.rejection_reason = notes
            
            rma_request.updated_at = datetime.utcnow()
            
            # Add timeline event
            self._add_timeline_event(
                rma_request.id,
                f'approval_{decision}',
                f'RMA request {decision} by {approver_info.get("name")}',
                'admin',
                approver_info.get('id'),
                old_status=old_status,
                new_status=rma_request.status,
                notes=notes
            )
            
            db.session.commit()
            
            # Send notification
            self._send_rma_notification(rma_request, f'approval_{decision}')
            
            logger.info(f"RMA request {rma_number} {decision} by {approver_info.get('name')}")
            return True
            
        except Exception as e:
            if self.db:
                self.db.rollback()
            logger.error(f"Error processing RMA approval: {str(e)}")
            raise RMAProcessingError(f"Failed to process approval: {str(e)}")
    
    def process_return_received(self, rma_number: str, received_items: List[Dict],
                              inspector_info: Dict) -> bool:
        """Process received return items and perform inspection"""
        try:
            from app import db, RMARequest, RMAItem, RMATimeline
            
            rma_request = db.session.query(RMARequest).options(
                joinedload(RMARequest.rma_items)
            ).filter(RMARequest.rma_number == rma_number).first()
            
            if not rma_request:
                raise RMAValidationError(f"RMA request {rma_number} not found")
            
            # Update RMA status
            old_status = rma_request.status
            rma_request.status = RMAStatus.RETURN_RECEIVED.value
            rma_request.return_received_at = datetime.utcnow()
            rma_request.updated_at = datetime.utcnow()
            
            # Process each received item
            inspection_passed = True
            for received_item in received_items:
                rma_item = next((item for item in rma_request.rma_items 
                               if item.id == received_item['rma_item_id']), None)
                
                if not rma_item:
                    continue
                
                # Update item inspection results
                rma_item.inspection_result = received_item.get('inspection_result', 'passed')
                rma_item.inspection_notes = received_item.get('inspection_notes')
                rma_item.item_status = 'inspected'
                rma_item.updated_at = datetime.utcnow()
                
                if rma_item.inspection_result != 'passed':
                    inspection_passed = False
            
            # Update overall inspection status
            if inspection_passed:
                rma_request.status = RMAStatus.INSPECTION_PASSED.value
                rma_request.inspection_result = InspectionResult.PASSED.value
            else:
                rma_request.status = RMAStatus.INSPECTION_FAILED.value
                rma_request.inspection_result = InspectionResult.FAILED.value
            
            rma_request.inspected_by = inspector_info.get('name')
            rma_request.inspected_at = datetime.utcnow()
            
            # Add timeline event
            self._add_timeline_event(
                rma_request.id,
                'return_received',
                f'Return items received and inspected by {inspector_info.get("name")}',
                'admin',
                inspector_info.get('id'),
                old_status=old_status,
                new_status=rma_request.status
            )
            
            # If inspection passed, initiate refund process
            if inspection_passed:
                self._initiate_refund_process(rma_request)
            
            db.session.commit()
            
            # Send notification
            self._send_rma_notification(rma_request, 'return_received')
            
            logger.info(f"Return received and processed for RMA {rma_number}")
            return True
            
        except Exception as e:
            if self.db:
                self.db.rollback()
            logger.error(f"Error processing return received: {str(e)}")
            raise RMAProcessingError(f"Failed to process return: {str(e)}")
    
    def _is_order_eligible_for_return(self, order) -> bool:
        """Check if order is eligible for returns"""
        # Check order status
        if order.status not in ['delivered', 'shipped']:
            return False
        
        # Check time limit
        if order.delivered_at:
            days_since_delivery = (datetime.utcnow() - order.delivered_at).days
            return days_since_delivery <= self.config.return_window_days
        
        # If not delivered yet, check shipped date
        if order.shipped_at:
            days_since_shipped = (datetime.utcnow() - order.shipped_at).days
            return days_since_shipped <= (self.config.return_window_days + 7)  # Extra grace period
        
        return False
    
    def _generate_rma_number(self) -> str:
        """Generate unique RMA number"""
        timestamp = datetime.utcnow().strftime("%Y%m")
        random_suffix = str(uuid.uuid4())[:8].upper()
        return f"RMA-{timestamp}-{random_suffix}"
    
    def _apply_workflow_rules(self, rma_request):
        """Apply business rules to RMA request"""
        from app import RMAItem
        
        # Create evaluation context
        items = self.db.query(RMAItem).filter(
            RMAItem.rma_request_id == rma_request.id
        ).all() if self.db else []
        
        context = {
            'total_amount': rma_request.total_refund_amount,
            'item_count': len(items),
            'rma_type': rma_request.rma_type,
            'return_reasons': [item.return_reason for item in items]
        }
        
        # Apply rules in priority order
        for rule in sorted(self.workflow_rules, key=lambda x: x.priority):
            try:
                if rule.evaluate_context(context):
                    self._execute_rule_action(rma_request, rule)
            except Exception as e:
                logger.warning(f"Error applying rule {rule.name}: {e}")
    
    def _execute_rule_action(self, rma_request, rule):
        """Execute action based on rule"""
        if rule.action == 'auto_approve':
            rma_request.requires_approval = False
            rma_request.status = RMAStatus.APPROVED.value
            rma_request.approved_by = 'System (Auto-approved)'
            rma_request.approved_at = datetime.utcnow()
            
        elif rule.action == 'auto_reject':
            rma_request.status = RMAStatus.REJECTED.value
            rma_request.rejection_reason = f'Auto-rejected by rule: {rule.name}'
            
        elif rule.action == 'require_approval':
            rma_request.requires_approval = True
            
        elif rule.action == 'require_photos':
            # This would be handled in the frontend validation
            pass
            
        elif rule.action == 'skip_inspection':
            rma_request.inspection_result = InspectionResult.PASSED.value
    
    def _add_timeline_event(self, rma_request_id: int, event_type: str, 
                           description: str, actor_type: str, actor_id: str = None,
                           old_status: str = None, new_status: str = None, 
                           notes: str = None):
        """Add event to RMA timeline"""
        try:
            from app import db, RMATimeline
            
            timeline_event = RMATimeline(
                rma_request_id=rma_request_id,
                event_type=event_type,
                event_description=description,
                old_status=old_status,
                new_status=new_status,
                actor_type=actor_type,
                actor_id=actor_id,
                notes=notes,
                created_at=datetime.utcnow()
            )
            
            db.session.add(timeline_event)
            
        except Exception as e:
            logger.error(f"Error adding timeline event: {e}")
    
    def _generate_return_label(self, rma_request):
        """Generate return shipping label"""
        # This would integrate with the shipping system
        # For now, we'll create a placeholder
        rma_request.return_label_url = f"/api/rma/labels/{rma_request.rma_number}"
        rma_request.return_carrier = "Blue Dart"
        rma_request.return_tracking_number = f"BD{datetime.utcnow().strftime('%Y%m%d')}{rma_request.id:06d}"
    
    def _initiate_refund_process(self, rma_request):
        """Initiate refund processing"""
        rma_request.status = RMAStatus.REFUND_PROCESSING.value
        rma_request.refund_method = RefundMethod.ORIGINAL_PAYMENT.value
        # Additional refund processing logic would go here
    
    def process_exchange_request(self, rma_number: str, exchange_items: List[Dict]) -> bool:
        """Process exchange request"""
        try:
            from app import db, RMARequest, RMAItem, Product, OrderItem

            rma_request = db.session.query(RMARequest).options(
                joinedload(RMARequest.rma_items)
            ).filter(RMARequest.rma_number == rma_number).first()

            if not rma_request:
                raise RMAValidationError(f"RMA request {rma_number} not found")

            if rma_request.rma_type not in ['exchange_same', 'exchange_different']:
                raise RMAValidationError("RMA request is not an exchange request")

            # Validate exchange items
            for exchange_item in exchange_items:
                rma_item = next((item for item in rma_request.rma_items
                               if item.id == exchange_item['rma_item_id']), None)

                if not rma_item:
                    raise RMAValidationError(f"RMA item {exchange_item['rma_item_id']} not found")

                # Validate exchange product exists and is available
                exchange_product = db.session.query(Product).filter(
                    Product.id == exchange_item['exchange_product_id']
                ).first()

                if not exchange_product:
                    raise RMAValidationError(f"Exchange product {exchange_item['exchange_product_id']} not found")

                if exchange_product.stock_quantity < exchange_item['exchange_quantity']:
                    raise RMAValidationError(f"Insufficient stock for exchange product {exchange_product.name}")

                # Update RMA item with exchange details
                rma_item.exchange_product_id = exchange_item['exchange_product_id']
                rma_item.exchange_quantity = exchange_item['exchange_quantity']
                rma_item.exchange_unit_price = exchange_product.price
                rma_item.updated_at = datetime.utcnow()

            # Update RMA status
            old_status = rma_request.status
            rma_request.status = RMAStatus.EXCHANGE_PROCESSING.value
            rma_request.updated_at = datetime.utcnow()

            # Add timeline event
            self._add_timeline_event(
                rma_request.id,
                'exchange_processing',
                'Exchange request processing started',
                'system',
                old_status=old_status,
                new_status=rma_request.status
            )

            # Process inventory updates
            self._process_exchange_inventory(rma_request)

            db.session.commit()

            # Send notification
            self._send_rma_notification(rma_request, 'exchange_processing')

            logger.info(f"Exchange request processed for RMA {rma_number}")
            return True

        except Exception as e:
            if self.db:
                self.db.rollback()
            logger.error(f"Error processing exchange request: {str(e)}")
            raise RMAProcessingError(f"Failed to process exchange: {str(e)}")

    def process_refund_completion(self, rma_number: str, refund_details: Dict) -> bool:
        """Process refund completion"""
        try:
            from app import db, RMARequest, Refund

            rma_request = db.session.query(RMARequest).filter(
                RMARequest.rma_number == rma_number
            ).first()

            if not rma_request:
                raise RMAValidationError(f"RMA request {rma_number} not found")

            # Create refund record
            refund = Refund(
                refund_id=f"REF-{datetime.utcnow().strftime('%Y%m%d')}-{rma_request.id:06d}",
                order_id=rma_request.order_id,
                user_id=rma_request.user_id,
                refund_type='partial' if len(rma_request.rma_items) < len(rma_request.order.order_items) else 'full',
                refund_reason='rma_approved',
                refund_amount=rma_request.total_refund_amount - rma_request.restocking_fee,
                original_amount=rma_request.total_refund_amount,
                refunded_items=[{
                    'product_id': item.product_id,
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_price': item.total_price
                } for item in rma_request.rma_items],
                status='completed',
                processing_method=refund_details.get('method', 'gateway_refund'),
                gateway_refund_id=refund_details.get('gateway_refund_id'),
                gateway_response=refund_details.get('gateway_response'),
                customer_notes=rma_request.customer_notes,
                admin_notes=f"Refund processed for RMA {rma_number}",
                requested_by='RMA System',
                approved_by=rma_request.approved_by,
                approved_at=rma_request.approved_at,
                requested_at=rma_request.created_at,
                processed_at=datetime.utcnow(),
                completed_at=datetime.utcnow()
            )

            db.session.add(refund)

            # Update RMA status
            old_status = rma_request.status
            rma_request.status = RMAStatus.REFUND_COMPLETED.value
            rma_request.refund_method = refund_details.get('method', 'original_payment')
            rma_request.refund_reference = refund.refund_id
            rma_request.refunded_at = datetime.utcnow()
            rma_request.updated_at = datetime.utcnow()

            # Add timeline event
            self._add_timeline_event(
                rma_request.id,
                'refund_completed',
                f'Refund of ₹{rma_request.total_refund_amount - rma_request.restocking_fee:.2f} completed',
                'system',
                old_status=old_status,
                new_status=rma_request.status
            )

            db.session.commit()

            # Send notification
            self._send_rma_notification(rma_request, 'refund_completed')

            logger.info(f"Refund completed for RMA {rma_number}")
            return True

        except Exception as e:
            if self.db:
                self.db.rollback()
            logger.error(f"Error processing refund completion: {str(e)}")
            raise RMAProcessingError(f"Failed to complete refund: {str(e)}")

    def get_rma_status(self, rma_number: str) -> Dict[str, Any]:
        """Get comprehensive RMA status"""
        try:
            from app import db, RMARequest, RMAItem, RMATimeline, RMADocument

            rma_request = db.session.query(RMARequest).options(
                joinedload(RMARequest.rma_items),
                joinedload(RMARequest.timeline_events),
                joinedload(RMARequest.documents),
                joinedload(RMARequest.order)
            ).filter(RMARequest.rma_number == rma_number).first()

            if not rma_request:
                raise RMAValidationError(f"RMA request {rma_number} not found")

            # Build comprehensive status response
            status_data = {
                'rma_number': rma_request.rma_number,
                'status': rma_request.status,
                'rma_type': rma_request.rma_type,
                'created_at': rma_request.created_at.isoformat(),
                'updated_at': rma_request.updated_at.isoformat(),
                'deadline': rma_request.deadline.isoformat() if rma_request.deadline else None,

                # Financial information
                'total_refund_amount': rma_request.total_refund_amount,
                'restocking_fee': rma_request.restocking_fee,
                'return_shipping_cost': rma_request.return_shipping_cost,
                'net_refund_amount': rma_request.total_refund_amount - rma_request.restocking_fee - rma_request.return_shipping_cost,

                # Order information
                'order': {
                    'id': rma_request.order.id,
                    'order_number': rma_request.order.order_number,
                    'order_date': rma_request.order.created_at.isoformat(),
                    'total_amount': rma_request.order.total_amount
                },

                # Items
                'items': [{
                    'id': item.id,
                    'product_id': item.product_id,
                    'product_name': item.product.name if item.product else 'Unknown Product',
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_price': item.total_price,
                    'return_reason': item.return_reason,
                    'condition_notes': item.condition_notes,
                    'photos': item.photos or [],
                    'item_status': item.item_status,
                    'inspection_result': item.inspection_result,
                    'inspection_notes': item.inspection_notes,
                    'exchange_product_id': item.exchange_product_id,
                    'exchange_quantity': item.exchange_quantity
                } for item in rma_request.rma_items],

                # Timeline
                'timeline': [{
                    'event_type': event.event_type,
                    'description': event.event_description,
                    'old_status': event.old_status,
                    'new_status': event.new_status,
                    'actor_type': event.actor_type,
                    'actor_name': event.actor_name,
                    'notes': event.notes,
                    'created_at': event.created_at.isoformat()
                } for event in sorted(rma_request.timeline_events, key=lambda x: x.created_at)],

                # Documents
                'documents': [{
                    'id': doc.id,
                    'document_type': doc.document_type,
                    'document_name': doc.document_name,
                    'document_url': doc.document_url,
                    'uploaded_at': doc.uploaded_at.isoformat(),
                    'description': doc.description,
                    'is_public': doc.is_public
                } for doc in rma_request.documents],

                # Return shipping
                'return_shipping': {
                    'tracking_number': rma_request.return_tracking_number,
                    'carrier': rma_request.return_carrier,
                    'label_url': rma_request.return_label_url,
                    'shipped_at': rma_request.return_shipped_at.isoformat() if rma_request.return_shipped_at else None,
                    'received_at': rma_request.return_received_at.isoformat() if rma_request.return_received_at else None
                },

                # Approval information
                'approval': {
                    'requires_approval': rma_request.requires_approval,
                    'approved_by': rma_request.approved_by,
                    'approved_at': rma_request.approved_at.isoformat() if rma_request.approved_at else None,
                    'rejection_reason': rma_request.rejection_reason
                },

                # Inspection information
                'inspection': {
                    'result': rma_request.inspection_result,
                    'notes': rma_request.inspection_notes,
                    'inspected_by': rma_request.inspected_by,
                    'inspected_at': rma_request.inspected_at.isoformat() if rma_request.inspected_at else None
                },

                # Refund information
                'refund': {
                    'method': rma_request.refund_method,
                    'reference': rma_request.refund_reference,
                    'refunded_at': rma_request.refunded_at.isoformat() if rma_request.refunded_at else None
                }
            }

            return status_data

        except Exception as e:
            logger.error(f"Error getting RMA status: {str(e)}")
            raise RMAProcessingError(f"Failed to get RMA status: {str(e)}")

    def _process_exchange_inventory(self, rma_request):
        """Process inventory updates for exchanges"""
        try:
            from app import db, Product

            for rma_item in rma_request.rma_items:
                if rma_item.exchange_product_id and rma_item.exchange_quantity:
                    # Reserve inventory for exchange product
                    exchange_product = db.session.query(Product).filter(
                        Product.id == rma_item.exchange_product_id
                    ).first()

                    if exchange_product:
                        exchange_product.stock_quantity -= rma_item.exchange_quantity

                        # Add back original product inventory (when return is received)
                        original_product = db.session.query(Product).filter(
                            Product.id == rma_item.product_id
                        ).first()

                        if original_product:
                            original_product.stock_quantity += rma_item.quantity

        except Exception as e:
            logger.error(f"Error processing exchange inventory: {e}")
            raise

    def _send_rma_notification(self, rma_request, event_type: str):
        """Send notification for RMA events"""
        # This would integrate with the notification system
        logger.info(f"Notification sent for RMA {rma_request.rma_number}: {event_type}")
        pass
