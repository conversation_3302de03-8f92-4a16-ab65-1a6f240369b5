/* Inventory Status Indicator Styles */
.inventory-status-indicator {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.inventory-status-indicator.small {
  font-size: 0.75rem;
}

.inventory-status-indicator.medium {
  font-size: 0.875rem;
}

.inventory-status-indicator.large {
  font-size: 1rem;
}

.status-main {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stock-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stock-indicator.animating {
  transform: scale(1.05);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.stock-indicator.in-stock {
  background: rgba(5, 150, 105, 0.1);
  color: #065f46;
}

.stock-indicator.low-stock {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
}

.stock-indicator.out-of-stock {
  background: rgba(220, 38, 38, 0.1);
  color: #991b1b;
}

.stock-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.stock-quantity {
  font-weight: 600;
  font-size: 0.875em;
  min-width: 20px;
  text-align: center;
}

.connection-warning {
  color: #f59e0b;
  display: flex;
  align-items: center;
  cursor: help;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.75em;
}

.status-text {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-label {
  font-weight: 500;
}

.status-label.in-stock {
  color: #059669;
}

.status-label.low-stock {
  color: #f59e0b;
}

.status-label.out-of-stock {
  color: #dc2626;
}

.last-update {
  color: #6b7280;
  font-size: 0.9em;
}

.stock-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f59e0b;
  font-size: 0.9em;
}

/* Sync Status Badge Styles */
.sync-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.sync-status-badge .status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.sync-status-badge.syncing {
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
}

.sync-status-badge.syncing .status-dot {
  background: #3b82f6;
  animation: pulse 2s infinite;
}

.sync-status-badge.success {
  background: rgba(5, 150, 105, 0.1);
  color: #065f46;
}

.sync-status-badge.success .status-dot {
  background: #059669;
}

.sync-status-badge.error {
  background: rgba(220, 38, 38, 0.1);
  color: #991b1b;
}

.sync-status-badge.error .status-dot {
  background: #dc2626;
}

.sync-status-badge.unknown {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

.sync-status-badge.unknown .status-dot {
  background: #6b7280;
}

.error-message {
  background: #dc2626;
  color: white;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  font-weight: bold;
  cursor: help;
}

/* Conflict Notification Badge Styles */
.conflict-notification-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(220, 38, 38, 0.1);
  color: #991b1b;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.conflict-count {
  background: #dc2626;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
}

.conflict-text {
  text-transform: lowercase;
}

/* Inventory Widget Styles */
.inventory-widget {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reserve-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 8px;
}

.reserve-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reserve-quantity-input {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  text-align: center;
}

.reserve-quantity-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.reserve-button {
  padding: 4px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reserve-button:hover:not(:disabled) {
  background: #2563eb;
}

.reserve-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.offline-notice {
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 4px;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 4px;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.stock-indicator.animating .stock-dot {
  animation: bounce 1s ease-in-out;
}

/* Responsive Design */
@media (max-width: 640px) {
  .inventory-widget {
    padding: 8px;
  }
  
  .reserve-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }
  
  .reserve-quantity-input {
    width: 100%;
  }
  
  .sync-status-badge {
    font-size: 0.7rem;
    padding: 1px 6px;
  }
  
  .conflict-notification-badge {
    font-size: 0.7rem;
    padding: 4px 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .inventory-widget {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .stock-indicator {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .reserve-quantity-input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .offline-notice {
    background: rgba(107, 114, 128, 0.2);
    color: #d1d5db;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .stock-indicator {
    border: 2px solid currentColor;
  }
  
  .sync-status-badge {
    border: 1px solid currentColor;
  }
  
  .conflict-notification-badge {
    border-width: 2px;
  }
}
