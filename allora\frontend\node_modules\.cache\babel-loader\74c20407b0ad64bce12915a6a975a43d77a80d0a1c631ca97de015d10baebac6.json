{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\contexts\\\\ImageContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useCallback, useRef } from 'react';\nimport { useError } from './ErrorContext';\nimport { useLoading } from './LoadingContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageContext = /*#__PURE__*/createContext();\nexport const useImage = () => {\n  _s();\n  const context = useContext(ImageContext);\n  if (!context) {\n    throw new Error('useImage must be used within an ImageProvider');\n  }\n  return context;\n};\n\n// Image cache for performance\n_s(useImage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst imageCache = new Map();\nconst loadingPromises = new Map();\nexport const ImageProvider = ({\n  children\n}) => {\n  _s2();\n  const [imageStates, setImageStates] = useState({});\n  const {\n    addError\n  } = useError();\n  const {\n    setLoading,\n    clearLoading\n  } = useLoading();\n  const abortControllers = useRef(new Map());\n\n  // Default fallback images\n  const defaultFallbacks = {\n    product: '/images/placeholder-product.jpg',\n    user: '/images/placeholder-user.jpg',\n    category: '/images/placeholder-category.jpg',\n    brand: '/images/placeholder-brand.jpg',\n    general: '/images/placeholder-general.jpg'\n  };\n\n  // Get optimized image URL\n  const getOptimizedImageUrl = useCallback((src, options = {}) => {\n    if (!src) return null;\n    const {\n      width,\n      height,\n      quality = 80,\n      format = 'webp',\n      fit = 'cover'\n    } = options;\n\n    // If it's already an external URL, return as is\n    if (src.startsWith('http://') || src.startsWith('https://')) {\n      return src;\n    }\n\n    // Build optimized URL with query parameters\n    const params = new URLSearchParams();\n    if (width) params.append('w', width);\n    if (height) params.append('h', height);\n    if (quality !== 80) params.append('q', quality);\n    if (format !== 'webp') params.append('f', format);\n    if (fit !== 'cover') params.append('fit', fit);\n    const queryString = params.toString();\n    return queryString ? `${src}?${queryString}` : src;\n  }, []);\n\n  // Preload image\n  const preloadImage = useCallback(async (src, options = {}) => {\n    const optimizedSrc = getOptimizedImageUrl(src, options);\n    if (!optimizedSrc) return null;\n\n    // Check cache first\n    if (imageCache.has(optimizedSrc)) {\n      return imageCache.get(optimizedSrc);\n    }\n\n    // Check if already loading\n    if (loadingPromises.has(optimizedSrc)) {\n      return loadingPromises.get(optimizedSrc);\n    }\n\n    // Create loading promise\n    const loadingPromise = new Promise((resolve, reject) => {\n      const img = new Image();\n      const abortController = new AbortController();\n      abortControllers.current.set(optimizedSrc, abortController);\n      const cleanup = () => {\n        abortControllers.current.delete(optimizedSrc);\n        loadingPromises.delete(optimizedSrc);\n      };\n      img.onload = () => {\n        imageCache.set(optimizedSrc, {\n          src: optimizedSrc,\n          width: img.naturalWidth,\n          height: img.naturalHeight,\n          loaded: true,\n          error: false\n        });\n        cleanup();\n        resolve(imageCache.get(optimizedSrc));\n      };\n      img.onerror = () => {\n        const errorInfo = {\n          src: optimizedSrc,\n          loaded: false,\n          error: true,\n          errorMessage: 'Failed to load image'\n        };\n        imageCache.set(optimizedSrc, errorInfo);\n        cleanup();\n        reject(new Error(`Failed to load image: ${optimizedSrc}`));\n      };\n\n      // Handle abort\n      abortController.signal.addEventListener('abort', () => {\n        cleanup();\n        reject(new Error('Image loading aborted'));\n      });\n      img.src = optimizedSrc;\n    });\n    loadingPromises.set(optimizedSrc, loadingPromise);\n    return loadingPromise;\n  }, [getOptimizedImageUrl]);\n\n  // Load image with state management\n  const loadImage = useCallback(async (key, src, options = {}) => {\n    const {\n      fallback,\n      fallbackType = 'general',\n      maxRetries = 3,\n      retryDelay = 1000\n    } = options;\n    setLoading(`image_${key}`, true, 'Loading image...');\n    setImageStates(prev => ({\n      ...prev,\n      [key]: {\n        loading: true,\n        loaded: false,\n        error: false,\n        src: null,\n        attempts: 0\n      }\n    }));\n    let attempts = 0;\n    let lastError = null;\n    while (attempts < maxRetries) {\n      try {\n        const imageInfo = await preloadImage(src, options);\n        setImageStates(prev => ({\n          ...prev,\n          [key]: {\n            loading: false,\n            loaded: true,\n            error: false,\n            src: imageInfo.src,\n            width: imageInfo.width,\n            height: imageInfo.height,\n            attempts: attempts + 1\n          }\n        }));\n        clearLoading(`image_${key}`);\n        return imageInfo;\n      } catch (error) {\n        lastError = error;\n        attempts++;\n        if (attempts < maxRetries) {\n          await new Promise(resolve => setTimeout(resolve, retryDelay * attempts));\n        }\n      }\n    }\n\n    // All attempts failed, try fallback\n    const fallbackSrc = fallback || defaultFallbacks[fallbackType] || defaultFallbacks.general;\n    try {\n      const fallbackInfo = await preloadImage(fallbackSrc);\n      setImageStates(prev => ({\n        ...prev,\n        [key]: {\n          loading: false,\n          loaded: true,\n          error: true,\n          src: fallbackInfo.src,\n          width: fallbackInfo.width,\n          height: fallbackInfo.height,\n          attempts,\n          fallbackUsed: true\n        }\n      }));\n      clearLoading(`image_${key}`);\n      return fallbackInfo;\n    } catch (fallbackError) {\n      setImageStates(prev => ({\n        ...prev,\n        [key]: {\n          loading: false,\n          loaded: false,\n          error: true,\n          src: null,\n          attempts,\n          errorMessage: lastError.message\n        }\n      }));\n      clearLoading(`image_${key}`);\n      addError(`image_${key}`, lastError, {\n        type: 'network',\n        severity: 'low',\n        context: {\n          src,\n          fallbackSrc\n        }\n      });\n      throw lastError;\n    }\n  }, [preloadImage, setLoading, clearLoading, addError, defaultFallbacks]);\n\n  // Get image state\n  const getImageState = useCallback(key => {\n    return imageStates[key] || {\n      loading: false,\n      loaded: false,\n      error: false,\n      src: null\n    };\n  }, [imageStates]);\n\n  // Clear image from cache and state\n  const clearImage = useCallback(key => {\n    const state = imageStates[key];\n    if (state !== null && state !== void 0 && state.src) {\n      imageCache.delete(state.src);\n    }\n    setImageStates(prev => {\n      const newState = {\n        ...prev\n      };\n      delete newState[key];\n      return newState;\n    });\n\n    // Abort loading if in progress\n    const abortController = abortControllers.current.get(key);\n    if (abortController) {\n      abortController.abort();\n    }\n  }, [imageStates]);\n\n  // Batch preload images\n  const preloadImages = useCallback(async images => {\n    const promises = images.map(({\n      src,\n      options\n    }) => preloadImage(src, options).catch(error => ({\n      error,\n      src\n    })));\n    return Promise.allSettled(promises);\n  }, [preloadImage]);\n\n  // Clear cache\n  const clearCache = useCallback(() => {\n    imageCache.clear();\n    loadingPromises.clear();\n    abortControllers.current.forEach(controller => controller.abort());\n    abortControllers.current.clear();\n  }, []);\n  const value = {\n    // Image loading\n    loadImage,\n    preloadImage,\n    preloadImages,\n    // Image utilities\n    getOptimizedImageUrl,\n    getImageState,\n    clearImage,\n    clearCache,\n    // State\n    imageStates,\n    // Constants\n    defaultFallbacks\n  };\n  return /*#__PURE__*/_jsxDEV(ImageContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 296,\n    columnNumber: 5\n  }, this);\n};\n_s2(ImageProvider, \"iCyYex5b9WtUjL7B2NLfidsnfXo=\", false, function () {\n  return [useError, useLoading];\n});\n_c = ImageProvider;\nexport default ImageContext;\nvar _c;\n$RefreshReg$(_c, \"ImageProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useCallback", "useRef", "useError", "useLoading", "jsxDEV", "_jsxDEV", "ImageContext", "useImage", "_s", "context", "Error", "imageCache", "Map", "loadingPromises", "ImageProvider", "children", "_s2", "imageStates", "setImageStates", "addError", "setLoading", "clearLoading", "abortControllers", "defaultFallbacks", "product", "user", "category", "brand", "general", "getOptimizedImageUrl", "src", "options", "width", "height", "quality", "format", "fit", "startsWith", "params", "URLSearchParams", "append", "queryString", "toString", "preloadImage", "optimizedSrc", "has", "get", "loadingPromise", "Promise", "resolve", "reject", "img", "Image", "abortController", "AbortController", "current", "set", "cleanup", "delete", "onload", "naturalWidth", "naturalHeight", "loaded", "error", "onerror", "errorInfo", "errorMessage", "signal", "addEventListener", "loadImage", "key", "fallback", "fallbackType", "maxRetries", "retry<PERSON><PERSON><PERSON>", "prev", "loading", "attempts", "lastError", "imageInfo", "setTimeout", "fallbackSrc", "fallbackInfo", "fallbackUsed", "fallback<PERSON><PERSON>r", "message", "type", "severity", "getImageState", "clearImage", "state", "newState", "abort", "preloadImages", "images", "promises", "map", "catch", "allSettled", "clearCache", "clear", "for<PERSON>ach", "controller", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/contexts/ImageContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useCallback, useRef } from 'react';\nimport { useError } from './ErrorContext';\nimport { useLoading } from './LoadingContext';\n\nconst ImageContext = createContext();\n\nexport const useImage = () => {\n  const context = useContext(ImageContext);\n  if (!context) {\n    throw new Error('useImage must be used within an ImageProvider');\n  }\n  return context;\n};\n\n// Image cache for performance\nconst imageCache = new Map();\nconst loadingPromises = new Map();\n\nexport const ImageProvider = ({ children }) => {\n  const [imageStates, setImageStates] = useState({});\n  const { addError } = useError();\n  const { setLoading, clearLoading } = useLoading();\n  const abortControllers = useRef(new Map());\n\n  // Default fallback images\n  const defaultFallbacks = {\n    product: '/images/placeholder-product.jpg',\n    user: '/images/placeholder-user.jpg',\n    category: '/images/placeholder-category.jpg',\n    brand: '/images/placeholder-brand.jpg',\n    general: '/images/placeholder-general.jpg'\n  };\n\n  // Get optimized image URL\n  const getOptimizedImageUrl = useCallback((src, options = {}) => {\n    if (!src) return null;\n    \n    const {\n      width,\n      height,\n      quality = 80,\n      format = 'webp',\n      fit = 'cover'\n    } = options;\n\n    // If it's already an external URL, return as is\n    if (src.startsWith('http://') || src.startsWith('https://')) {\n      return src;\n    }\n\n    // Build optimized URL with query parameters\n    const params = new URLSearchParams();\n    if (width) params.append('w', width);\n    if (height) params.append('h', height);\n    if (quality !== 80) params.append('q', quality);\n    if (format !== 'webp') params.append('f', format);\n    if (fit !== 'cover') params.append('fit', fit);\n\n    const queryString = params.toString();\n    return queryString ? `${src}?${queryString}` : src;\n  }, []);\n\n  // Preload image\n  const preloadImage = useCallback(async (src, options = {}) => {\n    const optimizedSrc = getOptimizedImageUrl(src, options);\n    if (!optimizedSrc) return null;\n\n    // Check cache first\n    if (imageCache.has(optimizedSrc)) {\n      return imageCache.get(optimizedSrc);\n    }\n\n    // Check if already loading\n    if (loadingPromises.has(optimizedSrc)) {\n      return loadingPromises.get(optimizedSrc);\n    }\n\n    // Create loading promise\n    const loadingPromise = new Promise((resolve, reject) => {\n      const img = new Image();\n      const abortController = new AbortController();\n      \n      abortControllers.current.set(optimizedSrc, abortController);\n\n      const cleanup = () => {\n        abortControllers.current.delete(optimizedSrc);\n        loadingPromises.delete(optimizedSrc);\n      };\n\n      img.onload = () => {\n        imageCache.set(optimizedSrc, {\n          src: optimizedSrc,\n          width: img.naturalWidth,\n          height: img.naturalHeight,\n          loaded: true,\n          error: false\n        });\n        cleanup();\n        resolve(imageCache.get(optimizedSrc));\n      };\n\n      img.onerror = () => {\n        const errorInfo = {\n          src: optimizedSrc,\n          loaded: false,\n          error: true,\n          errorMessage: 'Failed to load image'\n        };\n        imageCache.set(optimizedSrc, errorInfo);\n        cleanup();\n        reject(new Error(`Failed to load image: ${optimizedSrc}`));\n      };\n\n      // Handle abort\n      abortController.signal.addEventListener('abort', () => {\n        cleanup();\n        reject(new Error('Image loading aborted'));\n      });\n\n      img.src = optimizedSrc;\n    });\n\n    loadingPromises.set(optimizedSrc, loadingPromise);\n    return loadingPromise;\n  }, [getOptimizedImageUrl]);\n\n  // Load image with state management\n  const loadImage = useCallback(async (key, src, options = {}) => {\n    const {\n      fallback,\n      fallbackType = 'general',\n      maxRetries = 3,\n      retryDelay = 1000\n    } = options;\n\n    setLoading(`image_${key}`, true, 'Loading image...');\n    \n    setImageStates(prev => ({\n      ...prev,\n      [key]: {\n        loading: true,\n        loaded: false,\n        error: false,\n        src: null,\n        attempts: 0\n      }\n    }));\n\n    let attempts = 0;\n    let lastError = null;\n\n    while (attempts < maxRetries) {\n      try {\n        const imageInfo = await preloadImage(src, options);\n        \n        setImageStates(prev => ({\n          ...prev,\n          [key]: {\n            loading: false,\n            loaded: true,\n            error: false,\n            src: imageInfo.src,\n            width: imageInfo.width,\n            height: imageInfo.height,\n            attempts: attempts + 1\n          }\n        }));\n\n        clearLoading(`image_${key}`);\n        return imageInfo;\n\n      } catch (error) {\n        lastError = error;\n        attempts++;\n        \n        if (attempts < maxRetries) {\n          await new Promise(resolve => setTimeout(resolve, retryDelay * attempts));\n        }\n      }\n    }\n\n    // All attempts failed, try fallback\n    const fallbackSrc = fallback || defaultFallbacks[fallbackType] || defaultFallbacks.general;\n    \n    try {\n      const fallbackInfo = await preloadImage(fallbackSrc);\n      \n      setImageStates(prev => ({\n        ...prev,\n        [key]: {\n          loading: false,\n          loaded: true,\n          error: true,\n          src: fallbackInfo.src,\n          width: fallbackInfo.width,\n          height: fallbackInfo.height,\n          attempts,\n          fallbackUsed: true\n        }\n      }));\n\n      clearLoading(`image_${key}`);\n      return fallbackInfo;\n\n    } catch (fallbackError) {\n      setImageStates(prev => ({\n        ...prev,\n        [key]: {\n          loading: false,\n          loaded: false,\n          error: true,\n          src: null,\n          attempts,\n          errorMessage: lastError.message\n        }\n      }));\n\n      clearLoading(`image_${key}`);\n      addError(`image_${key}`, lastError, {\n        type: 'network',\n        severity: 'low',\n        context: { src, fallbackSrc }\n      });\n\n      throw lastError;\n    }\n  }, [preloadImage, setLoading, clearLoading, addError, defaultFallbacks]);\n\n  // Get image state\n  const getImageState = useCallback((key) => {\n    return imageStates[key] || {\n      loading: false,\n      loaded: false,\n      error: false,\n      src: null\n    };\n  }, [imageStates]);\n\n  // Clear image from cache and state\n  const clearImage = useCallback((key) => {\n    const state = imageStates[key];\n    if (state?.src) {\n      imageCache.delete(state.src);\n    }\n    \n    setImageStates(prev => {\n      const newState = { ...prev };\n      delete newState[key];\n      return newState;\n    });\n\n    // Abort loading if in progress\n    const abortController = abortControllers.current.get(key);\n    if (abortController) {\n      abortController.abort();\n    }\n  }, [imageStates]);\n\n  // Batch preload images\n  const preloadImages = useCallback(async (images) => {\n    const promises = images.map(({ src, options }) => \n      preloadImage(src, options).catch(error => ({ error, src }))\n    );\n    \n    return Promise.allSettled(promises);\n  }, [preloadImage]);\n\n  // Clear cache\n  const clearCache = useCallback(() => {\n    imageCache.clear();\n    loadingPromises.clear();\n    abortControllers.current.forEach(controller => controller.abort());\n    abortControllers.current.clear();\n  }, []);\n\n  const value = {\n    // Image loading\n    loadImage,\n    preloadImage,\n    preloadImages,\n    \n    // Image utilities\n    getOptimizedImageUrl,\n    getImageState,\n    clearImage,\n    clearCache,\n    \n    // State\n    imageStates,\n    \n    // Constants\n    defaultFallbacks\n  };\n\n  return (\n    <ImageContext.Provider value={value}>\n      {children}\n    </ImageContext.Provider>\n  );\n};\n\nexport default ImageContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvF,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,UAAU,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,YAAY,gBAAGT,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMU,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGX,UAAU,CAACQ,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,QAAQ;AASrB,MAAMI,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC5B,MAAMC,eAAe,GAAG,IAAID,GAAG,CAAC,CAAC;AAEjC,OAAO,MAAME,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM;IAAEoB;EAAS,CAAC,GAAGjB,QAAQ,CAAC,CAAC;EAC/B,MAAM;IAAEkB,UAAU;IAAEC;EAAa,CAAC,GAAGlB,UAAU,CAAC,CAAC;EACjD,MAAMmB,gBAAgB,GAAGrB,MAAM,CAAC,IAAIW,GAAG,CAAC,CAAC,CAAC;;EAE1C;EACA,MAAMW,gBAAgB,GAAG;IACvBC,OAAO,EAAE,iCAAiC;IAC1CC,IAAI,EAAE,8BAA8B;IACpCC,QAAQ,EAAE,kCAAkC;IAC5CC,KAAK,EAAE,+BAA+B;IACtCC,OAAO,EAAE;EACX,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAG7B,WAAW,CAAC,CAAC8B,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC9D,IAAI,CAACD,GAAG,EAAE,OAAO,IAAI;IAErB,MAAM;MACJE,KAAK;MACLC,MAAM;MACNC,OAAO,GAAG,EAAE;MACZC,MAAM,GAAG,MAAM;MACfC,GAAG,GAAG;IACR,CAAC,GAAGL,OAAO;;IAEX;IACA,IAAID,GAAG,CAACO,UAAU,CAAC,SAAS,CAAC,IAAIP,GAAG,CAACO,UAAU,CAAC,UAAU,CAAC,EAAE;MAC3D,OAAOP,GAAG;IACZ;;IAEA;IACA,MAAMQ,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIP,KAAK,EAAEM,MAAM,CAACE,MAAM,CAAC,GAAG,EAAER,KAAK,CAAC;IACpC,IAAIC,MAAM,EAAEK,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEP,MAAM,CAAC;IACtC,IAAIC,OAAO,KAAK,EAAE,EAAEI,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEN,OAAO,CAAC;IAC/C,IAAIC,MAAM,KAAK,MAAM,EAAEG,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEL,MAAM,CAAC;IACjD,IAAIC,GAAG,KAAK,OAAO,EAAEE,MAAM,CAACE,MAAM,CAAC,KAAK,EAAEJ,GAAG,CAAC;IAE9C,MAAMK,WAAW,GAAGH,MAAM,CAACI,QAAQ,CAAC,CAAC;IACrC,OAAOD,WAAW,GAAG,GAAGX,GAAG,IAAIW,WAAW,EAAE,GAAGX,GAAG;EACpD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMa,YAAY,GAAG3C,WAAW,CAAC,OAAO8B,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5D,MAAMa,YAAY,GAAGf,oBAAoB,CAACC,GAAG,EAAEC,OAAO,CAAC;IACvD,IAAI,CAACa,YAAY,EAAE,OAAO,IAAI;;IAE9B;IACA,IAAIjC,UAAU,CAACkC,GAAG,CAACD,YAAY,CAAC,EAAE;MAChC,OAAOjC,UAAU,CAACmC,GAAG,CAACF,YAAY,CAAC;IACrC;;IAEA;IACA,IAAI/B,eAAe,CAACgC,GAAG,CAACD,YAAY,CAAC,EAAE;MACrC,OAAO/B,eAAe,CAACiC,GAAG,CAACF,YAAY,CAAC;IAC1C;;IAEA;IACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtD,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvB,MAAMC,eAAe,GAAG,IAAIC,eAAe,CAAC,CAAC;MAE7ChC,gBAAgB,CAACiC,OAAO,CAACC,GAAG,CAACZ,YAAY,EAAES,eAAe,CAAC;MAE3D,MAAMI,OAAO,GAAGA,CAAA,KAAM;QACpBnC,gBAAgB,CAACiC,OAAO,CAACG,MAAM,CAACd,YAAY,CAAC;QAC7C/B,eAAe,CAAC6C,MAAM,CAACd,YAAY,CAAC;MACtC,CAAC;MAEDO,GAAG,CAACQ,MAAM,GAAG,MAAM;QACjBhD,UAAU,CAAC6C,GAAG,CAACZ,YAAY,EAAE;UAC3Bd,GAAG,EAAEc,YAAY;UACjBZ,KAAK,EAAEmB,GAAG,CAACS,YAAY;UACvB3B,MAAM,EAAEkB,GAAG,CAACU,aAAa;UACzBC,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE;QACT,CAAC,CAAC;QACFN,OAAO,CAAC,CAAC;QACTR,OAAO,CAACtC,UAAU,CAACmC,GAAG,CAACF,YAAY,CAAC,CAAC;MACvC,CAAC;MAEDO,GAAG,CAACa,OAAO,GAAG,MAAM;QAClB,MAAMC,SAAS,GAAG;UAChBnC,GAAG,EAAEc,YAAY;UACjBkB,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,IAAI;UACXG,YAAY,EAAE;QAChB,CAAC;QACDvD,UAAU,CAAC6C,GAAG,CAACZ,YAAY,EAAEqB,SAAS,CAAC;QACvCR,OAAO,CAAC,CAAC;QACTP,MAAM,CAAC,IAAIxC,KAAK,CAAC,yBAAyBkC,YAAY,EAAE,CAAC,CAAC;MAC5D,CAAC;;MAED;MACAS,eAAe,CAACc,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAE,MAAM;QACrDX,OAAO,CAAC,CAAC;QACTP,MAAM,CAAC,IAAIxC,KAAK,CAAC,uBAAuB,CAAC,CAAC;MAC5C,CAAC,CAAC;MAEFyC,GAAG,CAACrB,GAAG,GAAGc,YAAY;IACxB,CAAC,CAAC;IAEF/B,eAAe,CAAC2C,GAAG,CAACZ,YAAY,EAAEG,cAAc,CAAC;IACjD,OAAOA,cAAc;EACvB,CAAC,EAAE,CAAClB,oBAAoB,CAAC,CAAC;;EAE1B;EACA,MAAMwC,SAAS,GAAGrE,WAAW,CAAC,OAAOsE,GAAG,EAAExC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC9D,MAAM;MACJwC,QAAQ;MACRC,YAAY,GAAG,SAAS;MACxBC,UAAU,GAAG,CAAC;MACdC,UAAU,GAAG;IACf,CAAC,GAAG3C,OAAO;IAEXX,UAAU,CAAC,SAASkD,GAAG,EAAE,EAAE,IAAI,EAAE,kBAAkB,CAAC;IAEpDpD,cAAc,CAACyD,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACL,GAAG,GAAG;QACLM,OAAO,EAAE,IAAI;QACbd,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE,KAAK;QACZjC,GAAG,EAAE,IAAI;QACT+C,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC,CAAC;IAEH,IAAIA,QAAQ,GAAG,CAAC;IAChB,IAAIC,SAAS,GAAG,IAAI;IAEpB,OAAOD,QAAQ,GAAGJ,UAAU,EAAE;MAC5B,IAAI;QACF,MAAMM,SAAS,GAAG,MAAMpC,YAAY,CAACb,GAAG,EAAEC,OAAO,CAAC;QAElDb,cAAc,CAACyD,IAAI,KAAK;UACtB,GAAGA,IAAI;UACP,CAACL,GAAG,GAAG;YACLM,OAAO,EAAE,KAAK;YACdd,MAAM,EAAE,IAAI;YACZC,KAAK,EAAE,KAAK;YACZjC,GAAG,EAAEiD,SAAS,CAACjD,GAAG;YAClBE,KAAK,EAAE+C,SAAS,CAAC/C,KAAK;YACtBC,MAAM,EAAE8C,SAAS,CAAC9C,MAAM;YACxB4C,QAAQ,EAAEA,QAAQ,GAAG;UACvB;QACF,CAAC,CAAC,CAAC;QAEHxD,YAAY,CAAC,SAASiD,GAAG,EAAE,CAAC;QAC5B,OAAOS,SAAS;MAElB,CAAC,CAAC,OAAOhB,KAAK,EAAE;QACde,SAAS,GAAGf,KAAK;QACjBc,QAAQ,EAAE;QAEV,IAAIA,QAAQ,GAAGJ,UAAU,EAAE;UACzB,MAAM,IAAIzB,OAAO,CAACC,OAAO,IAAI+B,UAAU,CAAC/B,OAAO,EAAEyB,UAAU,GAAGG,QAAQ,CAAC,CAAC;QAC1E;MACF;IACF;;IAEA;IACA,MAAMI,WAAW,GAAGV,QAAQ,IAAIhD,gBAAgB,CAACiD,YAAY,CAAC,IAAIjD,gBAAgB,CAACK,OAAO;IAE1F,IAAI;MACF,MAAMsD,YAAY,GAAG,MAAMvC,YAAY,CAACsC,WAAW,CAAC;MAEpD/D,cAAc,CAACyD,IAAI,KAAK;QACtB,GAAGA,IAAI;QACP,CAACL,GAAG,GAAG;UACLM,OAAO,EAAE,KAAK;UACdd,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE,IAAI;UACXjC,GAAG,EAAEoD,YAAY,CAACpD,GAAG;UACrBE,KAAK,EAAEkD,YAAY,CAAClD,KAAK;UACzBC,MAAM,EAAEiD,YAAY,CAACjD,MAAM;UAC3B4C,QAAQ;UACRM,YAAY,EAAE;QAChB;MACF,CAAC,CAAC,CAAC;MAEH9D,YAAY,CAAC,SAASiD,GAAG,EAAE,CAAC;MAC5B,OAAOY,YAAY;IAErB,CAAC,CAAC,OAAOE,aAAa,EAAE;MACtBlE,cAAc,CAACyD,IAAI,KAAK;QACtB,GAAGA,IAAI;QACP,CAACL,GAAG,GAAG;UACLM,OAAO,EAAE,KAAK;UACdd,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,IAAI;UACXjC,GAAG,EAAE,IAAI;UACT+C,QAAQ;UACRX,YAAY,EAAEY,SAAS,CAACO;QAC1B;MACF,CAAC,CAAC,CAAC;MAEHhE,YAAY,CAAC,SAASiD,GAAG,EAAE,CAAC;MAC5BnD,QAAQ,CAAC,SAASmD,GAAG,EAAE,EAAEQ,SAAS,EAAE;QAClCQ,IAAI,EAAE,SAAS;QACfC,QAAQ,EAAE,KAAK;QACf9E,OAAO,EAAE;UAAEqB,GAAG;UAAEmD;QAAY;MAC9B,CAAC,CAAC;MAEF,MAAMH,SAAS;IACjB;EACF,CAAC,EAAE,CAACnC,YAAY,EAAEvB,UAAU,EAAEC,YAAY,EAAEF,QAAQ,EAAEI,gBAAgB,CAAC,CAAC;;EAExE;EACA,MAAMiE,aAAa,GAAGxF,WAAW,CAAEsE,GAAG,IAAK;IACzC,OAAOrD,WAAW,CAACqD,GAAG,CAAC,IAAI;MACzBM,OAAO,EAAE,KAAK;MACdd,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE,KAAK;MACZjC,GAAG,EAAE;IACP,CAAC;EACH,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMwE,UAAU,GAAGzF,WAAW,CAAEsE,GAAG,IAAK;IACtC,MAAMoB,KAAK,GAAGzE,WAAW,CAACqD,GAAG,CAAC;IAC9B,IAAIoB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE5D,GAAG,EAAE;MACdnB,UAAU,CAAC+C,MAAM,CAACgC,KAAK,CAAC5D,GAAG,CAAC;IAC9B;IAEAZ,cAAc,CAACyD,IAAI,IAAI;MACrB,MAAMgB,QAAQ,GAAG;QAAE,GAAGhB;MAAK,CAAC;MAC5B,OAAOgB,QAAQ,CAACrB,GAAG,CAAC;MACpB,OAAOqB,QAAQ;IACjB,CAAC,CAAC;;IAEF;IACA,MAAMtC,eAAe,GAAG/B,gBAAgB,CAACiC,OAAO,CAACT,GAAG,CAACwB,GAAG,CAAC;IACzD,IAAIjB,eAAe,EAAE;MACnBA,eAAe,CAACuC,KAAK,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC3E,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM4E,aAAa,GAAG7F,WAAW,CAAC,MAAO8F,MAAM,IAAK;IAClD,MAAMC,QAAQ,GAAGD,MAAM,CAACE,GAAG,CAAC,CAAC;MAAElE,GAAG;MAAEC;IAAQ,CAAC,KAC3CY,YAAY,CAACb,GAAG,EAAEC,OAAO,CAAC,CAACkE,KAAK,CAAClC,KAAK,KAAK;MAAEA,KAAK;MAAEjC;IAAI,CAAC,CAAC,CAC5D,CAAC;IAED,OAAOkB,OAAO,CAACkD,UAAU,CAACH,QAAQ,CAAC;EACrC,CAAC,EAAE,CAACpD,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMwD,UAAU,GAAGnG,WAAW,CAAC,MAAM;IACnCW,UAAU,CAACyF,KAAK,CAAC,CAAC;IAClBvF,eAAe,CAACuF,KAAK,CAAC,CAAC;IACvB9E,gBAAgB,CAACiC,OAAO,CAAC8C,OAAO,CAACC,UAAU,IAAIA,UAAU,CAACV,KAAK,CAAC,CAAC,CAAC;IAClEtE,gBAAgB,CAACiC,OAAO,CAAC6C,KAAK,CAAC,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,KAAK,GAAG;IACZ;IACAlC,SAAS;IACT1B,YAAY;IACZkD,aAAa;IAEb;IACAhE,oBAAoB;IACpB2D,aAAa;IACbC,UAAU;IACVU,UAAU;IAEV;IACAlF,WAAW;IAEX;IACAM;EACF,CAAC;EAED,oBACElB,OAAA,CAACC,YAAY,CAACkG,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAxF,QAAA,EACjCA;EAAQ;IAAA0F,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAAC5F,GAAA,CAzRWF,aAAa;EAAA,QAEHZ,QAAQ,EACQC,UAAU;AAAA;AAAA0G,EAAA,GAHpC/F,aAAa;AA2R1B,eAAeR,YAAY;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}