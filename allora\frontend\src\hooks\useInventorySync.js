import { useState, useEffect, useCallback, useRef } from 'react';
import { useError } from '../contexts/ErrorContext';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
const WS_URL = process.env.REACT_APP_WS_URL || 'ws://localhost:5000';

/**
 * Custom hook for real-time inventory synchronization
 * Provides WebSocket connection, inventory updates, and sync management
 */
export const useInventorySync = () => {
  const { handleApiError } = useError();
  const [isConnected, setIsConnected] = useState(false);
  const [inventoryUpdates, setInventoryUpdates] = useState({});
  const [syncStatus, setSyncStatus] = useState({});
  const [conflicts, setConflicts] = useState([]);
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Initialize WebSocket connection
  const connectWebSocket = useCallback(() => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const ws = new WebSocket(`${WS_URL}?token=${token}`);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('Inventory sync WebSocket connected');
        setIsConnected(true);
        reconnectAttempts.current = 0;
        
        // Subscribe to inventory updates
        ws.send(JSON.stringify({
          type: 'subscribe',
          channel: 'inventory_updates'
        }));
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('Inventory sync WebSocket disconnected:', event.code);
        setIsConnected(false);
        
        // Attempt to reconnect if not a manual close
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttempts.current) * 1000; // Exponential backoff
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connectWebSocket();
          }, delay);
        }
      };

      ws.onerror = (error) => {
        console.error('Inventory sync WebSocket error:', error);
        setIsConnected(false);
      };

    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
    }
  }, []);

  // Handle incoming WebSocket messages
  const handleWebSocketMessage = useCallback((message) => {
    switch (message.type) {
      case 'inventory_updated':
        handleInventoryUpdate(message);
        break;
      case 'sync_status_changed':
        handleSyncStatusChange(message);
        break;
      case 'conflict_detected':
        handleConflictDetected(message);
        break;
      case 'conflict_resolved':
        handleConflictResolved(message);
        break;
      default:
        console.log('Unknown WebSocket message type:', message.type);
    }
  }, []);

  // Handle inventory update messages
  const handleInventoryUpdate = useCallback((message) => {
    const { product_id, variant_id, new_quantity, old_quantity, timestamp } = message;
    
    const updateKey = variant_id ? `${product_id}_${variant_id}` : `${product_id}`;
    
    setInventoryUpdates(prev => ({
      ...prev,
      [updateKey]: {
        product_id,
        variant_id,
        quantity: new_quantity,
        previous_quantity: old_quantity,
        updated_at: timestamp,
        in_stock: new_quantity > 0
      }
    }));

    // Show notification for significant changes
    if (Math.abs(new_quantity - old_quantity) > 10) {
      showInventoryNotification(product_id, variant_id, new_quantity, old_quantity);
    }
  }, []);

  // Handle sync status changes
  const handleSyncStatusChange = useCallback((message) => {
    const { channel_id, channel_name, status, product_id, error } = message;
    
    setSyncStatus(prev => ({
      ...prev,
      [channel_id]: {
        channel_name,
        status,
        product_id,
        error,
        updated_at: new Date().toISOString()
      }
    }));
  }, []);

  // Handle conflict detection
  const handleConflictDetected = useCallback((message) => {
    const { conflict_id, product_id, product_name, conflict_type, priority } = message;
    
    setConflicts(prev => [
      ...prev.filter(c => c.id !== conflict_id),
      {
        id: conflict_id,
        product_id,
        product_name,
        conflict_type,
        priority,
        status: 'pending',
        detected_at: new Date().toISOString()
      }
    ]);

    // Show high-priority conflict notifications
    if (priority === 'high' || priority === 'critical') {
      showConflictNotification(product_name, conflict_type, priority);
    }
  }, []);

  // Handle conflict resolution
  const handleConflictResolved = useCallback((message) => {
    const { conflict_id, resolved_quantity, strategy } = message;
    
    setConflicts(prev => prev.map(conflict => 
      conflict.id === conflict_id 
        ? { ...conflict, status: 'resolved', resolved_quantity, strategy }
        : conflict
    ));
  }, []);

  // Show inventory update notification
  const showInventoryNotification = (productId, variantId, newQuantity, oldQuantity) => {
    const change = newQuantity - oldQuantity;
    const changeText = change > 0 ? `+${change}` : `${change}`;
    
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Inventory Updated', {
        body: `Product ${productId} inventory changed by ${changeText} (now ${newQuantity})`,
        icon: '/favicon.ico'
      });
    }
  };

  // Show conflict notification
  const showConflictNotification = (productName, conflictType, priority) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`${priority.toUpperCase()} Inventory Conflict`, {
        body: `${conflictType} detected for ${productName}`,
        icon: '/favicon.ico'
      });
    }
  };

  // API functions for inventory management
  const updateInventory = useCallback(async (productId, newQuantity, variantId = null, reason = 'Manual update') => {
    try {
      const response = await fetch(`${API_BASE_URL}/inventory/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          product_id: productId,
          new_quantity: newQuantity,
          variant_id: variantId,
          reason
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update inventory');
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      handleApiError(error, 'Failed to update inventory');
      return { success: false, error: error.message };
    }
  }, [handleApiError]);

  const reserveInventory = useCallback(async (productId, quantity, variantId = null, channelId = null, orderId = null) => {
    try {
      const response = await fetch(`${API_BASE_URL}/inventory/reserve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          product_id: productId,
          quantity,
          variant_id: variantId,
          channel_id: channelId,
          order_id: orderId
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to reserve inventory');
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      handleApiError(error, 'Failed to reserve inventory');
      return { success: false, error: error.message };
    }
  }, [handleApiError]);

  const releaseInventory = useCallback(async (productId, quantity, variantId = null, channelId = null, orderId = null) => {
    try {
      const response = await fetch(`${API_BASE_URL}/inventory/release`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          product_id: productId,
          quantity,
          variant_id: variantId,
          channel_id: channelId,
          order_id: orderId
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to release inventory');
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      handleApiError(error, 'Failed to release inventory');
      return { success: false, error: error.message };
    }
  }, [handleApiError]);

  const triggerSync = useCallback(async (channelId, productId = null) => {
    try {
      const response = await fetch(`${API_BASE_URL}/channels/${channelId}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ product_id: productId })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to trigger sync');
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      handleApiError(error, 'Failed to trigger sync');
      return { success: false, error: error.message };
    }
  }, [handleApiError]);

  // Get current inventory for a product
  const getInventoryStatus = useCallback((productId, variantId = null) => {
    const updateKey = variantId ? `${productId}_${variantId}` : `${productId}`;
    return inventoryUpdates[updateKey] || null;
  }, [inventoryUpdates]);

  // Get sync status for a channel
  const getChannelSyncStatus = useCallback((channelId) => {
    return syncStatus[channelId] || null;
  }, [syncStatus]);

  // Get pending conflicts
  const getPendingConflicts = useCallback(() => {
    return conflicts.filter(c => c.status === 'pending');
  }, [conflicts]);

  // Initialize connection on mount
  useEffect(() => {
    connectWebSocket();

    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close(1000); // Normal closure
      }
    };
  }, [connectWebSocket]);

  return {
    // Connection status
    isConnected,
    
    // Data
    inventoryUpdates,
    syncStatus,
    conflicts,
    
    // API functions
    updateInventory,
    reserveInventory,
    releaseInventory,
    triggerSync,
    
    // Helper functions
    getInventoryStatus,
    getChannelSyncStatus,
    getPendingConflicts,
    
    // Connection management
    reconnect: connectWebSocket
  };
};
