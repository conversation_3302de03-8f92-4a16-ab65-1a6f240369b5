import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '../config/api';

const SearchAnalytics = ({ 
  searchResults, 
  searchQuery, 
  searchFilters,
  showDetailedAnalytics = false 
}) => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [expandedSection, setExpandedSection] = useState(null);

  useEffect(() => {
    if (showDetailedAnalytics && searchResults) {
      fetchDetailedAnalytics();
    }
  }, [searchResults, searchQuery, searchFilters, showDetailedAnalytics]);

  const fetchDetailedAnalytics = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/search/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          filters: searchFilters,
          results_count: searchResults?.total || 0
        })
      });

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      }
    } catch (error) {
      console.error('Failed to fetch search analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (ms) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatPercentage = (value, total) => {
    if (!total) return '0%';
    return `${((value / total) * 100).toFixed(1)}%`;
  };

  const getPerformanceColor = (timeMs) => {
    if (timeMs < 100) return 'text-green-600';
    if (timeMs < 500) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceIcon = (timeMs) => {
    if (timeMs < 100) {
      return (
        <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      );
    }
    if (timeMs < 500) {
      return (
        <svg className="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    }
    return (
      <svg className="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    );
  };

  if (!searchResults) return null;

  const { performance, aggregations, total } = searchResults;

  return (
    <div className="search-analytics bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Search Analytics</h3>
        {showDetailedAnalytics && (
          <button
            onClick={fetchDetailedAnalytics}
            disabled={loading}
            className="px-3 py-1 text-sm text-green-600 hover:text-green-700 border border-green-600 rounded-md hover:bg-green-50 transition-colors disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Refresh Analytics'}
          </button>
        )}
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Time</p>
              <p className={`text-lg font-bold ${getPerformanceColor(performance?.total_time_ms || 0)}`}>
                {formatDuration(performance?.total_time_ms || 0)}
              </p>
            </div>
            {getPerformanceIcon(performance?.total_time_ms || 0)}
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Elasticsearch Time</p>
              <p className={`text-lg font-bold ${getPerformanceColor(performance?.elasticsearch_time_ms || 0)}`}>
                {formatDuration(performance?.elasticsearch_time_ms || 0)}
              </p>
            </div>
            <svg className="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
            </svg>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Results Found</p>
              <p className="text-lg font-bold text-gray-900">
                {total?.toLocaleString() || 0}
              </p>
            </div>
            <svg className="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Query Builder</p>
              <p className="text-lg font-bold text-gray-900">
                {performance?.query_builder || 'Standard'}
              </p>
            </div>
            <svg className="h-4 w-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Aggregations Insights */}
      {aggregations && (
        <div className="space-y-4">
          {/* Categories Distribution */}
          {aggregations.categories && (
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => setExpandedSection(expandedSection === 'categories' ? null : 'categories')}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <span className="font-medium text-gray-900">Categories Distribution</span>
                <svg 
                  className={`h-4 w-4 text-gray-500 transition-transform ${expandedSection === 'categories' ? 'rotate-180' : ''}`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {expandedSection === 'categories' && (
                <div className="px-4 pb-4">
                  <div className="space-y-2">
                    {aggregations.categories.buckets.map((bucket, index) => (
                      <div key={bucket.key} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 bg-green-500 rounded-full opacity-80" style={{opacity: 1 - (index * 0.1)}}></div>
                          <span className="text-sm text-gray-700">{bucket.key}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">{bucket.doc_count}</span>
                          <span className="text-xs text-gray-500">
                            ({formatPercentage(bucket.doc_count, total)})
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Brands Distribution */}
          {aggregations.brands && (
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => setExpandedSection(expandedSection === 'brands' ? null : 'brands')}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <span className="font-medium text-gray-900">Brands Distribution</span>
                <svg 
                  className={`h-4 w-4 text-gray-500 transition-transform ${expandedSection === 'brands' ? 'rotate-180' : ''}`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {expandedSection === 'brands' && (
                <div className="px-4 pb-4">
                  <div className="space-y-2">
                    {aggregations.brands.buckets.map((bucket, index) => (
                      <div key={bucket.key} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 bg-blue-500 rounded-full opacity-80" style={{opacity: 1 - (index * 0.1)}}></div>
                          <span className="text-sm text-gray-700">{bucket.key}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">{bucket.doc_count}</span>
                          <span className="text-xs text-gray-500">
                            ({formatPercentage(bucket.doc_count, total)})
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Price Statistics */}
          {aggregations.price_stats && (
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => setExpandedSection(expandedSection === 'price' ? null : 'price')}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <span className="font-medium text-gray-900">Price Statistics</span>
                <svg 
                  className={`h-4 w-4 text-gray-500 transition-transform ${expandedSection === 'price' ? 'rotate-180' : ''}`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {expandedSection === 'price' && (
                <div className="px-4 pb-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Minimum</p>
                      <p className="text-lg font-bold text-gray-900">
                        ₹{Math.round(aggregations.price_stats.min).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Maximum</p>
                      <p className="text-lg font-bold text-gray-900">
                        ₹{Math.round(aggregations.price_stats.max).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Average</p>
                      <p className="text-lg font-bold text-gray-900">
                        ₹{Math.round(aggregations.price_stats.avg).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Range</p>
                      <p className="text-lg font-bold text-gray-900">
                        ₹{Math.round(aggregations.price_stats.max - aggregations.price_stats.min).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Rating Distribution */}
          {aggregations.rating_ranges && (
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => setExpandedSection(expandedSection === 'ratings' ? null : 'ratings')}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <span className="font-medium text-gray-900">Rating Distribution</span>
                <svg 
                  className={`h-4 w-4 text-gray-500 transition-transform ${expandedSection === 'ratings' ? 'rotate-180' : ''}`}
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {expandedSection === 'ratings' && (
                <div className="px-4 pb-4">
                  <div className="space-y-2">
                    {aggregations.rating_ranges.buckets.map((bucket) => (
                      <div key={bucket.key} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <svg 
                                key={i} 
                                className={`h-4 w-4 ${i < parseFloat(bucket.key.split('-')[0]) ? 'text-yellow-400' : 'text-gray-300'}`} 
                                fill="currentColor" 
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                          </div>
                          <span className="text-sm text-gray-700">{bucket.key}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">{bucket.doc_count}</span>
                          <span className="text-xs text-gray-500">
                            ({formatPercentage(bucket.doc_count, total)})
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Search Tips */}
      {total === 0 && (
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Search Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Try using different keywords or synonyms</li>
            <li>• Remove some filters to broaden your search</li>
            <li>• Check your spelling</li>
            <li>• Use more general terms</li>
            <li>• Try the complex boolean search for advanced queries</li>
          </ul>
        </div>
      )}

      {/* Performance Tips */}
      {performance?.total_time_ms > 1000 && (
        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-yellow-900 mb-2">Performance Notice</h4>
          <p className="text-sm text-yellow-800">
            This search took longer than usual. Consider using more specific filters to improve performance.
          </p>
        </div>
      )}
    </div>
  );
};

export default SearchAnalytics;

// Hook for search history management
export const useSearchHistory = () => {
  const [searchHistory, setSearchHistory] = useState([]);

  useEffect(() => {
    loadSearchHistory();
  }, []);

  const loadSearchHistory = () => {
    try {
      const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
      setSearchHistory(history.slice(0, 10));
    } catch (error) {
      console.error('Failed to load search history:', error);
    }
  };

  const addToHistory = (query, filters) => {
    try {
      const historyItem = {
        query,
        filters,
        timestamp: new Date().toISOString()
      };

      const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
      const updatedHistory = [historyItem, ...history.filter(item =>
        item.query !== query || JSON.stringify(item.filters) !== JSON.stringify(filters)
      )].slice(0, 10);

      localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
      setSearchHistory(updatedHistory);
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  };

  const clearHistory = () => {
    localStorage.removeItem('searchHistory');
    setSearchHistory([]);
  };

  return { searchHistory, addToHistory, clearHistory };
};
