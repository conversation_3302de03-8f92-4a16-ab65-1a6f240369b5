import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { useSellerAuth } from '../contexts/SellerAuthContext';
import { useError } from '../contexts/ErrorContext';
import { useLoading } from '../contexts/LoadingContext';
import { LoadingButton } from '../components/LoadingComponents';
import { ErrorAlert } from '../components/ErrorComponents';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const SellerProductForm = () => {
  const navigate = useNavigate();
  const { productId } = useParams();
  const isEditMode = Boolean(productId);
  const { seller, isAuthenticated, getAuthHeaders } = useSellerAuth();
  const { addError, handleApiError } = useError();
  const { isLoading, withLoading } = useLoading();

  const [formData, setFormData] = useState({
    name: '',
    price: '',
    image: '',
    category: '',
    description: '',
    stock_quantity: '',
    low_stock_threshold: '10',
    sustainability_score: '50'
  });

  const [errors, setErrors] = useState({});

  const categories = [
    'Electronics',
    'Fashion & Apparel',
    'Home & Garden',
    'Health & Beauty',
    'Sports & Outdoors',
    'Books & Media',
    'Toys & Games',
    'Automotive',
    'Food & Beverages',
    'Other'
  ];

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated()) {
      navigate('/seller/login');
      return;
    }

    if (isEditMode) {
      fetchProduct();
    }
  }, [isAuthenticated, navigate, productId, isEditMode]);

  const fetchProduct = async () => {
    try {
      await withLoading('fetch_product', async () => {
        const response = await fetch(`${API_BASE_URL}/seller/products/${productId}`, {
          headers: {
            'Content-Type': 'application/json',
            ...getAuthHeaders()
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to load product');
        }

        const data = await response.json();
        const product = data.data;
        
        setFormData({
          name: product.name || '',
          price: product.price?.toString() || '',
          image: product.image || '',
          category: product.category || '',
          description: product.description || '',
          stock_quantity: product.stock_quantity?.toString() || '',
          low_stock_threshold: product.low_stock_threshold?.toString() || '10',
          sustainability_score: product.sustainability_score?.toString() || '50'
        });
      }, 'Loading product...');
    } catch (error) {
      handleApiError(error, { action: 'fetch_product' });
      navigate('/seller/products');
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    } else if (isNaN(formData.price) || parseFloat(formData.price) <= 0) {
      newErrors.price = 'Price must be a valid number greater than 0';
    }

    if (!formData.image.trim()) {
      newErrors.image = 'Product image URL is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.stock_quantity.trim()) {
      newErrors.stock_quantity = 'Stock quantity is required';
    } else if (isNaN(formData.stock_quantity) || parseInt(formData.stock_quantity) < 0) {
      newErrors.stock_quantity = 'Stock quantity must be a valid number (0 or greater)';
    }

    if (!formData.low_stock_threshold.trim()) {
      newErrors.low_stock_threshold = 'Low stock threshold is required';
    } else if (isNaN(formData.low_stock_threshold) || parseInt(formData.low_stock_threshold) < 0) {
      newErrors.low_stock_threshold = 'Low stock threshold must be a valid number (0 or greater)';
    }

    if (!formData.sustainability_score.trim()) {
      newErrors.sustainability_score = 'Sustainability score is required';
    } else {
      const score = parseInt(formData.sustainability_score);
      if (isNaN(score) || score < 0 || score > 100) {
        newErrors.sustainability_score = 'Sustainability score must be between 0 and 100';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const method = isEditMode ? 'PUT' : 'POST';
      const url = isEditMode 
        ? `${API_BASE_URL}/seller/products/${productId}`
        : `${API_BASE_URL}/seller/products`;

      await withLoading('save_product', async () => {
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...getAuthHeaders()
          },
          body: JSON.stringify({
            name: formData.name.trim(),
            price: parseFloat(formData.price),
            image: formData.image.trim(),
            category: formData.category,
            description: formData.description.trim(),
            stock_quantity: parseInt(formData.stock_quantity),
            low_stock_threshold: parseInt(formData.low_stock_threshold),
            sustainability_score: parseInt(formData.sustainability_score)
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to ${isEditMode ? 'update' : 'create'} product`);
        }

        navigate('/seller/products');
      }, `${isEditMode ? 'Updating' : 'Creating'} product...`);
    } catch (error) {
      handleApiError(error, { action: 'save_product' });
    }
  };

  if (!isAuthenticated()) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-4">
            <Link to="/seller/products" className="text-gray-500 hover:text-gray-700 mr-4">
              ← Back to Products
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEditMode ? 'Edit Product' : 'Add New Product'}
            </h1>
          </div>
        </div>
      </header>

      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="space-y-6 p-6">
            {/* Product Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter product name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>

            {/* Price */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price (₹) *
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                  errors.price ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="0.00"
              />
              {errors.price && <p className="mt-1 text-sm text-red-600">{errors.price}</p>}
            </div>

            {/* Image URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Image URL *
              </label>
              <input
                type="url"
                value={formData.image}
                onChange={(e) => handleInputChange('image', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                  errors.image ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="https://example.com/image.jpg"
              />
              {errors.image && <p className="mt-1 text-sm text-red-600">{errors.image}</p>}
              {formData.image && (
                <div className="mt-2">
                  <img
                    src={formData.image}
                    alt="Product preview"
                    className="h-32 w-32 object-cover rounded-lg border"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                  errors.category ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                rows={4}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Describe your product..."
              />
              {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
            </div>

            {/* Stock Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Stock Quantity *
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.stock_quantity}
                  onChange={(e) => handleInputChange('stock_quantity', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                    errors.stock_quantity ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="0"
                />
                {errors.stock_quantity && <p className="mt-1 text-sm text-red-600">{errors.stock_quantity}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Low Stock Threshold *
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.low_stock_threshold}
                  onChange={(e) => handleInputChange('low_stock_threshold', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                    errors.low_stock_threshold ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="10"
                />
                {errors.low_stock_threshold && <p className="mt-1 text-sm text-red-600">{errors.low_stock_threshold}</p>}
                <p className="mt-1 text-sm text-gray-500">
                  You'll be notified when stock falls below this number
                </p>
              </div>
            </div>

            {/* Sustainability Score */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sustainability Score (0-100) *
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={formData.sustainability_score}
                onChange={(e) => handleInputChange('sustainability_score', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${
                  errors.sustainability_score ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="50"
              />
              {errors.sustainability_score && <p className="mt-1 text-sm text-red-600">{errors.sustainability_score}</p>}
              <p className="mt-1 text-sm text-gray-500">
                Rate how sustainable this product is (higher is better)
              </p>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Link
                to="/seller/products"
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Cancel
              </Link>
              <LoadingButton
                type="submit"
                loading={isLoading('save_product')}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                {isEditMode ? 'Update Product' : 'Create Product'}
              </LoadingButton>
            </div>
          </form>
        </div>
      </div>

      {/* Global Error Display */}
      <ErrorAlert />
    </div>
  );
};

export default SellerProductForm;
