/* Order Tracking Styles */

/* Tracking Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Tracking Status Icons */
.tracking-status-icon {
  transition: all 0.3s ease;
}

.tracking-status-icon:hover {
  transform: scale(1.1);
}

/* Progress Timeline */
.tracking-timeline {
  position: relative;
}

.tracking-timeline::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #10b981, #3b82f6);
  border-radius: 1px;
}

.tracking-event {
  position: relative;
  padding-left: 40px;
  margin-bottom: 24px;
}

.tracking-event::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 8px;
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #10b981;
}

.tracking-event.exception::before {
  background: #ef4444;
  box-shadow: 0 0 0 2px #ef4444;
}

.tracking-event.delivered::before {
  background: #10b981;
  box-shadow: 0 0 0 2px #10b981;
  animation: pulse 2s infinite;
}

/* Status Cards */
.status-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.status-card.delivered {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #10b981;
}

.status-card.in-transit {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #3b82f6;
}

.status-card.exception {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border-color: #ef4444;
}

/* Tracking Widget */
.tracking-widget {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfeff 100%);
  border: 1px solid #d1fae5;
  transition: all 0.3s ease;
}

.tracking-widget:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

/* Loading States */
.tracking-loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.tracking-skeleton {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .tracking-modal {
    margin: 16px;
    max-height: calc(100vh - 32px);
  }
  
  .tracking-timeline::before {
    left: 8px;
  }
  
  .tracking-event {
    padding-left: 32px;
  }
  
  .tracking-event::before {
    left: 4px;
  }
}

/* Notification Styles */
.tracking-notification {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  animation: slideUp 0.3s ease;
}

.tracking-notification.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Interactive Elements */
.tracking-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tracking-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.tracking-button:active {
  transform: translateY(0);
}

.tracking-button.secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.tracking-button.secondary:hover {
  box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.delivered {
  background: #dcfce7;
  color: #166534;
}

.status-badge.in-transit {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.out-for-delivery {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.exception {
  background: #fecaca;
  color: #991b1b;
}

.status-badge.pending {
  background: #f3f4f6;
  color: #374151;
}

/* Tracking Page Specific */
.tracking-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

.tracking-hero {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
}

.tracking-hero h1 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 16px;
}

.tracking-hero p {
  font-size: 1.25rem;
  opacity: 0.9;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease;
}

.slide-up {
  animation: slideUp 0.5s ease;
}

.tracking-pulse {
  animation: pulse 2s infinite;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .tracking-widget {
    background: linear-gradient(135deg, #064e3b 0%, #0f766e 100%);
    border-color: #065f46;
    color: white;
  }
  
  .status-card {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #4b5563;
    color: white;
  }
  
  .tracking-page {
    background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
  }
}
