import React, { useState, useEffect } from 'react';
import { useError } from '../contexts/ErrorContext';
import { useLoading } from '../contexts/LoadingContext';
import { LoadingSpinner } from '../components/LoadingComponents';
import { ErrorAlert } from '../components/ErrorComponents';
import './InventorySyncDashboard.css';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const InventorySyncDashboard = () => {
  const { handleApiError } = useError();
  const { isLoading, withLoading } = useLoading();

  // State management
  const [channels, setChannels] = useState([]);
  const [conflicts, setConflicts] = useState([]);
  const [syncLogs, setSyncLogs] = useState([]);
  const [stats, setStats] = useState({});
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [showChannelModal, setShowChannelModal] = useState(false);
  const [showConflictModal, setShowConflictModal] = useState(false);
  const [selectedConflict, setSelectedConflict] = useState(null);

  // Pagination
  const [conflictsPagination, setConflictsPagination] = useState({
    page: 1,
    per_page: 10,
    total: 0
  });

  useEffect(() => {
    loadDashboardData();
    
    // Set up real-time updates
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        loadChannels(),
        loadConflicts(),
        loadSyncStats()
      ]);
    } catch (error) {
      handleApiError(error, 'Failed to load dashboard data');
    }
  };

  const loadChannels = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/admin/channels`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) throw new Error('Failed to fetch channels');
      
      const data = await response.json();
      setChannels(data.channels || []);
    } catch (error) {
      console.error('Error loading channels:', error);
    }
  };

  const loadConflicts = async (page = 1) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/inventory/conflicts?page=${page}&per_page=${conflictsPagination.per_page}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.ok) throw new Error('Failed to fetch conflicts');
      
      const data = await response.json();
      setConflicts(data.conflicts || []);
      setConflictsPagination(data.pagination || {});
    } catch (error) {
      console.error('Error loading conflicts:', error);
    }
  };

  const loadSyncStats = async () => {
    try {
      // This would be a new endpoint for sync statistics
      const response = await fetch(`${API_BASE_URL}/admin/inventory/stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error loading sync stats:', error);
    }
  };

  const triggerChannelSync = async (channelId, productId = null) => {
    try {
      await withLoading(async () => {
        const response = await fetch(`${API_BASE_URL}/channels/${channelId}/sync`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({ product_id: productId })
        });

        if (!response.ok) throw new Error('Failed to trigger sync');
        
        const data = await response.json();
        alert(`Sync triggered: ${data.message}`);
        loadChannels(); // Refresh channels
      });
    } catch (error) {
      handleApiError(error, 'Failed to trigger sync');
    }
  };

  const resolveConflict = async (conflictId, strategy, resolvedQuantity = null, notes = '') => {
    try {
      await withLoading(async () => {
        const response = await fetch(`${API_BASE_URL}/inventory/conflicts/${conflictId}/resolve`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            strategy,
            resolved_quantity: resolvedQuantity,
            notes
          })
        });

        if (!response.ok) throw new Error('Failed to resolve conflict');
        
        const data = await response.json();
        alert(`Conflict resolved: ${data.message}`);
        loadConflicts(); // Refresh conflicts
        setShowConflictModal(false);
        setSelectedConflict(null);
      });
    } catch (error) {
      handleApiError(error, 'Failed to resolve conflict');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'disabled': return 'text-gray-600';
      default: return 'text-yellow-600';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading) {
    return <LoadingSpinner message="Loading inventory sync dashboard..." />;
  }

  return (
    <div className="inventory-sync-dashboard">
      <div className="dashboard-header">
        <h1>Inventory Synchronization Dashboard</h1>
        <div className="dashboard-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setShowChannelModal(true)}
          >
            Add Channel
          </button>
          <button 
            className="btn btn-secondary"
            onClick={loadDashboardData}
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Dashboard Stats */}
      <div className="dashboard-stats">
        <div className="stat-card">
          <h3>Active Channels</h3>
          <div className="stat-value">{channels.filter(c => c.is_active).length}</div>
        </div>
        <div className="stat-card">
          <h3>Pending Conflicts</h3>
          <div className="stat-value text-red-600">{conflicts.filter(c => c.status === 'pending').length}</div>
        </div>
        <div className="stat-card">
          <h3>Sync Success Rate</h3>
          <div className="stat-value text-green-600">
            {stats.overall_success_rate ? `${stats.overall_success_rate.toFixed(1)}%` : 'N/A'}
          </div>
        </div>
        <div className="stat-card">
          <h3>Last Sync</h3>
          <div className="stat-value text-sm">
            {stats.last_sync_time ? new Date(stats.last_sync_time).toLocaleString() : 'Never'}
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="dashboard-tabs">
        <button 
          className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button 
          className={`tab ${activeTab === 'channels' ? 'active' : ''}`}
          onClick={() => setActiveTab('channels')}
        >
          Channels
        </button>
        <button 
          className={`tab ${activeTab === 'conflicts' ? 'active' : ''}`}
          onClick={() => setActiveTab('conflicts')}
        >
          Conflicts ({conflicts.filter(c => c.status === 'pending').length})
        </button>
        <button 
          className={`tab ${activeTab === 'logs' ? 'active' : ''}`}
          onClick={() => setActiveTab('logs')}
        >
          Sync Logs
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'overview' && (
          <OverviewTab 
            channels={channels} 
            conflicts={conflicts} 
            stats={stats}
            onTriggerSync={triggerChannelSync}
          />
        )}
        
        {activeTab === 'channels' && (
          <ChannelsTab 
            channels={channels} 
            onTriggerSync={triggerChannelSync}
            onEditChannel={setSelectedChannel}
          />
        )}
        
        {activeTab === 'conflicts' && (
          <ConflictsTab 
            conflicts={conflicts}
            pagination={conflictsPagination}
            onLoadPage={loadConflicts}
            onResolveConflict={(conflict) => {
              setSelectedConflict(conflict);
              setShowConflictModal(true);
            }}
            getPriorityColor={getPriorityColor}
          />
        )}
        
        {activeTab === 'logs' && (
          <SyncLogsTab syncLogs={syncLogs} />
        )}
      </div>

      {/* Conflict Resolution Modal */}
      {showConflictModal && selectedConflict && (
        <ConflictResolutionModal
          conflict={selectedConflict}
          onResolve={resolveConflict}
          onClose={() => {
            setShowConflictModal(false);
            setSelectedConflict(null);
          }}
        />
      )}
    </div>
  );
};

// Overview Tab Component
const OverviewTab = ({ channels, conflicts, stats, onTriggerSync }) => (
  <div className="overview-tab">
    <div className="overview-grid">
      <div className="overview-section">
        <h3>Channel Status</h3>
        <div className="channel-status-list">
          {channels.slice(0, 5).map(channel => (
            <div key={channel.id} className="channel-status-item">
              <span className="channel-name">{channel.name}</span>
              <span className={`status ${channel.status}`}>{channel.status}</span>
              <span className="sync-time">
                {channel.last_sync_at ? 
                  new Date(channel.last_sync_at).toLocaleString() : 
                  'Never synced'
                }
              </span>
            </div>
          ))}
        </div>
      </div>
      
      <div className="overview-section">
        <h3>Recent Conflicts</h3>
        <div className="conflicts-list">
          {conflicts.slice(0, 5).map(conflict => (
            <div key={conflict.id} className="conflict-item">
              <span className="product-name">{conflict.product_name}</span>
              <span className={`priority ${conflict.priority}`}>{conflict.priority}</span>
              <span className="conflict-type">{conflict.conflict_type}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Channels Tab Component
const ChannelsTab = ({ channels, onTriggerSync, onEditChannel }) => (
  <div className="channels-tab">
    <div className="channels-table">
      <table>
        <thead>
          <tr>
            <th>Channel Name</th>
            <th>Type</th>
            <th>Status</th>
            <th>Priority</th>
            <th>Last Sync</th>
            <th>Success Rate</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {channels.map(channel => (
            <tr key={channel.id}>
              <td>
                <div className="channel-info">
                  <span className="channel-name">{channel.name}</span>
                  {!channel.is_active && <span className="inactive-badge">Inactive</span>}
                </div>
              </td>
              <td>{channel.channel_type}</td>
              <td>
                <span className={`status-badge ${channel.status}`}>
                  {channel.status}
                </span>
              </td>
              <td>
                <span className="priority-badge">P{channel.priority}</span>
              </td>
              <td>
                {channel.last_sync_at ?
                  new Date(channel.last_sync_at).toLocaleString() :
                  'Never'
                }
              </td>
              <td>
                <span className={`success-rate ${channel.stats?.success_rate > 90 ? 'high' : 'low'}`}>
                  {channel.stats?.success_rate?.toFixed(1) || 0}%
                </span>
              </td>
              <td>
                <div className="channel-actions">
                  <button
                    className="btn btn-sm btn-primary"
                    onClick={() => onTriggerSync(channel.id)}
                    disabled={!channel.sync_enabled}
                  >
                    Sync Now
                  </button>
                  <button
                    className="btn btn-sm btn-secondary"
                    onClick={() => onEditChannel(channel)}
                  >
                    Edit
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

// Conflicts Tab Component
const ConflictsTab = ({ conflicts, pagination, onLoadPage, onResolveConflict, getPriorityColor }) => (
  <div className="conflicts-tab">
    <div className="conflicts-table">
      <table>
        <thead>
          <tr>
            <th>Product</th>
            <th>Conflict Type</th>
            <th>Priority</th>
            <th>Master Qty</th>
            <th>Conflicting Data</th>
            <th>Created</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {conflicts.map(conflict => (
            <tr key={conflict.id}>
              <td>
                <div className="product-info">
                  <span className="product-name">{conflict.product_name}</span>
                  <span className="product-id">ID: {conflict.product_id}</span>
                </div>
              </td>
              <td>{conflict.conflict_type}</td>
              <td>
                <span className={`priority-badge ${getPriorityColor(conflict.priority)}`}>
                  {conflict.priority}
                </span>
              </td>
              <td>{conflict.master_quantity}</td>
              <td>
                <div className="conflicting-data">
                  {typeof conflict.conflicting_data === 'object' ?
                    JSON.stringify(conflict.conflicting_data, null, 2) :
                    conflict.conflicting_data
                  }
                </div>
              </td>
              <td>{new Date(conflict.created_at).toLocaleString()}</td>
              <td>
                <button
                  className="btn btn-sm btn-primary"
                  onClick={() => onResolveConflict(conflict)}
                >
                  Resolve
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>

    {/* Pagination */}
    <div className="pagination">
      <button
        onClick={() => onLoadPage(pagination.page - 1)}
        disabled={!pagination.has_prev}
        className="btn btn-sm"
      >
        Previous
      </button>
      <span className="page-info">
        Page {pagination.page} of {pagination.pages}
        ({pagination.total} total conflicts)
      </span>
      <button
        onClick={() => onLoadPage(pagination.page + 1)}
        disabled={!pagination.has_next}
        className="btn btn-sm"
      >
        Next
      </button>
    </div>
  </div>
);

// Sync Logs Tab Component
const SyncLogsTab = ({ syncLogs }) => (
  <div className="sync-logs-tab">
    <div className="logs-table">
      <table>
        <thead>
          <tr>
            <th>Timestamp</th>
            <th>Product</th>
            <th>Channel</th>
            <th>Operation</th>
            <th>Status</th>
            <th>Duration</th>
            <th>Details</th>
          </tr>
        </thead>
        <tbody>
          {syncLogs.map(log => (
            <tr key={log.id}>
              <td>{new Date(log.started_at).toLocaleString()}</td>
              <td>
                <div className="product-info">
                  <span>{log.product_name}</span>
                  <small>ID: {log.product_id}</small>
                </div>
              </td>
              <td>{log.channel_name}</td>
              <td>{log.operation}</td>
              <td>
                <span className={`status-badge ${log.status}`}>
                  {log.status}
                </span>
              </td>
              <td>
                {log.completed_at && log.started_at ?
                  `${((new Date(log.completed_at) - new Date(log.started_at)) / 1000).toFixed(2)}s` :
                  'N/A'
                }
              </td>
              <td>
                {log.error_message && (
                  <span className="error-message">{log.error_message}</span>
                )}
                {log.quantity_change && (
                  <span className="quantity-change">
                    {log.old_quantity} → {log.new_quantity}
                  </span>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

// Conflict Resolution Modal Component
const ConflictResolutionModal = ({ conflict, onResolve, onClose }) => {
  const [strategy, setStrategy] = useState('use_master');
  const [resolvedQuantity, setResolvedQuantity] = useState('');
  const [notes, setNotes] = useState('');

  const handleResolve = () => {
    const quantity = strategy === 'manual' ? parseInt(resolvedQuantity) : null;
    onResolve(conflict.id, strategy, quantity, notes);
  };

  return (
    <div className="modal-overlay">
      <div className="modal conflict-resolution-modal">
        <div className="modal-header">
          <h3>Resolve Inventory Conflict</h3>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          <div className="conflict-details">
            <h4>Conflict Details</h4>
            <p><strong>Product:</strong> {conflict.product_name}</p>
            <p><strong>Type:</strong> {conflict.conflict_type}</p>
            <p><strong>Master Quantity:</strong> {conflict.master_quantity}</p>
            <p><strong>Priority:</strong> {conflict.priority}</p>
            <div className="conflicting-data">
              <strong>Conflicting Data:</strong>
              <pre>{JSON.stringify(conflict.conflicting_data, null, 2)}</pre>
            </div>
          </div>

          <div className="resolution-options">
            <h4>Resolution Strategy</h4>
            <div className="radio-group">
              <label>
                <input
                  type="radio"
                  value="use_master"
                  checked={strategy === 'use_master'}
                  onChange={(e) => setStrategy(e.target.value)}
                />
                Use Master Inventory ({conflict.master_quantity})
              </label>
              <label>
                <input
                  type="radio"
                  value="use_channel"
                  checked={strategy === 'use_channel'}
                  onChange={(e) => setStrategy(e.target.value)}
                />
                Use Highest Priority Channel
              </label>
              <label>
                <input
                  type="radio"
                  value="manual"
                  checked={strategy === 'manual'}
                  onChange={(e) => setStrategy(e.target.value)}
                />
                Manual Resolution
              </label>
            </div>

            {strategy === 'manual' && (
              <div className="manual-quantity">
                <label>Resolved Quantity:</label>
                <input
                  type="number"
                  value={resolvedQuantity}
                  onChange={(e) => setResolvedQuantity(e.target.value)}
                  min="0"
                  required
                />
              </div>
            )}

            <div className="resolution-notes">
              <label>Resolution Notes:</label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Optional notes about this resolution..."
                rows="3"
              />
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Cancel
          </button>
          <button
            className="btn btn-primary"
            onClick={handleResolve}
            disabled={strategy === 'manual' && !resolvedQuantity}
          >
            Resolve Conflict
          </button>
        </div>
      </div>
    </div>
  );
};

export default InventorySyncDashboard;
