{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\components\\\\TrackingWidget.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Package, Search, ArrowRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrackingWidget = () => {\n  _s();\n  const [trackingNumber, setTrackingNumber] = useState('');\n  const navigate = useNavigate();\n  const handleTrackSubmit = e => {\n    e.preventDefault();\n    if (trackingNumber.trim()) {\n      navigate(`/track/${trackingNumber.trim()}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 mb-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(Package, {\n          className: \"w-8 h-8 text-green-600 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Track Your Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Enter your tracking number to get real-time updates on your shipment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleTrackSubmit,\n      className: \"max-w-md mx-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: trackingNumber,\n            onChange: e => setTrackingNumber(e.target.value),\n            placeholder: \"Enter tracking number\",\n            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"w-5 h-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), \"Track\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/track'),\n        className: \"inline-flex items-center text-green-600 hover:text-green-700 font-medium transition-colors\",\n        children: [\"Need help finding your tracking number?\", /*#__PURE__*/_jsxDEV(ArrowRight, {\n          className: \"w-4 h-4 ml-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(TrackingWidget, \"LIpd040XUOuwd0nebndoXrquHdo=\", false, function () {\n  return [useNavigate];\n});\n_c = TrackingWidget;\nexport default TrackingWidget;\nvar _c;\n$RefreshReg$(_c, \"TrackingWidget\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Package", "Search", "ArrowRight", "jsxDEV", "_jsxDEV", "TrackingWidget", "_s", "trackingNumber", "setTrackingNumber", "navigate", "handleTrackSubmit", "e", "preventDefault", "trim", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/components/TrackingWidget.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Package, Search, ArrowRight } from 'lucide-react';\n\nconst TrackingWidget = () => {\n  const [trackingNumber, setTrackingNumber] = useState('');\n  const navigate = useNavigate();\n\n  const handleTrackSubmit = (e) => {\n    e.preventDefault();\n    if (trackingNumber.trim()) {\n      navigate(`/track/${trackingNumber.trim()}`);\n    }\n  };\n\n  return (\n    <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 mb-8\">\n      <div className=\"text-center mb-4\">\n        <div className=\"flex items-center justify-center mb-2\">\n          <Package className=\"w-8 h-8 text-green-600 mr-2\" />\n          <h2 className=\"text-2xl font-bold text-gray-900\">Track Your Order</h2>\n        </div>\n        <p className=\"text-gray-600\">\n          Enter your tracking number to get real-time updates on your shipment\n        </p>\n      </div>\n\n      <form onSubmit={handleTrackSubmit} className=\"max-w-md mx-auto\">\n        <div className=\"flex space-x-2\">\n          <div className=\"flex-1\">\n            <input\n              type=\"text\"\n              value={trackingNumber}\n              onChange={(e) => setTrackingNumber(e.target.value)}\n              placeholder=\"Enter tracking number\"\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n            />\n          </div>\n          <button\n            type=\"submit\"\n            className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center\"\n          >\n            <Search className=\"w-5 h-5 mr-2\" />\n            Track\n          </button>\n        </div>\n      </form>\n\n      <div className=\"text-center mt-4\">\n        <button\n          onClick={() => navigate('/track')}\n          className=\"inline-flex items-center text-green-600 hover:text-green-700 font-medium transition-colors\"\n        >\n          Need help finding your tracking number?\n          <ArrowRight className=\"w-4 h-4 ml-1\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default TrackingWidget;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIL,cAAc,CAACM,IAAI,CAAC,CAAC,EAAE;MACzBJ,QAAQ,CAAC,UAAUF,cAAc,CAACM,IAAI,CAAC,CAAC,EAAE,CAAC;IAC7C;EACF,CAAC;EAED,oBACET,OAAA;IAAKU,SAAS,EAAC,+DAA+D;IAAAC,QAAA,gBAC5EX,OAAA;MAAKU,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BX,OAAA;QAAKU,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDX,OAAA,CAACJ,OAAO;UAACc,SAAS,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDf,OAAA;UAAIU,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACNf,OAAA;QAAGU,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE7B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENf,OAAA;MAAMgB,QAAQ,EAAEV,iBAAkB;MAACI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC7DX,OAAA;QAAKU,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BX,OAAA;UAAKU,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrBX,OAAA;YACEiB,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEf,cAAe;YACtBgB,QAAQ,EAAGZ,CAAC,IAAKH,iBAAiB,CAACG,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;YACnDG,WAAW,EAAC,uBAAuB;YACnCX,SAAS,EAAC;UAA+G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1H;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNf,OAAA;UACEiB,IAAI,EAAC,QAAQ;UACbP,SAAS,EAAC,2JAA2J;UAAAC,QAAA,gBAErKX,OAAA,CAACH,MAAM;YAACa,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPf,OAAA;MAAKU,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BX,OAAA;QACEsB,OAAO,EAAEA,CAAA,KAAMjB,QAAQ,CAAC,QAAQ,CAAE;QAClCK,SAAS,EAAC,4FAA4F;QAAAC,QAAA,GACvG,yCAEC,eAAAX,OAAA,CAACF,UAAU;UAACY,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CAvDID,cAAc;EAAA,QAEDN,WAAW;AAAA;AAAA4B,EAAA,GAFxBtB,cAAc;AAyDpB,eAAeA,cAAc;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}