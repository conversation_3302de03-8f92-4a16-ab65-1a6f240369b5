{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\SellerProductForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams, Link } from 'react-router-dom';\nimport { useSellerAuth } from '../contexts/SellerAuthContext';\nimport { useError } from '../contexts/ErrorContext';\nimport { useLoading } from '../contexts/LoadingContext';\nimport { LoadingButton } from '../components/LoadingComponents';\nimport { ErrorAlert } from '../components/ErrorComponents';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nconst SellerProductForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    productId\n  } = useParams();\n  const isEditMode = Boolean(productId);\n  const {\n    seller,\n    isAuthenticated,\n    getAuthHeaders\n  } = useSellerAuth();\n  const {\n    addError,\n    handleApiError\n  } = useError();\n  const {\n    isLoading,\n    withLoading\n  } = useLoading();\n  const [formData, setFormData] = useState({\n    name: '',\n    price: '',\n    image: '',\n    category: '',\n    description: '',\n    stock_quantity: '',\n    low_stock_threshold: '10',\n    sustainability_score: '50'\n  });\n  const [errors, setErrors] = useState({});\n  const categories = ['Electronics', 'Fashion & Apparel', 'Home & Garden', 'Health & Beauty', 'Sports & Outdoors', 'Books & Media', 'Toys & Games', 'Automotive', 'Food & Beverages', 'Other'];\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!isAuthenticated()) {\n      navigate('/seller/login');\n      return;\n    }\n    if (isEditMode) {\n      fetchProduct();\n    }\n  }, [isAuthenticated, navigate, productId, isEditMode]);\n  const fetchProduct = async () => {\n    try {\n      await withLoading('fetch_product', async () => {\n        var _product$price, _product$stock_quanti, _product$low_stock_th, _product$sustainabili;\n        const response = await fetch(`${API_BASE_URL}/seller/products/${productId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            ...getAuthHeaders()\n          }\n        });\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to load product');\n        }\n        const data = await response.json();\n        const product = data.data;\n        setFormData({\n          name: product.name || '',\n          price: ((_product$price = product.price) === null || _product$price === void 0 ? void 0 : _product$price.toString()) || '',\n          image: product.image || '',\n          category: product.category || '',\n          description: product.description || '',\n          stock_quantity: ((_product$stock_quanti = product.stock_quantity) === null || _product$stock_quanti === void 0 ? void 0 : _product$stock_quanti.toString()) || '',\n          low_stock_threshold: ((_product$low_stock_th = product.low_stock_threshold) === null || _product$low_stock_th === void 0 ? void 0 : _product$low_stock_th.toString()) || '10',\n          sustainability_score: ((_product$sustainabili = product.sustainability_score) === null || _product$sustainabili === void 0 ? void 0 : _product$sustainabili.toString()) || '50'\n        });\n      }, 'Loading product...');\n    } catch (error) {\n      handleApiError(error, {\n        action: 'fetch_product'\n      });\n      navigate('/seller/products');\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Product name is required';\n    }\n    if (!formData.price.trim()) {\n      newErrors.price = 'Price is required';\n    } else if (isNaN(formData.price) || parseFloat(formData.price) <= 0) {\n      newErrors.price = 'Price must be a valid number greater than 0';\n    }\n    if (!formData.image.trim()) {\n      newErrors.image = 'Product image URL is required';\n    }\n    if (!formData.category) {\n      newErrors.category = 'Category is required';\n    }\n    if (!formData.description.trim()) {\n      newErrors.description = 'Description is required';\n    }\n    if (!formData.stock_quantity.trim()) {\n      newErrors.stock_quantity = 'Stock quantity is required';\n    } else if (isNaN(formData.stock_quantity) || parseInt(formData.stock_quantity) < 0) {\n      newErrors.stock_quantity = 'Stock quantity must be a valid number (0 or greater)';\n    }\n    if (!formData.low_stock_threshold.trim()) {\n      newErrors.low_stock_threshold = 'Low stock threshold is required';\n    } else if (isNaN(formData.low_stock_threshold) || parseInt(formData.low_stock_threshold) < 0) {\n      newErrors.low_stock_threshold = 'Low stock threshold must be a valid number (0 or greater)';\n    }\n    if (!formData.sustainability_score.trim()) {\n      newErrors.sustainability_score = 'Sustainability score is required';\n    } else {\n      const score = parseInt(formData.sustainability_score);\n      if (isNaN(score) || score < 0 || score > 100) {\n        newErrors.sustainability_score = 'Sustainability score must be between 0 and 100';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      const method = isEditMode ? 'PUT' : 'POST';\n      const url = isEditMode ? `${API_BASE_URL}/seller/products/${productId}` : `${API_BASE_URL}/seller/products`;\n      await withLoading('save_product', async () => {\n        const response = await fetch(url, {\n          method,\n          headers: {\n            'Content-Type': 'application/json',\n            ...getAuthHeaders()\n          },\n          body: JSON.stringify({\n            name: formData.name.trim(),\n            price: parseFloat(formData.price),\n            image: formData.image.trim(),\n            category: formData.category,\n            description: formData.description.trim(),\n            stock_quantity: parseInt(formData.stock_quantity),\n            low_stock_threshold: parseInt(formData.low_stock_threshold),\n            sustainability_score: parseInt(formData.sustainability_score)\n          })\n        });\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || `Failed to ${isEditMode ? 'update' : 'create'} product`);\n        }\n        navigate('/seller/products');\n      }, `${isEditMode ? 'Updating' : 'Creating'} product...`);\n    } catch (error) {\n      handleApiError(error, {\n        action: 'save_product'\n      });\n    }\n  };\n  if (!isAuthenticated()) {\n    return null; // Will redirect\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/seller/products\",\n            className: \"text-gray-500 hover:text-gray-700 mr-4\",\n            children: \"\\u2190 Back to Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: isEditMode ? 'Edit Product' : 'Add New Product'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-6 p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.name,\n              onChange: e => handleInputChange('name', e.target.value),\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${errors.name ? 'border-red-300' : 'border-gray-300'}`,\n              placeholder: \"Enter product name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Price (\\u20B9) *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              step: \"0.01\",\n              min: \"0\",\n              value: formData.price,\n              onChange: e => handleInputChange('price', e.target.value),\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${errors.price ? 'border-red-300' : 'border-gray-300'}`,\n              placeholder: \"0.00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), errors.price && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.price\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Product Image URL *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"url\",\n              value: formData.image,\n              onChange: e => handleInputChange('image', e.target.value),\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${errors.image ? 'border-red-300' : 'border-gray-300'}`,\n              placeholder: \"https://example.com/image.jpg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), errors.image && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.image\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 32\n            }, this), formData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: formData.image,\n                alt: \"Product preview\",\n                className: \"h-32 w-32 object-cover rounded-lg border\",\n                onError: e => {\n                  e.target.style.display = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Category *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: formData.category,\n              onChange: e => handleInputChange('category', e.target.value),\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${errors.category ? 'border-red-300' : 'border-gray-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category,\n                children: category\n              }, category, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), errors.category && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Description *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              rows: 4,\n              value: formData.description,\n              onChange: e => handleInputChange('description', e.target.value),\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${errors.description ? 'border-red-300' : 'border-gray-300'}`,\n              placeholder: \"Describe your product...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), errors.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Stock Quantity *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: \"0\",\n                value: formData.stock_quantity,\n                onChange: e => handleInputChange('stock_quantity', e.target.value),\n                className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${errors.stock_quantity ? 'border-red-300' : 'border-gray-300'}`,\n                placeholder: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), errors.stock_quantity && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.stock_quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Low Stock Threshold *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: \"0\",\n                value: formData.low_stock_threshold,\n                onChange: e => handleInputChange('low_stock_threshold', e.target.value),\n                className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${errors.low_stock_threshold ? 'border-red-300' : 'border-gray-300'}`,\n                placeholder: \"10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), errors.low_stock_threshold && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.low_stock_threshold\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 48\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"You'll be notified when stock falls below this number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Sustainability Score (0-100) *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              min: \"0\",\n              max: \"100\",\n              value: formData.sustainability_score,\n              onChange: e => handleInputChange('sustainability_score', e.target.value),\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${errors.sustainability_score ? 'border-red-300' : 'border-gray-300'}`,\n              placeholder: \"50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), errors.sustainability_score && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.sustainability_score\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 47\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-500\",\n              children: \"Rate how sustainable this product is (higher is better)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 pt-6 border-t\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/seller/products\",\n              className: \"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n              type: \"submit\",\n              loading: isLoading('save_product'),\n              className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n              children: isEditMode ? 'Update Product' : 'Create Product'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ErrorAlert, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n};\n_s(SellerProductForm, \"TBM/y3dlye3in+uUq8SdYEXJUjg=\", false, function () {\n  return [useNavigate, useParams, useSellerAuth, useError, useLoading];\n});\n_c = SellerProductForm;\nexport default SellerProductForm;\nvar _c;\n$RefreshReg$(_c, \"SellerProductForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useParams", "Link", "useSellerAuth", "useError", "useLoading", "LoadingButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "SellerProductForm", "_s", "navigate", "productId", "isEditMode", "Boolean", "seller", "isAuthenticated", "getAuthHeaders", "addError", "handleApiError", "isLoading", "with<PERSON>oa<PERSON>", "formData", "setFormData", "name", "price", "image", "category", "description", "stock_quantity", "low_stock_threshold", "sustainability_score", "errors", "setErrors", "categories", "fetchProduct", "_product$price", "_product$stock_quanti", "_product$low_stock_th", "_product$sustainabili", "response", "fetch", "headers", "ok", "errorData", "json", "Error", "error", "data", "product", "toString", "action", "handleInputChange", "field", "value", "prev", "validateForm", "newErrors", "trim", "isNaN", "parseFloat", "parseInt", "score", "Object", "keys", "length", "handleSubmit", "e", "preventDefault", "method", "url", "body", "JSON", "stringify", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "target", "placeholder", "step", "min", "src", "alt", "onError", "style", "display", "map", "rows", "max", "loading", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/SellerProductForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams, Link } from 'react-router-dom';\nimport { useSellerAuth } from '../contexts/SellerAuthContext';\nimport { useError } from '../contexts/ErrorContext';\nimport { useLoading } from '../contexts/LoadingContext';\nimport { LoadingButton } from '../components/LoadingComponents';\nimport { ErrorAlert } from '../components/ErrorComponents';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nconst SellerProductForm = () => {\n  const navigate = useNavigate();\n  const { productId } = useParams();\n  const isEditMode = Boolean(productId);\n  const { seller, isAuthenticated, getAuthHeaders } = useSellerAuth();\n  const { addError, handleApiError } = useError();\n  const { isLoading, withLoading } = useLoading();\n\n  const [formData, setFormData] = useState({\n    name: '',\n    price: '',\n    image: '',\n    category: '',\n    description: '',\n    stock_quantity: '',\n    low_stock_threshold: '10',\n    sustainability_score: '50'\n  });\n\n  const [errors, setErrors] = useState({});\n\n  const categories = [\n    'Electronics',\n    'Fashion & Apparel',\n    'Home & Garden',\n    'Health & Beauty',\n    'Sports & Outdoors',\n    'Books & Media',\n    'Toys & Games',\n    'Automotive',\n    'Food & Beverages',\n    'Other'\n  ];\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!isAuthenticated()) {\n      navigate('/seller/login');\n      return;\n    }\n\n    if (isEditMode) {\n      fetchProduct();\n    }\n  }, [isAuthenticated, navigate, productId, isEditMode]);\n\n  const fetchProduct = async () => {\n    try {\n      await withLoading('fetch_product', async () => {\n        const response = await fetch(`${API_BASE_URL}/seller/products/${productId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            ...getAuthHeaders()\n          }\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to load product');\n        }\n\n        const data = await response.json();\n        const product = data.data;\n        \n        setFormData({\n          name: product.name || '',\n          price: product.price?.toString() || '',\n          image: product.image || '',\n          category: product.category || '',\n          description: product.description || '',\n          stock_quantity: product.stock_quantity?.toString() || '',\n          low_stock_threshold: product.low_stock_threshold?.toString() || '10',\n          sustainability_score: product.sustainability_score?.toString() || '50'\n        });\n      }, 'Loading product...');\n    } catch (error) {\n      handleApiError(error, { action: 'fetch_product' });\n      navigate('/seller/products');\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear field error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Product name is required';\n    }\n\n    if (!formData.price.trim()) {\n      newErrors.price = 'Price is required';\n    } else if (isNaN(formData.price) || parseFloat(formData.price) <= 0) {\n      newErrors.price = 'Price must be a valid number greater than 0';\n    }\n\n    if (!formData.image.trim()) {\n      newErrors.image = 'Product image URL is required';\n    }\n\n    if (!formData.category) {\n      newErrors.category = 'Category is required';\n    }\n\n    if (!formData.description.trim()) {\n      newErrors.description = 'Description is required';\n    }\n\n    if (!formData.stock_quantity.trim()) {\n      newErrors.stock_quantity = 'Stock quantity is required';\n    } else if (isNaN(formData.stock_quantity) || parseInt(formData.stock_quantity) < 0) {\n      newErrors.stock_quantity = 'Stock quantity must be a valid number (0 or greater)';\n    }\n\n    if (!formData.low_stock_threshold.trim()) {\n      newErrors.low_stock_threshold = 'Low stock threshold is required';\n    } else if (isNaN(formData.low_stock_threshold) || parseInt(formData.low_stock_threshold) < 0) {\n      newErrors.low_stock_threshold = 'Low stock threshold must be a valid number (0 or greater)';\n    }\n\n    if (!formData.sustainability_score.trim()) {\n      newErrors.sustainability_score = 'Sustainability score is required';\n    } else {\n      const score = parseInt(formData.sustainability_score);\n      if (isNaN(score) || score < 0 || score > 100) {\n        newErrors.sustainability_score = 'Sustainability score must be between 0 and 100';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      const method = isEditMode ? 'PUT' : 'POST';\n      const url = isEditMode \n        ? `${API_BASE_URL}/seller/products/${productId}`\n        : `${API_BASE_URL}/seller/products`;\n\n      await withLoading('save_product', async () => {\n        const response = await fetch(url, {\n          method,\n          headers: {\n            'Content-Type': 'application/json',\n            ...getAuthHeaders()\n          },\n          body: JSON.stringify({\n            name: formData.name.trim(),\n            price: parseFloat(formData.price),\n            image: formData.image.trim(),\n            category: formData.category,\n            description: formData.description.trim(),\n            stock_quantity: parseInt(formData.stock_quantity),\n            low_stock_threshold: parseInt(formData.low_stock_threshold),\n            sustainability_score: parseInt(formData.sustainability_score)\n          })\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || `Failed to ${isEditMode ? 'update' : 'create'} product`);\n        }\n\n        navigate('/seller/products');\n      }, `${isEditMode ? 'Updating' : 'Creating'} product...`);\n    } catch (error) {\n      handleApiError(error, { action: 'save_product' });\n    }\n  };\n\n  if (!isAuthenticated()) {\n    return null; // Will redirect\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center py-4\">\n            <Link to=\"/seller/products\" className=\"text-gray-500 hover:text-gray-700 mr-4\">\n              ← Back to Products\n            </Link>\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              {isEditMode ? 'Edit Product' : 'Add New Product'}\n            </h1>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white shadow rounded-lg\">\n          <form onSubmit={handleSubmit} className=\"space-y-6 p-6\">\n            {/* Product Name */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${\n                  errors.name ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter product name\"\n              />\n              {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\n            </div>\n\n            {/* Price */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Price (₹) *\n              </label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                value={formData.price}\n                onChange={(e) => handleInputChange('price', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${\n                  errors.price ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"0.00\"\n              />\n              {errors.price && <p className=\"mt-1 text-sm text-red-600\">{errors.price}</p>}\n            </div>\n\n            {/* Image URL */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Product Image URL *\n              </label>\n              <input\n                type=\"url\"\n                value={formData.image}\n                onChange={(e) => handleInputChange('image', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${\n                  errors.image ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"https://example.com/image.jpg\"\n              />\n              {errors.image && <p className=\"mt-1 text-sm text-red-600\">{errors.image}</p>}\n              {formData.image && (\n                <div className=\"mt-2\">\n                  <img\n                    src={formData.image}\n                    alt=\"Product preview\"\n                    className=\"h-32 w-32 object-cover rounded-lg border\"\n                    onError={(e) => {\n                      e.target.style.display = 'none';\n                    }}\n                  />\n                </div>\n              )}\n            </div>\n\n            {/* Category */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category *\n              </label>\n              <select\n                value={formData.category}\n                onChange={(e) => handleInputChange('category', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${\n                  errors.category ? 'border-red-300' : 'border-gray-300'\n                }`}\n              >\n                <option value=\"\">Select a category</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>{category}</option>\n                ))}\n              </select>\n              {errors.category && <p className=\"mt-1 text-sm text-red-600\">{errors.category}</p>}\n            </div>\n\n            {/* Description */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Description *\n              </label>\n              <textarea\n                rows={4}\n                value={formData.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${\n                  errors.description ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"Describe your product...\"\n              />\n              {errors.description && <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>}\n            </div>\n\n            {/* Stock Information */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Stock Quantity *\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.stock_quantity}\n                  onChange={(e) => handleInputChange('stock_quantity', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${\n                    errors.stock_quantity ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"0\"\n                />\n                {errors.stock_quantity && <p className=\"mt-1 text-sm text-red-600\">{errors.stock_quantity}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Low Stock Threshold *\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.low_stock_threshold}\n                  onChange={(e) => handleInputChange('low_stock_threshold', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${\n                    errors.low_stock_threshold ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"10\"\n                />\n                {errors.low_stock_threshold && <p className=\"mt-1 text-sm text-red-600\">{errors.low_stock_threshold}</p>}\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  You'll be notified when stock falls below this number\n                </p>\n              </div>\n            </div>\n\n            {/* Sustainability Score */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Sustainability Score (0-100) *\n              </label>\n              <input\n                type=\"number\"\n                min=\"0\"\n                max=\"100\"\n                value={formData.sustainability_score}\n                onChange={(e) => handleInputChange('sustainability_score', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${\n                  errors.sustainability_score ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"50\"\n              />\n              {errors.sustainability_score && <p className=\"mt-1 text-sm text-red-600\">{errors.sustainability_score}</p>}\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Rate how sustainable this product is (higher is better)\n              </p>\n            </div>\n\n            {/* Submit Buttons */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t\">\n              <Link\n                to=\"/seller/products\"\n                className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n              >\n                Cancel\n              </Link>\n              <LoadingButton\n                type=\"submit\"\n                loading={isLoading('save_product')}\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n              >\n                {isEditMode ? 'Update Product' : 'Create Product'}\n              </LoadingButton>\n            </div>\n          </form>\n        </div>\n      </div>\n\n      {/* Global Error Display */}\n      <ErrorAlert />\n    </div>\n  );\n};\n\nexport default SellerProductForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,UAAU,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB;EAAU,CAAC,GAAGhB,SAAS,CAAC,CAAC;EACjC,MAAMiB,UAAU,GAAGC,OAAO,CAACF,SAAS,CAAC;EACrC,MAAM;IAAEG,MAAM;IAAEC,eAAe;IAAEC;EAAe,CAAC,GAAGnB,aAAa,CAAC,CAAC;EACnE,MAAM;IAAEoB,QAAQ;IAAEC;EAAe,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EAC/C,MAAM;IAAEqB,SAAS;IAAEC;EAAY,CAAC,GAAGrB,UAAU,CAAC,CAAC;EAE/C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACvC+B,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,mBAAmB,EAAE,IAAI;IACzBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAMyC,UAAU,GAAG,CACjB,aAAa,EACb,mBAAmB,EACnB,eAAe,EACf,iBAAiB,EACjB,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,OAAO,CACR;;EAED;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI,CAACsB,eAAe,CAAC,CAAC,EAAE;MACtBL,QAAQ,CAAC,eAAe,CAAC;MACzB;IACF;IAEA,IAAIE,UAAU,EAAE;MACdsB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACnB,eAAe,EAAEL,QAAQ,EAAEC,SAAS,EAAEC,UAAU,CAAC,CAAC;EAEtD,MAAMsB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMd,WAAW,CAAC,eAAe,EAAE,YAAY;QAAA,IAAAe,cAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAC7C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGpC,YAAY,oBAAoBO,SAAS,EAAE,EAAE;UAC3E8B,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,GAAGzB,cAAc,CAAC;UACpB;QACF,CAAC,CAAC;QAEF,IAAI,CAACuB,QAAQ,CAACG,EAAE,EAAE;UAChB,MAAMC,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,KAAK,IAAI,wBAAwB,CAAC;QAC9D;QAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClC,MAAMI,OAAO,GAAGD,IAAI,CAACA,IAAI;QAEzBzB,WAAW,CAAC;UACVC,IAAI,EAAEyB,OAAO,CAACzB,IAAI,IAAI,EAAE;UACxBC,KAAK,EAAE,EAAAW,cAAA,GAAAa,OAAO,CAACxB,KAAK,cAAAW,cAAA,uBAAbA,cAAA,CAAec,QAAQ,CAAC,CAAC,KAAI,EAAE;UACtCxB,KAAK,EAAEuB,OAAO,CAACvB,KAAK,IAAI,EAAE;UAC1BC,QAAQ,EAAEsB,OAAO,CAACtB,QAAQ,IAAI,EAAE;UAChCC,WAAW,EAAEqB,OAAO,CAACrB,WAAW,IAAI,EAAE;UACtCC,cAAc,EAAE,EAAAQ,qBAAA,GAAAY,OAAO,CAACpB,cAAc,cAAAQ,qBAAA,uBAAtBA,qBAAA,CAAwBa,QAAQ,CAAC,CAAC,KAAI,EAAE;UACxDpB,mBAAmB,EAAE,EAAAQ,qBAAA,GAAAW,OAAO,CAACnB,mBAAmB,cAAAQ,qBAAA,uBAA3BA,qBAAA,CAA6BY,QAAQ,CAAC,CAAC,KAAI,IAAI;UACpEnB,oBAAoB,EAAE,EAAAQ,qBAAA,GAAAU,OAAO,CAAClB,oBAAoB,cAAAQ,qBAAA,uBAA5BA,qBAAA,CAA8BW,QAAQ,CAAC,CAAC,KAAI;QACpE,CAAC,CAAC;MACJ,CAAC,EAAE,oBAAoB,CAAC;IAC1B,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd5B,cAAc,CAAC4B,KAAK,EAAE;QAAEI,MAAM,EAAE;MAAgB,CAAC,CAAC;MAClDxC,QAAQ,CAAC,kBAAkB,CAAC;IAC9B;EACF,CAAC;EAED,MAAMyC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C/B,WAAW,CAACgC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAItB,MAAM,CAACqB,KAAK,CAAC,EAAE;MACjBpB,SAAS,CAACsB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACF,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACnC,QAAQ,CAACE,IAAI,CAACkC,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACjC,IAAI,GAAG,0BAA0B;IAC7C;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACiC,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAChC,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAIkC,KAAK,CAACrC,QAAQ,CAACG,KAAK,CAAC,IAAImC,UAAU,CAACtC,QAAQ,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;MACnEgC,SAAS,CAAChC,KAAK,GAAG,6CAA6C;IACjE;IAEA,IAAI,CAACH,QAAQ,CAACI,KAAK,CAACgC,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC/B,KAAK,GAAG,+BAA+B;IACnD;IAEA,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;MACtB8B,SAAS,CAAC9B,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACL,QAAQ,CAACM,WAAW,CAAC8B,IAAI,CAAC,CAAC,EAAE;MAChCD,SAAS,CAAC7B,WAAW,GAAG,yBAAyB;IACnD;IAEA,IAAI,CAACN,QAAQ,CAACO,cAAc,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACnCD,SAAS,CAAC5B,cAAc,GAAG,4BAA4B;IACzD,CAAC,MAAM,IAAI8B,KAAK,CAACrC,QAAQ,CAACO,cAAc,CAAC,IAAIgC,QAAQ,CAACvC,QAAQ,CAACO,cAAc,CAAC,GAAG,CAAC,EAAE;MAClF4B,SAAS,CAAC5B,cAAc,GAAG,sDAAsD;IACnF;IAEA,IAAI,CAACP,QAAQ,CAACQ,mBAAmB,CAAC4B,IAAI,CAAC,CAAC,EAAE;MACxCD,SAAS,CAAC3B,mBAAmB,GAAG,iCAAiC;IACnE,CAAC,MAAM,IAAI6B,KAAK,CAACrC,QAAQ,CAACQ,mBAAmB,CAAC,IAAI+B,QAAQ,CAACvC,QAAQ,CAACQ,mBAAmB,CAAC,GAAG,CAAC,EAAE;MAC5F2B,SAAS,CAAC3B,mBAAmB,GAAG,2DAA2D;IAC7F;IAEA,IAAI,CAACR,QAAQ,CAACS,oBAAoB,CAAC2B,IAAI,CAAC,CAAC,EAAE;MACzCD,SAAS,CAAC1B,oBAAoB,GAAG,kCAAkC;IACrE,CAAC,MAAM;MACL,MAAM+B,KAAK,GAAGD,QAAQ,CAACvC,QAAQ,CAACS,oBAAoB,CAAC;MACrD,IAAI4B,KAAK,CAACG,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,GAAG,EAAE;QAC5CL,SAAS,CAAC1B,oBAAoB,GAAG,gDAAgD;MACnF;IACF;IAEAE,SAAS,CAACwB,SAAS,CAAC;IACpB,OAAOM,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACQ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACZ,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF,MAAMa,MAAM,GAAGxD,UAAU,GAAG,KAAK,GAAG,MAAM;MAC1C,MAAMyD,GAAG,GAAGzD,UAAU,GAClB,GAAGR,YAAY,oBAAoBO,SAAS,EAAE,GAC9C,GAAGP,YAAY,kBAAkB;MAErC,MAAMgB,WAAW,CAAC,cAAc,EAAE,YAAY;QAC5C,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC6B,GAAG,EAAE;UAChCD,MAAM;UACN3B,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,GAAGzB,cAAc,CAAC;UACpB,CAAC;UACDsD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBjD,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACkC,IAAI,CAAC,CAAC;YAC1BjC,KAAK,EAAEmC,UAAU,CAACtC,QAAQ,CAACG,KAAK,CAAC;YACjCC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,CAACgC,IAAI,CAAC,CAAC;YAC5B/B,QAAQ,EAAEL,QAAQ,CAACK,QAAQ;YAC3BC,WAAW,EAAEN,QAAQ,CAACM,WAAW,CAAC8B,IAAI,CAAC,CAAC;YACxC7B,cAAc,EAAEgC,QAAQ,CAACvC,QAAQ,CAACO,cAAc,CAAC;YACjDC,mBAAmB,EAAE+B,QAAQ,CAACvC,QAAQ,CAACQ,mBAAmB,CAAC;YAC3DC,oBAAoB,EAAE8B,QAAQ,CAACvC,QAAQ,CAACS,oBAAoB;UAC9D,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAACS,QAAQ,CAACG,EAAE,EAAE;UAChB,MAAMC,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,KAAK,IAAI,aAAalC,UAAU,GAAG,QAAQ,GAAG,QAAQ,UAAU,CAAC;QAC7F;QAEAF,QAAQ,CAAC,kBAAkB,CAAC;MAC9B,CAAC,EAAE,GAAGE,UAAU,GAAG,UAAU,GAAG,UAAU,aAAa,CAAC;IAC1D,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACd5B,cAAc,CAAC4B,KAAK,EAAE;QAAEI,MAAM,EAAE;MAAe,CAAC,CAAC;IACnD;EACF,CAAC;EAED,IAAI,CAACnC,eAAe,CAAC,CAAC,EAAE;IACtB,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,oBACEZ,OAAA;IAAKsE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCvE,OAAA;MAAQsE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC7CvE,OAAA;QAAKsE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDvE,OAAA;UAAKsE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCvE,OAAA,CAACP,IAAI;YAAC+E,EAAE,EAAC,kBAAkB;YAACF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAE/E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5E,OAAA;YAAIsE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAC7C9D,UAAU,GAAG,cAAc,GAAG;UAAiB;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET5E,OAAA;MAAKsE,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1DvE,OAAA;QAAKsE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCvE,OAAA;UAAM6E,QAAQ,EAAEf,YAAa;UAACQ,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAErDvE,OAAA;YAAAuE,QAAA,gBACEvE,OAAA;cAAOsE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5E,OAAA;cACE8E,IAAI,EAAC,MAAM;cACX5B,KAAK,EAAEhC,QAAQ,CAACE,IAAK;cACrB2D,QAAQ,EAAGhB,CAAC,IAAKf,iBAAiB,CAAC,MAAM,EAAEe,CAAC,CAACiB,MAAM,CAAC9B,KAAK,CAAE;cAC3DoB,SAAS,EAAE,+GACT1C,MAAM,CAACR,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,EACjD;cACH6D,WAAW,EAAC;YAAoB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,EACDhD,MAAM,CAACR,IAAI,iBAAIpB,OAAA;cAAGsE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3C,MAAM,CAACR;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAGN5E,OAAA;YAAAuE,QAAA,gBACEvE,OAAA;cAAOsE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5E,OAAA;cACE8E,IAAI,EAAC,QAAQ;cACbI,IAAI,EAAC,MAAM;cACXC,GAAG,EAAC,GAAG;cACPjC,KAAK,EAAEhC,QAAQ,CAACG,KAAM;cACtB0D,QAAQ,EAAGhB,CAAC,IAAKf,iBAAiB,CAAC,OAAO,EAAEe,CAAC,CAACiB,MAAM,CAAC9B,KAAK,CAAE;cAC5DoB,SAAS,EAAE,+GACT1C,MAAM,CAACP,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,EAClD;cACH4D,WAAW,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACDhD,MAAM,CAACP,KAAK,iBAAIrB,OAAA;cAAGsE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3C,MAAM,CAACP;YAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAGN5E,OAAA;YAAAuE,QAAA,gBACEvE,OAAA;cAAOsE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5E,OAAA;cACE8E,IAAI,EAAC,KAAK;cACV5B,KAAK,EAAEhC,QAAQ,CAACI,KAAM;cACtByD,QAAQ,EAAGhB,CAAC,IAAKf,iBAAiB,CAAC,OAAO,EAAEe,CAAC,CAACiB,MAAM,CAAC9B,KAAK,CAAE;cAC5DoB,SAAS,EAAE,+GACT1C,MAAM,CAACN,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,EAClD;cACH2D,WAAW,EAAC;YAA+B;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,EACDhD,MAAM,CAACN,KAAK,iBAAItB,OAAA;cAAGsE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3C,MAAM,CAACN;YAAK;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC3E1D,QAAQ,CAACI,KAAK,iBACbtB,OAAA;cAAKsE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBvE,OAAA;gBACEoF,GAAG,EAAElE,QAAQ,CAACI,KAAM;gBACpB+D,GAAG,EAAC,iBAAiB;gBACrBf,SAAS,EAAC,0CAA0C;gBACpDgB,OAAO,EAAGvB,CAAC,IAAK;kBACdA,CAAC,CAACiB,MAAM,CAACO,KAAK,CAACC,OAAO,GAAG,MAAM;gBACjC;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5E,OAAA;YAAAuE,QAAA,gBACEvE,OAAA;cAAOsE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5E,OAAA;cACEkD,KAAK,EAAEhC,QAAQ,CAACK,QAAS;cACzBwD,QAAQ,EAAGhB,CAAC,IAAKf,iBAAiB,CAAC,UAAU,EAAEe,CAAC,CAACiB,MAAM,CAAC9B,KAAK,CAAE;cAC/DoB,SAAS,EAAE,+GACT1C,MAAM,CAACL,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,EACrD;cAAAgD,QAAA,gBAEHvE,OAAA;gBAAQkD,KAAK,EAAC,EAAE;gBAAAqB,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC1C9C,UAAU,CAAC2D,GAAG,CAAClE,QAAQ,iBACtBvB,OAAA;gBAAuBkD,KAAK,EAAE3B,QAAS;gBAAAgD,QAAA,EAAEhD;cAAQ,GAApCA,QAAQ;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACRhD,MAAM,CAACL,QAAQ,iBAAIvB,OAAA;cAAGsE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3C,MAAM,CAACL;YAAQ;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAGN5E,OAAA;YAAAuE,QAAA,gBACEvE,OAAA;cAAOsE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5E,OAAA;cACE0F,IAAI,EAAE,CAAE;cACRxC,KAAK,EAAEhC,QAAQ,CAACM,WAAY;cAC5BuD,QAAQ,EAAGhB,CAAC,IAAKf,iBAAiB,CAAC,aAAa,EAAEe,CAAC,CAACiB,MAAM,CAAC9B,KAAK,CAAE;cAClEoB,SAAS,EAAE,+GACT1C,MAAM,CAACJ,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,EACxD;cACHyD,WAAW,EAAC;YAA0B;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,EACDhD,MAAM,CAACJ,WAAW,iBAAIxB,OAAA;cAAGsE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3C,MAAM,CAACJ;YAAW;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,eAGN5E,OAAA;YAAKsE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDvE,OAAA;cAAAuE,QAAA,gBACEvE,OAAA;gBAAOsE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5E,OAAA;gBACE8E,IAAI,EAAC,QAAQ;gBACbK,GAAG,EAAC,GAAG;gBACPjC,KAAK,EAAEhC,QAAQ,CAACO,cAAe;gBAC/BsD,QAAQ,EAAGhB,CAAC,IAAKf,iBAAiB,CAAC,gBAAgB,EAAEe,CAAC,CAACiB,MAAM,CAAC9B,KAAK,CAAE;gBACrEoB,SAAS,EAAE,+GACT1C,MAAM,CAACH,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,EAC3D;gBACHwD,WAAW,EAAC;cAAG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,EACDhD,MAAM,CAACH,cAAc,iBAAIzB,OAAA;gBAAGsE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE3C,MAAM,CAACH;cAAc;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eAEN5E,OAAA;cAAAuE,QAAA,gBACEvE,OAAA;gBAAOsE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5E,OAAA;gBACE8E,IAAI,EAAC,QAAQ;gBACbK,GAAG,EAAC,GAAG;gBACPjC,KAAK,EAAEhC,QAAQ,CAACQ,mBAAoB;gBACpCqD,QAAQ,EAAGhB,CAAC,IAAKf,iBAAiB,CAAC,qBAAqB,EAAEe,CAAC,CAACiB,MAAM,CAAC9B,KAAK,CAAE;gBAC1EoB,SAAS,EAAE,+GACT1C,MAAM,CAACF,mBAAmB,GAAG,gBAAgB,GAAG,iBAAiB,EAChE;gBACHuD,WAAW,EAAC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EACDhD,MAAM,CAACF,mBAAmB,iBAAI1B,OAAA;gBAAGsE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE3C,MAAM,CAACF;cAAmB;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxG5E,OAAA;gBAAGsE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5E,OAAA;YAAAuE,QAAA,gBACEvE,OAAA;cAAOsE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5E,OAAA;cACE8E,IAAI,EAAC,QAAQ;cACbK,GAAG,EAAC,GAAG;cACPQ,GAAG,EAAC,KAAK;cACTzC,KAAK,EAAEhC,QAAQ,CAACS,oBAAqB;cACrCoD,QAAQ,EAAGhB,CAAC,IAAKf,iBAAiB,CAAC,sBAAsB,EAAEe,CAAC,CAACiB,MAAM,CAAC9B,KAAK,CAAE;cAC3EoB,SAAS,EAAE,+GACT1C,MAAM,CAACD,oBAAoB,GAAG,gBAAgB,GAAG,iBAAiB,EACjE;cACHsD,WAAW,EAAC;YAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACDhD,MAAM,CAACD,oBAAoB,iBAAI3B,OAAA;cAAGsE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3C,MAAM,CAACD;YAAoB;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1G5E,OAAA;cAAGsE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN5E,OAAA;YAAKsE,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDvE,OAAA,CAACP,IAAI;cACH+E,EAAE,EAAC,kBAAkB;cACrBF,SAAS,EAAC,4LAA4L;cAAAC,QAAA,EACvM;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP5E,OAAA,CAACH,aAAa;cACZiF,IAAI,EAAC,QAAQ;cACbc,OAAO,EAAE5E,SAAS,CAAC,cAAc,CAAE;cACnCsD,SAAS,EAAC,kMAAkM;cAAAC,QAAA,EAE3M9D,UAAU,GAAG,gBAAgB,GAAG;YAAgB;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5E,OAAA,CAACF,UAAU;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEV,CAAC;AAACtE,EAAA,CAjZID,iBAAiB;EAAA,QACJd,WAAW,EACNC,SAAS,EAEqBE,aAAa,EAC5BC,QAAQ,EACVC,UAAU;AAAA;AAAAiG,EAAA,GANzCxF,iBAAiB;AAmZvB,eAAeA,iBAAiB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}