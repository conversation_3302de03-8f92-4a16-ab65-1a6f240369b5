import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Package, Search, ArrowRight } from 'lucide-react';

const TrackingWidget = () => {
  const [trackingNumber, setTrackingNumber] = useState('');
  const navigate = useNavigate();

  const handleTrackSubmit = (e) => {
    e.preventDefault();
    if (trackingNumber.trim()) {
      navigate(`/track/${trackingNumber.trim()}`);
    }
  };

  return (
    <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 mb-8">
      <div className="text-center mb-4">
        <div className="flex items-center justify-center mb-2">
          <Package className="w-8 h-8 text-green-600 mr-2" />
          <h2 className="text-2xl font-bold text-gray-900">Track Your Order</h2>
        </div>
        <p className="text-gray-600">
          Enter your tracking number to get real-time updates on your shipment
        </p>
      </div>

      <form onSubmit={handleTrackSubmit} className="max-w-md mx-auto">
        <div className="flex space-x-2">
          <div className="flex-1">
            <input
              type="text"
              value={trackingNumber}
              onChange={(e) => setTrackingNumber(e.target.value)}
              placeholder="Enter tracking number"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <button
            type="submit"
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center"
          >
            <Search className="w-5 h-5 mr-2" />
            Track
          </button>
        </div>
      </form>

      <div className="text-center mt-4">
        <button
          onClick={() => navigate('/track')}
          className="inline-flex items-center text-green-600 hover:text-green-700 font-medium transition-colors"
        >
          Need help finding your tracking number?
          <ArrowRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  );
};

export default TrackingWidget;
