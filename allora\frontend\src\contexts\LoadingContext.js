import React, { createContext, useContext, useState, useCallback } from 'react';

const LoadingContext = createContext();

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

export const LoadingProvider = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState({});
  const [globalLoading, setGlobalLoading] = useState(false);

  // Set loading state for a specific key
  const setLoading = useCallback((key, isLoading, message = '') => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: isLoading ? { loading: true, message } : undefined
    }));
  }, []);

  // Get loading state for a specific key
  const isLoading = useCallback((key) => {
    return loadingStates[key]?.loading || false;
  }, [loadingStates]);

  // Get loading message for a specific key
  const getLoadingMessage = useCallback((key) => {
    return loadingStates[key]?.message || '';
  }, [loadingStates]);

  // Check if any loading state is active
  const hasAnyLoading = useCallback(() => {
    return Object.values(loadingStates).some(state => state?.loading) || globalLoading;
  }, [loadingStates, globalLoading]);

  // Get all active loading states
  const getActiveLoadingStates = useCallback(() => {
    return Object.entries(loadingStates)
      .filter(([_, state]) => state?.loading)
      .map(([key, state]) => ({ key, message: state.message }));
  }, [loadingStates]);

  // Clear specific loading state
  const clearLoading = useCallback((key) => {
    setLoadingStates(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  }, []);

  // Clear all loading states
  const clearAllLoading = useCallback(() => {
    setLoadingStates({});
    setGlobalLoading(false);
  }, []);

  // Set global loading state
  const setGlobalLoadingState = useCallback((isLoading, message = '') => {
    setGlobalLoading(isLoading);
    if (isLoading && message) {
      setLoading('global', true, message);
    } else if (!isLoading) {
      clearLoading('global');
    }
  }, [setLoading, clearLoading]);

  // Async wrapper that automatically manages loading state
  const withLoading = useCallback(async (key, asyncFunction, message = 'Loading...') => {
    try {
      setLoading(key, true, message);
      const result = await asyncFunction();
      return result;
    } catch (error) {
      throw error;
    } finally {
      clearLoading(key);
    }
  }, [setLoading, clearLoading]);

  const value = {
    // State getters
    isLoading,
    getLoadingMessage,
    hasAnyLoading,
    getActiveLoadingStates,
    globalLoading,
    
    // State setters
    setLoading,
    clearLoading,
    clearAllLoading,
    setGlobalLoadingState,
    
    // Utilities
    withLoading
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
};

// Higher-order component for automatic loading management
export const withLoadingState = (WrappedComponent, loadingKey) => {
  return function LoadingWrappedComponent(props) {
    const { isLoading, setLoading, clearLoading, getLoadingMessage } = useLoading();
    
    const loadingProps = {
      isLoading: isLoading(loadingKey),
      setLoading: (loading, message) => setLoading(loadingKey, loading, message),
      clearLoading: () => clearLoading(loadingKey),
      loadingMessage: getLoadingMessage(loadingKey)
    };
    
    return <WrappedComponent {...props} {...loadingProps} />;
  };
};

export default LoadingContext;
