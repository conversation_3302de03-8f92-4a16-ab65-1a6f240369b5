.analytics-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.analytics-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.analytics-header h1 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 600;
}

.analytics-controls {
  display: flex;
  gap: 30px;
  align-items: center;
  flex-wrap: wrap;
}

.date-range-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.date-range-controls label,
.algorithm-filter label {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-weight: 500;
  color: #555;
}

.date-range-controls input,
.algorithm-filter select {
  padding: 8px 12px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.date-range-controls input:focus,
.algorithm-filter select:focus {
  outline: none;
  border-color: #3498db;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-message h3 {
  color: #e74c3c;
  margin-bottom: 15px;
}

.retry-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background: #2980b9;
}

.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.metrics-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.metrics-section h2 {
  margin: 0 0 25px 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
  border-bottom: 3px solid #3498db;
  padding-bottom: 10px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.metric-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  border-left: 5px solid #3498db;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.metric-card.highlight {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border-left: 5px solid #1abc9c;
}

.metric-card h3 {
  margin: 0 0 10px 0;
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.8;
}

.metric-card .metric-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2c3e50;
}

.metric-card.highlight .metric-value {
  color: white;
}

.algorithm-performance,
.algorithm-comparison {
  margin-top: 25px;
}

.algorithm-performance h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  font-size: 1.3rem;
}

.algorithm-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.algorithm-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 2px solid #e1e8ed;
  transition: border-color 0.3s ease;
}

.algorithm-card:hover {
  border-color: #3498db;
}

.algorithm-card h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.algorithm-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.algorithm-metrics span {
  font-size: 0.9rem;
  color: #555;
}

.algorithm-performance-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  border: 2px solid #e1e8ed;
  margin-bottom: 20px;
  transition: border-color 0.3s ease, transform 0.3s ease;
}

.algorithm-performance-card:hover {
  border-color: #3498db;
  transform: translateY(-2px);
}

.algorithm-performance-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.performance-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e1e8ed;
}

.performance-row:last-child {
  border-bottom: none;
}

.performance-row span:first-child {
  font-weight: 500;
  color: #555;
}

.performance-row span:last-child {
  font-weight: 600;
  color: #2c3e50;
}

.engagement-segments,
.recommendations-list {
  margin-top: 25px;
}

.engagement-segments h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  font-size: 1.3rem;
}

.segments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
}

.segment-card {
  background: linear-gradient(135deg, #1abc9c, #16a085);
  color: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  transition: transform 0.3s ease;
}

.segment-card:hover {
  transform: translateY(-3px);
}

.segment-card h4 {
  margin: 0 0 10px 0;
  font-size: 1rem;
  font-weight: 500;
}

.segment-count {
  font-size: 1.8rem;
  font-weight: 700;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.recommendation-card {
  padding: 25px;
  border-radius: 10px;
  border-left: 5px solid #3498db;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.recommendation-card.priority-high {
  border-left-color: #e74c3c;
  background: linear-gradient(135deg, #fff5f5, #f8f9fa);
}

.recommendation-card.priority-medium {
  border-left-color: #f39c12;
  background: linear-gradient(135deg, #fffbf0, #f8f9fa);
}

.recommendation-card.priority-low {
  border-left-color: #27ae60;
  background: linear-gradient(135deg, #f0fff4, #f8f9fa);
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.recommendation-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.priority-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.priority-high {
  background: #e74c3c;
  color: white;
}

.priority-badge.priority-medium {
  background: #f39c12;
  color: white;
}

.priority-badge.priority-low {
  background: #27ae60;
  color: white;
}

.recommendation-card p {
  margin: 0 0 15px 0;
  color: #555;
  line-height: 1.6;
}

.recommendation-meta {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #777;
}

.recommendation-meta span {
  font-weight: 500;
}

.temporal-insights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.insight-card {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
  padding: 25px;
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-3px);
}

.insight-card h3 {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.insight-card p {
  margin: 5px 0;
  font-size: 1rem;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .analytics-container {
    padding: 15px;
  }
  
  .analytics-header {
    padding: 20px;
  }
  
  .analytics-header h1 {
    font-size: 2rem;
  }
  
  .analytics-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .date-range-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
  }
  
  .recommendation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .recommendation-meta {
    flex-direction: column;
    gap: 5px;
  }
}
