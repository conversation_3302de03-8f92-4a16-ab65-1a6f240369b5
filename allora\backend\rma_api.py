"""
RMA API Endpoints
================

Comprehensive API endpoints for Return Merchandise Authorization (RMA) system.
Provides both customer-facing and admin APIs for RMA management.

API Categories:
1. Customer RMA APIs - Create, track, and manage returns
2. Admin RMA APIs - Approve, process, and manage all RMAs
3. RMA Status APIs - Real-time status tracking
4. RMA Analytics APIs - Reporting and analytics
5. RMA Configuration APIs - System configuration

Authentication:
- Customer APIs: JWT token required
- Admin APIs: Admin JWT token required
- Public APIs: No authentication (limited functionality)
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from functools import wraps
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Import RMA components
from rma_engine import RMAEngine, RMAProcessingError, RMAValidationError
from rma_utils import RMAValidator, RMACalculator, RMAReportGenerator
from rma_architecture import R<PERSON><PERSON>tatus, RMAType, ReturnReason

# Database imports
from app import db, RMARequest, RMAItem, RMATimeline, Order, OrderItem, User

logger = logging.getLogger(__name__)

# Create Blueprint
rma_bp = Blueprint('rma', __name__, url_prefix='/api/rma')

def admin_required(f):
    """Decorator to require admin authentication"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        claims = get_jwt()
        if not claims.get('is_admin', False):
            return jsonify({'error': 'Admin access required'}), 403
        return f(*args, **kwargs)
    return decorated_function

def seller_or_admin_required(f):
    """Decorator to require seller or admin authentication"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        claims = get_jwt()
        if not (claims.get('is_admin', False) or claims.get('is_seller', False)):
            return jsonify({'error': 'Seller or admin access required'}), 403
        return f(*args, **kwargs)
    return decorated_function

# ============================================================================
# CUSTOMER RMA APIs
# ============================================================================

@rma_bp.route('/create', methods=['POST'])
@jwt_required()
def create_rma_request():
    """Create a new RMA request"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['order_id', 'items', 'customer_info']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate order ownership
        order = Order.query.filter_by(id=data['order_id'], user_id=user_id).first()
        if not order:
            return jsonify({'error': 'Order not found or access denied'}), 404
        
        # Validate return eligibility
        from rma_architecture import RMA_CONFIG
        is_eligible, message = RMAValidator.validate_return_eligibility(order, RMA_CONFIG)
        if not is_eligible:
            return jsonify({'error': message}), 400
        
        # Validate return items
        order_items = OrderItem.query.filter_by(order_id=order.id).all()
        is_valid, message = RMAValidator.validate_return_items(order_items, data['items'])
        if not is_valid:
            return jsonify({'error': message}), 400
        
        # Create RMA request
        rma_engine = RMAEngine(db.session)
        rma_number = rma_engine.create_rma_request(
            order_id=data['order_id'],
            items=data['items'],
            customer_info=data['customer_info'],
            rma_type=data.get('rma_type', 'return_refund')
        )
        
        return jsonify({
            'success': True,
            'rma_number': rma_number,
            'message': 'RMA request created successfully'
        }), 201
        
    except RMAValidationError as e:
        return jsonify({'error': str(e)}), 400
    except RMAProcessingError as e:
        return jsonify({'error': str(e)}), 500
    except Exception as e:
        logger.error(f"Error creating RMA request: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/my-requests', methods=['GET'])
@jwt_required()
def get_my_rma_requests():
    """Get user's RMA requests"""
    try:
        user_id = get_jwt_identity()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status')
        
        # Build query
        query = RMARequest.query.filter_by(user_id=user_id)
        
        if status:
            query = query.filter_by(status=status)
        
        # Paginate results
        rma_requests = query.order_by(RMARequest.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Format response
        requests_data = []
        for rma in rma_requests.items:
            requests_data.append({
                'rma_number': rma.rma_number,
                'order_id': rma.order_id,
                'status': rma.status,
                'rma_type': rma.rma_type,
                'total_refund_amount': rma.total_refund_amount,
                'created_at': rma.created_at.isoformat(),
                'deadline': rma.deadline.isoformat() if rma.deadline else None,
                'items_count': len(rma.rma_items)
            })
        
        return jsonify({
            'success': True,
            'requests': requests_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': rma_requests.total,
                'pages': rma_requests.pages,
                'has_next': rma_requests.has_next,
                'has_prev': rma_requests.has_prev
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting user RMA requests: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/<rma_number>/status', methods=['GET'])
@jwt_required()
def get_rma_status(rma_number):
    """Get detailed RMA status"""
    try:
        user_id = get_jwt_identity()
        
        # Get RMA request
        rma_request = RMARequest.query.filter_by(rma_number=rma_number).first()
        if not rma_request:
            return jsonify({'error': 'RMA request not found'}), 404
        
        # Check ownership (unless admin)
        claims = get_jwt()
        if not claims.get('is_admin', False) and rma_request.user_id != user_id:
            return jsonify({'error': 'Access denied'}), 403
        
        # Get comprehensive status
        rma_engine = RMAEngine(db.session)
        status_data = rma_engine.get_rma_status(rma_number)
        
        return jsonify({
            'success': True,
            'rma_status': status_data
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting RMA status: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/<rma_number>/cancel', methods=['POST'])
@jwt_required()
def cancel_rma_request(rma_number):
    """Cancel RMA request"""
    try:
        user_id = get_jwt_identity()
        
        # Get RMA request
        rma_request = RMARequest.query.filter_by(rma_number=rma_number, user_id=user_id).first()
        if not rma_request:
            return jsonify({'error': 'RMA request not found'}), 404
        
        # Check if cancellable
        if rma_request.status not in ['pending', 'approved']:
            return jsonify({'error': 'RMA request cannot be cancelled at this stage'}), 400
        
        # Cancel request
        old_status = rma_request.status
        rma_request.status = RMAStatus.CANCELLED.value
        rma_request.updated_at = datetime.utcnow()
        
        # Add timeline event
        timeline_event = RMATimeline(
            rma_request_id=rma_request.id,
            event_type='cancelled',
            event_description='RMA request cancelled by customer',
            old_status=old_status,
            new_status=rma_request.status,
            actor_type='customer',
            actor_id=str(user_id),
            created_at=datetime.utcnow()
        )
        
        db.session.add(timeline_event)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'RMA request cancelled successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Error cancelling RMA request: {str(e)}")
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# ADMIN RMA APIs
# ============================================================================

@rma_bp.route('/admin/requests', methods=['GET'])
@admin_required
def get_all_rma_requests():
    """Get all RMA requests (admin only)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        rma_type = request.args.get('rma_type')
        search = request.args.get('search')
        
        # Build query
        query = RMARequest.query
        
        if status:
            query = query.filter_by(status=status)
        
        if rma_type:
            query = query.filter_by(rma_type=rma_type)
        
        if search:
            query = query.filter(
                db.or_(
                    RMARequest.rma_number.ilike(f'%{search}%'),
                    RMARequest.customer_email.ilike(f'%{search}%')
                )
            )
        
        # Paginate results
        rma_requests = query.order_by(RMARequest.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Format response
        requests_data = []
        for rma in rma_requests.items:
            requests_data.append({
                'id': rma.id,
                'rma_number': rma.rma_number,
                'order_id': rma.order_id,
                'customer_email': rma.customer_email,
                'status': rma.status,
                'rma_type': rma.rma_type,
                'total_refund_amount': rma.total_refund_amount,
                'requires_approval': rma.requires_approval,
                'created_at': rma.created_at.isoformat(),
                'updated_at': rma.updated_at.isoformat(),
                'deadline': rma.deadline.isoformat() if rma.deadline else None,
                'items_count': len(rma.rma_items)
            })
        
        return jsonify({
            'success': True,
            'requests': requests_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': rma_requests.total,
                'pages': rma_requests.pages,
                'has_next': rma_requests.has_next,
                'has_prev': rma_requests.has_prev
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting admin RMA requests: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/admin/<rma_number>/approve', methods=['POST'])
@admin_required
def approve_rma_request(rma_number):
    """Approve RMA request (admin only)"""
    try:
        admin_id = get_jwt_identity()
        data = request.get_json()
        
        # Get admin user info
        admin_user = User.query.get(admin_id)
        approver_info = {
            'type': 'admin',
            'id': str(admin_id),
            'name': admin_user.name if admin_user else 'Admin'
        }
        
        # Process approval
        rma_engine = RMAEngine(db.session)
        success = rma_engine.process_rma_approval(
            rma_number=rma_number,
            decision='approved',
            approver_info=approver_info,
            notes=data.get('notes')
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': 'RMA request approved successfully'
            }), 200
        else:
            return jsonify({'error': 'Failed to approve RMA request'}), 500
        
    except RMAValidationError as e:
        return jsonify({'error': str(e)}), 400
    except RMAProcessingError as e:
        return jsonify({'error': str(e)}), 500
    except Exception as e:
        logger.error(f"Error approving RMA request: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/admin/<rma_number>/reject', methods=['POST'])
@admin_required
def reject_rma_request(rma_number):
    """Reject RMA request (admin only)"""
    try:
        admin_id = get_jwt_identity()
        data = request.get_json()
        
        if not data.get('reason'):
            return jsonify({'error': 'Rejection reason is required'}), 400
        
        # Get admin user info
        admin_user = User.query.get(admin_id)
        approver_info = {
            'type': 'admin',
            'id': str(admin_id),
            'name': admin_user.name if admin_user else 'Admin'
        }
        
        # Process rejection
        rma_engine = RMAEngine(db.session)
        success = rma_engine.process_rma_approval(
            rma_number=rma_number,
            decision='rejected',
            approver_info=approver_info,
            notes=data.get('reason')
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': 'RMA request rejected successfully'
            }), 200
        else:
            return jsonify({'error': 'Failed to reject RMA request'}), 500
        
    except RMAValidationError as e:
        return jsonify({'error': str(e)}), 400
    except RMAProcessingError as e:
        return jsonify({'error': str(e)}), 500
    except Exception as e:
        logger.error(f"Error rejecting RMA request: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/admin/<rma_number>/process-return', methods=['POST'])
@admin_required
def process_return_received(rma_number):
    """Process received return items (admin only)"""
    try:
        admin_id = get_jwt_identity()
        data = request.get_json()

        if not data.get('received_items'):
            return jsonify({'error': 'Received items data is required'}), 400

        # Get admin user info
        admin_user = User.query.get(admin_id)
        inspector_info = {
            'id': str(admin_id),
            'name': admin_user.name if admin_user else 'Admin'
        }

        # Process return
        rma_engine = RMAEngine(db.session)
        success = rma_engine.process_return_received(
            rma_number=rma_number,
            received_items=data['received_items'],
            inspector_info=inspector_info
        )

        if success:
            return jsonify({
                'success': True,
                'message': 'Return items processed successfully'
            }), 200
        else:
            return jsonify({'error': 'Failed to process return items'}), 500

    except RMAValidationError as e:
        return jsonify({'error': str(e)}), 400
    except RMAProcessingError as e:
        return jsonify({'error': str(e)}), 500
    except Exception as e:
        logger.error(f"Error processing return: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/admin/<rma_number>/complete-refund', methods=['POST'])
@admin_required
def complete_refund(rma_number):
    """Complete refund processing (admin only)"""
    try:
        data = request.get_json()

        refund_details = {
            'method': data.get('method', 'original_payment'),
            'gateway_refund_id': data.get('gateway_refund_id'),
            'gateway_response': data.get('gateway_response')
        }

        # Process refund completion
        rma_engine = RMAEngine(db.session)
        success = rma_engine.process_refund_completion(
            rma_number=rma_number,
            refund_details=refund_details
        )

        if success:
            return jsonify({
                'success': True,
                'message': 'Refund completed successfully'
            }), 200
        else:
            return jsonify({'error': 'Failed to complete refund'}), 500

    except RMAValidationError as e:
        return jsonify({'error': str(e)}), 400
    except RMAProcessingError as e:
        return jsonify({'error': str(e)}), 500
    except Exception as e:
        logger.error(f"Error completing refund: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# RMA ANALYTICS AND REPORTING APIs
# ============================================================================

@rma_bp.route('/admin/analytics/summary', methods=['GET'])
@admin_required
def get_rma_analytics_summary():
    """Get RMA analytics summary (admin only)"""
    try:
        # Get date range parameters
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        if start_date_str and end_date_str:
            start_date = datetime.fromisoformat(start_date_str)
            end_date = datetime.fromisoformat(end_date_str)
        else:
            # Default to last 30 days
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)

        # Generate report
        report = RMAReportGenerator.generate_rma_summary_report(start_date, end_date)

        return jsonify({
            'success': True,
            'analytics': report
        }), 200

    except Exception as e:
        logger.error(f"Error getting RMA analytics: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/admin/export/csv', methods=['GET'])
@admin_required
def export_rma_data():
    """Export RMA data to CSV (admin only)"""
    try:
        # Get filter parameters
        status = request.args.get('status')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        # Build query
        query = RMARequest.query

        if status:
            query = query.filter_by(status=status)

        if start_date_str and end_date_str:
            start_date = datetime.fromisoformat(start_date_str)
            end_date = datetime.fromisoformat(end_date_str)
            query = query.filter(
                RMARequest.created_at >= start_date,
                RMARequest.created_at <= end_date
            )

        rma_requests = query.all()

        # Generate CSV
        csv_data = RMAReportGenerator.export_rma_data_csv(rma_requests)

        return jsonify({
            'success': True,
            'csv_data': csv_data,
            'filename': f'rma_export_{datetime.utcnow().strftime("%Y%m%d_%H%M%S")}.csv'
        }), 200

    except Exception as e:
        logger.error(f"Error exporting RMA data: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# RMA CONFIGURATION APIs
# ============================================================================

@rma_bp.route('/config', methods=['GET'])
def get_rma_configuration():
    """Get RMA configuration (public)"""
    try:
        from app import RMAConfiguration

        # Get public configuration
        configs = RMAConfiguration.query.filter(
            RMAConfiguration.config_key.in_([
                'return_window_days',
                'exchange_window_days',
                'require_photos',
                'free_return_shipping'
            ])
        ).all()

        config_data = {}
        for config in configs:
            if config.data_type == 'boolean':
                config_data[config.config_key] = config.config_value.lower() == 'true'
            elif config.data_type == 'integer':
                config_data[config.config_key] = int(config.config_value)
            elif config.data_type == 'float':
                config_data[config.config_key] = float(config.config_value)
            else:
                config_data[config.config_key] = config.config_value

        return jsonify({
            'success': True,
            'configuration': config_data
        }), 200

    except Exception as e:
        logger.error(f"Error getting RMA configuration: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/admin/config', methods=['GET'])
@admin_required
def get_admin_rma_configuration():
    """Get full RMA configuration (admin only)"""
    try:
        from app import RMAConfiguration

        configs = RMAConfiguration.query.all()

        config_data = {}
        for config in configs:
            config_data[config.config_key] = {
                'value': config.config_value,
                'data_type': config.data_type,
                'description': config.description,
                'category': config.category,
                'updated_at': config.updated_at.isoformat()
            }

        return jsonify({
            'success': True,
            'configuration': config_data
        }), 200

    except Exception as e:
        logger.error(f"Error getting admin RMA configuration: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/admin/config', methods=['PUT'])
@admin_required
def update_rma_configuration():
    """Update RMA configuration (admin only)"""
    try:
        admin_id = get_jwt_identity()
        data = request.get_json()

        from app import RMAConfiguration

        updated_configs = []

        for config_key, config_value in data.items():
            config = RMAConfiguration.query.filter_by(config_key=config_key).first()

            if config:
                config.config_value = str(config_value)
                config.updated_at = datetime.utcnow()
                config.updated_by = str(admin_id)
                updated_configs.append(config_key)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Updated {len(updated_configs)} configuration(s)',
            'updated_configs': updated_configs
        }), 200

    except Exception as e:
        logger.error(f"Error updating RMA configuration: {str(e)}")
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# UTILITY APIs
# ============================================================================

@rma_bp.route('/return-reasons', methods=['GET'])
def get_return_reasons():
    """Get available return reasons"""
    try:
        reasons = [
            {'value': 'defective', 'label': 'Defective Product'},
            {'value': 'wrong_item', 'label': 'Wrong Item Received'},
            {'value': 'not_as_described', 'label': 'Not as Described'},
            {'value': 'size_issue', 'label': 'Size Issue'},
            {'value': 'color_issue', 'label': 'Color Issue'},
            {'value': 'damaged_shipping', 'label': 'Damaged During Shipping'},
            {'value': 'changed_mind', 'label': 'Changed Mind'},
            {'value': 'better_price_found', 'label': 'Better Price Found'},
            {'value': 'duplicate_order', 'label': 'Duplicate Order'},
            {'value': 'quality_issue', 'label': 'Quality Issue'},
            {'value': 'missing_parts', 'label': 'Missing Parts'},
            {'value': 'expired_product', 'label': 'Expired Product'},
            {'value': 'other', 'label': 'Other'}
        ]

        return jsonify({
            'success': True,
            'return_reasons': reasons
        }), 200

    except Exception as e:
        logger.error(f"Error getting return reasons: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/calculate-refund', methods=['POST'])
@jwt_required()
def calculate_refund_amount():
    """Calculate refund amount for items"""
    try:
        data = request.get_json()

        if not data.get('items'):
            return jsonify({'error': 'Items are required'}), 400

        # Mock RMA items for calculation
        class MockRMAItem:
            def __init__(self, total_price):
                self.total_price = total_price

        mock_items = [MockRMAItem(item['total_price']) for item in data['items']]

        # Calculate refund
        from rma_architecture import RMA_CONFIG
        refund_calculation = RMACalculator.calculate_refund_amount(mock_items, RMA_CONFIG)

        return jsonify({
            'success': True,
            'refund_calculation': refund_calculation
        }), 200

    except Exception as e:
        logger.error(f"Error calculating refund: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
