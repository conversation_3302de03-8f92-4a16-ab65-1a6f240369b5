import React, { createContext, useContext, useState, useEffect } from 'react';

const SellerAuthContext = createContext();

export const useSellerAuth = () => {
  const context = useContext(SellerAuthContext);
  if (!context) {
    throw new Error('useSellerAuth must be used within a SellerAuthProvider');
  }
  return context;
};

export const SellerAuthProvider = ({ children }) => {
  const [seller, setSeller] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state from localStorage
  useEffect(() => {
    const savedToken = localStorage.getItem('sellerToken');
    const savedSeller = localStorage.getItem('sellerData');
    
    if (savedToken && savedSeller) {
      try {
        setToken(savedToken);
        setSeller(JSON.parse(savedSeller));
      } catch (error) {
        console.error('Error parsing saved seller data:', error);
        localStorage.removeItem('sellerToken');
        localStorage.removeItem('sellerData');
      }
    }
    setLoading(false);
  }, []);

  const login = (sellerData, authToken) => {
    setSeller(sellerData);
    setToken(authToken);
    localStorage.setItem('sellerToken', authToken);
    localStorage.setItem('sellerData', JSON.stringify(sellerData));
  };

  const logout = () => {
    setSeller(null);
    setToken(null);
    localStorage.removeItem('sellerToken');
    localStorage.removeItem('sellerData');
  };

  const updateSeller = (updatedData) => {
    const newSellerData = { ...seller, ...updatedData };
    setSeller(newSellerData);
    localStorage.setItem('sellerData', JSON.stringify(newSellerData));
  };

  const isAuthenticated = () => {
    return !!(seller && token);
  };

  const getAuthHeaders = () => {
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  };

  const value = {
    seller,
    token,
    loading,
    login,
    logout,
    updateSeller,
    isAuthenticated,
    getAuthHeaders
  };

  return (
    <SellerAuthContext.Provider value={value}>
      {children}
    </SellerAuthContext.Provider>
  );
};

export default SellerAuthContext;
