import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useNotification } from '../contexts/NotificationContext';
import { useError } from '../contexts/ErrorContext';
import { useLoading } from '../contexts/LoadingContext';
import { API_BASE_URL } from '../config/api';
import './ProductDetails.css';
import { ProductImage } from '../components/EnhancedImage';
import { LoadingWrapper, SectionLoading, LoadingButton } from '../components/LoadingComponents';
import { PageError } from '../components/ErrorComponents';
import { formatPrice, calculateDiscountPercentage } from '../utils/currency';
import { useCart } from '../contexts/CartContext';
import SellerInfo from '../components/SellerInfo';

// Import new product feature components
import ProductImageGallery from '../components/ProductImageGallery';
import ProductVariants from '../components/ProductVariants';
import ProductReviews from '../components/ProductReviews';
import RecentlyViewed from '../components/RecentlyViewed';
import ProductComparison from '../components/ProductComparison';
import AvailabilityNotification from '../components/AvailabilityNotification';

// Enhanced RecommendedProductCard component with improved UI
const RecommendedProductCard = React.memo(({ product, style, onProductClick }) => {
  const handleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Recommended product clicked:', product.id);
    if (onProductClick) {
      onProductClick(product.id);
    }
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="text-yellow-400">★</span>);
    }
    if (hasHalfStar) {
      stars.push(<span key="half" className="text-yellow-400">☆</span>);
    }
    for (let i = stars.length; i < 5; i++) {
      stars.push(<span key={i} className="text-gray-300">★</span>);
    }
    return stars;
  };

  return (
    <div style={style} className="p-2">
      <div
        onClick={handleClick}
        className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:border-green-300 transition-all duration-300 cursor-pointer group h-full flex flex-col"
      >
        {/* Product Image */}
        <div className="relative overflow-hidden bg-gray-50">
          <LazyLoadImage
            src={getProductImageUrl(product)}
            alt={product.name || 'Product Image'}
            effect="blur"
            height="200px"
            width="100%"
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
            placeholder={
              <div className="w-full h-48 bg-gray-200 animate-pulse flex items-center justify-center">
                <div className="text-gray-400 text-sm">Loading...</div>
              </div>
            }
            threshold={300}
            onError={(e) => handleImageError(e)}
          />

          {/* Sustainability Badge */}
          {product.sustainabilityScore >= 80 && (
            <div className="absolute top-3 left-3 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center space-x-1">
              <span>🌱</span>
              <span>Eco</span>
            </div>
          )}

          {/* Discount Badge */}
          {product.originalPrice && product.originalPrice > product.price && (
            <div className="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              {calculateDiscountPercentage(product.originalPrice, product.price)}% OFF
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="p-4 flex-1 flex flex-col">
          {/* Product Name */}
          <h4 className="text-sm font-semibold text-gray-900 line-clamp-2 mb-2 group-hover:text-green-600 transition-colors">
            {product.name}
          </h4>

          {/* Brand */}
          {product.brand && (
            <p className="text-xs text-gray-500 mb-2">{product.brand}</p>
          )}

          {/* Rating */}
          {product.averageRating > 0 && (
            <div className="flex items-center space-x-1 mb-2">
              <div className="flex text-sm">
                {renderStars(product.averageRating)}
              </div>
              <span className="text-xs text-gray-500">
                ({product.totalReviews || 0})
              </span>
            </div>
          )}

          {/* Price */}
          <div className="mt-auto">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-lg font-bold text-green-600">
                {formatPrice(product.price)}
              </span>
              {product.originalPrice && product.originalPrice > product.price && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.originalPrice)}
                </span>
              )}
            </div>

            {/* Sustainability Score */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${
                  product.sustainabilityScore >= 80 ? 'bg-green-500' :
                  product.sustainabilityScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <span className="text-xs text-gray-600">
                  Eco Score: {product.sustainabilityScore}/100
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

const ProductDetail = () => {
  const { id } = useParams();
  const { addToCart } = useCart();
  const { success, error } = useNotification();
  const { addError, handleApiError } = useError();
  const { setLoading: setGlobalLoading, withLoading } = useLoading();
  const [product, setProduct] = useState(null);
  const [impactData, setImpactData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [errorState, setErrorState] = useState(null);

  // Debug logging
  console.log('ProductDetail component loaded with id:', id);



  // Navigation handler for recommended products
  const handleRecommendedProductClick = (productId) => {
    console.log('Navigating to recommended product:', productId);
    window.location.href = `/product/${productId}`;
  };

  // New state for product features
  const [selectedVariants, setSelectedVariants] = useState({});
  const [finalPrice, setFinalPrice] = useState(0);
  const [isInStock, setIsInStock] = useState(true);
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonProducts, setComparisonProducts] = useState([]);

  // Additional state for cart and wishlist functionality
  const [quantity, setQuantity] = useState(1);
  const [addingToCart, setAddingToCart] = useState(false);
  const [wishlistLoading, setWishlistLoading] = useState(false);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/products/${id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch product');
        }
        const data = await response.json();
        setProduct(data);
        setFinalPrice(data.price);
        setIsInStock(data.stockQuantity > 0);

        // Fetch impact analysis
        const impactResponse = await fetch(`${API_BASE_URL}/impact_analysis/${id}`);
        if (impactResponse.ok) {
          const impactData = await impactResponse.json();
          setImpactData(impactData);
        }

        // Track recently viewed
        trackRecentlyViewed(id);

        setLoading(false);
      } catch (err) {
        setErrorState(err.message);
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  const trackRecentlyViewed = async (productId) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      await fetch(`${API_BASE_URL}/recently-viewed`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ product_id: productId })
      });
    } catch (err) {
      console.error('Failed to track recently viewed:', err);
    }
  };

  const handleAddToCart = async () => {
    if (!isInStock) {
      error('Product is out of stock', {
        title: 'Out of Stock'
      });
      return;
    }

    setAddingToCart(true);
    try {
      await addToCart(id, quantity);
      success(`${quantity} item(s) added to cart!`, {
        title: 'Added to Cart'
      });
    } catch (err) {
      console.error('Error adding to cart:', err);
      error(err.message || 'Failed to add to cart', {
        title: 'Cart Error'
      });
    } finally {
      setAddingToCart(false);
    }
  };

  const handleVariantChange = (variantData) => {
    setSelectedVariants(variantData);
    setFinalPrice(variantData.finalPrice);
    setIsInStock(variantData.isInStock);
  };

  const addToComparison = () => {
    const currentProducts = JSON.parse(localStorage.getItem('comparisonProducts') || '[]');
    if (!currentProducts.includes(parseInt(id))) {
      const updatedProducts = [...currentProducts, parseInt(id)];
      localStorage.setItem('comparisonProducts', JSON.stringify(updatedProducts));
      setComparisonProducts(updatedProducts);
      success('Product added to comparison!', {
        title: 'Comparison Updated'
      });
    } else {
      error('Product already in comparison', {
        title: 'Already Added'
      });
    }
  };

  const handleWishlist = async () => {
    setWishlistLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        error('Please login to add items to wishlist', {
          title: 'Login Required'
        });
        return;
      }

      const response = await fetch(`${API_BASE_URL}/wishlist/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ product_id: id }),
      });

      if (response.ok) {
        success('Product added to wishlist!', {
          title: 'Wishlist Updated'
        });
      } else {
        const errorData = await response.json();
        error(errorData.message || 'Failed to add to wishlist', {
          title: 'Wishlist Error'
        });
      }
    } catch (err) {
      console.error('Error adding to wishlist:', err);
      error('Failed to add to wishlist. Please try again.', {
        title: 'Wishlist Error'
      });
    } finally {
      setWishlistLoading(false);
    }
  };

  if (loading) return <div className="p-4 text-center">Loading...</div>;
  if (errorState) return <div className="p-4 text-center text-red-500">Error: {errorState}</div>;
  if (!product) return <div className="p-4 text-center">Product not found</div>;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-emerald-50">
      {/* Unique Floating Header */}
      <div className="relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-emerald-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 py-12">
          {/* Modern Breadcrumb with Glass Effect */}
          <nav className="flex items-center space-x-2 text-sm mb-8 bg-white/60 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20 shadow-lg w-fit">
            <Link to="/" className="hover:text-emerald-600 transition-colors font-medium">🏠 Home</Link>
            <span className="text-gray-400">•</span>
            <Link to="/search" className="hover:text-emerald-600 transition-colors font-medium">🔍 Products</Link>
            <span className="text-gray-400">•</span>
            <span className="text-emerald-600 font-semibold">{product.name}</span>
          </nav>

          {/* Unique Product Header with Floating Elements */}
          <div className="relative">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8">
              <div className="flex-1">
                {/* Product Title with Gradient Text */}
                <h1 className="text-5xl lg:text-7xl font-black mb-6 leading-tight">
                  <span className="bg-gradient-to-r from-gray-900 via-emerald-600 to-blue-600 bg-clip-text text-transparent">
                    {product.name}
                  </span>
                </h1>

                {/* Floating Badge Container */}
                <div className="flex flex-wrap items-center gap-4 mb-6">
                  <div className="group relative">
                    <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-full blur opacity-25 group-hover:opacity-75 transition duration-300"></div>
                    <span className="relative inline-flex items-center px-6 py-3 rounded-full bg-white text-emerald-700 font-bold text-sm shadow-lg">
                      <span className="w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse"></span>
                      {product.brand || 'Premium Brand'}
                    </span>
                  </div>

                  <div className="group relative">
                    <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-purple-600 rounded-full blur opacity-25 group-hover:opacity-75 transition duration-300"></div>
                    <span className="relative inline-flex items-center px-6 py-3 rounded-full bg-white text-blue-700 font-bold text-sm shadow-lg">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
                      {product.category || 'Featured'}
                    </span>
                  </div>

                  {product.sustainabilityScore >= 80 && (
                    <div className="group relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-green-400 to-emerald-600 rounded-full blur opacity-25 group-hover:opacity-75 transition duration-300"></div>
                      <span className="relative inline-flex items-center px-6 py-3 rounded-full bg-white text-green-700 font-bold text-sm shadow-lg">
                        <span className="mr-2">🌱</span>
                        Eco-Certified
                      </span>
                    </div>
                  )}
                </div>

                {/* SKU with Modern Design */}
                <div className="inline-flex items-center px-4 py-2 bg-gray-100/80 backdrop-blur-sm rounded-lg border border-gray-200/50">
                  <svg className="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                  </svg>
                  <span className="text-sm font-mono text-gray-600">SKU: {product.sku || 'AL-' + id}</span>
                </div>
              </div>

              {/* Floating Action Buttons */}
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleWishlist}
                  disabled={wishlistLoading}
                  className="group relative p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-white/20"
                >
                  <div className="absolute -inset-1 bg-gradient-to-r from-red-400 to-pink-600 rounded-2xl blur opacity-0 group-hover:opacity-25 transition duration-300"></div>
                  <svg className="relative w-6 h-6 text-red-500 group-hover:text-red-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </button>

                <button className="group relative p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-white/20">
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-purple-600 rounded-2xl blur opacity-0 group-hover:opacity-25 transition duration-300"></div>
                  <svg className="relative w-6 h-6 text-blue-500 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                </button>

                <button
                  onClick={addToComparison}
                  className="group relative p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-white/20"
                >
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 to-teal-600 rounded-2xl blur opacity-0 group-hover:opacity-25 transition duration-300"></div>
                  <svg className="relative w-6 h-6 text-emerald-500 group-hover:text-emerald-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Container with Unique Layout */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Unique Floating Grid Layout */}
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-8 mb-16">
          {/* Left Column - Immersive Image Gallery */}
          <div className="xl:col-span-3 relative">
            {/* Floating Image Container with 3D Effect */}
            <div className="group relative">
              <div className="absolute -inset-4 bg-gradient-to-r from-emerald-400/20 via-blue-400/20 to-purple-400/20 rounded-3xl blur-2xl group-hover:blur-3xl transition-all duration-500"></div>
              <div className="relative bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden transform group-hover:scale-[1.02] transition-all duration-500">
                <ProductImageGallery
                  productId={id}
                  primaryImage={product.image}
                  productName={product.name}
                />

                {/* Floating Badges on Image */}
                <div className="absolute top-6 left-6 flex flex-col space-y-3">
                  {product.originalPrice && product.originalPrice > product.price && (
                    <div className="bg-red-500/90 backdrop-blur-sm text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg">
                      {calculateDiscountPercentage(product.originalPrice, product.price)}% OFF
                    </div>
                  )}
                  {product.sustainabilityScore >= 80 && (
                    <div className="bg-green-500/90 backdrop-blur-sm text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg flex items-center">
                      <span className="mr-2">🌱</span>
                      Eco-Friendly
                    </div>
                  )}
                  {!isInStock && (
                    <div className="bg-gray-500/90 backdrop-blur-sm text-white px-4 py-2 rounded-full font-bold text-sm shadow-lg">
                      Out of Stock
                    </div>
                  )}
                </div>

                {/* Floating Quick Actions on Image */}
                <div className="absolute top-6 right-6 flex flex-col space-y-3 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <button className="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center hover:scale-110">
                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                  <button className="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center hover:scale-110">
                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Unique Floating Info Cards */}
          <div className="xl:col-span-2 space-y-8">
            {/* Floating Price Card with Gradient Background */}
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 via-blue-500 to-purple-600 rounded-3xl blur opacity-25 group-hover:opacity-40 transition duration-500"></div>
              <div className="relative bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 p-8">
                {/* Price Section with Animation */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-baseline space-x-4">
                      <span className="text-5xl font-black bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                        {formatPrice(finalPrice)}
                      </span>
                      {finalPrice !== product.price && (
                        <div className="flex flex-col">
                          <span className="text-2xl text-gray-400 line-through font-semibold">
                            {formatPrice(product.price)}
                          </span>
                          <span className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-red-500 to-pink-500 text-white text-sm font-bold shadow-lg">
                            Save {Math.round(((product.price - finalPrice) / product.price) * 100)}%
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Animated Status Indicators */}
                  <div className="flex flex-wrap items-center gap-4 mb-6">
                    <div className={`group relative overflow-hidden rounded-2xl px-6 py-3 font-bold text-sm shadow-lg transition-all duration-300 ${
                      isInStock
                        ? 'bg-gradient-to-r from-emerald-500 to-green-600 text-white'
                        : 'bg-gradient-to-r from-red-500 to-pink-600 text-white'
                    }`}>
                      <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                      <div className="relative flex items-center">
                        <span className={`w-3 h-3 rounded-full mr-3 animate-pulse ${
                          isInStock ? 'bg-white' : 'bg-white'
                        }`}></span>
                        {isInStock ? '✅ In Stock' : '❌ Out of Stock'}
                      </div>
                    </div>

                    {/* Eco Score with Progress Bar */}
                    <div className="bg-gradient-to-r from-green-100 to-emerald-100 rounded-2xl px-6 py-3 border border-green-200">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">🌱</span>
                        <div>
                          <div className="text-sm font-bold text-green-800">Eco Score</div>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 h-2 bg-green-200 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-gradient-to-r from-green-500 to-emerald-600 rounded-full transition-all duration-1000"
                                style={{ width: `${product.sustainabilityScore}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-bold text-green-700">{product.sustainabilityScore}/100</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Rating Display */}
                  {product.averageRating > 0 && (
                    <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-4 border border-yellow-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-1">
                            {[...Array(5)].map((_, i) => (
                              <svg
                                key={i}
                                className={`w-6 h-6 transition-all duration-200 ${
                                  i < Math.floor(product.averageRating)
                                    ? 'text-yellow-400 scale-110'
                                    : 'text-gray-300'
                                }`}
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                          </div>
                          <div>
                            <div className="text-lg font-bold text-yellow-700">{product.averageRating.toFixed(1)}</div>
                            <div className="text-sm text-yellow-600">({product.totalReviews || 0} reviews)</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl">⭐</div>
                          <div className="text-xs text-yellow-600 font-medium">Highly Rated</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Unique Description Card with Glassmorphism */}
            {product.description && (
              <div className="group relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 rounded-3xl blur opacity-20 group-hover:opacity-30 transition duration-500"></div>
                <div className="relative bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 p-8">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Product Story
                      </h3>
                      <p className="text-gray-500 text-sm">Discover what makes this product special</p>
                    </div>
                  </div>
                  <div className="prose prose-lg max-w-none">
                    <p className="text-gray-700 leading-relaxed text-lg font-medium">{product.description}</p>
                  </div>

                  {/* Decorative Elements */}
                  <div className="absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full blur-xl"></div>
                  <div className="absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-pink-200/30 to-red-200/30 rounded-full blur-xl"></div>
                </div>
              </div>
            )}

          {/* Product Variants */}
          <ProductVariants
            productId={id}
            basePrice={product.price}
            onVariantChange={handleVariantChange}
          />

          {/* Availability Notifications */}
          <AvailabilityNotification
            productId={id}
            variants={selectedVariants.selectedVariants || []}
            isInStock={isInStock}
          />

            {/* Unique Interactive Purchase Card */}
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 via-teal-500 to-blue-600 rounded-3xl blur opacity-25 group-hover:opacity-40 transition duration-500"></div>
              <div className="relative bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                      Purchase Options
                    </h3>
                    <p className="text-gray-500 text-sm">Choose your quantity and add to cart</p>
                  </div>
                </div>

                {/* Enhanced Quantity Selector */}
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-6 mb-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-lg font-bold text-gray-800 block mb-2">Quantity</label>
                      <p className="text-sm text-gray-600">Available: {product.stockQuantity || 10} units</p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        disabled={!isInStock || quantity <= 1}
                        className="w-12 h-12 rounded-2xl bg-white shadow-lg border-2 border-gray-200 flex items-center justify-center hover:border-emerald-300 hover:bg-emerald-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-110"
                      >
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M20 12H4" />
                        </svg>
                      </button>
                      <div className="bg-white rounded-2xl px-6 py-3 shadow-lg border-2 border-gray-200 min-w-[80px]">
                        <span className="text-2xl font-bold text-center block">{quantity}</span>
                      </div>
                      <button
                        onClick={() => setQuantity(Math.min(product.stockQuantity || 10, quantity + 1))}
                        disabled={!isInStock || quantity >= (product.stockQuantity || 10)}
                        className="w-12 h-12 rounded-2xl bg-white shadow-lg border-2 border-gray-200 flex items-center justify-center hover:border-emerald-300 hover:bg-emerald-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-110"
                      >
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M12 4v16m8-8H4" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Enhanced Action Buttons */}
                <div className="space-y-4">
                  {/* Main Add to Cart Button */}
                  <button
                    onClick={handleAddToCart}
                    disabled={!isInStock || addingToCart}
                    className={`group relative w-full py-6 px-8 rounded-2xl font-bold text-lg text-white transition-all duration-300 transform hover:scale-105 overflow-hidden ${
                      isInStock && !addingToCart
                        ? 'bg-gradient-to-r from-emerald-600 via-teal-600 to-blue-600 hover:from-emerald-700 hover:via-teal-700 hover:to-blue-700 shadow-2xl hover:shadow-3xl'
                        : 'bg-gray-400 cursor-not-allowed'
                    }`}
                  >
                    <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                    <div className="relative flex items-center justify-center space-x-3">
                      {addingToCart ? (
                        <>
                          <div className="w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>Adding to Cart...</span>
                        </>
                      ) : isInStock ? (
                        <>
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01" />
                          </svg>
                          <span>Add {quantity} to Cart • {formatPrice(finalPrice * quantity)}</span>
                        </>
                      ) : (
                        <>
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18 12M6 6l12 12" />
                          </svg>
                          <span>Out of Stock</span>
                        </>
                      )}
                    </div>
                  </button>

                  {/* Secondary Action Buttons */}
                  <div className="grid grid-cols-3 gap-4">
                    <button
                      onClick={handleWishlist}
                      disabled={wishlistLoading}
                      className="group relative py-4 px-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl border border-white/20 transition-all duration-300 hover:scale-105"
                    >
                      <div className="absolute -inset-1 bg-gradient-to-r from-red-400 to-pink-600 rounded-2xl blur opacity-0 group-hover:opacity-25 transition duration-300"></div>
                      <div className="relative flex flex-col items-center space-y-2">
                        <svg className="w-6 h-6 text-red-500 group-hover:text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span className="text-sm font-bold text-gray-700 group-hover:text-red-600">Wishlist</span>
                      </div>
                    </button>

                    <button
                      onClick={addToComparison}
                      className="group relative py-4 px-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl border border-white/20 transition-all duration-300 hover:scale-105"
                    >
                      <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-purple-600 rounded-2xl blur opacity-0 group-hover:opacity-25 transition duration-300"></div>
                      <div className="relative flex flex-col items-center space-y-2">
                        <svg className="w-6 h-6 text-blue-500 group-hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <span className="text-sm font-bold text-gray-700 group-hover:text-blue-600">Compare</span>
                      </div>
                    </button>

                    <button className="group relative py-4 px-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl border border-white/20 transition-all duration-300 hover:scale-105">
                      <div className="absolute -inset-1 bg-gradient-to-r from-green-400 to-teal-600 rounded-2xl blur opacity-0 group-hover:opacity-25 transition duration-300"></div>
                      <div className="relative flex flex-col items-center space-y-2">
                        <svg className="w-6 h-6 text-green-500 group-hover:text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                        <span className="text-sm font-bold text-gray-700 group-hover:text-green-600">Share</span>
                      </div>
                    </button>
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-4 right-4 w-16 h-16 bg-gradient-to-br from-emerald-200/30 to-teal-200/30 rounded-full blur-xl"></div>
                <div className="absolute bottom-4 left-4 w-12 h-12 bg-gradient-to-br from-teal-200/30 to-blue-200/30 rounded-full blur-xl"></div>
              </div>
            </div>

            {/* Seller Information Section */}
            {product.seller && (
              <SellerInfo seller={product.seller} />
            )}

            {/* Modern Impact Analysis Section */}
            {impactData && (
              <div className="bg-gradient-to-br from-emerald-50 to-green-100 rounded-2xl shadow-lg border border-emerald-200 p-6">
                <h4 className="text-xl font-bold text-emerald-800 mb-4 flex items-center">
                  <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  Environmental Impact
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white rounded-xl p-4 shadow-sm">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-600">CO2 Saved</span>
                      <span className="text-2xl">🌍</span>
                    </div>
                    <div className="text-2xl font-bold text-emerald-700">{impactData.impact_metrics.co2_saved_kg} kg</div>
                    <div className="text-xs text-gray-500 mt-1">Carbon footprint reduction</div>
                  </div>
                  <div className="bg-white rounded-xl p-4 shadow-sm">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-600">Water Conserved</span>
                      <span className="text-2xl">💧</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-700">{impactData.impact_metrics.water_conserved_liters} L</div>
                    <div className="text-xs text-gray-500 mt-1">Water usage efficiency</div>
                  </div>
                  <div className="bg-white rounded-xl p-4 shadow-sm">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-600">Waste Diverted</span>
                      <span className="text-2xl">♻️</span>
                    </div>
                    <div className="text-2xl font-bold text-green-700">{impactData.impact_metrics.waste_diverted_kg} kg</div>
                    <div className="text-xs text-gray-500 mt-1">Waste reduction impact</div>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-emerald-100 rounded-xl">
                  <p className="text-sm text-emerald-800 font-medium text-center">
                    🌱 By choosing this sustainable product, you're making a positive environmental impact!
                  </p>
                </div>
              </div>
            )}

            {/* Modern Product Specifications */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                Product Specifications
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {product.weight && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700 flex items-center">
                      <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-3m-3 3l-3-3" />
                      </svg>
                      Weight
                    </span>
                    <span className="text-gray-900 font-medium">{product.weight} kg</span>
                  </div>
                )}
                {product.dimensions && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700 flex items-center">
                      <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                      </svg>
                      Dimensions
                    </span>
                    <span className="text-gray-900 font-medium">{product.dimensions}</span>
                  </div>
                )}
                {product.material && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700 flex items-center">
                      <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                      </svg>
                      Material
                    </span>
                    <span className="text-gray-900 font-medium">{product.material}</span>
                  </div>
                )}
              </div>
              {product.care_instructions && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2 flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Care Instructions
                  </h4>
                  <p className="text-blue-800 text-sm leading-relaxed">{product.care_instructions}</p>
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Modern Reviews Section */}
        <div className="mb-12">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                <svg className="w-6 h-6 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                Customer Reviews
              </h2>
            </div>
            <div className="p-6">
              <ProductReviews productId={id} />
            </div>
          </div>
        </div>

        {/* Modern Recently Viewed Section */}
        <div className="mb-12">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                <svg className="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                Recently Viewed
              </h2>
            </div>
            <div className="p-6">
              <RecentlyViewed limit={6} showTitle={false} />
            </div>
          </div>
        </div>

      {/* Analytics Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Sales Data Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3 text-blue-800 flex items-center">
            📊 Sales Analytics
          </h3>
          {product.sales && product.sales.length > 0 ? (
            <div>
              <div className="grid grid-cols-2 gap-4 mb-3">
                <div className="bg-white p-3 rounded border analytics-card">
                  <p className="text-sm text-gray-600">Total Sales (30 days)</p>
                  <p className="text-xl font-bold text-blue-600">
                    {product.sales.reduce((sum, sale) => sum + sale.units_sold, 0)} units
                  </p>
                </div>
                <div className="bg-white p-3 rounded border analytics-card">
                  <p className="text-sm text-gray-600">Daily Average</p>
                  <p className="text-xl font-bold text-blue-600">
                    {Math.round(product.sales.reduce((sum, sale) => sum + sale.units_sold, 0) / product.sales.length)} units
                  </p>
                </div>
              </div>
              <div className="max-h-32 overflow-y-auto bg-white rounded border analytics-scroll">
                <div className="p-2">
                  <p className="text-xs text-gray-500 mb-2">Recent Sales (Last 7 days)</p>
                  <div className="space-y-1">
                    {product.sales.slice(0, 7).map((sale, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-gray-600">{sale.date}</span>
                        <span className="font-medium text-blue-600">{sale.units_sold} units</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 text-sm">No sales data available.</p>
          )}
        </div>

        {/* Price History Section */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3 text-green-800 flex items-center">
            💰 Price Trends
          </h3>
          {product.priceHistory && product.priceHistory.length > 0 ? (
            <div>
              <div className="grid grid-cols-2 gap-4 mb-3">
                <div className="bg-white p-3 rounded border analytics-card">
                  <p className="text-sm text-gray-600">Current Price</p>
                  <p className="text-xl font-bold text-green-600">₹{finalPrice.toFixed(2)}</p>
                </div>
                <div className="bg-white p-3 rounded border analytics-card">
                  <p className="text-sm text-gray-600">30-Day Average</p>
                  <p className="text-xl font-bold text-green-600">
                    ₹{(product.priceHistory.reduce((sum, price) => sum + price.price, 0) / product.priceHistory.length).toFixed(2)}
                  </p>
                </div>
              </div>
              <div className="max-h-32 overflow-y-auto bg-white rounded border analytics-scroll">
                <div className="p-2">
                  <p className="text-xs text-gray-500 mb-2">Recent Price Changes (Last 7 days)</p>
                  <div className="space-y-1">
                    {product.priceHistory.slice(0, 7).map((price, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-gray-600">{price.date}</span>
                        <span className="font-medium text-green-600">₹{price.price.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 text-sm">No price history available.</p>
          )}
        </div>
      </div>

        {/* Modern Recommended Products Section */}
        <div className="mb-12">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                    <svg className="w-6 h-6 mr-2 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    You Might Also Like
                  </h2>
                  <p className="text-gray-600 text-sm mt-1">Curated recommendations based on your preferences</p>
                </div>
                {product.recommendations && product.recommendations.length > 4 && (
                  <button className="bg-emerald-100 hover:bg-emerald-200 text-emerald-700 px-4 py-2 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2">
                    <span>View All</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            <div className="p-6">
              {product.recommendations && product.recommendations.length > 0 ? (
                <div>
                  {/* Grid Layout for larger screens */}
                  <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {product.recommendations.slice(0, 8).map((recommendedProduct) => (
                      <div key={recommendedProduct.id} className="transform hover:scale-105 transition-transform duration-200">
                        <RecommendedProductCard
                          product={recommendedProduct}
                          style={{}}
                          onProductClick={handleRecommendedProductClick}
                        />
                      </div>
                    ))}
                  </div>

                  {/* Horizontal scroll for mobile */}
                  <div className="md:hidden">
                    <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-hide">
                      {product.recommendations.map((recommendedProduct) => (
                        <div key={recommendedProduct.id} className="flex-shrink-0 w-64">
                          <RecommendedProductCard
                            product={recommendedProduct}
                            style={{}}
                            onProductClick={handleRecommendedProductClick}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Show more button for mobile if many products */}
                  {product.recommendations.length > 4 && (
                    <div className="mt-6 text-center">
                      <button className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        View All {product.recommendations.length} Recommendations
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-12 text-center">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">No Recommendations Yet</h4>
                  <p className="text-gray-600 max-w-md mx-auto">We're working on finding the perfect sustainable products for you! Check back soon for personalized recommendations.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Product Comparison Modal */}
        {showComparison && (
          <ProductComparison
            productIds={comparisonProducts}
            onClose={() => setShowComparison(false)}
          />
        )}
      </div>
    </div>
  );
};

export default ProductDetail;