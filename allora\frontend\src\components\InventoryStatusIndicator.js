import React, { useState, useEffect } from 'react';
import { useInventorySync } from '../hooks/useInventorySync';
import './InventoryStatusIndicator.css';

/**
 * Real-time inventory status indicator component
 * Shows current stock levels with live updates
 */
const InventoryStatusIndicator = ({ 
  productId, 
  variantId = null, 
  initialQuantity = 0,
  lowStockThreshold = 10,
  showDetails = false,
  size = 'medium',
  className = ''
}) => {
  const { getInventoryStatus, isConnected } = useInventorySync();
  const [currentQuantity, setCurrentQuantity] = useState(initialQuantity);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [isAnimating, setIsAnimating] = useState(false);

  // Update quantity when real-time data is available
  useEffect(() => {
    const inventoryStatus = getInventoryStatus(productId, variantId);
    if (inventoryStatus) {
      const newQuantity = inventoryStatus.quantity;
      
      // Animate if quantity changed
      if (newQuantity !== currentQuantity) {
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 1000);
      }
      
      setCurrentQuantity(newQuantity);
      setLastUpdate(inventoryStatus.updated_at);
    }
  }, [getInventoryStatus, productId, variantId, currentQuantity]);

  // Determine stock status
  const getStockStatus = () => {
    if (currentQuantity === 0) return 'out-of-stock';
    if (currentQuantity <= lowStockThreshold) return 'low-stock';
    return 'in-stock';
  };

  const stockStatus = getStockStatus();

  // Get status color
  const getStatusColor = () => {
    switch (stockStatus) {
      case 'out-of-stock': return '#dc2626';
      case 'low-stock': return '#f59e0b';
      case 'in-stock': return '#059669';
      default: return '#6b7280';
    }
  };

  // Get status text
  const getStatusText = () => {
    switch (stockStatus) {
      case 'out-of-stock': return 'Out of Stock';
      case 'low-stock': return 'Low Stock';
      case 'in-stock': return 'In Stock';
      default: return 'Unknown';
    }
  };

  // Format last update time
  const formatLastUpdate = () => {
    if (!lastUpdate) return null;
    
    const updateTime = new Date(lastUpdate);
    const now = new Date();
    const diffMs = now - updateTime;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return updateTime.toLocaleDateString();
  };

  return (
    <div className={`inventory-status-indicator ${size} ${className}`}>
      <div className="status-main">
        {/* Stock Level Indicator */}
        <div className={`stock-indicator ${stockStatus} ${isAnimating ? 'animating' : ''}`}>
          <div 
            className="stock-dot"
            style={{ backgroundColor: getStatusColor() }}
          />
          <span className="stock-quantity">
            {currentQuantity}
          </span>
        </div>

        {/* Connection Status */}
        {!isConnected && (
          <div className="connection-warning" title="Real-time updates disconnected">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
              <line x1="12" y1="9" x2="12" y2="13"/>
              <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>
          </div>
        )}
      </div>

      {/* Detailed Information */}
      {showDetails && (
        <div className="status-details">
          <div className="status-text">
            <span className={`status-label ${stockStatus}`}>
              {getStatusText()}
            </span>
          </div>
          
          {lastUpdate && (
            <div className="last-update">
              Updated {formatLastUpdate()}
            </div>
          )}
          
          {stockStatus === 'low-stock' && (
            <div className="stock-warning">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                <line x1="12" y1="9" x2="12" y2="13"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
              </svg>
              <span>Low stock alert</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Inventory sync status badge for channels
 */
export const SyncStatusBadge = ({ channelId, channelName, className = '' }) => {
  const { getChannelSyncStatus, isConnected } = useInventorySync();
  const [syncStatus, setSyncStatus] = useState(null);

  useEffect(() => {
    const status = getChannelSyncStatus(channelId);
    setSyncStatus(status);
  }, [getChannelSyncStatus, channelId]);

  if (!syncStatus) {
    return (
      <div className={`sync-status-badge unknown ${className}`}>
        <span className="status-dot"></span>
        <span>Unknown</span>
      </div>
    );
  }

  const getStatusClass = () => {
    switch (syncStatus.status) {
      case 'syncing': return 'syncing';
      case 'success': return 'success';
      case 'error': return 'error';
      default: return 'unknown';
    }
  };

  return (
    <div className={`sync-status-badge ${getStatusClass()} ${className}`}>
      <span className="status-dot"></span>
      <span className="status-text">
        {syncStatus.status === 'syncing' && 'Syncing...'}
        {syncStatus.status === 'success' && 'Synced'}
        {syncStatus.status === 'error' && 'Error'}
      </span>
      {syncStatus.error && (
        <span className="error-message" title={syncStatus.error}>
          !
        </span>
      )}
    </div>
  );
};

/**
 * Conflict notification badge
 */
export const ConflictNotificationBadge = ({ className = '' }) => {
  const { getPendingConflicts } = useInventorySync();
  const [conflictCount, setConflictCount] = useState(0);

  useEffect(() => {
    const conflicts = getPendingConflicts();
    setConflictCount(conflicts.length);
  }, [getPendingConflicts]);

  if (conflictCount === 0) return null;

  return (
    <div className={`conflict-notification-badge ${className}`}>
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
        <line x1="12" y1="9" x2="12" y2="13"/>
        <line x1="12" y1="17" x2="12.01" y2="17"/>
      </svg>
      <span className="conflict-count">{conflictCount}</span>
      <span className="conflict-text">
        {conflictCount === 1 ? 'conflict' : 'conflicts'}
      </span>
    </div>
  );
};

/**
 * Real-time inventory widget for product cards
 */
export const InventoryWidget = ({ 
  productId, 
  variantId = null, 
  initialQuantity = 0,
  showReserveButton = false,
  onReserve = null,
  className = ''
}) => {
  const { 
    getInventoryStatus, 
    reserveInventory, 
    isConnected 
  } = useInventorySync();
  
  const [isReserving, setIsReserving] = useState(false);
  const [reserveQuantity, setReserveQuantity] = useState(1);

  const inventoryStatus = getInventoryStatus(productId, variantId);
  const currentQuantity = inventoryStatus?.quantity ?? initialQuantity;
  const isInStock = currentQuantity > 0;

  const handleReserve = async () => {
    if (!onReserve || isReserving) return;

    setIsReserving(true);
    try {
      const result = await reserveInventory(productId, reserveQuantity, variantId);
      if (result.success && onReserve) {
        onReserve(reserveQuantity);
      }
    } catch (error) {
      console.error('Failed to reserve inventory:', error);
    } finally {
      setIsReserving(false);
    }
  };

  return (
    <div className={`inventory-widget ${className}`}>
      <InventoryStatusIndicator
        productId={productId}
        variantId={variantId}
        initialQuantity={initialQuantity}
        showDetails={true}
        size="small"
      />
      
      {showReserveButton && isInStock && (
        <div className="reserve-section">
          <div className="reserve-controls">
            <input
              type="number"
              min="1"
              max={currentQuantity}
              value={reserveQuantity}
              onChange={(e) => setReserveQuantity(parseInt(e.target.value) || 1)}
              className="reserve-quantity-input"
            />
            <button
              onClick={handleReserve}
              disabled={isReserving || !isConnected}
              className="reserve-button"
            >
              {isReserving ? 'Reserving...' : 'Reserve'}
            </button>
          </div>
        </div>
      )}
      
      {!isConnected && (
        <div className="offline-notice">
          <small>Real-time updates unavailable</small>
        </div>
      )}
    </div>
  );
};

export default InventoryStatusIndicator;
