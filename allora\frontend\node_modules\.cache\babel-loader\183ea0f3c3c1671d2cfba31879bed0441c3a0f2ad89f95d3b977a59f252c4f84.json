{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AdminLayout from '../components/AdminLayout';\nimport './AdminDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _statistics$total_rev;\n  const [dashboardData, setDashboardData] = useState(null);\n  const [fulfillmentData, setFulfillmentData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchDashboardData();\n    fetchFulfillmentData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        navigate('/admin/login');\n        return;\n      }\n      const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setDashboardData(data);\n      } else if (response.status === 401) {\n        localStorage.removeItem('adminToken');\n        localStorage.removeItem('adminUser');\n        navigate('/admin/login');\n      } else {\n        setError('Failed to load dashboard data');\n      }\n    } catch (error) {\n      setError('Network error. Please try again.');\n      console.error('Dashboard fetch error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchDashboardData,\n          className: \"retry-btn\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this);\n  }\n  const {\n    statistics,\n    recent_orders,\n    low_stock_products\n  } = dashboardData || {};\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-dashboard\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Dashboard Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome to the Allora Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon products\",\n            children: \"\\uD83D\\uDCE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_products) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon orders\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_orders) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon users\",\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_users) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon revenue\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\u20B9\", (statistics === null || statistics === void 0 ? void 0 : (_statistics$total_rev = statistics.total_revenue) === null || _statistics$total_rev === void 0 ? void 0 : _statistics$total_rev.toLocaleString()) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Revenue (30 days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/orders'),\n              className: \"view-all-btn\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"orders-table\",\n            children: recent_orders && recent_orders.length > 0 ? /*#__PURE__*/_jsxDEV(\"table\", {\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Order #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Customer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recent_orders.map(order => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [\"#\", order.order_number]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: order.customer_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status-badge ${order.status}`,\n                      children: order.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 150,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [\"\\u20B9\", order.total_amount.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(order.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 49\n                  }, this)]\n                }, order.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 45\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"No recent orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Low Stock Alert\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/inventory'),\n              className: \"view-all-btn\",\n              children: \"View Inventory\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"low-stock-list\",\n            children: low_stock_products && low_stock_products.length > 0 ? low_stock_products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"low-stock-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Stock: \", product.stock_quantity, \" / Threshold: \", product.low_stock_threshold]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stock-level\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stock-bar\",\n                  style: {\n                    width: `${Math.min(product.stock_quantity / product.low_stock_threshold * 100, 100)}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 41\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 37\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"All products are well stocked\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 9\n  }, this);\n};\n_s(AdminDashboard, \"IckvB8unvifWDN/lAv5wcCqHmOM=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "AdminLayout", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_statistics$total_rev", "dashboardData", "setDashboardData", "fulfillmentData", "setFulfillmentData", "loading", "setLoading", "error", "setError", "navigate", "fetchDashboardData", "fetchFulfillmentData", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "data", "json", "status", "removeItem", "console", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "statistics", "recent_orders", "low_stock_products", "total_products", "total_orders", "total_users", "total_revenue", "toLocaleString", "length", "map", "order", "order_number", "customer_email", "total_amount", "Date", "created_at", "toLocaleDateString", "id", "product", "name", "stock_quantity", "low_stock_threshold", "style", "width", "Math", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AdminLayout from '../components/AdminLayout';\nimport './AdminDashboard.css';\n\nconst AdminDashboard = () => {\n    const [dashboardData, setDashboardData] = useState(null);\n    const [fulfillmentData, setFulfillmentData] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        fetchDashboardData();\n        fetchFulfillmentData();\n    }, []);\n\n    const fetchDashboardData = async () => {\n        try {\n            const token = localStorage.getItem('adminToken');\n            if (!token) {\n                navigate('/admin/login');\n                return;\n            }\n\n            const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                },\n            });\n\n            if (response.ok) {\n                const data = await response.json();\n                setDashboardData(data);\n            } else if (response.status === 401) {\n                localStorage.removeItem('adminToken');\n                localStorage.removeItem('adminUser');\n                navigate('/admin/login');\n            } else {\n                setError('Failed to load dashboard data');\n            }\n        } catch (error) {\n            setError('Network error. Please try again.');\n            console.error('Dashboard fetch error:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    if (loading) {\n        return (\n            <AdminLayout>\n                <div className=\"admin-loading\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading dashboard...</p>\n                </div>\n            </AdminLayout>\n        );\n    }\n\n    if (error) {\n        return (\n            <AdminLayout>\n                <div className=\"admin-error\">\n                    <p>{error}</p>\n                    <button onClick={fetchDashboardData} className=\"retry-btn\">\n                        Retry\n                    </button>\n                </div>\n            </AdminLayout>\n        );\n    }\n\n    const { statistics, recent_orders, low_stock_products } = dashboardData || {};\n\n    return (\n        <AdminLayout>\n            <div className=\"admin-dashboard\">\n                <div className=\"dashboard-header\">\n                    <h1>Dashboard Overview</h1>\n                    <p>Welcome to the Allora Admin Panel</p>\n                </div>\n\n                {/* Statistics Cards */}\n                <div className=\"stats-grid\">\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon products\">📦</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_products || 0}</h3>\n                            <p>Total Products</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon orders\">🛒</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_orders || 0}</h3>\n                            <p>Total Orders</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon users\">👥</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_users || 0}</h3>\n                            <p>Total Users</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon revenue\">💰</div>\n                        <div className=\"stat-content\">\n                            <h3>₹{statistics?.total_revenue?.toLocaleString() || 0}</h3>\n                            <p>Revenue (30 days)</p>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"dashboard-content\">\n                    {/* Recent Orders */}\n                    <div className=\"dashboard-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Orders</h2>\n                            <button \n                                onClick={() => navigate('/admin/orders')}\n                                className=\"view-all-btn\"\n                            >\n                                View All\n                            </button>\n                        </div>\n                        \n                        <div className=\"orders-table\">\n                            {recent_orders && recent_orders.length > 0 ? (\n                                <table>\n                                    <thead>\n                                        <tr>\n                                            <th>Order #</th>\n                                            <th>Customer</th>\n                                            <th>Status</th>\n                                            <th>Amount</th>\n                                            <th>Date</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        {recent_orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>#{order.order_number}</td>\n                                                <td>{order.customer_email}</td>\n                                                <td>\n                                                    <span className={`status-badge ${order.status}`}>\n                                                        {order.status}\n                                                    </span>\n                                                </td>\n                                                <td>₹{order.total_amount.toLocaleString()}</td>\n                                                <td>{new Date(order.created_at).toLocaleDateString()}</td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </table>\n                            ) : (\n                                <p className=\"no-data\">No recent orders</p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Low Stock Alert */}\n                    <div className=\"dashboard-section\">\n                        <div className=\"section-header\">\n                            <h2>Low Stock Alert</h2>\n                            <button \n                                onClick={() => navigate('/admin/inventory')}\n                                className=\"view-all-btn\"\n                            >\n                                View Inventory\n                            </button>\n                        </div>\n                        \n                        <div className=\"low-stock-list\">\n                            {low_stock_products && low_stock_products.length > 0 ? (\n                                low_stock_products.map(product => (\n                                    <div key={product.id} className=\"low-stock-item\">\n                                        <div className=\"product-info\">\n                                            <h4>{product.name}</h4>\n                                            <p>Stock: {product.stock_quantity} / Threshold: {product.low_stock_threshold}</p>\n                                        </div>\n                                        <div className=\"stock-level\">\n                                            <div \n                                                className=\"stock-bar\"\n                                                style={{\n                                                    width: `${Math.min((product.stock_quantity / product.low_stock_threshold) * 100, 100)}%`\n                                                }}\n                                            ></div>\n                                        </div>\n                                    </div>\n                                ))\n                            ) : (\n                                <p className=\"no-data\">All products are well stocked</p>\n                            )}\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </AdminLayout>\n    );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACW,eAAe,EAAEC,kBAAkB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZiB,kBAAkB,CAAC,CAAC;IACpBC,oBAAoB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACF,KAAK,EAAE;QACRH,QAAQ,CAAC,cAAc,CAAC;QACxB;MACJ;MAEA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;QACtEC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUL,KAAK;QACpC;MACJ,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACb,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClClB,gBAAgB,CAACiB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIJ,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;QAChCR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;QACrCT,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;QACpCb,QAAQ,CAAC,cAAc,CAAC;MAC5B,CAAC,MAAM;QACHD,QAAQ,CAAC,+BAA+B,CAAC;MAC7C;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZC,QAAQ,CAAC,kCAAkC,CAAC;MAC5Ce,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACND,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,IAAID,OAAO,EAAE;IACT,oBACIR,OAAA,CAACF,WAAW;MAAA6B,QAAA,eACR3B,OAAA;QAAK4B,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC1B3B,OAAA;UAAK4B,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvChC,OAAA;UAAA2B,QAAA,EAAG;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEtB;EAEA,IAAItB,KAAK,EAAE;IACP,oBACIV,OAAA,CAACF,WAAW;MAAA6B,QAAA,eACR3B,OAAA;QAAK4B,SAAS,EAAC,aAAa;QAAAD,QAAA,gBACxB3B,OAAA;UAAA2B,QAAA,EAAIjB;QAAK;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdhC,OAAA;UAAQiC,OAAO,EAAEpB,kBAAmB;UAACe,SAAS,EAAC,WAAW;UAAAD,QAAA,EAAC;QAE3D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEtB;EAEA,MAAM;IAAEE,UAAU;IAAEC,aAAa;IAAEC;EAAmB,CAAC,GAAGhC,aAAa,IAAI,CAAC,CAAC;EAE7E,oBACIJ,OAAA,CAACF,WAAW;IAAA6B,QAAA,eACR3B,OAAA;MAAK4B,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC5B3B,OAAA;QAAK4B,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7B3B,OAAA;UAAA2B,QAAA,EAAI;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BhC,OAAA;UAAA2B,QAAA,EAAG;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAGNhC,OAAA;QAAK4B,SAAS,EAAC,YAAY;QAAAD,QAAA,gBACvB3B,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB3B,OAAA;YAAK4B,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5ChC,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB3B,OAAA;cAAA2B,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,cAAc,KAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1ChC,OAAA;cAAA2B,QAAA,EAAG;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB3B,OAAA;YAAK4B,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1ChC,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB3B,OAAA;cAAA2B,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,YAAY,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxChC,OAAA;cAAA2B,QAAA,EAAG;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB3B,OAAA;YAAK4B,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzChC,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB3B,OAAA;cAAA2B,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,KAAI;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvChC,OAAA;cAAA2B,QAAA,EAAG;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB3B,OAAA;YAAK4B,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3ChC,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB3B,OAAA;cAAA2B,QAAA,GAAI,QAAC,EAAC,CAAAO,UAAU,aAAVA,UAAU,wBAAA/B,qBAAA,GAAV+B,UAAU,CAAEM,aAAa,cAAArC,qBAAA,uBAAzBA,qBAAA,CAA2BsC,cAAc,CAAC,CAAC,KAAI,CAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5DhC,OAAA;cAAA2B,QAAA,EAAG;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENhC,OAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAE9B3B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9B3B,OAAA;YAAK4B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B3B,OAAA;cAAA2B,QAAA,EAAI;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBhC,OAAA;cACIiC,OAAO,EAAEA,CAAA,KAAMrB,QAAQ,CAAC,eAAe,CAAE;cACzCgB,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENhC,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAAD,QAAA,EACxBQ,aAAa,IAAIA,aAAa,CAACO,MAAM,GAAG,CAAC,gBACtC1C,OAAA;cAAA2B,QAAA,gBACI3B,OAAA;gBAAA2B,QAAA,eACI3B,OAAA;kBAAA2B,QAAA,gBACI3B,OAAA;oBAAA2B,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBhC,OAAA;oBAAA2B,QAAA,EAAI;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBhC,OAAA;oBAAA2B,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfhC,OAAA;oBAAA2B,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfhC,OAAA;oBAAA2B,QAAA,EAAI;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACRhC,OAAA;gBAAA2B,QAAA,EACKQ,aAAa,CAACQ,GAAG,CAACC,KAAK,iBACpB5C,OAAA;kBAAA2B,QAAA,gBACI3B,OAAA;oBAAA2B,QAAA,GAAI,GAAC,EAACiB,KAAK,CAACC,YAAY;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BhC,OAAA;oBAAA2B,QAAA,EAAKiB,KAAK,CAACE;kBAAc;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/BhC,OAAA;oBAAA2B,QAAA,eACI3B,OAAA;sBAAM4B,SAAS,EAAE,gBAAgBgB,KAAK,CAACpB,MAAM,EAAG;sBAAAG,QAAA,EAC3CiB,KAAK,CAACpB;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACLhC,OAAA;oBAAA2B,QAAA,GAAI,QAAC,EAACiB,KAAK,CAACG,YAAY,CAACN,cAAc,CAAC,CAAC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/ChC,OAAA;oBAAA2B,QAAA,EAAK,IAAIqB,IAAI,CAACJ,KAAK,CAACK,UAAU,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GATrDY,KAAK,CAACO,EAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUb,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAERhC,OAAA;cAAG4B,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC7C;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNhC,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9B3B,OAAA;YAAK4B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B3B,OAAA;cAAA2B,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBhC,OAAA;cACIiC,OAAO,EAAEA,CAAA,KAAMrB,QAAQ,CAAC,kBAAkB,CAAE;cAC5CgB,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENhC,OAAA;YAAK4B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC1BS,kBAAkB,IAAIA,kBAAkB,CAACM,MAAM,GAAG,CAAC,GAChDN,kBAAkB,CAACO,GAAG,CAACS,OAAO,iBAC1BpD,OAAA;cAAsB4B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC5C3B,OAAA;gBAAK4B,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB3B,OAAA;kBAAA2B,QAAA,EAAKyB,OAAO,CAACC;gBAAI;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBhC,OAAA;kBAAA2B,QAAA,GAAG,SAAO,EAACyB,OAAO,CAACE,cAAc,EAAC,gBAAc,EAACF,OAAO,CAACG,mBAAmB;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACNhC,OAAA;gBAAK4B,SAAS,EAAC,aAAa;gBAAAD,QAAA,eACxB3B,OAAA;kBACI4B,SAAS,EAAC,WAAW;kBACrB4B,KAAK,EAAE;oBACHC,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAEP,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,mBAAmB,GAAI,GAAG,EAAE,GAAG,CAAC;kBACzF;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAZAoB,OAAO,CAACD,EAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaf,CACR,CAAC,gBAEFhC,OAAA;cAAG4B,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC1D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAC9B,EAAA,CAvMID,cAAc;EAAA,QAKCJ,WAAW;AAAA;AAAA+D,EAAA,GAL1B3D,cAAc;AAyMpB,eAAeA,cAAc;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}