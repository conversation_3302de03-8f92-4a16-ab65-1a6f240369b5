.seller-analytics {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f5f9;
}

.analytics-header h1 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.period-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.period-select {
    padding: 8px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s ease;
}

.period-select:hover {
    border-color: #3b82f6;
}

.period-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.analytics-tabs {
    display: flex;
    gap: 4px;
    margin-bottom: 30px;
    background: #f8fafc;
    padding: 4px;
    border-radius: 12px;
    width: fit-content;
}

.tab-btn {
    padding: 12px 24px;
    border: none;
    background: transparent;
    color: #64748b;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background: white;
    color: #3b82f6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
    color: #475569;
    background: rgba(255, 255, 255, 0.5);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.metric-card {
    background: white;
    padding: 24px;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.metric-header h3 {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.growth-indicator {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
    background: rgba(16, 185, 129, 0.1);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.2;
}

.top-products-section,
.sales-chart-section {
    background: white;
    padding: 24px;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    margin-bottom: 30px;
}

.top-products-section h2,
.sales-chart-section h2 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 20px 0;
}

.products-table,
.products-analytics-table {
    display: grid;
    gap: 1px;
    background: #f1f5f9;
    border-radius: 12px;
    overflow: hidden;
}

.products-table {
    grid-template-columns: 2fr 1fr 1fr 1fr;
}

.products-analytics-table {
    grid-template-columns: 2fr 1fr 1fr 80px 80px 1fr 1fr 120px;
}

.table-header {
    display: contents;
}

.table-header > div {
    background: #64748b;
    color: white;
    padding: 16px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-row {
    display: contents;
}

.table-row > div {
    background: white;
    padding: 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f1f5f9;
}

.product-name {
    font-weight: 500;
    color: #1e293b;
}

.product-info {
    display: flex;
    flex-direction: column;
}

.low-stock {
    color: #ef4444;
    font-weight: 600;
}

.rating {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
}

.performance-score {
    display: flex;
    align-items: center;
    gap: 8px;
}

.score-bar {
    flex: 1;
    height: 8px;
    background: #f1f5f9;
    border-radius: 4px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
    transition: width 0.3s ease;
}

.chart-container {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    min-height: 300px;
}

.chart-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #64748b;
}

.chart-data {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    overflow-x: auto;
    width: 100%;
}

.chart-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.bar-label {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 4px;
}

.bar-value {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
}

.analytics-actions {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.back-btn {
    padding: 12px 24px;
    background: #64748b;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: #475569;
    transform: translateY(-1px);
}

.loading-container,
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f1f5f9;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-container h3 {
    color: #ef4444;
    margin-bottom: 8px;
}

.error-container p {
    color: #64748b;
    margin-bottom: 20px;
}

.retry-btn {
    padding: 10px 20px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.retry-btn:hover {
    background: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .seller-analytics {
        padding: 15px;
    }
    
    .analytics-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .products-table {
        grid-template-columns: 2fr 1fr 1fr;
    }
    
    .products-analytics-table {
        grid-template-columns: 1fr;
    }
    
    .table-row > div {
        padding: 12px;
    }
    
    .chart-data {
        flex-direction: column;
        gap: 8px;
    }
}
