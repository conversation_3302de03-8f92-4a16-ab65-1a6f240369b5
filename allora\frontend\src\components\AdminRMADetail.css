.admin-rma-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

/* Header */
.admin-rma-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.header-content h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 10px 0;
}

.rma-number {
    color: #3498db;
    font-size: 1.2rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* Status and Actions */
.status-actions-section {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    margin-bottom: 30px;
}

.current-status,
.available-actions {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.current-status h3,
.available-actions h3 {
    color: #2c3e50;
    margin: 0 0 20px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.status-badge.large {
    padding: 12px 24px;
    font-size: 1rem;
}

.status-badge.small {
    padding: 6px 12px;
    font-size: 0.75rem;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.action-btn {
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* RMA Information */
.rma-information {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.rma-information h3 {
    color: #2c3e50;
    margin: 0 0 25px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item .label {
    color: #666;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item .value {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.info-item .value.highlight {
    color: #27ae60;
    font-size: 1.2rem;
}

/* Return Items */
.return-items-section {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.return-items-section h3 {
    color: #2c3e50;
    margin: 0 0 25px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.return-item-card {
    display: flex;
    gap: 20px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f8f9fa;
}

.item-image {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.item-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.item-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.condition-notes {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.condition-notes strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 8px;
}

.condition-notes p {
    margin: 0;
    color: #666;
    font-style: italic;
}

.item-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.refund-amount {
    color: #27ae60;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Timeline */
.timeline-section {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.timeline-section h3 {
    color: #2c3e50;
    margin: 0 0 25px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e0e0e0;
}

.timeline-event {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -37px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #3498db;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #3498db;
}

.timeline-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.event-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 8px;
}

.event-description {
    color: #666;
    margin-bottom: 12px;
    line-height: 1.5;
}

.event-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: #999;
}

.event-date {
    font-weight: 500;
}

.event-actor {
    font-style: italic;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #e0e0e0;
    color: #2c3e50;
}

.modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.refund-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading and Error States */
.loading-spinner,
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message h2 {
    color: #e74c3c;
    margin-bottom: 15px;
}

.error-message p {
    color: #666;
    margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-rma-detail-container {
        padding: 15px;
    }
    
    .admin-rma-detail-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 20px;
    }
    
    .header-content h1 {
        font-size: 1.6rem;
    }
    
    .status-actions-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .return-item-card {
        flex-direction: column;
        gap: 15px;
    }
    
    .item-image {
        align-self: center;
    }
    
    .item-status {
        align-items: flex-start;
    }
    
    .timeline {
        padding-left: 20px;
    }
    
    .timeline-marker {
        left: -27px;
    }
    
    .modal {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }
    
    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        width: 100%;
    }
}
