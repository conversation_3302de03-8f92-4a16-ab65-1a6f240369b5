{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\components\\\\SimpleHome.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useCart } from '../contexts/CartContext';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { useLoading } from '../contexts/LoadingContext';\nimport { Link } from 'react-router-dom';\nimport { API_BASE_URL } from '../config/api';\nimport { ProductImage } from './EnhancedImage';\nimport { LoadingWrapper, ProductCardSkeleton } from './LoadingComponents';\nimport { formatPrice, calculateDiscountPercentage } from '../utils/currency';\n\n// Add custom CSS animations\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst customStyles = `\n  @keyframes float {\n    0%, 100% { transform: translateY(0px); }\n    50% { transform: translateY(-20px); }\n  }\n\n  @keyframes float-delayed {\n    0%, 100% { transform: translateY(0px); }\n    50% { transform: translateY(-15px); }\n  }\n\n  @keyframes slow-zoom {\n    0% { transform: scale(1.1); }\n    100% { transform: scale(1.2); }\n  }\n\n  @keyframes slide-up {\n    0% { transform: translateY(30px); opacity: 0; }\n    100% { transform: translateY(0); opacity: 1; }\n  }\n\n  @keyframes slide-up-delayed {\n    0% { transform: translateY(30px); opacity: 0; }\n    100% { transform: translateY(0); opacity: 1; }\n  }\n\n  @keyframes fade-in-up {\n    0% { transform: translateY(20px); opacity: 0; }\n    100% { transform: translateY(0); opacity: 1; }\n  }\n\n  @keyframes fade-in-up-delayed {\n    0% { transform: translateY(20px); opacity: 0; }\n    100% { transform: translateY(0); opacity: 1; }\n  }\n\n  .animate-float { animation: float 6s ease-in-out infinite; }\n  .animate-float-delayed { animation: float-delayed 6s ease-in-out infinite 2s; }\n  .animate-slow-zoom { animation: slow-zoom 20s ease-in-out infinite alternate; }\n  .animate-slide-up { animation: slide-up 1s ease-out forwards; }\n  .animate-slide-up-delayed { animation: slide-up-delayed 1s ease-out 0.3s forwards; opacity: 0; }\n  .animate-fade-in-up { animation: fade-in-up 0.8s ease-out forwards; }\n  .animate-fade-in-up-delayed { animation: fade-in-up-delayed 0.8s ease-out 0.5s forwards; opacity: 0; }\n\n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n\n  .shadow-3xl {\n    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);\n  }\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.textContent = customStyles;\n  document.head.appendChild(styleSheet);\n}\n\n// Modern Unique Product Card with Wishlist and Cart\nconst ProductCard = ({\n  product,\n  onAddToCart,\n  size = 'normal'\n}) => {\n  _s();\n  const [isWishlisted, setIsWishlisted] = useState(false);\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Render star rating\n  const renderStars = rating => {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    for (let i = 0; i < fullStars; i++) {\n      stars.push(/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-yellow-400\",\n        children: \"\\u2605\"\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 18\n      }, this));\n    }\n    if (hasHalfStar) {\n      stars.push(/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-yellow-400\",\n        children: \"\\u2606\"\n      }, \"half\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 18\n      }, this));\n    }\n    for (let i = stars.length; i < 5; i++) {\n      stars.push(/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-300\",\n        children: \"\\u2605\"\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 18\n      }, this));\n    }\n    return stars;\n  };\n  const handleWishlistToggle = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsWishlisted(!isWishlisted);\n    // Here you would typically call an API to add/remove from wishlist\n  };\n  const handleAddToCart = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    onAddToCart(product);\n  };\n  const cardClasses = size === 'small' ? \"relative bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer group h-full flex flex-col transform hover:-translate-y-2\" : \"relative bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer group h-full flex flex-col transform hover:-translate-y-2\";\n  const imageHeight = size === 'small' ? 'h-40' : 'h-64';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cardClasses,\n    onMouseEnter: () => setIsHovered(true),\n    onMouseLeave: () => setIsHovered(false),\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleWishlistToggle,\n      className: `absolute top-4 right-4 z-10 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${isWishlisted ? 'bg-red-500 text-white shadow-lg' : 'bg-white/80 backdrop-blur-sm text-gray-600 hover:bg-red-500 hover:text-white'} ${isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-75'}`,\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: isWishlisted ? 'currentColor' : 'none',\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: `/product/${product.id}`,\n      className: \"block h-full flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `relative overflow-hidden ${imageHeight}`,\n        children: [/*#__PURE__*/_jsxDEV(LazyLoadImage, {\n          src: getProductImageUrl(product),\n          alt: product.name || 'Product Image',\n          effect: \"blur\",\n          className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700\",\n          placeholder: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-full ${imageHeight} bg-gradient-to-br from-gray-100 to-gray-200 animate-pulse flex items-center justify-center`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 text-sm\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this),\n          threshold: 300,\n          onError: e => handleImageError(e)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-4 left-4 flex flex-col space-y-2\",\n          children: [product.sustainabilityScore >= 80 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center space-x-1 shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83C\\uDF31\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Eco-Friendly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), product.original_price && product.original_price > product.price && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n            children: [calculateDiscountPercentage(product.original_price, product.price), \"% OFF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), product.stockQuantity <= 5 && product.stockQuantity > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute bottom-4 left-4 bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse\",\n          children: [\"Only \", product.stockQuantity, \" left!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `absolute bottom-4 right-4 flex space-x-2 transition-all duration-300 ${isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`,\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddToCart,\n            disabled: product.stockQuantity === 0,\n            className: \"bg-emerald-500 hover:bg-emerald-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110 disabled:bg-gray-400 disabled:cursor-not-allowed\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4m16 0H4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 flex-1 flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-2\",\n          children: [product.brand && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-semibold text-emerald-600 uppercase tracking-wider bg-emerald-50 px-2 py-1 rounded-full\",\n            children: product.brand\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), product.category && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n            children: product.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors line-clamp-2 ${size === 'small' ? 'text-sm' : 'text-lg'}`,\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), (product.averageRating || product.average_rating) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex text-sm\",\n            children: renderStars(product.averageRating || product.average_rating)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n            children: [product.totalReviews || product.total_reviews || 0, \" reviews\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: `font-bold text-emerald-600 ${size === 'small' ? 'text-xl' : 'text-2xl'}`,\n              children: formatPrice(product.price)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), product.original_price && product.original_price > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg text-gray-400 line-through\",\n              children: formatPrice(product.original_price)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), product.material && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-600 bg-gray-100 px-3 py-1 rounded-full\",\n              children: product.material\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddToCart,\n            disabled: product.stockQuantity === 0,\n            className: `w-full py-3 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${product.stockQuantity === 0 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'}`,\n            children: product.stockQuantity === 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Out of Stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4m16 0H4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Add to Cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n\n// Unique Floating Category Cards\n_s(ProductCard, \"nGz5vi1AsNZVdHpsX4uPQSgFVX4=\");\n_c = ProductCard;\nconst FloatingCategorySection = ({\n  categories,\n  onCategoryClick\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-5\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-10 left-10 w-32 h-32 bg-emerald-500 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-20 right-20 w-40 h-40 bg-blue-500 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n          children: [\"Explore Our\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-transparent bg-clip-text bg-gradient-to-r from-emerald-500 to-blue-500\",\n            children: \" Universe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n          children: \"Discover sustainable products across different categories, each carefully curated for quality and environmental impact.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n        children: categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: () => onCategoryClick(category),\n          className: \"group cursor-pointer transform hover:scale-105 transition-all duration-500\",\n          style: {\n            animationDelay: `${index * 100}ms`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-br from-emerald-50 via-white to-blue-50 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: category.image,\n                  alt: category.name,\n                  className: \"w-12 h-12 object-cover rounded-lg filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors\",\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm leading-relaxed\",\n                children: category.description || \"Sustainable and eco-friendly options\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"inline-flex items-center text-emerald-600 font-semibold\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Explore\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 ml-2\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 283,\n    columnNumber: 5\n  }, this);\n};\n\n// Modern Product Showcase Section\n_c2 = FloatingCategorySection;\nconst ModernProductShowcase = ({\n  title,\n  products,\n  onAddToCart,\n  theme = 'emerald'\n}) => {\n  const themeClasses = {\n    emerald: 'from-emerald-500 to-emerald-600',\n    blue: 'from-blue-500 to-blue-600',\n    purple: 'from-purple-500 to-purple-600',\n    pink: 'from-pink-500 to-pink-600'\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-white relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-1 bg-gradient-to-r from-emerald-500 to-blue-500 mx-auto rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n        children: products.slice(0, 8).map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in-up\",\n          style: {\n            animationDelay: `${index * 100}ms`\n          },\n          children: /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product,\n            onAddToCart: onAddToCart\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)\n        }, product.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-16\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/search\",\n          className: `inline-flex items-center px-8 py-4 bg-gradient-to-r ${themeClasses[theme]} text-white font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"View All Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 ml-2\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 360,\n    columnNumber: 5\n  }, this);\n};\n\n// Unique Modern Hero Section\n_c3 = ModernProductShowcase;\nconst ModernHero = () => {\n  _s2();\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const heroSlides = [{\n    id: 1,\n    title: \"Sustainable Living\",\n    subtitle: \"Made Simple\",\n    description: \"Discover eco-friendly products that make a difference for you and the planet\",\n    image: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop\",\n    cta: \"Shop Sustainable\",\n    link: \"/search?sustainable=true\",\n    accent: \"emerald\"\n  }, {\n    id: 2,\n    title: \"Green Technology\",\n    subtitle: \"For Tomorrow\",\n    description: \"Innovative eco-tech solutions that reduce your carbon footprint\",\n    image: \"https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1200&h=600&fit=crop\",\n    cta: \"Explore Tech\",\n    link: \"/category/electronics\",\n    accent: \"blue\"\n  }, {\n    id: 3,\n    title: \"Natural Beauty\",\n    subtitle: \"Pure & Clean\",\n    description: \"Organic beauty products that nurture your skin and the environment\",\n    image: \"https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=1200&h=600&fit=crop\",\n    cta: \"Discover Beauty\",\n    link: \"/category/beauty\",\n    accent: \"pink\"\n  }];\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % heroSlides.length);\n    }, 6000);\n    return () => clearInterval(timer);\n  }, [heroSlides.length]);\n  const currentHero = heroSlides[currentSlide];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0\",\n      children: heroSlides.map((slide, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute inset-0 transition-opacity duration-1000 ${index === currentSlide ? 'opacity-100' : 'opacity-0'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: slide.image,\n          alt: slide.title,\n          className: \"w-full h-full object-cover scale-110 animate-slow-zoom\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this)]\n      }, slide.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-20 left-10 w-32 h-32 bg-emerald-500/10 rounded-full blur-xl animate-float\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 right-20 w-24 h-24 bg-blue-500/10 rounded-full blur-xl animate-float-delayed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-32 left-1/4 w-40 h-40 bg-pink-500/10 rounded-full blur-xl animate-float\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 max-w-7xl mx-auto px-4 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-6xl md:text-8xl font-bold text-white mb-4 leading-tight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block animate-slide-up\",\n              children: currentHero.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `block text-transparent bg-clip-text bg-gradient-to-r ${currentHero.accent === 'emerald' ? 'from-emerald-400 to-emerald-600' : currentHero.accent === 'blue' ? 'from-blue-400 to-blue-600' : 'from-pink-400 to-pink-600'} animate-slide-up-delayed`,\n              children: currentHero.subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed animate-fade-in-up\",\n          children: currentHero.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up-delayed\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: currentHero.link,\n            className: `group relative px-8 py-4 bg-gradient-to-r ${currentHero.accent === 'emerald' ? 'from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700' : currentHero.accent === 'blue' ? 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' : 'from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700'} text-white font-semibold rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300`,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative z-10 flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: currentHero.cta\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/categories\",\n            className: \"group px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-full hover:bg-white/10 hover:border-white/50 transition-all duration-300 backdrop-blur-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Browse All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 group-hover:rotate-45 transition-transform\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M4 6h16M4 12h16M4 18h16\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3\",\n      children: heroSlides.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentSlide(index),\n        className: `w-3 h-3 rounded-full transition-all duration-300 ${index === currentSlide ? 'bg-white scale-125' : 'bg-white/40 hover:bg-white/60'}`\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-8 right-8 animate-bounce\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 452,\n    columnNumber: 5\n  }, this);\n};\n\n// Main Home Component\n_s2(ModernHero, \"/jm+XmndjAYlDCFyCnfFEXJOloU=\");\n_c4 = ModernHero;\nconst SimpleHome = () => {\n  _s3();\n  const {\n    addToCart\n  } = useCart();\n  const {\n    success\n  } = useNotification();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Modern unique categories\n  const uniqueCategories = [{\n    name: 'Eco Fashion',\n    image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200&h=150&fit=crop',\n    link: '/search?category=clothing',\n    description: 'Sustainable clothing that looks good and feels great'\n  }, {\n    name: 'Green Home',\n    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=150&fit=crop',\n    link: '/search?category=home-garden',\n    description: 'Transform your space with eco-friendly home essentials'\n  }, {\n    name: 'Natural Beauty',\n    image: 'https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=200&h=150&fit=crop',\n    link: '/search?category=beauty',\n    description: 'Pure, organic beauty products for radiant skin'\n  }, {\n    name: 'Smart Tech',\n    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=150&fit=crop',\n    link: '/search?category=electronics',\n    description: 'Innovative technology that respects the environment'\n  }, {\n    name: 'Wellness',\n    image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=200&h=150&fit=crop',\n    link: '/search?category=wellness',\n    description: 'Holistic products for mind, body, and soul'\n  }, {\n    name: 'Outdoor Life',\n    image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=200&h=150&fit=crop',\n    link: '/search?category=outdoor',\n    description: 'Gear for sustainable outdoor adventures'\n  }, {\n    name: 'Zero Waste',\n    image: 'https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?w=200&h=150&fit=crop',\n    link: '/search?category=zero-waste',\n    description: 'Products that help you live waste-free'\n  }, {\n    name: 'Kids & Baby',\n    image: 'https://images.unsplash.com/photo-1515488042361-ee00e0ddd4e4?w=200&h=150&fit=crop',\n    link: '/search?category=kids',\n    description: 'Safe, sustainable products for little ones'\n  }];\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/products?limit=20`);\n        if (!response.ok) {\n          throw new Error('Failed to fetch products');\n        }\n        const data = await response.json();\n        setProducts(data.products || data);\n        setLoading(false);\n      } catch (err) {\n        setError(err.message);\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n  }, []);\n  const handleAddToCart = async product => {\n    try {\n      await addToCart(product.id, 1);\n      success(`${product.name} added to cart!`);\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      error('Failed to add item to cart');\n    }\n  };\n  const handleCategoryClick = category => {\n    window.location.href = category.link;\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Oops! Something went wrong\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors duration-200\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-64 bg-gray-200 rounded-lg mb-8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n            children: [...Array(6)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg border border-gray-200 p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-6 bg-gray-200 rounded mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4\",\n                children: [...Array(4)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-24 bg-gray-200 rounded mb-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 683,\n                    columnNumber: 25\n                  }, this)]\n                }, i, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 672,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(ModernHero, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 699,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FloatingCategorySection, {\n      categories: uniqueCategories,\n      onCategoryClick: handleCategoryClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 702,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModernProductShowcase, {\n      title: \"Featured Sustainable Products\",\n      products: products.slice(0, 8),\n      onAddToCart: handleAddToCart,\n      theme: \"emerald\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gradient-to-br from-emerald-50 to-blue-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n            children: \"Trending Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Discover what's popular in sustainable living\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: products.slice(8, 14).map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fade-in-up\",\n            style: {\n              animationDelay: `${index * 150}ms`\n            },\n            children: /*#__PURE__*/_jsxDEV(ProductCard, {\n              product: product,\n              onAddToCart: handleAddToCart\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this)\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-white relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-blue-500/5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n            children: \"Customer Favorites\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-24 h-1 bg-gradient-to-r from-emerald-500 to-blue-500 mx-auto rounded-full mb-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n            children: \"Products our customers can't stop talking about\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: products.slice(14, 18).map((product, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"transform hover:scale-105 transition-all duration-500\",\n            style: {\n              animationDelay: `${index * 100}ms`\n            },\n            children: /*#__PURE__*/_jsxDEV(ProductCard, {\n              product: product,\n              onAddToCart: handleAddToCart\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this)\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-12 bg-emerald-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Your Impact Matters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n            children: \"Every sustainable choice you make contributes to a better planet. See the collective impact of our community.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-white rounded-xl p-6 shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-emerald-600 mb-2\",\n              children: \"2.5M+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"CO\\u2082 Saved (kg)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-white rounded-xl p-6 shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-blue-600 mb-2\",\n              children: \"1.8M+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Water Conserved (L)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center bg-white rounded-xl p-6 shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-orange-600 mb-2\",\n              children: \"950K+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Waste Diverted (kg)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 780,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 779,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-8 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-6\",\n          children: \"What Our Customers Say\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n          children: [{\n            name: \"Priya Sharma\",\n            rating: 5,\n            comment: \"Amazing quality sustainable products! The bamboo t-shirts are so comfortable and eco-friendly.\",\n            product: \"Eco-Friendly Bamboo T-Shirt\"\n          }, {\n            name: \"Rahul Kumar\",\n            rating: 5,\n            comment: \"Fast delivery and excellent customer service. Love supporting sustainable brands through Allora.\",\n            product: \"Organic Cotton Slim Jeans\"\n          }, {\n            name: \"Anita Patel\",\n            rating: 4,\n            comment: \"Great variety of eco-friendly products. The packaging is also completely plastic-free!\",\n            product: \"Recycled Canvas Backpack\"\n          }].map((review, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex text-yellow-400 mr-2\",\n                children: [...Array(review.rating)].map((_, i) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u2605\"\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: [review.rating, \"/5\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 mb-3\",\n              children: [\"\\\"\", review.comment, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-medium text-gray-900\",\n                children: review.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-500\",\n                children: [\"Verified purchase: \", review.product]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 806,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 805,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-12 bg-emerald-600\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-white mb-4\",\n          children: \"Stay Updated with Sustainable Living\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-emerald-100 mb-8 text-lg\",\n          children: \"Get the latest eco-friendly products, sustainability tips, and exclusive deals delivered to your inbox.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            placeholder: \"Enter your email\",\n            className: \"flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-emerald-300 focus:outline-none\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-white text-emerald-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\",\n            children: \"Subscribe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 862,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 850,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 697,\n    columnNumber: 5\n  }, this);\n};\n_s3(SimpleHome, \"NeriU07Q4UstxB8hsEA/MRhErHs=\", false, function () {\n  return [useCart, useNotification];\n});\n_c5 = SimpleHome;\nexport default SimpleHome;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"ProductCard\");\n$RefreshReg$(_c2, \"FloatingCategorySection\");\n$RefreshReg$(_c3, \"ModernProductShowcase\");\n$RefreshReg$(_c4, \"ModernHero\");\n$RefreshReg$(_c5, \"SimpleHome\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCart", "useNotification", "useLoading", "Link", "API_BASE_URL", "ProductImage", "LoadingWrapper", "ProductCardSkeleton", "formatPrice", "calculateDiscountPercentage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "customStyles", "document", "styleSheet", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "ProductCard", "product", "onAddToCart", "size", "_s", "isWishlisted", "setIsWishlisted", "isHovered", "setIsHovered", "renderStars", "rating", "stars", "fullStars", "Math", "floor", "hasHalfStar", "i", "push", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "handleWishlistToggle", "e", "preventDefault", "stopPropagation", "handleAddToCart", "cardClasses", "imageHeight", "onMouseEnter", "onMouseLeave", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "to", "id", "LazyLoadImage", "src", "getProductImageUrl", "alt", "name", "effect", "placeholder", "threshold", "onError", "handleImageError", "sustainabilityScore", "original_price", "price", "stockQuantity", "disabled", "brand", "category", "averageRating", "average_rating", "totalReviews", "total_reviews", "material", "_c", "FloatingCategorySection", "categories", "onCategoryClick", "map", "index", "style", "animationDelay", "image", "description", "_c2", "ModernProductShowcase", "title", "products", "theme", "themeClasses", "emerald", "blue", "purple", "pink", "slice", "_c3", "ModernHero", "_s2", "currentSlide", "setCurrentSlide", "heroSlides", "subtitle", "cta", "link", "accent", "timer", "setInterval", "prev", "clearInterval", "currentHero", "slide", "_", "_c4", "SimpleHome", "_s3", "addToCart", "success", "setProducts", "loading", "setLoading", "error", "setError", "uniqueCategories", "fetchProducts", "response", "fetch", "ok", "Error", "data", "json", "err", "message", "console", "handleCategoryClick", "window", "location", "href", "reload", "Array", "comment", "review", "type", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/components/SimpleHome.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useCart } from '../contexts/CartContext';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { useLoading } from '../contexts/LoadingContext';\nimport { Link } from 'react-router-dom';\nimport { API_BASE_URL } from '../config/api';\nimport { ProductImage } from './EnhancedImage';\nimport { LoadingWrapper, ProductCardSkeleton } from './LoadingComponents';\nimport { formatPrice, calculateDiscountPercentage } from '../utils/currency';\n\n// Add custom CSS animations\nconst customStyles = `\n  @keyframes float {\n    0%, 100% { transform: translateY(0px); }\n    50% { transform: translateY(-20px); }\n  }\n\n  @keyframes float-delayed {\n    0%, 100% { transform: translateY(0px); }\n    50% { transform: translateY(-15px); }\n  }\n\n  @keyframes slow-zoom {\n    0% { transform: scale(1.1); }\n    100% { transform: scale(1.2); }\n  }\n\n  @keyframes slide-up {\n    0% { transform: translateY(30px); opacity: 0; }\n    100% { transform: translateY(0); opacity: 1; }\n  }\n\n  @keyframes slide-up-delayed {\n    0% { transform: translateY(30px); opacity: 0; }\n    100% { transform: translateY(0); opacity: 1; }\n  }\n\n  @keyframes fade-in-up {\n    0% { transform: translateY(20px); opacity: 0; }\n    100% { transform: translateY(0); opacity: 1; }\n  }\n\n  @keyframes fade-in-up-delayed {\n    0% { transform: translateY(20px); opacity: 0; }\n    100% { transform: translateY(0); opacity: 1; }\n  }\n\n  .animate-float { animation: float 6s ease-in-out infinite; }\n  .animate-float-delayed { animation: float-delayed 6s ease-in-out infinite 2s; }\n  .animate-slow-zoom { animation: slow-zoom 20s ease-in-out infinite alternate; }\n  .animate-slide-up { animation: slide-up 1s ease-out forwards; }\n  .animate-slide-up-delayed { animation: slide-up-delayed 1s ease-out 0.3s forwards; opacity: 0; }\n  .animate-fade-in-up { animation: fade-in-up 0.8s ease-out forwards; }\n  .animate-fade-in-up-delayed { animation: fade-in-up-delayed 0.8s ease-out 0.5s forwards; opacity: 0; }\n\n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n\n  .shadow-3xl {\n    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);\n  }\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.textContent = customStyles;\n  document.head.appendChild(styleSheet);\n}\n\n// Modern Unique Product Card with Wishlist and Cart\nconst ProductCard = ({ product, onAddToCart, size = 'normal' }) => {\n  const [isWishlisted, setIsWishlisted] = useState(false);\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Render star rating\n  const renderStars = (rating) => {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n\n    for (let i = 0; i < fullStars; i++) {\n      stars.push(<span key={i} className=\"text-yellow-400\">★</span>);\n    }\n    if (hasHalfStar) {\n      stars.push(<span key=\"half\" className=\"text-yellow-400\">☆</span>);\n    }\n    for (let i = stars.length; i < 5; i++) {\n      stars.push(<span key={i} className=\"text-gray-300\">★</span>);\n    }\n    return stars;\n  };\n\n  const handleWishlistToggle = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsWishlisted(!isWishlisted);\n    // Here you would typically call an API to add/remove from wishlist\n  };\n\n  const handleAddToCart = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    onAddToCart(product);\n  };\n\n  const cardClasses = size === 'small'\n    ? \"relative bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer group h-full flex flex-col transform hover:-translate-y-2\"\n    : \"relative bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer group h-full flex flex-col transform hover:-translate-y-2\";\n\n  const imageHeight = size === 'small' ? 'h-40' : 'h-64';\n\n  return (\n    <div\n      className={cardClasses}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      {/* Wishlist Button */}\n      <button\n        onClick={handleWishlistToggle}\n        className={`absolute top-4 right-4 z-10 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${\n          isWishlisted\n            ? 'bg-red-500 text-white shadow-lg'\n            : 'bg-white/80 backdrop-blur-sm text-gray-600 hover:bg-red-500 hover:text-white'\n        } ${isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-75'}`}\n      >\n        <svg className=\"w-5 h-5\" fill={isWishlisted ? 'currentColor' : 'none'} stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n        </svg>\n      </button>\n\n      <Link to={`/product/${product.id}`} className=\"block h-full flex flex-col\">\n        {/* Product Image with Gradient Overlay */}\n        <div className={`relative overflow-hidden ${imageHeight}`}>\n          <LazyLoadImage\n            src={getProductImageUrl(product)}\n            alt={product.name || 'Product Image'}\n            effect=\"blur\"\n            className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700\"\n            placeholder={\n              <div className={`w-full ${imageHeight} bg-gradient-to-br from-gray-100 to-gray-200 animate-pulse flex items-center justify-center`}>\n                <div className=\"text-gray-400 text-sm\">Loading...</div>\n              </div>\n            }\n            threshold={300}\n            onError={(e) => handleImageError(e)}\n          />\n\n          {/* Gradient Overlay */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n          {/* Floating Badges */}\n          <div className=\"absolute top-4 left-4 flex flex-col space-y-2\">\n            {product.sustainabilityScore >= 80 && (\n              <div className=\"bg-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center space-x-1 shadow-lg\">\n                <span>🌱</span>\n                <span>Eco-Friendly</span>\n              </div>\n            )}\n            {product.original_price && product.original_price > product.price && (\n              <div className=\"bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\">\n                {calculateDiscountPercentage(product.original_price, product.price)}% OFF\n              </div>\n            )}\n          </div>\n\n          {/* Stock Status */}\n          {product.stockQuantity <= 5 && product.stockQuantity > 0 && (\n            <div className=\"absolute bottom-4 left-4 bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse\">\n              Only {product.stockQuantity} left!\n            </div>\n          )}\n\n          {/* Quick Action Buttons */}\n          <div className={`absolute bottom-4 right-4 flex space-x-2 transition-all duration-300 ${\n            isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'\n          }`}>\n            <button\n              onClick={handleAddToCart}\n              disabled={product.stockQuantity === 0}\n              className=\"bg-emerald-500 hover:bg-emerald-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4m16 0H4\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Product Info with Modern Design */}\n        <div className=\"p-6 flex-1 flex flex-col\">\n          {/* Brand & Category */}\n          <div className=\"flex items-center justify-between mb-2\">\n            {product.brand && (\n              <span className=\"text-xs font-semibold text-emerald-600 uppercase tracking-wider bg-emerald-50 px-2 py-1 rounded-full\">\n                {product.brand}\n              </span>\n            )}\n            {product.category && (\n              <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\n                {product.category}\n              </span>\n            )}\n          </div>\n\n          {/* Product Name */}\n          <h3 className={`font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors line-clamp-2 ${\n            size === 'small' ? 'text-sm' : 'text-lg'\n          }`}>\n            {product.name}\n          </h3>\n\n          {/* Rating */}\n          {(product.averageRating || product.average_rating) > 0 && (\n            <div className=\"flex items-center space-x-2 mb-3\">\n              <div className=\"flex text-sm\">\n                {renderStars(product.averageRating || product.average_rating)}\n              </div>\n              <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\n                {product.totalReviews || product.total_reviews || 0} reviews\n              </span>\n            </div>\n          )}\n\n          {/* Price Section with Modern Design */}\n          <div className=\"mt-auto\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <span className={`font-bold text-emerald-600 ${size === 'small' ? 'text-xl' : 'text-2xl'}`}>\n                {formatPrice(product.price)}\n              </span>\n              {product.original_price && product.original_price > product.price && (\n                <span className=\"text-lg text-gray-400 line-through\">\n                  {formatPrice(product.original_price)}\n                </span>\n              )}\n            </div>\n\n            {/* Material/Features */}\n            {product.material && (\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <span className=\"text-xs text-gray-600 bg-gray-100 px-3 py-1 rounded-full\">\n                  {product.material}\n                </span>\n              </div>\n            )}\n\n            {/* Add to Cart Button - Always Visible */}\n            <button\n              onClick={handleAddToCart}\n              disabled={product.stockQuantity === 0}\n              className={`w-full py-3 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${\n                product.stockQuantity === 0\n                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'\n                  : 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'\n              }`}\n            >\n              {product.stockQuantity === 0 ? (\n                <span>Out of Stock</span>\n              ) : (\n                <>\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4m16 0H4\" />\n                  </svg>\n                  <span>Add to Cart</span>\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </Link>\n    </div>\n  );\n};\n\n// Unique Floating Category Cards\nconst FloatingCategorySection = ({ categories, onCategoryClick }) => {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-emerald-500 rounded-full blur-3xl\" />\n        <div className=\"absolute bottom-20 right-20 w-40 h-40 bg-blue-500 rounded-full blur-3xl\" />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 relative z-10\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Explore Our\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-emerald-500 to-blue-500\"> Universe</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Discover sustainable products across different categories, each carefully curated for quality and environmental impact.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {categories.map((category, index) => (\n            <div\n              key={index}\n              onClick={() => onCategoryClick(category)}\n              className=\"group cursor-pointer transform hover:scale-105 transition-all duration-500\"\n              style={{ animationDelay: `${index * 100}ms` }}\n            >\n              <div className=\"relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 p-8\">\n                {/* Gradient Background */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-50 via-white to-blue-50 opacity-50\" />\n\n                {/* Content */}\n                <div className=\"relative z-10 text-center\">\n                  <div className=\"w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300\">\n                    <img\n                      src={category.image}\n                      alt={category.name}\n                      className=\"w-12 h-12 object-cover rounded-lg filter brightness-0 invert\"\n                    />\n                  </div>\n\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors\">\n                    {category.name}\n                  </h3>\n\n                  <p className=\"text-gray-600 text-sm leading-relaxed\">\n                    {category.description || \"Sustainable and eco-friendly options\"}\n                  </p>\n\n                  {/* Hover Arrow */}\n                  <div className=\"mt-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0\">\n                    <div className=\"inline-flex items-center text-emerald-600 font-semibold\">\n                      <span>Explore</span>\n                      <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Modern Product Showcase Section\nconst ModernProductShowcase = ({ title, products, onAddToCart, theme = 'emerald' }) => {\n  const themeClasses = {\n    emerald: 'from-emerald-500 to-emerald-600',\n    blue: 'from-blue-500 to-blue-600',\n    purple: 'from-purple-500 to-purple-600',\n    pink: 'from-pink-500 to-pink-600'\n  };\n\n  return (\n    <section className=\"py-20 bg-white relative overflow-hidden\">\n      {/* Decorative Elements */}\n      <div className=\"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-500\" />\n\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            {title}\n          </h2>\n          <div className=\"w-24 h-1 bg-gradient-to-r from-emerald-500 to-blue-500 mx-auto rounded-full\" />\n        </div>\n\n        {/* Products Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n          {products.slice(0, 8).map((product, index) => (\n            <div\n              key={product.id}\n              className=\"animate-fade-in-up\"\n              style={{ animationDelay: `${index * 100}ms` }}\n            >\n              <ProductCard\n                product={product}\n                onAddToCart={onAddToCart}\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <div className=\"text-center mt-16\">\n          <Link\n            to=\"/search\"\n            className={`inline-flex items-center px-8 py-4 bg-gradient-to-r ${themeClasses[theme]} text-white font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300`}\n          >\n            <span>View All Products</span>\n            <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Unique Modern Hero Section\nconst ModernHero = () => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  const heroSlides = [\n    {\n      id: 1,\n      title: \"Sustainable Living\",\n      subtitle: \"Made Simple\",\n      description: \"Discover eco-friendly products that make a difference for you and the planet\",\n      image: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop\",\n      cta: \"Shop Sustainable\",\n      link: \"/search?sustainable=true\",\n      accent: \"emerald\"\n    },\n    {\n      id: 2,\n      title: \"Green Technology\",\n      subtitle: \"For Tomorrow\",\n      description: \"Innovative eco-tech solutions that reduce your carbon footprint\",\n      image: \"https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1200&h=600&fit=crop\",\n      cta: \"Explore Tech\",\n      link: \"/category/electronics\",\n      accent: \"blue\"\n    },\n    {\n      id: 3,\n      title: \"Natural Beauty\",\n      subtitle: \"Pure & Clean\",\n      description: \"Organic beauty products that nurture your skin and the environment\",\n      image: \"https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=1200&h=600&fit=crop\",\n      cta: \"Discover Beauty\",\n      link: \"/category/beauty\",\n      accent: \"pink\"\n    }\n  ];\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);\n    }, 6000);\n    return () => clearInterval(timer);\n  }, [heroSlides.length]);\n\n  const currentHero = heroSlides[currentSlide];\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black\">\n      {/* Background Image with Parallax Effect */}\n      <div className=\"absolute inset-0\">\n        {heroSlides.map((slide, index) => (\n          <div\n            key={slide.id}\n            className={`absolute inset-0 transition-opacity duration-1000 ${\n              index === currentSlide ? 'opacity-100' : 'opacity-0'\n            }`}\n          >\n            <img\n              src={slide.image}\n              alt={slide.title}\n              className=\"w-full h-full object-cover scale-110 animate-slow-zoom\"\n            />\n            <div className=\"absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent\" />\n          </div>\n        ))}\n      </div>\n\n      {/* Floating Elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-emerald-500/10 rounded-full blur-xl animate-float\" />\n        <div className=\"absolute top-40 right-20 w-24 h-24 bg-blue-500/10 rounded-full blur-xl animate-float-delayed\" />\n        <div className=\"absolute bottom-32 left-1/4 w-40 h-40 bg-pink-500/10 rounded-full blur-xl animate-float\" />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Animated Title */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-6xl md:text-8xl font-bold text-white mb-4 leading-tight\">\n              <span className=\"block animate-slide-up\">{currentHero.title}</span>\n              <span className={`block text-transparent bg-clip-text bg-gradient-to-r ${\n                currentHero.accent === 'emerald' ? 'from-emerald-400 to-emerald-600' :\n                currentHero.accent === 'blue' ? 'from-blue-400 to-blue-600' :\n                'from-pink-400 to-pink-600'\n              } animate-slide-up-delayed`}>\n                {currentHero.subtitle}\n              </span>\n            </h1>\n          </div>\n\n          {/* Description */}\n          <p className=\"text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed animate-fade-in-up\">\n            {currentHero.description}\n          </p>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up-delayed\">\n            <Link\n              to={currentHero.link}\n              className={`group relative px-8 py-4 bg-gradient-to-r ${\n                currentHero.accent === 'emerald' ? 'from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700' :\n                currentHero.accent === 'blue' ? 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' :\n                'from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700'\n              } text-white font-semibold rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300`}\n            >\n              <span className=\"relative z-10 flex items-center space-x-2\">\n                <span>{currentHero.cta}</span>\n                <svg className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                </svg>\n              </span>\n            </Link>\n\n            <Link\n              to=\"/categories\"\n              className=\"group px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-full hover:bg-white/10 hover:border-white/50 transition-all duration-300 backdrop-blur-sm\"\n            >\n              <span className=\"flex items-center space-x-2\">\n                <span>Browse All</span>\n                <svg className=\"w-5 h-5 group-hover:rotate-45 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </span>\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Slide Indicators */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3\">\n        {heroSlides.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentSlide(index)}\n            className={`w-3 h-3 rounded-full transition-all duration-300 ${\n              index === currentSlide\n                ? 'bg-white scale-125'\n                : 'bg-white/40 hover:bg-white/60'\n            }`}\n          />\n        ))}\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 right-8 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse\" />\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Main Home Component\nconst SimpleHome = () => {\n  const { addToCart } = useCart();\n  const { success } = useNotification();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Modern unique categories\n  const uniqueCategories = [\n    {\n      name: 'Eco Fashion',\n      image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200&h=150&fit=crop',\n      link: '/search?category=clothing',\n      description: 'Sustainable clothing that looks good and feels great'\n    },\n    {\n      name: 'Green Home',\n      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=150&fit=crop',\n      link: '/search?category=home-garden',\n      description: 'Transform your space with eco-friendly home essentials'\n    },\n    {\n      name: 'Natural Beauty',\n      image: 'https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=200&h=150&fit=crop',\n      link: '/search?category=beauty',\n      description: 'Pure, organic beauty products for radiant skin'\n    },\n    {\n      name: 'Smart Tech',\n      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=150&fit=crop',\n      link: '/search?category=electronics',\n      description: 'Innovative technology that respects the environment'\n    },\n    {\n      name: 'Wellness',\n      image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=200&h=150&fit=crop',\n      link: '/search?category=wellness',\n      description: 'Holistic products for mind, body, and soul'\n    },\n    {\n      name: 'Outdoor Life',\n      image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=200&h=150&fit=crop',\n      link: '/search?category=outdoor',\n      description: 'Gear for sustainable outdoor adventures'\n    },\n    {\n      name: 'Zero Waste',\n      image: 'https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?w=200&h=150&fit=crop',\n      link: '/search?category=zero-waste',\n      description: 'Products that help you live waste-free'\n    },\n    {\n      name: 'Kids & Baby',\n      image: 'https://images.unsplash.com/photo-1515488042361-ee00e0ddd4e4?w=200&h=150&fit=crop',\n      link: '/search?category=kids',\n      description: 'Safe, sustainable products for little ones'\n    }\n  ];\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/products?limit=20`);\n        if (!response.ok) {\n          throw new Error('Failed to fetch products');\n        }\n        const data = await response.json();\n        setProducts(data.products || data);\n        setLoading(false);\n      } catch (err) {\n        setError(err.message);\n        setLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  const handleAddToCart = async (product) => {\n    try {\n      await addToCart(product.id, 1);\n      success(`${product.name} added to cart!`);\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      error('Failed to add item to cart');\n    }\n  };\n\n  const handleCategoryClick = (category) => {\n    window.location.href = category.link;\n  };\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Oops! Something went wrong</h2>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors duration-200\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-64 bg-gray-200 rounded-lg mb-8\"></div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[...Array(6)].map((_, index) => (\n                <div key={index} className=\"bg-white rounded-lg border border-gray-200 p-6\">\n                  <div className=\"h-6 bg-gray-200 rounded mb-4\"></div>\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    {[...Array(4)].map((_, i) => (\n                      <div key={i}>\n                        <div className=\"h-24 bg-gray-200 rounded mb-2\"></div>\n                        <div className=\"h-4 bg-gray-200 rounded\"></div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Modern Unique Hero Section */}\n      <ModernHero />\n\n      {/* Floating Category Section */}\n      <FloatingCategorySection\n        categories={uniqueCategories}\n        onCategoryClick={handleCategoryClick}\n      />\n\n      {/* Featured Products Showcase */}\n      <ModernProductShowcase\n        title=\"Featured Sustainable Products\"\n        products={products.slice(0, 8)}\n        onAddToCart={handleAddToCart}\n        theme=\"emerald\"\n      />\n\n      {/* Trending Products */}\n      <section className=\"py-20 bg-gradient-to-br from-emerald-50 to-blue-50\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Trending Now\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover what's popular in sustainable living\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {products.slice(8, 14).map((product, index) => (\n              <div\n                key={product.id}\n                className=\"animate-fade-in-up\"\n                style={{ animationDelay: `${index * 150}ms` }}\n              >\n                <ProductCard\n                  product={product}\n                  onAddToCart={handleAddToCart}\n                />\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Best Sellers with Unique Layout */}\n      <section className=\"py-20 bg-white relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-blue-500/5\" />\n\n        <div className=\"max-w-7xl mx-auto px-4 relative z-10\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Customer Favorites\n            </h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-emerald-500 to-blue-500 mx-auto rounded-full mb-6\" />\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Products our customers can't stop talking about\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {products.slice(14, 18).map((product, index) => (\n              <div\n                key={product.id}\n                className=\"transform hover:scale-105 transition-all duration-500\"\n                style={{ animationDelay: `${index * 100}ms` }}\n              >\n                <ProductCard\n                  product={product}\n                  onAddToCart={handleAddToCart}\n                />\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n\n\n      {/* Sustainability Impact Section */}\n      <section className=\"py-12 bg-emerald-50\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"text-center mb-8\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Your Impact Matters</h2>\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n              Every sustainable choice you make contributes to a better planet. See the collective impact of our community.\n            </p>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center bg-white rounded-xl p-6 shadow-sm\">\n              <div className=\"text-4xl font-bold text-emerald-600 mb-2\">2.5M+</div>\n              <div className=\"text-gray-600\">CO₂ Saved (kg)</div>\n            </div>\n            <div className=\"text-center bg-white rounded-xl p-6 shadow-sm\">\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">1.8M+</div>\n              <div className=\"text-gray-600\">Water Conserved (L)</div>\n            </div>\n            <div className=\"text-center bg-white rounded-xl p-6 shadow-sm\">\n              <div className=\"text-4xl font-bold text-orange-600 mb-2\">950K+</div>\n              <div className=\"text-gray-600\">Waste Diverted (kg)</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Customer Reviews Section */}\n      <section className=\"py-8 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">What Our Customers Say</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {[\n              {\n                name: \"Priya Sharma\",\n                rating: 5,\n                comment: \"Amazing quality sustainable products! The bamboo t-shirts are so comfortable and eco-friendly.\",\n                product: \"Eco-Friendly Bamboo T-Shirt\"\n              },\n              {\n                name: \"Rahul Kumar\",\n                rating: 5,\n                comment: \"Fast delivery and excellent customer service. Love supporting sustainable brands through Allora.\",\n                product: \"Organic Cotton Slim Jeans\"\n              },\n              {\n                name: \"Anita Patel\",\n                rating: 4,\n                comment: \"Great variety of eco-friendly products. The packaging is also completely plastic-free!\",\n                product: \"Recycled Canvas Backpack\"\n              }\n            ].map((review, index) => (\n              <div key={index} className=\"bg-gray-50 rounded-lg p-6\">\n                <div className=\"flex items-center mb-3\">\n                  <div className=\"flex text-yellow-400 mr-2\">\n                    {[...Array(review.rating)].map((_, i) => (\n                      <span key={i}>★</span>\n                    ))}\n                  </div>\n                  <span className=\"text-sm text-gray-600\">{review.rating}/5</span>\n                </div>\n                <p className=\"text-gray-700 mb-3\">\"{review.comment}\"</p>\n                <div className=\"text-sm\">\n                  <div className=\"font-medium text-gray-900\">{review.name}</div>\n                  <div className=\"text-gray-500\">Verified purchase: {review.product}</div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Newsletter Section */}\n      <section className=\"py-12 bg-emerald-600\">\n        <div className=\"max-w-4xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-4\">Stay Updated with Sustainable Living</h2>\n          <p className=\"text-emerald-100 mb-8 text-lg\">\n            Get the latest eco-friendly products, sustainability tips, and exclusive deals delivered to your inbox.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              className=\"flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-emerald-300 focus:outline-none\"\n            />\n            <button className=\"bg-white text-emerald-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\">\n              Subscribe\n            </button>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default SimpleHome;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,qBAAqB;AACzE,SAASC,WAAW,EAAEC,2BAA2B,QAAQ,mBAAmB;;AAE5E;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,UAAU,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EAClDD,UAAU,CAACE,WAAW,GAAGJ,YAAY;EACrCC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,UAAU,CAAC;AACvC;;AAEA;AACA,MAAMK,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,WAAW;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAMgC,WAAW,GAAIC,MAAM,IAAK;IAC9B,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC;IACpC,MAAMK,WAAW,GAAGL,MAAM,GAAG,CAAC,KAAK,CAAC;IAEpC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,EAAEI,CAAC,EAAE,EAAE;MAClCL,KAAK,CAACM,IAAI,cAAC3B,OAAA;QAAc4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAC,GAAhCH,CAAC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqC,CAAC,CAAC;IAChE;IACA,IAAIR,WAAW,EAAE;MACfJ,KAAK,CAACM,IAAI,cAAC3B,OAAA;QAAiB4B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAC,GAApC,MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoC,CAAC,CAAC;IACnE;IACA,KAAK,IAAIP,CAAC,GAAGL,KAAK,CAACa,MAAM,EAAER,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACrCL,KAAK,CAACM,IAAI,cAAC3B,OAAA;QAAc4B,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAC,GAA9BH,CAAC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAmC,CAAC,CAAC;IAC9D;IACA,OAAOZ,KAAK;EACd,CAAC;EAED,MAAMc,oBAAoB,GAAIC,CAAC,IAAK;IAClCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBtB,eAAe,CAAC,CAACD,YAAY,CAAC;IAC9B;EACF,CAAC;EAED,MAAMwB,eAAe,GAAIH,CAAC,IAAK;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB1B,WAAW,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAM6B,WAAW,GAAG3B,IAAI,KAAK,OAAO,GAChC,wLAAwL,GACxL,wLAAwL;EAE5L,MAAM4B,WAAW,GAAG5B,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM;EAEtD,oBACEb,OAAA;IACE4B,SAAS,EAAEY,WAAY;IACvBE,YAAY,EAAEA,CAAA,KAAMxB,YAAY,CAAC,IAAI,CAAE;IACvCyB,YAAY,EAAEA,CAAA,KAAMzB,YAAY,CAAC,KAAK,CAAE;IAAAW,QAAA,gBAGxC7B,OAAA;MACE4C,OAAO,EAAET,oBAAqB;MAC9BP,SAAS,EAAE,mHACTb,YAAY,GACR,iCAAiC,GACjC,8EAA8E,IAChFE,SAAS,GAAG,uBAAuB,GAAG,oBAAoB,EAAG;MAAAY,QAAA,eAEjE7B,OAAA;QAAK4B,SAAS,EAAC,SAAS;QAACiB,IAAI,EAAE9B,YAAY,GAAG,cAAc,GAAG,MAAO;QAAC+B,MAAM,EAAC,cAAc;QAACC,OAAO,EAAC,WAAW;QAAAlB,QAAA,eAC9G7B,OAAA;UAAMgD,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,WAAW,EAAE,CAAE;UAACC,CAAC,EAAC;QAA6H;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETjC,OAAA,CAACR,IAAI;MAAC4D,EAAE,EAAE,YAAYzC,OAAO,CAAC0C,EAAE,EAAG;MAACzB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBAExE7B,OAAA;QAAK4B,SAAS,EAAE,4BAA4Ba,WAAW,EAAG;QAAAZ,QAAA,gBACxD7B,OAAA,CAACsD,aAAa;UACZC,GAAG,EAAEC,kBAAkB,CAAC7C,OAAO,CAAE;UACjC8C,GAAG,EAAE9C,OAAO,CAAC+C,IAAI,IAAI,eAAgB;UACrCC,MAAM,EAAC,MAAM;UACb/B,SAAS,EAAC,oFAAoF;UAC9FgC,WAAW,eACT5D,OAAA;YAAK4B,SAAS,EAAE,UAAUa,WAAW,6FAA8F;YAAAZ,QAAA,eACjI7B,OAAA;cAAK4B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CACN;UACD4B,SAAS,EAAE,GAAI;UACfC,OAAO,EAAG1B,CAAC,IAAK2B,gBAAgB,CAAC3B,CAAC;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAGFjC,OAAA;UAAK4B,SAAS,EAAC;QAAkJ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGpKjC,OAAA;UAAK4B,SAAS,EAAC,+CAA+C;UAAAC,QAAA,GAC3DlB,OAAO,CAACqD,mBAAmB,IAAI,EAAE,iBAChChE,OAAA;YAAK4B,SAAS,EAAC,0GAA0G;YAAAC,QAAA,gBACvH7B,OAAA;cAAA6B,QAAA,EAAM;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACfjC,OAAA;cAAA6B,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN,EACAtB,OAAO,CAACsD,cAAc,IAAItD,OAAO,CAACsD,cAAc,GAAGtD,OAAO,CAACuD,KAAK,iBAC/DlE,OAAA;YAAK4B,SAAS,EAAC,yGAAyG;YAAAC,QAAA,GACrH/B,2BAA2B,CAACa,OAAO,CAACsD,cAAc,EAAEtD,OAAO,CAACuD,KAAK,CAAC,EAAC,OACtE;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLtB,OAAO,CAACwD,aAAa,IAAI,CAAC,IAAIxD,OAAO,CAACwD,aAAa,GAAG,CAAC,iBACtDnE,OAAA;UAAK4B,SAAS,EAAC,oHAAoH;UAAAC,QAAA,GAAC,OAC7H,EAAClB,OAAO,CAACwD,aAAa,EAAC,QAC9B;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAGDjC,OAAA;UAAK4B,SAAS,EAAE,wEACdX,SAAS,GAAG,2BAA2B,GAAG,yBAAyB,EAClE;UAAAY,QAAA,eACD7B,OAAA;YACE4C,OAAO,EAAEL,eAAgB;YACzB6B,QAAQ,EAAEzD,OAAO,CAACwD,aAAa,KAAK,CAAE;YACtCvC,SAAS,EAAC,wKAAwK;YAAAC,QAAA,eAElL7B,OAAA;cAAK4B,SAAS,EAAC,SAAS;cAACiB,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAlB,QAAA,eAC5E7B,OAAA;gBAAMgD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAgJ;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA;QAAK4B,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBAEvC7B,OAAA;UAAK4B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GACpDlB,OAAO,CAAC0D,KAAK,iBACZrE,OAAA;YAAM4B,SAAS,EAAC,sGAAsG;YAAAC,QAAA,EACnHlB,OAAO,CAAC0D;UAAK;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACP,EACAtB,OAAO,CAAC2D,QAAQ,iBACftE,OAAA;YAAM4B,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACvElB,OAAO,CAAC2D;UAAQ;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjC,OAAA;UAAI4B,SAAS,EAAE,4FACbf,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS,EACvC;UAAAgB,QAAA,EACAlB,OAAO,CAAC+C;QAAI;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGJ,CAACtB,OAAO,CAAC4D,aAAa,IAAI5D,OAAO,CAAC6D,cAAc,IAAI,CAAC,iBACpDxE,OAAA;UAAK4B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C7B,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BV,WAAW,CAACR,OAAO,CAAC4D,aAAa,IAAI5D,OAAO,CAAC6D,cAAc;UAAC;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNjC,OAAA;YAAM4B,SAAS,EAAC,0DAA0D;YAAAC,QAAA,GACvElB,OAAO,CAAC8D,YAAY,IAAI9D,OAAO,CAAC+D,aAAa,IAAI,CAAC,EAAC,UACtD;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGDjC,OAAA;UAAK4B,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtB7B,OAAA;YAAK4B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C7B,OAAA;cAAM4B,SAAS,EAAE,8BAA8Bf,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,UAAU,EAAG;cAAAgB,QAAA,EACxFhC,WAAW,CAACc,OAAO,CAACuD,KAAK;YAAC;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACNtB,OAAO,CAACsD,cAAc,IAAItD,OAAO,CAACsD,cAAc,GAAGtD,OAAO,CAACuD,KAAK,iBAC/DlE,OAAA;cAAM4B,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EACjDhC,WAAW,CAACc,OAAO,CAACsD,cAAc;YAAC;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLtB,OAAO,CAACgE,QAAQ,iBACf3E,OAAA;YAAK4B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,eAC/C7B,OAAA;cAAM4B,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACvElB,OAAO,CAACgE;YAAQ;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,eAGDjC,OAAA;YACE4C,OAAO,EAAEL,eAAgB;YACzB6B,QAAQ,EAAEzD,OAAO,CAACwD,aAAa,KAAK,CAAE;YACtCvC,SAAS,EAAE,oHACTjB,OAAO,CAACwD,aAAa,KAAK,CAAC,GACvB,8CAA8C,GAC9C,6JAA6J,EAChK;YAAAtC,QAAA,EAEFlB,OAAO,CAACwD,aAAa,KAAK,CAAC,gBAC1BnE,OAAA;cAAA6B,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEzBjC,OAAA,CAAAE,SAAA;cAAA2B,QAAA,gBACE7B,OAAA;gBAAK4B,SAAS,EAAC,SAAS;gBAACiB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eAC5E7B,OAAA;kBAAMgD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAgJ;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrN,CAAC,eACNjC,OAAA;gBAAA6B,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACxB;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;;AAED;AAAAnB,EAAA,CA5MMJ,WAAW;AAAAkE,EAAA,GAAXlE,WAAW;AA6MjB,MAAMmE,uBAAuB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAgB,CAAC,KAAK;EACnE,oBACE/E,OAAA;IAAS4B,SAAS,EAAC,wEAAwE;IAAAC,QAAA,gBAEzF7B,OAAA;MAAK4B,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC7B,OAAA;QAAK4B,SAAS,EAAC;MAAwE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1FjC,OAAA;QAAK4B,SAAS,EAAC;MAAyE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC,eAENjC,OAAA;MAAK4B,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD7B,OAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7B,OAAA;UAAI4B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAAC,aAEhE,eAAA7B,OAAA;YAAM4B,SAAS,EAAC,6EAA6E;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC,eACLjC,OAAA;UAAG4B,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENjC,OAAA;QAAK4B,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEiD,UAAU,CAACE,GAAG,CAAC,CAACV,QAAQ,EAAEW,KAAK,kBAC9BjF,OAAA;UAEE4C,OAAO,EAAEA,CAAA,KAAMmC,eAAe,CAACT,QAAQ,CAAE;UACzC1C,SAAS,EAAC,4EAA4E;UACtFsD,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAK,CAAE;UAAApD,QAAA,eAE9C7B,OAAA;YAAK4B,SAAS,EAAC,mHAAmH;YAAAC,QAAA,gBAEhI7B,OAAA;cAAK4B,SAAS,EAAC;YAAoF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGtGjC,OAAA;cAAK4B,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC7B,OAAA;gBAAK4B,SAAS,EAAC,mLAAmL;gBAAAC,QAAA,eAChM7B,OAAA;kBACEuD,GAAG,EAAEe,QAAQ,CAACc,KAAM;kBACpB3B,GAAG,EAAEa,QAAQ,CAACZ,IAAK;kBACnB9B,SAAS,EAAC;gBAA8D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjC,OAAA;gBAAI4B,SAAS,EAAC,qFAAqF;gBAAAC,QAAA,EAChGyC,QAAQ,CAACZ;cAAI;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAELjC,OAAA;gBAAG4B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EACjDyC,QAAQ,CAACe,WAAW,IAAI;cAAsC;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eAGJjC,OAAA;gBAAK4B,SAAS,EAAC,sHAAsH;gBAAAC,QAAA,eACnI7B,OAAA;kBAAK4B,SAAS,EAAC,yDAAyD;kBAAAC,QAAA,gBACtE7B,OAAA;oBAAA6B,QAAA,EAAM;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpBjC,OAAA;oBAAK4B,SAAS,EAAC,cAAc;oBAACiB,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAlB,QAAA,eACjF7B,OAAA;sBAAMgD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA0B;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GArCDgD,KAAK;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsCP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAAqD,GAAA,GArEMT,uBAAuB;AAsE7B,MAAMU,qBAAqB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAE7E,WAAW;EAAE8E,KAAK,GAAG;AAAU,CAAC,KAAK;EACrF,MAAMC,YAAY,GAAG;IACnBC,OAAO,EAAE,iCAAiC;IAC1CC,IAAI,EAAE,2BAA2B;IACjCC,MAAM,EAAE,+BAA+B;IACvCC,IAAI,EAAE;EACR,CAAC;EAED,oBACE/F,OAAA;IAAS4B,SAAS,EAAC,yCAAyC;IAAAC,QAAA,gBAE1D7B,OAAA;MAAK4B,SAAS,EAAC;IAA+F;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjHjC,OAAA;MAAK4B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrC7B,OAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7B,OAAA;UAAI4B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAC9D2D;QAAK;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACLjC,OAAA;UAAK4B,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC,eAGNjC,OAAA;QAAK4B,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjF4D,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,GAAG,CAAC,CAACrE,OAAO,EAAEsE,KAAK,kBACvCjF,OAAA;UAEE4B,SAAS,EAAC,oBAAoB;UAC9BsD,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAK,CAAE;UAAApD,QAAA,eAE9C7B,OAAA,CAACU,WAAW;YACVC,OAAO,EAAEA,OAAQ;YACjBC,WAAW,EAAEA;UAAY;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC,GAPGtB,OAAO,CAAC0C,EAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNjC,OAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChC7B,OAAA,CAACR,IAAI;UACH4D,EAAE,EAAC,SAAS;UACZxB,SAAS,EAAE,uDAAuD+D,YAAY,CAACD,KAAK,CAAC,wHAAyH;UAAA7D,QAAA,gBAE9M7B,OAAA;YAAA6B,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9BjC,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAACiB,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAlB,QAAA,eACjF7B,OAAA;cAAMgD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA0B;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAAgE,GAAA,GAtDMV,qBAAqB;AAuD3B,MAAMW,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlH,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAMmH,UAAU,GAAG,CACjB;IACEjD,EAAE,EAAE,CAAC;IACLmC,KAAK,EAAE,oBAAoB;IAC3Be,QAAQ,EAAE,aAAa;IACvBlB,WAAW,EAAE,8EAA8E;IAC3FD,KAAK,EAAE,oFAAoF;IAC3FoB,GAAG,EAAE,kBAAkB;IACvBC,IAAI,EAAE,0BAA0B;IAChCC,MAAM,EAAE;EACV,CAAC,EACD;IACErD,EAAE,EAAE,CAAC;IACLmC,KAAK,EAAE,kBAAkB;IACzBe,QAAQ,EAAE,cAAc;IACxBlB,WAAW,EAAE,iEAAiE;IAC9ED,KAAK,EAAE,oFAAoF;IAC3FoB,GAAG,EAAE,cAAc;IACnBC,IAAI,EAAE,uBAAuB;IAC7BC,MAAM,EAAE;EACV,CAAC,EACD;IACErD,EAAE,EAAE,CAAC;IACLmC,KAAK,EAAE,gBAAgB;IACvBe,QAAQ,EAAE,cAAc;IACxBlB,WAAW,EAAE,oEAAoE;IACjFD,KAAK,EAAE,iFAAiF;IACxFoB,GAAG,EAAE,iBAAiB;IACtBC,IAAI,EAAE,kBAAkB;IACxBC,MAAM,EAAE;EACV,CAAC,CACF;EAEDtH,SAAS,CAAC,MAAM;IACd,MAAMuH,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BP,eAAe,CAAEQ,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIP,UAAU,CAACpE,MAAM,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAM4E,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACL,UAAU,CAACpE,MAAM,CAAC,CAAC;EAEvB,MAAM6E,WAAW,GAAGT,UAAU,CAACF,YAAY,CAAC;EAE5C,oBACEpG,OAAA;IAAS4B,SAAS,EAAC,8HAA8H;IAAAC,QAAA,gBAE/I7B,OAAA;MAAK4B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9ByE,UAAU,CAACtB,GAAG,CAAC,CAACgC,KAAK,EAAE/B,KAAK,kBAC3BjF,OAAA;QAEE4B,SAAS,EAAE,qDACTqD,KAAK,KAAKmB,YAAY,GAAG,aAAa,GAAG,WAAW,EACnD;QAAAvE,QAAA,gBAEH7B,OAAA;UACEuD,GAAG,EAAEyD,KAAK,CAAC5B,KAAM;UACjB3B,GAAG,EAAEuD,KAAK,CAACxB,KAAM;UACjB5D,SAAS,EAAC;QAAwD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACFjC,OAAA;UAAK4B,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,GAV1F+E,KAAK,CAAC3D,EAAE;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE7B,OAAA;QAAK4B,SAAS,EAAC;MAAwF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1GjC,OAAA;QAAK4B,SAAS,EAAC;MAA8F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChHjC,OAAA;QAAK4B,SAAS,EAAC;MAAyF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eAC/D7B,OAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhC7B,OAAA;UAAK4B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB7B,OAAA;YAAI4B,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBAC1E7B,OAAA;cAAM4B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAEkF,WAAW,CAACvB;YAAK;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnEjC,OAAA;cAAM4B,SAAS,EAAE,wDACfmF,WAAW,CAACL,MAAM,KAAK,SAAS,GAAG,iCAAiC,GACpEK,WAAW,CAACL,MAAM,KAAK,MAAM,GAAG,2BAA2B,GAC3D,2BAA2B,2BACD;cAAA7E,QAAA,EACzBkF,WAAW,CAACR;YAAQ;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNjC,OAAA;UAAG4B,SAAS,EAAC,8FAA8F;UAAAC,QAAA,EACxGkF,WAAW,CAAC1B;QAAW;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAGJjC,OAAA;UAAK4B,SAAS,EAAC,wFAAwF;UAAAC,QAAA,gBACrG7B,OAAA,CAACR,IAAI;YACH4D,EAAE,EAAE2D,WAAW,CAACN,IAAK;YACrB7E,SAAS,EAAE,6CACTmF,WAAW,CAACL,MAAM,KAAK,SAAS,GAAG,6EAA6E,GAChHK,WAAW,CAACL,MAAM,KAAK,MAAM,GAAG,iEAAiE,GACjG,iEAAiE,0HACwD;YAAA7E,QAAA,eAE3H7B,OAAA;cAAM4B,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACzD7B,OAAA;gBAAA6B,QAAA,EAAOkF,WAAW,CAACP;cAAG;gBAAA1E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BjC,OAAA;gBAAK4B,SAAS,EAAC,wDAAwD;gBAACiB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eAC3H7B,OAAA;kBAAMgD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA0B;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPjC,OAAA,CAACR,IAAI;YACH4D,EAAE,EAAC,aAAa;YAChBxB,SAAS,EAAC,qKAAqK;YAAAC,QAAA,eAE/K7B,OAAA;cAAM4B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC3C7B,OAAA;gBAAA6B,QAAA,EAAM;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvBjC,OAAA;gBAAK4B,SAAS,EAAC,oDAAoD;gBAACiB,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eACvH7B,OAAA;kBAAMgD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAyB;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,sEAAsE;MAAAC,QAAA,EAClFyE,UAAU,CAACtB,GAAG,CAAC,CAACiC,CAAC,EAAEhC,KAAK,kBACvBjF,OAAA;QAEE4C,OAAO,EAAEA,CAAA,KAAMyD,eAAe,CAACpB,KAAK,CAAE;QACtCrD,SAAS,EAAE,oDACTqD,KAAK,KAAKmB,YAAY,GAClB,oBAAoB,GACpB,+BAA+B;MAClC,GANEnB,KAAK;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eACvD7B,OAAA;QAAK4B,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjF7B,OAAA;UAAK4B,SAAS,EAAC;QAAqD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;;AAED;AAAAkE,GAAA,CAzJMD,UAAU;AAAAgB,GAAA,GAAVhB,UAAU;AA0JhB,MAAMiB,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM;IAAEC;EAAU,CAAC,GAAGhI,OAAO,CAAC,CAAC;EAC/B,MAAM;IAAEiI;EAAQ,CAAC,GAAGhI,eAAe,CAAC,CAAC;EACrC,MAAM,CAACmG,QAAQ,EAAE8B,WAAW,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqI,OAAO,EAAEC,UAAU,CAAC,GAAGtI,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuI,KAAK,EAAEC,QAAQ,CAAC,GAAGxI,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMyI,gBAAgB,GAAG,CACvB;IACElE,IAAI,EAAE,aAAa;IACnB0B,KAAK,EAAE,mFAAmF;IAC1FqB,IAAI,EAAE,2BAA2B;IACjCpB,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,IAAI,EAAE,YAAY;IAClB0B,KAAK,EAAE,mFAAmF;IAC1FqB,IAAI,EAAE,8BAA8B;IACpCpB,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,IAAI,EAAE,gBAAgB;IACtB0B,KAAK,EAAE,gFAAgF;IACvFqB,IAAI,EAAE,yBAAyB;IAC/BpB,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,IAAI,EAAE,YAAY;IAClB0B,KAAK,EAAE,mFAAmF;IAC1FqB,IAAI,EAAE,8BAA8B;IACpCpB,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,IAAI,EAAE,UAAU;IAChB0B,KAAK,EAAE,gFAAgF;IACvFqB,IAAI,EAAE,2BAA2B;IACjCpB,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,IAAI,EAAE,cAAc;IACpB0B,KAAK,EAAE,gFAAgF;IACvFqB,IAAI,EAAE,0BAA0B;IAChCpB,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,IAAI,EAAE,YAAY;IAClB0B,KAAK,EAAE,mFAAmF;IAC1FqB,IAAI,EAAE,6BAA6B;IACnCpB,WAAW,EAAE;EACf,CAAC,EACD;IACE3B,IAAI,EAAE,aAAa;IACnB0B,KAAK,EAAE,mFAAmF;IAC1FqB,IAAI,EAAE,uBAAuB;IAC7BpB,WAAW,EAAE;EACf,CAAC,CACF;EAEDjG,SAAS,CAAC,MAAM;IACd,MAAMyI,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGtI,YAAY,oBAAoB,CAAC;QACjE,IAAI,CAACqI,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;QAC7C;QACA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCZ,WAAW,CAACW,IAAI,CAACzC,QAAQ,IAAIyC,IAAI,CAAC;QAClCT,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOW,GAAG,EAAE;QACZT,QAAQ,CAACS,GAAG,CAACC,OAAO,CAAC;QACrBZ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDI,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMtF,eAAe,GAAG,MAAO5B,OAAO,IAAK;IACzC,IAAI;MACF,MAAM0G,SAAS,CAAC1G,OAAO,CAAC0C,EAAE,EAAE,CAAC,CAAC;MAC9BiE,OAAO,CAAC,GAAG3G,OAAO,CAAC+C,IAAI,iBAAiB,CAAC;IAC3C,CAAC,CAAC,OAAOgE,KAAK,EAAE;MACdY,OAAO,CAACZ,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CA,KAAK,CAAC,4BAA4B,CAAC;IACrC;EACF,CAAC;EAED,MAAMa,mBAAmB,GAAIjE,QAAQ,IAAK;IACxCkE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGpE,QAAQ,CAACmC,IAAI;EACtC,CAAC;EAED,IAAIiB,KAAK,EAAE;IACT,oBACE1H,OAAA;MAAK4B,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D7B,OAAA;QAAK4B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7B,OAAA;UAAI4B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrFjC,OAAA;UAAG4B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAE6F;QAAK;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CjC,OAAA;UACE4C,OAAO,EAAEA,CAAA,KAAM4F,MAAM,CAACC,QAAQ,CAACE,MAAM,CAAC,CAAE;UACxC/G,SAAS,EAAC,oGAAoG;UAAAC,QAAA,EAC/G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIuF,OAAO,EAAE;IACX,oBACExH,OAAA;MAAK4B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtC7B,OAAA;QAAK4B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1C7B,OAAA;UAAK4B,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7B,OAAA;YAAK4B,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDjC,OAAA;YAAK4B,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAClE,CAAC,GAAG+G,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC5D,GAAG,CAAC,CAACiC,CAAC,EAAEhC,KAAK,kBAC1BjF,OAAA;cAAiB4B,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBACzE7B,OAAA;gBAAK4B,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDjC,OAAA;gBAAK4B,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpC,CAAC,GAAG+G,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC5D,GAAG,CAAC,CAACiC,CAAC,EAAEvF,CAAC,kBACtB1B,OAAA;kBAAA6B,QAAA,gBACE7B,OAAA;oBAAK4B,SAAS,EAAC;kBAA+B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrDjC,OAAA;oBAAK4B,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAFvCP,CAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGN,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GATEgD,KAAK;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjC,OAAA;IAAK4B,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpC7B,OAAA,CAACkG,UAAU;MAAApE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGdjC,OAAA,CAAC6E,uBAAuB;MACtBC,UAAU,EAAE8C,gBAAiB;MAC7B7C,eAAe,EAAEwD;IAAoB;MAAAzG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGFjC,OAAA,CAACuF,qBAAqB;MACpBC,KAAK,EAAC,+BAA+B;MACrCC,QAAQ,EAAEA,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;MAC/BpF,WAAW,EAAE2B,eAAgB;MAC7BmD,KAAK,EAAC;IAAS;MAAA5D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAGFjC,OAAA;MAAS4B,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACrE7B,OAAA;QAAK4B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC7B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7B,OAAA;YAAI4B,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLjC,OAAA;YAAG4B,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENjC,OAAA;UAAK4B,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClE4D,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAChB,GAAG,CAAC,CAACrE,OAAO,EAAEsE,KAAK,kBACxCjF,OAAA;YAEE4B,SAAS,EAAC,oBAAoB;YAC9BsD,KAAK,EAAE;cAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;YAAK,CAAE;YAAApD,QAAA,eAE9C7B,OAAA,CAACU,WAAW;cACVC,OAAO,EAAEA,OAAQ;cACjBC,WAAW,EAAE2B;YAAgB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC,GAPGtB,OAAO,CAAC0C,EAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjC,OAAA;MAAS4B,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBAC1D7B,OAAA;QAAK4B,SAAS,EAAC;MAAoF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEtGjC,OAAA;QAAK4B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD7B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7B,OAAA;YAAI4B,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLjC,OAAA;YAAK4B,SAAS,EAAC;UAAkF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpGjC,OAAA;YAAG4B,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENjC,OAAA;UAAK4B,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClE4D,QAAQ,CAACO,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAChB,GAAG,CAAC,CAACrE,OAAO,EAAEsE,KAAK,kBACzCjF,OAAA;YAEE4B,SAAS,EAAC,uDAAuD;YACjEsD,KAAK,EAAE;cAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;YAAK,CAAE;YAAApD,QAAA,eAE9C7B,OAAA,CAACU,WAAW;cACVC,OAAO,EAAEA,OAAQ;cACjBC,WAAW,EAAE2B;YAAgB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC,GAPGtB,OAAO,CAAC0C,EAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAKVjC,OAAA;MAAS4B,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtC7B,OAAA;QAAK4B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC7B,OAAA;UAAK4B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B7B,OAAA;YAAI4B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EjC,OAAA;YAAG4B,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD7B,OAAA;YAAK4B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5D7B,OAAA;cAAK4B,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrEjC,OAAA;cAAK4B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5D7B,OAAA;cAAK4B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClEjC,OAAA;cAAK4B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5D7B,OAAA;cAAK4B,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpEjC,OAAA;cAAK4B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjC,OAAA;MAAS4B,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChC7B,OAAA;QAAK4B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC7B,OAAA;UAAI4B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFjC,OAAA;UAAK4B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD,CACC;YACE6B,IAAI,EAAE,cAAc;YACpBtC,MAAM,EAAE,CAAC;YACTyH,OAAO,EAAE,gGAAgG;YACzGlI,OAAO,EAAE;UACX,CAAC,EACD;YACE+C,IAAI,EAAE,aAAa;YACnBtC,MAAM,EAAE,CAAC;YACTyH,OAAO,EAAE,kGAAkG;YAC3GlI,OAAO,EAAE;UACX,CAAC,EACD;YACE+C,IAAI,EAAE,aAAa;YACnBtC,MAAM,EAAE,CAAC;YACTyH,OAAO,EAAE,wFAAwF;YACjGlI,OAAO,EAAE;UACX,CAAC,CACF,CAACqE,GAAG,CAAC,CAAC8D,MAAM,EAAE7D,KAAK,kBAClBjF,OAAA;YAAiB4B,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACpD7B,OAAA;cAAK4B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC7B,OAAA;gBAAK4B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvC,CAAC,GAAG+G,KAAK,CAACE,MAAM,CAAC1H,MAAM,CAAC,CAAC,CAAC4D,GAAG,CAAC,CAACiC,CAAC,EAAEvF,CAAC,kBAClC1B,OAAA;kBAAA6B,QAAA,EAAc;gBAAC,GAAJH,CAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjC,OAAA;gBAAM4B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAEiH,MAAM,CAAC1H,MAAM,EAAC,IAAE;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNjC,OAAA;cAAG4B,SAAS,EAAC,oBAAoB;cAAAC,QAAA,GAAC,IAAC,EAACiH,MAAM,CAACD,OAAO,EAAC,IAAC;YAAA;cAAA/G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDjC,OAAA;cAAK4B,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACtB7B,OAAA;gBAAK4B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEiH,MAAM,CAACpF;cAAI;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9DjC,OAAA;gBAAK4B,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,qBAAmB,EAACiH,MAAM,CAACnI,OAAO;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA,GAbEgD,KAAK;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjC,OAAA;MAAS4B,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACvC7B,OAAA;QAAK4B,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjD7B,OAAA;UAAI4B,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5FjC,OAAA;UAAG4B,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjC,OAAA;UAAK4B,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/D7B,OAAA;YACE+I,IAAI,EAAC,OAAO;YACZnF,WAAW,EAAC,kBAAkB;YAC9BhC,SAAS,EAAC;UAA6F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eACFjC,OAAA;YAAQ4B,SAAS,EAAC,kGAAkG;YAAAC,QAAA,EAAC;UAErH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACmF,GAAA,CAtTID,UAAU;EAAA,QACQ9H,OAAO,EACTC,eAAe;AAAA;AAAA0J,GAAA,GAF/B7B,UAAU;AAwThB,eAAeA,UAAU;AAAC,IAAAvC,EAAA,EAAAU,GAAA,EAAAW,GAAA,EAAAiB,GAAA,EAAA8B,GAAA;AAAAC,YAAA,CAAArE,EAAA;AAAAqE,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}