{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AdminLayout from '../components/AdminLayout';\nimport './AdminDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _statistics$total_rev, _fulfillmentData$fulf, _fulfillmentData$fulf2, _fulfillmentData$fulf3, _fulfillmentData$trac;\n  const [dashboardData, setDashboardData] = useState(null);\n  const [fulfillmentData, setFulfillmentData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchDashboardData();\n    fetchFulfillmentData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        navigate('/admin/login');\n        return;\n      }\n      const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setDashboardData(data);\n      } else if (response.status === 401) {\n        localStorage.removeItem('adminToken');\n        localStorage.removeItem('adminUser');\n        navigate('/admin/login');\n      } else {\n        setError('Failed to load dashboard data');\n      }\n    } catch (error) {\n      setError('Network error. Please try again.');\n      console.error('Dashboard fetch error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchFulfillmentData = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      if (!token) return;\n      const response = await fetch('http://localhost:5000/api/fulfillment/dashboard/summary', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setFulfillmentData(data.summary);\n      }\n    } catch (error) {\n      console.error('Fulfillment data fetch error:', error);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchDashboardData,\n          className: \"retry-btn\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this);\n  }\n  const {\n    statistics,\n    recent_orders,\n    low_stock_products\n  } = dashboardData || {};\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-dashboard\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Dashboard Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome to the Allora Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon products\",\n            children: \"\\uD83D\\uDCE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_products) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon orders\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_orders) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon users\",\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_users) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon revenue\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\u20B9\", (statistics === null || statistics === void 0 ? void 0 : (_statistics$total_rev = statistics.total_revenue) === null || _statistics$total_rev === void 0 ? void 0 : _statistics$total_rev.toLocaleString()) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Revenue (30 days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon fulfillment\",\n            children: \"\\uD83D\\uDE9A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (fulfillmentData === null || fulfillmentData === void 0 ? void 0 : (_fulfillmentData$fulf = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf === void 0 ? void 0 : _fulfillmentData$fulf.total_shipments) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Active Shipments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon delivery\",\n            children: \"\\uD83D\\uDCE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (fulfillmentData === null || fulfillmentData === void 0 ? void 0 : (_fulfillmentData$fulf2 = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf2 === void 0 ? void 0 : _fulfillmentData$fulf2.delivered_today) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Delivered Today\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon performance\",\n            children: \"\\u26A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [(fulfillmentData === null || fulfillmentData === void 0 ? void 0 : (_fulfillmentData$fulf3 = fulfillmentData.fulfillment_metrics) === null || _fulfillmentData$fulf3 === void 0 ? void 0 : _fulfillmentData$fulf3.on_time_delivery_rate) || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"On-Time Delivery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon exceptions\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (fulfillmentData === null || fulfillmentData === void 0 ? void 0 : (_fulfillmentData$trac = fulfillmentData.tracking_metrics) === null || _fulfillmentData$trac === void 0 ? void 0 : _fulfillmentData$trac.exception_count) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Exceptions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/orders'),\n              className: \"view-all-btn\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"orders-table\",\n            children: recent_orders && recent_orders.length > 0 ? /*#__PURE__*/_jsxDEV(\"table\", {\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Order #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Customer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recent_orders.map(order => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [\"#\", order.order_number]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: order.customer_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status-badge ${order.status}`,\n                      children: order.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [\"\\u20B9\", order.total_amount.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(order.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 49\n                  }, this)]\n                }, order.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 45\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"No recent orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Low Stock Alert\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/inventory'),\n              className: \"view-all-btn\",\n              children: \"View Inventory\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"low-stock-list\",\n            children: low_stock_products && low_stock_products.length > 0 ? low_stock_products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"low-stock-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Stock: \", product.stock_quantity, \" / Threshold: \", product.low_stock_threshold]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stock-level\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stock-bar\",\n                  style: {\n                    width: `${Math.min(product.stock_quantity / product.low_stock_threshold * 100, 100)}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 41\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 37\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"All products are well stocked\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 9\n  }, this);\n};\n_s(AdminDashboard, \"IckvB8unvifWDN/lAv5wcCqHmOM=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "AdminLayout", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_statistics$total_rev", "_fulfillmentData$fulf", "_fulfillmentData$fulf2", "_fulfillmentData$fulf3", "_fulfillmentData$trac", "dashboardData", "setDashboardData", "fulfillmentData", "setFulfillmentData", "loading", "setLoading", "error", "setError", "navigate", "fetchDashboardData", "fetchFulfillmentData", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "data", "json", "status", "removeItem", "console", "summary", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "statistics", "recent_orders", "low_stock_products", "total_products", "total_orders", "total_users", "total_revenue", "toLocaleString", "fulfillment_metrics", "total_shipments", "delivered_today", "on_time_delivery_rate", "tracking_metrics", "exception_count", "length", "map", "order", "order_number", "customer_email", "total_amount", "Date", "created_at", "toLocaleDateString", "id", "product", "name", "stock_quantity", "low_stock_threshold", "style", "width", "Math", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AdminLayout from '../components/AdminLayout';\nimport './AdminDashboard.css';\n\nconst AdminDashboard = () => {\n    const [dashboardData, setDashboardData] = useState(null);\n    const [fulfillmentData, setFulfillmentData] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        fetchDashboardData();\n        fetchFulfillmentData();\n    }, []);\n\n    const fetchDashboardData = async () => {\n        try {\n            const token = localStorage.getItem('adminToken');\n            if (!token) {\n                navigate('/admin/login');\n                return;\n            }\n\n            const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                },\n            });\n\n            if (response.ok) {\n                const data = await response.json();\n                setDashboardData(data);\n            } else if (response.status === 401) {\n                localStorage.removeItem('adminToken');\n                localStorage.removeItem('adminUser');\n                navigate('/admin/login');\n            } else {\n                setError('Failed to load dashboard data');\n            }\n        } catch (error) {\n            setError('Network error. Please try again.');\n            console.error('Dashboard fetch error:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchFulfillmentData = async () => {\n        try {\n            const token = localStorage.getItem('adminToken');\n            if (!token) return;\n\n            const response = await fetch('http://localhost:5000/api/fulfillment/dashboard/summary', {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                },\n            });\n\n            if (response.ok) {\n                const data = await response.json();\n                setFulfillmentData(data.summary);\n            }\n        } catch (error) {\n            console.error('Fulfillment data fetch error:', error);\n        }\n    };\n\n    if (loading) {\n        return (\n            <AdminLayout>\n                <div className=\"admin-loading\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading dashboard...</p>\n                </div>\n            </AdminLayout>\n        );\n    }\n\n    if (error) {\n        return (\n            <AdminLayout>\n                <div className=\"admin-error\">\n                    <p>{error}</p>\n                    <button onClick={fetchDashboardData} className=\"retry-btn\">\n                        Retry\n                    </button>\n                </div>\n            </AdminLayout>\n        );\n    }\n\n    const { statistics, recent_orders, low_stock_products } = dashboardData || {};\n\n    return (\n        <AdminLayout>\n            <div className=\"admin-dashboard\">\n                <div className=\"dashboard-header\">\n                    <h1>Dashboard Overview</h1>\n                    <p>Welcome to the Allora Admin Panel</p>\n                </div>\n\n                {/* Statistics Cards */}\n                <div className=\"stats-grid\">\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon products\">📦</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_products || 0}</h3>\n                            <p>Total Products</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon orders\">🛒</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_orders || 0}</h3>\n                            <p>Total Orders</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon users\">👥</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_users || 0}</h3>\n                            <p>Total Users</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon revenue\">💰</div>\n                        <div className=\"stat-content\">\n                            <h3>₹{statistics?.total_revenue?.toLocaleString() || 0}</h3>\n                            <p>Revenue (30 days)</p>\n                        </div>\n                    </div>\n\n                    {/* Fulfillment Statistics */}\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon fulfillment\">🚚</div>\n                        <div className=\"stat-content\">\n                            <h3>{fulfillmentData?.fulfillment_metrics?.total_shipments || 0}</h3>\n                            <p>Active Shipments</p>\n                        </div>\n                    </div>\n\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon delivery\">📦</div>\n                        <div className=\"stat-content\">\n                            <h3>{fulfillmentData?.fulfillment_metrics?.delivered_today || 0}</h3>\n                            <p>Delivered Today</p>\n                        </div>\n                    </div>\n\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon performance\">⚡</div>\n                        <div className=\"stat-content\">\n                            <h3>{fulfillmentData?.fulfillment_metrics?.on_time_delivery_rate || 0}%</h3>\n                            <p>On-Time Delivery</p>\n                        </div>\n                    </div>\n\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon exceptions\">⚠️</div>\n                        <div className=\"stat-content\">\n                            <h3>{fulfillmentData?.tracking_metrics?.exception_count || 0}</h3>\n                            <p>Exceptions</p>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"dashboard-content\">\n                    {/* Recent Orders */}\n                    <div className=\"dashboard-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Orders</h2>\n                            <button \n                                onClick={() => navigate('/admin/orders')}\n                                className=\"view-all-btn\"\n                            >\n                                View All\n                            </button>\n                        </div>\n                        \n                        <div className=\"orders-table\">\n                            {recent_orders && recent_orders.length > 0 ? (\n                                <table>\n                                    <thead>\n                                        <tr>\n                                            <th>Order #</th>\n                                            <th>Customer</th>\n                                            <th>Status</th>\n                                            <th>Amount</th>\n                                            <th>Date</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        {recent_orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>#{order.order_number}</td>\n                                                <td>{order.customer_email}</td>\n                                                <td>\n                                                    <span className={`status-badge ${order.status}`}>\n                                                        {order.status}\n                                                    </span>\n                                                </td>\n                                                <td>₹{order.total_amount.toLocaleString()}</td>\n                                                <td>{new Date(order.created_at).toLocaleDateString()}</td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </table>\n                            ) : (\n                                <p className=\"no-data\">No recent orders</p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Low Stock Alert */}\n                    <div className=\"dashboard-section\">\n                        <div className=\"section-header\">\n                            <h2>Low Stock Alert</h2>\n                            <button \n                                onClick={() => navigate('/admin/inventory')}\n                                className=\"view-all-btn\"\n                            >\n                                View Inventory\n                            </button>\n                        </div>\n                        \n                        <div className=\"low-stock-list\">\n                            {low_stock_products && low_stock_products.length > 0 ? (\n                                low_stock_products.map(product => (\n                                    <div key={product.id} className=\"low-stock-item\">\n                                        <div className=\"product-info\">\n                                            <h4>{product.name}</h4>\n                                            <p>Stock: {product.stock_quantity} / Threshold: {product.low_stock_threshold}</p>\n                                        </div>\n                                        <div className=\"stock-level\">\n                                            <div \n                                                className=\"stock-bar\"\n                                                style={{\n                                                    width: `${Math.min((product.stock_quantity / product.low_stock_threshold) * 100, 100)}%`\n                                                }}\n                                            ></div>\n                                        </div>\n                                    </div>\n                                ))\n                            ) : (\n                                <p className=\"no-data\">All products are well stocked</p>\n                            )}\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </AdminLayout>\n    );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMqB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZqB,kBAAkB,CAAC,CAAC;IACpBC,oBAAoB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACF,KAAK,EAAE;QACRH,QAAQ,CAAC,cAAc,CAAC;QACxB;MACJ;MAEA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;QACtEC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUL,KAAK;QACpC;MACJ,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACb,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClClB,gBAAgB,CAACiB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIJ,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;QAChCR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;QACrCT,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;QACpCb,QAAQ,CAAC,cAAc,CAAC;MAC5B,CAAC,MAAM;QACHD,QAAQ,CAAC,+BAA+B,CAAC;MAC7C;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZC,QAAQ,CAAC,kCAAkC,CAAC;MAC5Ce,OAAO,CAAChB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACND,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMK,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACF,KAAK,EAAE;MAEZ,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,yDAAyD,EAAE;QACpFC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUL,KAAK;QACpC;MACJ,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACb,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClChB,kBAAkB,CAACe,IAAI,CAACK,OAAO,CAAC;MACpC;IACJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACZgB,OAAO,CAAChB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACzD;EACJ,CAAC;EAED,IAAIF,OAAO,EAAE;IACT,oBACIZ,OAAA,CAACF,WAAW;MAAAkC,QAAA,eACRhC,OAAA;QAAKiC,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC1BhC,OAAA;UAAKiC,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCrC,OAAA;UAAAgC,QAAA,EAAG;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEtB;EAEA,IAAIvB,KAAK,EAAE;IACP,oBACId,OAAA,CAACF,WAAW;MAAAkC,QAAA,eACRhC,OAAA;QAAKiC,SAAS,EAAC,aAAa;QAAAD,QAAA,gBACxBhC,OAAA;UAAAgC,QAAA,EAAIlB;QAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdrC,OAAA;UAAQsC,OAAO,EAAErB,kBAAmB;UAACgB,SAAS,EAAC,WAAW;UAAAD,QAAA,EAAC;QAE3D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEtB;EAEA,MAAM;IAAEE,UAAU;IAAEC,aAAa;IAAEC;EAAmB,CAAC,GAAGjC,aAAa,IAAI,CAAC,CAAC;EAE7E,oBACIR,OAAA,CAACF,WAAW;IAAAkC,QAAA,eACRhC,OAAA;MAAKiC,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC5BhC,OAAA;QAAKiC,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7BhC,OAAA;UAAAgC,QAAA,EAAI;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BrC,OAAA;UAAAgC,QAAA,EAAG;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAGNrC,OAAA;QAAKiC,SAAS,EAAC,YAAY;QAAAD,QAAA,gBACvBhC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtBhC,OAAA;YAAKiC,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5CrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,cAAc,KAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1CrC,OAAA;cAAAgC,QAAA,EAAG;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtBhC,OAAA;YAAKiC,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1CrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,YAAY,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxCrC,OAAA;cAAAgC,QAAA,EAAG;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtBhC,OAAA;YAAKiC,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,KAAI;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCrC,OAAA;cAAAgC,QAAA,EAAG;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtBhC,OAAA;YAAKiC,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3CrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,GAAI,QAAC,EAAC,CAAAO,UAAU,aAAVA,UAAU,wBAAApC,qBAAA,GAAVoC,UAAU,CAAEM,aAAa,cAAA1C,qBAAA,uBAAzBA,qBAAA,CAA2B2C,cAAc,CAAC,CAAC,KAAI,CAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5DrC,OAAA;cAAAgC,QAAA,EAAG;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNrC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtBhC,OAAA;YAAKiC,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAK,CAAAtB,eAAe,aAAfA,eAAe,wBAAAN,qBAAA,GAAfM,eAAe,CAAEqC,mBAAmB,cAAA3C,qBAAA,uBAApCA,qBAAA,CAAsC4C,eAAe,KAAI;YAAC;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrErC,OAAA;cAAAgC,QAAA,EAAG;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtBhC,OAAA;YAAKiC,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5CrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAK,CAAAtB,eAAe,aAAfA,eAAe,wBAAAL,sBAAA,GAAfK,eAAe,CAAEqC,mBAAmB,cAAA1C,sBAAA,uBAApCA,sBAAA,CAAsC4C,eAAe,KAAI;YAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrErC,OAAA;cAAAgC,QAAA,EAAG;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtBhC,OAAA;YAAKiC,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,GAAK,CAAAtB,eAAe,aAAfA,eAAe,wBAAAJ,sBAAA,GAAfI,eAAe,CAAEqC,mBAAmB,cAAAzC,sBAAA,uBAApCA,sBAAA,CAAsC4C,qBAAqB,KAAI,CAAC,EAAC,GAAC;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5ErC,OAAA;cAAAgC,QAAA,EAAG;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtBhC,OAAA;YAAKiC,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBhC,OAAA;cAAAgC,QAAA,EAAK,CAAAtB,eAAe,aAAfA,eAAe,wBAAAH,qBAAA,GAAfG,eAAe,CAAEyC,gBAAgB,cAAA5C,qBAAA,uBAAjCA,qBAAA,CAAmC6C,eAAe,KAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClErC,OAAA;cAAAgC,QAAA,EAAG;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENrC,OAAA;QAAKiC,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAE9BhC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9BhC,OAAA;YAAKiC,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3BhC,OAAA;cAAAgC,QAAA,EAAI;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBrC,OAAA;cACIsC,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,eAAe,CAAE;cACzCiB,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENrC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAAAD,QAAA,EACxBQ,aAAa,IAAIA,aAAa,CAACa,MAAM,GAAG,CAAC,gBACtCrD,OAAA;cAAAgC,QAAA,gBACIhC,OAAA;gBAAAgC,QAAA,eACIhC,OAAA;kBAAAgC,QAAA,gBACIhC,OAAA;oBAAAgC,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBrC,OAAA;oBAAAgC,QAAA,EAAI;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBrC,OAAA;oBAAAgC,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfrC,OAAA;oBAAAgC,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfrC,OAAA;oBAAAgC,QAAA,EAAI;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACRrC,OAAA;gBAAAgC,QAAA,EACKQ,aAAa,CAACc,GAAG,CAACC,KAAK,iBACpBvD,OAAA;kBAAAgC,QAAA,gBACIhC,OAAA;oBAAAgC,QAAA,GAAI,GAAC,EAACuB,KAAK,CAACC,YAAY;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BrC,OAAA;oBAAAgC,QAAA,EAAKuB,KAAK,CAACE;kBAAc;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/BrC,OAAA;oBAAAgC,QAAA,eACIhC,OAAA;sBAAMiC,SAAS,EAAE,gBAAgBsB,KAAK,CAAC3B,MAAM,EAAG;sBAAAI,QAAA,EAC3CuB,KAAK,CAAC3B;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACLrC,OAAA;oBAAAgC,QAAA,GAAI,QAAC,EAACuB,KAAK,CAACG,YAAY,CAACZ,cAAc,CAAC,CAAC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/CrC,OAAA;oBAAAgC,QAAA,EAAK,IAAI2B,IAAI,CAACJ,KAAK,CAACK,UAAU,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GATrDkB,KAAK,CAACO,EAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUb,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAERrC,OAAA;cAAGiC,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC7C;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNrC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9BhC,OAAA;YAAKiC,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3BhC,OAAA;cAAAgC,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBrC,OAAA;cACIsC,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAAC,kBAAkB,CAAE;cAC5CiB,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENrC,OAAA;YAAKiC,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC1BS,kBAAkB,IAAIA,kBAAkB,CAACY,MAAM,GAAG,CAAC,GAChDZ,kBAAkB,CAACa,GAAG,CAACS,OAAO,iBAC1B/D,OAAA;cAAsBiC,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC5ChC,OAAA;gBAAKiC,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzBhC,OAAA;kBAAAgC,QAAA,EAAK+B,OAAO,CAACC;gBAAI;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBrC,OAAA;kBAAAgC,QAAA,GAAG,SAAO,EAAC+B,OAAO,CAACE,cAAc,EAAC,gBAAc,EAACF,OAAO,CAACG,mBAAmB;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACNrC,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAD,QAAA,eACxBhC,OAAA;kBACIiC,SAAS,EAAC,WAAW;kBACrBkC,KAAK,EAAE;oBACHC,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAEP,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,mBAAmB,GAAI,GAAG,EAAE,GAAG,CAAC;kBACzF;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAZA0B,OAAO,CAACD,EAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaf,CACR,CAAC,gBAEFrC,OAAA;cAAGiC,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC1D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAACnC,EAAA,CA5PID,cAAc;EAAA,QAKCJ,WAAW;AAAA;AAAA0E,EAAA,GAL1BtE,cAAc;AA8PpB,eAAeA,cAAc;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}