# Elasticsearch Configuration for Allora E-commerce Platform

# Cluster Configuration
cluster.name: allora-cluster
node.name: allora-es-node

# Network Configuration
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# Discovery Configuration
discovery.type: single-node

# Memory Configuration
bootstrap.memory_lock: true

# Security Configuration (disabled for development)
xpack.security.enabled: false
xpack.security.enrollment.enabled: false
xpack.security.http.ssl.enabled: false
xpack.security.transport.ssl.enabled: false

# Monitoring Configuration
xpack.monitoring.collection.enabled: true

# Index Configuration
action.auto_create_index: true
action.destructive_requires_name: true

# Search Configuration
search.max_buckets: 65536
indices.query.bool.max_clause_count: 10000

# Performance Configuration
thread_pool.search.size: 4
thread_pool.search.queue_size: 1000
thread_pool.write.size: 4
thread_pool.write.queue_size: 1000

# Index Templates and Policies
cluster.routing.allocation.disk.threshold_enabled: true
cluster.routing.allocation.disk.watermark.low: 85%
cluster.routing.allocation.disk.watermark.high: 90%
cluster.routing.allocation.disk.watermark.flood_stage: 95%

# Logging Configuration
logger.level: INFO
logger.org.elasticsearch.discovery: DEBUG

# HTTP Configuration
http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-methods: OPTIONS, HEAD, GET, POST, PUT, DELETE
http.cors.allow-headers: X-Requested-With, Content-Type, Content-Length, Authorization

# Index Lifecycle Management
xpack.ilm.enabled: true

# Snapshot Configuration
path.repo: ["/usr/share/elasticsearch/backups"]
