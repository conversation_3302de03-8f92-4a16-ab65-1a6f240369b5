import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import { useError } from './ErrorContext';
import { useLoading } from './LoadingContext';

const ImageContext = createContext();

export const useImage = () => {
  const context = useContext(ImageContext);
  if (!context) {
    throw new Error('useImage must be used within an ImageProvider');
  }
  return context;
};

// Image cache for performance
const imageCache = new Map();
const loadingPromises = new Map();

export const ImageProvider = ({ children }) => {
  const [imageStates, setImageStates] = useState({});
  const { addError } = useError();
  const { setLoading, clearLoading } = useLoading();
  const abortControllers = useRef(new Map());

  // Default fallback images
  const defaultFallbacks = {
    product: '/images/placeholder-product.jpg',
    user: '/images/placeholder-user.jpg',
    category: '/images/placeholder-category.jpg',
    brand: '/images/placeholder-brand.jpg',
    general: '/images/placeholder-general.jpg'
  };

  // Get optimized image URL
  const getOptimizedImageUrl = useCallback((src, options = {}) => {
    if (!src) return null;
    
    const {
      width,
      height,
      quality = 80,
      format = 'webp',
      fit = 'cover'
    } = options;

    // If it's already an external URL, return as is
    if (src.startsWith('http://') || src.startsWith('https://')) {
      return src;
    }

    // Build optimized URL with query parameters
    const params = new URLSearchParams();
    if (width) params.append('w', width);
    if (height) params.append('h', height);
    if (quality !== 80) params.append('q', quality);
    if (format !== 'webp') params.append('f', format);
    if (fit !== 'cover') params.append('fit', fit);

    const queryString = params.toString();
    return queryString ? `${src}?${queryString}` : src;
  }, []);

  // Preload image
  const preloadImage = useCallback(async (src, options = {}) => {
    const optimizedSrc = getOptimizedImageUrl(src, options);
    if (!optimizedSrc) return null;

    // Check cache first
    if (imageCache.has(optimizedSrc)) {
      return imageCache.get(optimizedSrc);
    }

    // Check if already loading
    if (loadingPromises.has(optimizedSrc)) {
      return loadingPromises.get(optimizedSrc);
    }

    // Create loading promise
    const loadingPromise = new Promise((resolve, reject) => {
      const img = new Image();
      const abortController = new AbortController();
      
      abortControllers.current.set(optimizedSrc, abortController);

      const cleanup = () => {
        abortControllers.current.delete(optimizedSrc);
        loadingPromises.delete(optimizedSrc);
      };

      img.onload = () => {
        imageCache.set(optimizedSrc, {
          src: optimizedSrc,
          width: img.naturalWidth,
          height: img.naturalHeight,
          loaded: true,
          error: false
        });
        cleanup();
        resolve(imageCache.get(optimizedSrc));
      };

      img.onerror = () => {
        const errorInfo = {
          src: optimizedSrc,
          loaded: false,
          error: true,
          errorMessage: 'Failed to load image'
        };
        imageCache.set(optimizedSrc, errorInfo);
        cleanup();
        reject(new Error(`Failed to load image: ${optimizedSrc}`));
      };

      // Handle abort
      abortController.signal.addEventListener('abort', () => {
        cleanup();
        reject(new Error('Image loading aborted'));
      });

      img.src = optimizedSrc;
    });

    loadingPromises.set(optimizedSrc, loadingPromise);
    return loadingPromise;
  }, [getOptimizedImageUrl]);

  // Load image with state management
  const loadImage = useCallback(async (key, src, options = {}) => {
    const {
      fallback,
      fallbackType = 'general',
      maxRetries = 3,
      retryDelay = 1000
    } = options;

    setLoading(`image_${key}`, true, 'Loading image...');
    
    setImageStates(prev => ({
      ...prev,
      [key]: {
        loading: true,
        loaded: false,
        error: false,
        src: null,
        attempts: 0
      }
    }));

    let attempts = 0;
    let lastError = null;

    while (attempts < maxRetries) {
      try {
        const imageInfo = await preloadImage(src, options);
        
        setImageStates(prev => ({
          ...prev,
          [key]: {
            loading: false,
            loaded: true,
            error: false,
            src: imageInfo.src,
            width: imageInfo.width,
            height: imageInfo.height,
            attempts: attempts + 1
          }
        }));

        clearLoading(`image_${key}`);
        return imageInfo;

      } catch (error) {
        lastError = error;
        attempts++;
        
        if (attempts < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempts));
        }
      }
    }

    // All attempts failed, try fallback
    const fallbackSrc = fallback || defaultFallbacks[fallbackType] || defaultFallbacks.general;
    
    try {
      const fallbackInfo = await preloadImage(fallbackSrc);
      
      setImageStates(prev => ({
        ...prev,
        [key]: {
          loading: false,
          loaded: true,
          error: true,
          src: fallbackInfo.src,
          width: fallbackInfo.width,
          height: fallbackInfo.height,
          attempts,
          fallbackUsed: true
        }
      }));

      clearLoading(`image_${key}`);
      return fallbackInfo;

    } catch (fallbackError) {
      setImageStates(prev => ({
        ...prev,
        [key]: {
          loading: false,
          loaded: false,
          error: true,
          src: null,
          attempts,
          errorMessage: lastError.message
        }
      }));

      clearLoading(`image_${key}`);
      addError(`image_${key}`, lastError, {
        type: 'network',
        severity: 'low',
        context: { src, fallbackSrc }
      });

      throw lastError;
    }
  }, [preloadImage, setLoading, clearLoading, addError, defaultFallbacks]);

  // Get image state
  const getImageState = useCallback((key) => {
    return imageStates[key] || {
      loading: false,
      loaded: false,
      error: false,
      src: null
    };
  }, [imageStates]);

  // Clear image from cache and state
  const clearImage = useCallback((key) => {
    const state = imageStates[key];
    if (state?.src) {
      imageCache.delete(state.src);
    }
    
    setImageStates(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });

    // Abort loading if in progress
    const abortController = abortControllers.current.get(key);
    if (abortController) {
      abortController.abort();
    }
  }, [imageStates]);

  // Batch preload images
  const preloadImages = useCallback(async (images) => {
    const promises = images.map(({ src, options }) => 
      preloadImage(src, options).catch(error => ({ error, src }))
    );
    
    return Promise.allSettled(promises);
  }, [preloadImage]);

  // Clear cache
  const clearCache = useCallback(() => {
    imageCache.clear();
    loadingPromises.clear();
    abortControllers.current.forEach(controller => controller.abort());
    abortControllers.current.clear();
  }, []);

  const value = {
    // Image loading
    loadImage,
    preloadImage,
    preloadImages,
    
    // Image utilities
    getOptimizedImageUrl,
    getImageState,
    clearImage,
    clearCache,
    
    // State
    imageStates,
    
    // Constants
    defaultFallbacks
  };

  return (
    <ImageContext.Provider value={value}>
      {children}
    </ImageContext.Provider>
  );
};

export default ImageContext;
