.rma-request-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.rma-request-header {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.rma-request-header h1 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 2.2rem;
    font-weight: 600;
}

.order-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.order-info h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.order-info p {
    margin: 5px 0;
    color: #666;
    font-size: 1rem;
}

.rma-request-form {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-section {
    padding: 30px;
    border-bottom: 1px solid #eee;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.4rem;
    font-weight: 600;
}

/* RMA Type Selection */
.rma-type-selection {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.radio-option:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

.radio-option input[type="radio"]:checked + span {
    color: #3498db;
    font-weight: 600;
}

.radio-option input[type="radio"] {
    margin: 0;
}

/* Order Items */
.order-items {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.order-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: white;
}

.order-item:hover {
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
}

.item-checkbox input[type="checkbox"] {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.item-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.1rem;
    font-weight: 600;
}

.item-details p {
    margin: 4px 0;
    color: #666;
    font-size: 0.9rem;
}

/* Item Return Details */
.item-return-details {
    flex: 1;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-left: 20px;
}

.quantity-input,
.reason-select,
.condition-notes,
.photo-upload {
    margin-bottom: 15px;
}

.quantity-input label,
.reason-select label,
.condition-notes label,
.photo-upload label {
    display: block;
    margin-bottom: 5px;
    color: #2c3e50;
    font-weight: 500;
    font-size: 0.9rem;
}

.quantity-input input,
.reason-select select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.condition-notes textarea {
    width: 100%;
    min-height: 80px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    resize: vertical;
}

.photo-upload input[type="file"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.uploaded-photos {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.uploaded-photos img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #ddd;
}

/* Customer Information */
.customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group:last-child {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
    font-size: 1rem;
}

.form-group input,
.form-group textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* Form Actions */
.form-actions {
    padding: 30px;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading and Error States */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: #666;
    font-size: 1.1rem;
}

.error-message {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-message h2 {
    color: #e74c3c;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.error-message p {
    color: #666;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rma-request-container {
        padding: 15px;
    }
    
    .rma-request-header,
    .form-section {
        padding: 20px;
    }
    
    .rma-type-selection {
        flex-direction: column;
    }
    
    .order-item {
        flex-direction: column;
        gap: 15px;
    }
    
    .item-return-details {
        margin-left: 0;
        margin-top: 15px;
    }
    
    .customer-info {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        width: 100%;
    }
}
