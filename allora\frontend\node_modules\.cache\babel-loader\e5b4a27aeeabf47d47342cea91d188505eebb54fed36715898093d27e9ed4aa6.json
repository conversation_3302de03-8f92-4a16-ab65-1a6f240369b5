{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AdminLayout from '../components/AdminLayout';\nimport './AdminDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _statistics$total_rev;\n  const [dashboardData, setDashboardData] = useState(null);\n  const [fulfillmentData, setFulfillmentData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        navigate('/admin/login');\n        return;\n      }\n      const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setDashboardData(data);\n      } else if (response.status === 401) {\n        localStorage.removeItem('adminToken');\n        localStorage.removeItem('adminUser');\n        navigate('/admin/login');\n      } else {\n        setError('Failed to load dashboard data');\n      }\n    } catch (error) {\n      setError('Network error. Please try again.');\n      console.error('Dashboard fetch error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchDashboardData,\n          className: \"retry-btn\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this);\n  }\n  const {\n    statistics,\n    recent_orders,\n    low_stock_products\n  } = dashboardData || {};\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-dashboard\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Dashboard Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome to the Allora Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon products\",\n            children: \"\\uD83D\\uDCE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_products) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon orders\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_orders) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon users\",\n            children: \"\\uD83D\\uDC65\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: (statistics === null || statistics === void 0 ? void 0 : statistics.total_users) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon revenue\",\n            children: \"\\uD83D\\uDCB0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\u20B9\", (statistics === null || statistics === void 0 ? void 0 : (_statistics$total_rev = statistics.total_revenue) === null || _statistics$total_rev === void 0 ? void 0 : _statistics$total_rev.toLocaleString()) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Revenue (30 days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/orders'),\n              className: \"view-all-btn\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"orders-table\",\n            children: recent_orders && recent_orders.length > 0 ? /*#__PURE__*/_jsxDEV(\"table\", {\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Order #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Customer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: recent_orders.map(order => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [\"#\", order.order_number]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: order.customer_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status-badge ${order.status}`,\n                      children: order.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [\"\\u20B9\", order.total_amount.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: new Date(order.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 49\n                  }, this)]\n                }, order.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 45\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"No recent orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Low Stock Alert\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/inventory'),\n              className: \"view-all-btn\",\n              children: \"View Inventory\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"low-stock-list\",\n            children: low_stock_products && low_stock_products.length > 0 ? low_stock_products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"low-stock-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Stock: \", product.stock_quantity, \" / Threshold: \", product.low_stock_threshold]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stock-level\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stock-bar\",\n                  style: {\n                    width: `${Math.min(product.stock_quantity / product.low_stock_threshold * 100, 100)}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 41\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 37\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-data\",\n              children: \"All products are well stocked\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 9\n  }, this);\n};\n_s(AdminDashboard, \"IckvB8unvifWDN/lAv5wcCqHmOM=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "AdminLayout", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_statistics$total_rev", "dashboardData", "setDashboardData", "fulfillmentData", "setFulfillmentData", "loading", "setLoading", "error", "setError", "navigate", "fetchDashboardData", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "data", "json", "status", "removeItem", "console", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "statistics", "recent_orders", "low_stock_products", "total_products", "total_orders", "total_users", "total_revenue", "toLocaleString", "length", "map", "order", "order_number", "customer_email", "total_amount", "Date", "created_at", "toLocaleDateString", "id", "product", "name", "stock_quantity", "low_stock_threshold", "style", "width", "Math", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AdminLayout from '../components/AdminLayout';\nimport './AdminDashboard.css';\n\nconst AdminDashboard = () => {\n    const [dashboardData, setDashboardData] = useState(null);\n    const [fulfillmentData, setFulfillmentData] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        fetchDashboardData();\n    }, []);\n\n    const fetchDashboardData = async () => {\n        try {\n            const token = localStorage.getItem('adminToken');\n            if (!token) {\n                navigate('/admin/login');\n                return;\n            }\n\n            const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                },\n            });\n\n            if (response.ok) {\n                const data = await response.json();\n                setDashboardData(data);\n            } else if (response.status === 401) {\n                localStorage.removeItem('adminToken');\n                localStorage.removeItem('adminUser');\n                navigate('/admin/login');\n            } else {\n                setError('Failed to load dashboard data');\n            }\n        } catch (error) {\n            setError('Network error. Please try again.');\n            console.error('Dashboard fetch error:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    if (loading) {\n        return (\n            <AdminLayout>\n                <div className=\"admin-loading\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading dashboard...</p>\n                </div>\n            </AdminLayout>\n        );\n    }\n\n    if (error) {\n        return (\n            <AdminLayout>\n                <div className=\"admin-error\">\n                    <p>{error}</p>\n                    <button onClick={fetchDashboardData} className=\"retry-btn\">\n                        Retry\n                    </button>\n                </div>\n            </AdminLayout>\n        );\n    }\n\n    const { statistics, recent_orders, low_stock_products } = dashboardData || {};\n\n    return (\n        <AdminLayout>\n            <div className=\"admin-dashboard\">\n                <div className=\"dashboard-header\">\n                    <h1>Dashboard Overview</h1>\n                    <p>Welcome to the Allora Admin Panel</p>\n                </div>\n\n                {/* Statistics Cards */}\n                <div className=\"stats-grid\">\n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon products\">📦</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_products || 0}</h3>\n                            <p>Total Products</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon orders\">🛒</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_orders || 0}</h3>\n                            <p>Total Orders</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon users\">👥</div>\n                        <div className=\"stat-content\">\n                            <h3>{statistics?.total_users || 0}</h3>\n                            <p>Total Users</p>\n                        </div>\n                    </div>\n                    \n                    <div className=\"stat-card\">\n                        <div className=\"stat-icon revenue\">💰</div>\n                        <div className=\"stat-content\">\n                            <h3>₹{statistics?.total_revenue?.toLocaleString() || 0}</h3>\n                            <p>Revenue (30 days)</p>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"dashboard-content\">\n                    {/* Recent Orders */}\n                    <div className=\"dashboard-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Orders</h2>\n                            <button \n                                onClick={() => navigate('/admin/orders')}\n                                className=\"view-all-btn\"\n                            >\n                                View All\n                            </button>\n                        </div>\n                        \n                        <div className=\"orders-table\">\n                            {recent_orders && recent_orders.length > 0 ? (\n                                <table>\n                                    <thead>\n                                        <tr>\n                                            <th>Order #</th>\n                                            <th>Customer</th>\n                                            <th>Status</th>\n                                            <th>Amount</th>\n                                            <th>Date</th>\n                                        </tr>\n                                    </thead>\n                                    <tbody>\n                                        {recent_orders.map(order => (\n                                            <tr key={order.id}>\n                                                <td>#{order.order_number}</td>\n                                                <td>{order.customer_email}</td>\n                                                <td>\n                                                    <span className={`status-badge ${order.status}`}>\n                                                        {order.status}\n                                                    </span>\n                                                </td>\n                                                <td>₹{order.total_amount.toLocaleString()}</td>\n                                                <td>{new Date(order.created_at).toLocaleDateString()}</td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </table>\n                            ) : (\n                                <p className=\"no-data\">No recent orders</p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Low Stock Alert */}\n                    <div className=\"dashboard-section\">\n                        <div className=\"section-header\">\n                            <h2>Low Stock Alert</h2>\n                            <button \n                                onClick={() => navigate('/admin/inventory')}\n                                className=\"view-all-btn\"\n                            >\n                                View Inventory\n                            </button>\n                        </div>\n                        \n                        <div className=\"low-stock-list\">\n                            {low_stock_products && low_stock_products.length > 0 ? (\n                                low_stock_products.map(product => (\n                                    <div key={product.id} className=\"low-stock-item\">\n                                        <div className=\"product-info\">\n                                            <h4>{product.name}</h4>\n                                            <p>Stock: {product.stock_quantity} / Threshold: {product.low_stock_threshold}</p>\n                                        </div>\n                                        <div className=\"stock-level\">\n                                            <div \n                                                className=\"stock-bar\"\n                                                style={{\n                                                    width: `${Math.min((product.stock_quantity / product.low_stock_threshold) * 100, 100)}%`\n                                                }}\n                                            ></div>\n                                        </div>\n                                    </div>\n                                ))\n                            ) : (\n                                <p className=\"no-data\">All products are well stocked</p>\n                            )}\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </AdminLayout>\n    );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACW,eAAe,EAAEC,kBAAkB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZiB,kBAAkB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACF,KAAK,EAAE;QACRF,QAAQ,CAAC,cAAc,CAAC;QACxB;MACJ;MAEA,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;QACtEC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUL,KAAK;QACpC;MACJ,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACb,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCjB,gBAAgB,CAACgB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIJ,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;QAChCR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;QACrCT,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;QACpCZ,QAAQ,CAAC,cAAc,CAAC;MAC5B,CAAC,MAAM;QACHD,QAAQ,CAAC,+BAA+B,CAAC;MAC7C;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZC,QAAQ,CAAC,kCAAkC,CAAC;MAC5Cc,OAAO,CAACf,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACND,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,IAAID,OAAO,EAAE;IACT,oBACIR,OAAA,CAACF,WAAW;MAAA4B,QAAA,eACR1B,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC1B1B,OAAA;UAAK2B,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC/B,OAAA;UAAA0B,QAAA,EAAG;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEtB;EAEA,IAAIrB,KAAK,EAAE;IACP,oBACIV,OAAA,CAACF,WAAW;MAAA4B,QAAA,eACR1B,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAD,QAAA,gBACxB1B,OAAA;UAAA0B,QAAA,EAAIhB;QAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd/B,OAAA;UAAQgC,OAAO,EAAEnB,kBAAmB;UAACc,SAAS,EAAC,WAAW;UAAAD,QAAA,EAAC;QAE3D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEtB;EAEA,MAAM;IAAEE,UAAU;IAAEC,aAAa;IAAEC;EAAmB,CAAC,GAAG/B,aAAa,IAAI,CAAC,CAAC;EAE7E,oBACIJ,OAAA,CAACF,WAAW;IAAA4B,QAAA,eACR1B,OAAA;MAAK2B,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC5B1B,OAAA;QAAK2B,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7B1B,OAAA;UAAA0B,QAAA,EAAI;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B/B,OAAA;UAAA0B,QAAA,EAAG;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAGN/B,OAAA;QAAK2B,SAAS,EAAC,YAAY;QAAAD,QAAA,gBACvB1B,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1B,OAAA;YAAK2B,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5C/B,OAAA;YAAK2B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1B,OAAA;cAAA0B,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEG,cAAc,KAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1C/B,OAAA;cAAA0B,QAAA,EAAG;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/B,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1B,OAAA;YAAK2B,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C/B,OAAA;YAAK2B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1B,OAAA;cAAA0B,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,YAAY,KAAI;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxC/B,OAAA;cAAA0B,QAAA,EAAG;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/B,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1B,OAAA;YAAK2B,SAAS,EAAC,iBAAiB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzC/B,OAAA;YAAK2B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1B,OAAA;cAAA0B,QAAA,EAAK,CAAAO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,KAAI;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvC/B,OAAA;cAAA0B,QAAA,EAAG;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/B,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACtB1B,OAAA;YAAK2B,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3C/B,OAAA;YAAK2B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzB1B,OAAA;cAAA0B,QAAA,GAAI,QAAC,EAAC,CAAAO,UAAU,aAAVA,UAAU,wBAAA9B,qBAAA,GAAV8B,UAAU,CAAEM,aAAa,cAAApC,qBAAA,uBAAzBA,qBAAA,CAA2BqC,cAAc,CAAC,CAAC,KAAI,CAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5D/B,OAAA;cAAA0B,QAAA,EAAG;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/B,OAAA;QAAK2B,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAE9B1B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9B1B,OAAA;YAAK2B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B1B,OAAA;cAAA0B,QAAA,EAAI;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtB/B,OAAA;cACIgC,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAAC,eAAe,CAAE;cACzCe,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEN/B,OAAA;YAAK2B,SAAS,EAAC,cAAc;YAAAD,QAAA,EACxBQ,aAAa,IAAIA,aAAa,CAACO,MAAM,GAAG,CAAC,gBACtCzC,OAAA;cAAA0B,QAAA,gBACI1B,OAAA;gBAAA0B,QAAA,eACI1B,OAAA;kBAAA0B,QAAA,gBACI1B,OAAA;oBAAA0B,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB/B,OAAA;oBAAA0B,QAAA,EAAI;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB/B,OAAA;oBAAA0B,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf/B,OAAA;oBAAA0B,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf/B,OAAA;oBAAA0B,QAAA,EAAI;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACR/B,OAAA;gBAAA0B,QAAA,EACKQ,aAAa,CAACQ,GAAG,CAACC,KAAK,iBACpB3C,OAAA;kBAAA0B,QAAA,gBACI1B,OAAA;oBAAA0B,QAAA,GAAI,GAAC,EAACiB,KAAK,CAACC,YAAY;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9B/B,OAAA;oBAAA0B,QAAA,EAAKiB,KAAK,CAACE;kBAAc;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/B/B,OAAA;oBAAA0B,QAAA,eACI1B,OAAA;sBAAM2B,SAAS,EAAE,gBAAgBgB,KAAK,CAACpB,MAAM,EAAG;sBAAAG,QAAA,EAC3CiB,KAAK,CAACpB;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACL/B,OAAA;oBAAA0B,QAAA,GAAI,QAAC,EAACiB,KAAK,CAACG,YAAY,CAACN,cAAc,CAAC,CAAC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/C/B,OAAA;oBAAA0B,QAAA,EAAK,IAAIqB,IAAI,CAACJ,KAAK,CAACK,UAAU,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GATrDY,KAAK,CAACO,EAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUb,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAER/B,OAAA;cAAG2B,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC7C;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/B,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9B1B,OAAA;YAAK2B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B1B,OAAA;cAAA0B,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB/B,OAAA;cACIgC,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAAC,kBAAkB,CAAE;cAC5Ce,SAAS,EAAC,cAAc;cAAAD,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEN/B,OAAA;YAAK2B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC1BS,kBAAkB,IAAIA,kBAAkB,CAACM,MAAM,GAAG,CAAC,GAChDN,kBAAkB,CAACO,GAAG,CAACS,OAAO,iBAC1BnD,OAAA;cAAsB2B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC5C1B,OAAA;gBAAK2B,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB1B,OAAA;kBAAA0B,QAAA,EAAKyB,OAAO,CAACC;gBAAI;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvB/B,OAAA;kBAAA0B,QAAA,GAAG,SAAO,EAACyB,OAAO,CAACE,cAAc,EAAC,gBAAc,EAACF,OAAO,CAACG,mBAAmB;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACN/B,OAAA;gBAAK2B,SAAS,EAAC,aAAa;gBAAAD,QAAA,eACxB1B,OAAA;kBACI2B,SAAS,EAAC,WAAW;kBACrB4B,KAAK,EAAE;oBACHC,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAEP,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,mBAAmB,GAAI,GAAG,EAAE,GAAG,CAAC;kBACzF;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAZAoB,OAAO,CAACD,EAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaf,CACR,CAAC,gBAEF/B,OAAA;cAAG2B,SAAS,EAAC,SAAS;cAAAD,QAAA,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC1D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAC7B,EAAA,CAtMID,cAAc;EAAA,QAKCJ,WAAW;AAAA;AAAA8D,EAAA,GAL1B1D,cAAc;AAwMpB,eAAeA,cAAc;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}