{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\nimport { API_BASE_URL } from '../config/api';\nimport { useError } from '../contexts/ErrorContext';\nimport { useLoading } from '../contexts/LoadingContext';\n\n/**\r\n * Custom hook for API calls with loading, error, and success states\r\n * @param {string} baseUrl - The base URL for API calls\r\n * @returns {object} - API utilities and state\r\n */\nexport const useApi = (baseUrl = API_BASE_URL) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Generic API call function with retry logic\n  const apiCall = useCallback(async (endpoint, options = {}, retryCount = 0) => {\n    const maxRetries = 3;\n    const baseDelay = 1000; // 1 second\n\n    setLoading(true);\n    setError(null);\n    try {\n      const url = `${baseUrl}${endpoint}`;\n      const config = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers\n        },\n        ...options\n      };\n      const response = await fetch(url, config);\n      if (!response.ok) {\n        // Handle rate limiting with retry\n        if (response.status === 429 && retryCount < maxRetries) {\n          const retryAfter = parseInt(response.headers.get('Retry-After') || '60', 10);\n          const delay = Math.min(retryAfter * 1000, baseDelay * Math.pow(2, retryCount));\n          console.log(`Rate limited. Retrying in ${delay / 1000} seconds... (Attempt ${retryCount + 1}/${maxRetries})`);\n          await new Promise(resolve => setTimeout(resolve, delay));\n          return apiCall(endpoint, options, retryCount + 1);\n        }\n        const errorData = await response.json().catch(() => ({}));\n        if (response.status === 429) {\n          throw new Error('Rate limit exceeded. Please try again later.');\n        }\n        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      return data;\n    } catch (err) {\n      setError(err.message);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [baseUrl]);\n\n  // GET request\n  const get = useCallback((endpoint, token = null) => {\n    const headers = token ? {\n      'Authorization': token\n    } : {};\n    return apiCall(endpoint, {\n      method: 'GET',\n      headers\n    });\n  }, [apiCall]);\n\n  // POST request\n  const post = useCallback((endpoint, data, token = null) => {\n    const headers = {\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': token\n      } : {})\n    };\n    return apiCall(endpoint, {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(data)\n    });\n  }, [apiCall]);\n\n  // POST request with FormData (for file uploads)\n  const postFormData = useCallback((endpoint, formData, token = null) => {\n    const headers = token ? {\n      'Authorization': token\n    } : {};\n    // Don't set Content-Type for FormData, let browser set it with boundary\n    delete headers['Content-Type'];\n    return apiCall(endpoint, {\n      method: 'POST',\n      headers,\n      body: formData\n    });\n  }, [apiCall]);\n\n  // PUT request\n  const put = useCallback((endpoint, data, token = null) => {\n    const headers = {\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': token\n      } : {})\n    };\n    return apiCall(endpoint, {\n      method: 'PUT',\n      headers,\n      body: JSON.stringify(data)\n    });\n  }, [apiCall]);\n\n  // PATCH request\n  const patch = useCallback((endpoint, data, token = null) => {\n    const headers = {\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': token\n      } : {})\n    };\n    return apiCall(endpoint, {\n      method: 'PATCH',\n      headers,\n      body: JSON.stringify(data)\n    });\n  }, [apiCall]);\n\n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n  return {\n    loading,\n    error,\n    get,\n    post,\n    postFormData,\n    put,\n    patch,\n    clearError\n  };\n};\n_s(useApi, \"ysszz8uZ3HuSfJuv5YJKWkgijDg=\");\nexport default useApi;", "map": {"version": 3, "names": ["useState", "useCallback", "API_BASE_URL", "useError", "useLoading", "useApi", "baseUrl", "_s", "loading", "setLoading", "error", "setError", "apiCall", "endpoint", "options", "retryCount", "maxRetries", "baseDelay", "url", "config", "headers", "response", "fetch", "ok", "status", "retryAfter", "parseInt", "get", "delay", "Math", "min", "pow", "console", "log", "Promise", "resolve", "setTimeout", "errorData", "json", "catch", "Error", "data", "err", "message", "token", "method", "post", "body", "JSON", "stringify", "postFormData", "formData", "put", "patch", "clearError"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/hooks/useApi.js"], "sourcesContent": ["import { useState, useCallback } from 'react';\r\nimport { API_BASE_URL } from '../config/api';\r\nimport { useError } from '../contexts/ErrorContext';\r\nimport { useLoading } from '../contexts/LoadingContext';\r\n\r\n/**\r\n * Custom hook for API calls with loading, error, and success states\r\n * @param {string} baseUrl - The base URL for API calls\r\n * @returns {object} - API utilities and state\r\n */\r\nexport const useApi = (baseUrl = API_BASE_URL) => {\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Generic API call function with retry logic\r\n  const apiCall = useCallback(async (endpoint, options = {}, retryCount = 0) => {\r\n    const maxRetries = 3;\r\n    const baseDelay = 1000; // 1 second\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const url = `${baseUrl}${endpoint}`;\r\n      const config = {\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          ...options.headers,\r\n        },\r\n        ...options,\r\n      };\r\n\r\n      const response = await fetch(url, config);\r\n\r\n      if (!response.ok) {\r\n        // Handle rate limiting with retry\r\n        if (response.status === 429 && retryCount < maxRetries) {\r\n          const retryAfter = parseInt(response.headers.get('Retry-After') || '60', 10);\r\n          const delay = Math.min(retryAfter * 1000, baseDelay * Math.pow(2, retryCount));\r\n\r\n          console.log(`Rate limited. Retrying in ${delay/1000} seconds... (Attempt ${retryCount + 1}/${maxRetries})`);\r\n\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return apiCall(endpoint, options, retryCount + 1);\r\n        }\r\n\r\n        const errorData = await response.json().catch(() => ({}));\r\n\r\n        if (response.status === 429) {\r\n          throw new Error('Rate limit exceeded. Please try again later.');\r\n        }\r\n\r\n        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data;\r\n    } catch (err) {\r\n      setError(err.message);\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [baseUrl]);\r\n\r\n  // GET request\r\n  const get = useCallback((endpoint, token = null) => {\r\n    const headers = token ? { 'Authorization': token } : {};\r\n    return apiCall(endpoint, { method: 'GET', headers });\r\n  }, [apiCall]);\r\n\r\n  // POST request\r\n  const post = useCallback((endpoint, data, token = null) => {\r\n    const headers = {\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': token } : {})\r\n    };\r\n    return apiCall(endpoint, {\r\n      method: 'POST',\r\n      headers,\r\n      body: JSON.stringify(data),\r\n    });\r\n  }, [apiCall]);\r\n\r\n  // POST request with FormData (for file uploads)\r\n  const postFormData = useCallback((endpoint, formData, token = null) => {\r\n    const headers = token ? { 'Authorization': token } : {};\r\n    // Don't set Content-Type for FormData, let browser set it with boundary\r\n    delete headers['Content-Type'];\r\n    return apiCall(endpoint, {\r\n      method: 'POST',\r\n      headers,\r\n      body: formData,\r\n    });\r\n  }, [apiCall]);\r\n\r\n  // PUT request\r\n  const put = useCallback((endpoint, data, token = null) => {\r\n    const headers = {\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': token } : {})\r\n    };\r\n    return apiCall(endpoint, {\r\n      method: 'PUT',\r\n      headers,\r\n      body: JSON.stringify(data),\r\n    });\r\n  }, [apiCall]);\r\n\r\n  // PATCH request\r\n  const patch = useCallback((endpoint, data, token = null) => {\r\n    const headers = {\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': token } : {})\r\n    };\r\n    return apiCall(endpoint, {\r\n      method: 'PATCH',\r\n      headers,\r\n      body: JSON.stringify(data),\r\n    });\r\n  }, [apiCall]);\r\n\r\n  // Clear error\r\n  const clearError = useCallback(() => {\r\n    setError(null);\r\n  }, []);\r\n\r\n  return {\r\n    loading,\r\n    error,\r\n    get,\r\n    post,\r\n    postFormData,\r\n    put,\r\n    patch,\r\n    clearError,\r\n  };\r\n};\r\n\r\nexport default useApi;\r\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC7C,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,UAAU,QAAQ,4BAA4B;;AAEvD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAGA,CAACC,OAAO,GAAGJ,YAAY,KAAK;EAAAK,EAAA;EAChD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMY,OAAO,GAAGX,WAAW,CAAC,OAAOY,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,UAAU,GAAG,CAAC,KAAK;IAC5E,MAAMC,UAAU,GAAG,CAAC;IACpB,MAAMC,SAAS,GAAG,IAAI,CAAC,CAAC;;IAExBR,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMO,GAAG,GAAG,GAAGZ,OAAO,GAAGO,QAAQ,EAAE;MACnC,MAAMM,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,GAAGN,OAAO,CAACM;QACb,CAAC;QACD,GAAGN;MACL,CAAC;MAED,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEC,MAAM,CAAC;MAEzC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;QAChB;QACA,IAAIF,QAAQ,CAACG,MAAM,KAAK,GAAG,IAAIT,UAAU,GAAGC,UAAU,EAAE;UACtD,MAAMS,UAAU,GAAGC,QAAQ,CAACL,QAAQ,CAACD,OAAO,CAACO,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;UAC5E,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACL,UAAU,GAAG,IAAI,EAAER,SAAS,GAAGY,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEhB,UAAU,CAAC,CAAC;UAE9EiB,OAAO,CAACC,GAAG,CAAC,6BAA6BL,KAAK,GAAC,IAAI,wBAAwBb,UAAU,GAAG,CAAC,IAAIC,UAAU,GAAG,CAAC;UAE3G,MAAM,IAAIkB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEP,KAAK,CAAC,CAAC;UACxD,OAAOhB,OAAO,CAACC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,GAAG,CAAC,CAAC;QACnD;QAEA,MAAMsB,SAAS,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAIlB,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;UAC3B,MAAM,IAAIgB,KAAK,CAAC,8CAA8C,CAAC;QACjE;QAEA,MAAM,IAAIA,KAAK,CAACH,SAAS,CAAC3B,KAAK,IAAI,uBAAuBW,QAAQ,CAACG,MAAM,EAAE,CAAC;MAC9E;MAEA,MAAMiB,IAAI,GAAG,MAAMpB,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAClC,OAAOG,IAAI;IACb,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ/B,QAAQ,CAAC+B,GAAG,CAACC,OAAO,CAAC;MACrB,MAAMD,GAAG;IACX,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACH,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMqB,GAAG,GAAG1B,WAAW,CAAC,CAACY,QAAQ,EAAE+B,KAAK,GAAG,IAAI,KAAK;IAClD,MAAMxB,OAAO,GAAGwB,KAAK,GAAG;MAAE,eAAe,EAAEA;IAAM,CAAC,GAAG,CAAC,CAAC;IACvD,OAAOhC,OAAO,CAACC,QAAQ,EAAE;MAAEgC,MAAM,EAAE,KAAK;MAAEzB;IAAQ,CAAC,CAAC;EACtD,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMkC,IAAI,GAAG7C,WAAW,CAAC,CAACY,QAAQ,EAAE4B,IAAI,EAAEG,KAAK,GAAG,IAAI,KAAK;IACzD,MAAMxB,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAEA;MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IACD,OAAOhC,OAAO,CAACC,QAAQ,EAAE;MACvBgC,MAAM,EAAE,MAAM;MACdzB,OAAO;MACP2B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMsC,YAAY,GAAGjD,WAAW,CAAC,CAACY,QAAQ,EAAEsC,QAAQ,EAAEP,KAAK,GAAG,IAAI,KAAK;IACrE,MAAMxB,OAAO,GAAGwB,KAAK,GAAG;MAAE,eAAe,EAAEA;IAAM,CAAC,GAAG,CAAC,CAAC;IACvD;IACA,OAAOxB,OAAO,CAAC,cAAc,CAAC;IAC9B,OAAOR,OAAO,CAACC,QAAQ,EAAE;MACvBgC,MAAM,EAAE,MAAM;MACdzB,OAAO;MACP2B,IAAI,EAAEI;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvC,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMwC,GAAG,GAAGnD,WAAW,CAAC,CAACY,QAAQ,EAAE4B,IAAI,EAAEG,KAAK,GAAG,IAAI,KAAK;IACxD,MAAMxB,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAEA;MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IACD,OAAOhC,OAAO,CAACC,QAAQ,EAAE;MACvBgC,MAAM,EAAE,KAAK;MACbzB,OAAO;MACP2B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMyC,KAAK,GAAGpD,WAAW,CAAC,CAACY,QAAQ,EAAE4B,IAAI,EAAEG,KAAK,GAAG,IAAI,KAAK;IAC1D,MAAMxB,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAEA;MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IACD,OAAOhC,OAAO,CAACC,QAAQ,EAAE;MACvBgC,MAAM,EAAE,OAAO;MACfzB,OAAO;MACP2B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC;;EAEb;EACA,MAAM0C,UAAU,GAAGrD,WAAW,CAAC,MAAM;IACnCU,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLH,OAAO;IACPE,KAAK;IACLiB,GAAG;IACHmB,IAAI;IACJI,YAAY;IACZE,GAAG;IACHC,KAAK;IACLC;EACF,CAAC;AACH,CAAC;AAAC/C,EAAA,CA/HWF,MAAM;AAiInB,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}