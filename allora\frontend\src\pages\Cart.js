import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '../config/api';
import { useCart } from '../contexts/CartContext';
import { useDialog } from '../contexts/DialogContext';
import { useError } from '../contexts/ErrorContext';
import { useLoading } from '../contexts/LoadingContext';
import SmartBundles from '../components/SmartBundles';
import SavedCartManager from '../components/SavedCartManager';
import TaxCalculator from '../components/TaxCalculator';
import CouponManager from '../components/CouponManager';
import { ProductImage } from '../components/EnhancedImage';
import { LoadingWrapper, LoadingButton } from '../components/LoadingComponents';
import { formatPrice } from '../utils/currency';
import {
  ShoppingBag,
  Plus,
  Minus,
  Trash2,
  Heart,
  ArrowRight,
  ShoppingCart,
  Sparkles,
  Gift,
  Star,
  TrendingUp,
  Package,
  CreditCard,
  Shield,
  Truck,
  Clock,
  Tag,
  Percent
} from 'lucide-react';

const Cart = ({ token }) => {
  const { cartItems, loading: cartLoading, fetchCart, updateCartItem, removeFromCart, guestSession } = useCart();
  const { showModal, closeAllDialogs, showSuccess } = useDialog();
  const { addError, clearError, handleApiError } = useError();
  const { setLoading: setGlobalLoading, clearLoading, withLoading } = useLoading();
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  const [taxAmount, setTaxAmount] = useState(0);
  const [defaultAddress, setDefaultAddress] = useState(null);
  const [appliedCoupon, setAppliedCoupon] = useState(null);
  const [couponDiscount, setCouponDiscount] = useState(0);
  const [isCheckingOut, setIsCheckingOut] = useState(false);

  useEffect(() => {
    // Set loading to false when cart context loading is done
    setLoading(cartLoading);

    // Fetch default address for authenticated users
    if (token) {
      fetchDefaultAddress();
    }
  }, [token, cartLoading]);

  const fetchDefaultAddress = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/addresses`, {
        headers: { 'Authorization': token }
      });

      if (response.ok) {
        const addresses = await response.json();
        const defaultAddr = addresses.find(addr => addr.is_default) || addresses[0];
        setDefaultAddress(defaultAddr);
      }
    } catch (error) {
      console.error('Failed to fetch default address:', error);
    }
  };

  const handleUpdateQuantity = async (cartItemId, newQuantity) => {
    try {
      const success = await withLoading(
        `update_cart_${cartItemId}`,
        () => updateCartItem(cartItemId, newQuantity),
        'Updating quantity...'
      );
      if (!success) {
        addError('cart_update', new Error('Failed to update cart item'), {
          type: 'validation',
          severity: 'medium'
        });
      }
    } catch (err) {
      handleApiError(err, { action: 'update_cart_quantity', cartItemId });
    }
  };

  const handleRemoveItem = async (cartItemId) => {
    try {
      const success = await withLoading(
        `remove_cart_${cartItemId}`,
        () => removeFromCart(cartItemId),
        'Removing item...'
      );
      if (!success) {
        addError('cart_remove', new Error('Failed to remove cart item'), {
          type: 'validation',
          severity: 'medium'
        });
      }
    } catch (err) {
      handleApiError(err, { action: 'remove_cart_item', cartItemId });
    }
  };

  const handleBundleAdded = (addedItems) => {
    // Refresh cart to show newly added items
    fetchCart();
  };

  const handleTaxCalculated = (taxData) => {
    setTaxAmount(taxData.tax_amount);
  };

  const handleCouponApplied = (coupon, discount) => {
    setAppliedCoupon(coupon);
    setCouponDiscount(discount);
  };

  const handleCouponRemoved = () => {
    setAppliedCoupon(null);
    setCouponDiscount(0);
  };

  // Handle guest checkout with required email
  const handleGuestCheckout = async () => {
    try {
      // Show email prompt dialog and wait for user input
      const email = await showEmailPromptDialog();
      if (email && email.trim()) {
        // Store email for guest checkout
        localStorage.setItem('guest_checkout_email', email.trim());
        setIsCheckingOut(true);

        // Save email to guest session in database
        await saveGuestEmailToDatabase(email.trim());

        // Show success confirmation and wait for user acknowledgment
        await showCheckoutConfirmationDialog(email.trim());

        // Proceed to checkout after user confirms
        window.location.href = '/guest-checkout';
      }
      // If no email provided, stay on cart page
    } catch (error) {
      // User cancelled the dialog, stay on cart page
      console.log('User cancelled guest checkout email prompt');
    }
  };

  const handleAuthenticatedCheckout = () => {
    setIsCheckingOut(true);
    window.location.href = '/checkout';
  };

  // Save guest email to database
  const saveGuestEmailToDatabase = async (email) => {
    try {
      // Get or create guest session
      let sessionId = guestSession || localStorage.getItem('guest_session_id');

      if (!sessionId) {
        // Create new guest session with email
        const response = await fetch(`${API_BASE_URL}/guest/session`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email })
        });

        if (response.ok) {
          const data = await response.json();
          sessionId = data.session_id;
          localStorage.setItem('guest_session_id', sessionId);
        }
      } else {
        // Update existing guest session with email
        await fetch(`${API_BASE_URL}/guest/session/${sessionId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email })
        });
      }
    } catch (error) {
      console.error('Failed to save guest email to database:', error);
      // Don't block checkout if database save fails
    }
  };

  // Modern email prompt dialog for guest checkout
  const showEmailPromptDialog = () => {
    return new Promise((resolve, reject) => {
      let emailInput = '';

      const handleCancel = () => {
        closeAllDialogs();
        reject(new Error('User cancelled'));
      };

      const handleContinue = () => {
        if (emailInput.trim()) {
          closeAllDialogs();
          resolve(emailInput.trim());
        } else {
          // Show validation error
          const input = document.querySelector('input[type="email"]');
          if (input) {
            input.focus();
            input.style.borderColor = '#ef4444';
            input.placeholder = 'Email is required!';
          }
        }
      };

      showModal({
        title: '📧 Guest Checkout',
        size: 'md',
        preventClose: true, // Remove X button and prevent closing
        onClose: () => {
          reject(new Error('User cancelled'));
        },
        children: (
          <div className="space-y-4">
            <p className="text-gray-600">
              Please provide your email address to continue with guest checkout. We'll use this to send you order updates and confirmation.
            </p>
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Email Address
              </label>
              <input
                type="email"
                placeholder="<EMAIL>"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                onChange={(e) => { emailInput = e.target.value; }}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleContinue();
                  }
                }}
                autoFocus
              />
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleContinue}
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
              >
                Continue to Checkout
              </button>
            </div>
          </div>
        )
      });
    });
  };

  // Success confirmation dialog for guest checkout
  const showCheckoutConfirmationDialog = (email) => {
    return new Promise((resolve, reject) => {
      const handleContinue = () => {
        closeAllDialogs();
        resolve();
      };

      showModal({
        title: '✅ Email Confirmed',
        size: 'md',
        preventClose: true, // Remove X button and prevent closing
        onClose: () => {
          resolve(); // Still proceed even if somehow closed
        },
        children: (
          <div className="space-y-4">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Ready to Checkout!
              </h3>
              <p className="text-gray-600 mb-4">
                We've saved your email address: <span className="font-medium text-blue-600">{email}</span>
              </p>
              <p className="text-sm text-gray-500">
                You'll receive order updates and confirmation at this email address.
              </p>
            </div>

            <div className="flex justify-center pt-4">
              <button
                onClick={handleContinue}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
              >
                Continue to Checkout
              </button>
            </div>
          </div>
        )
      });
    });
  };







  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  const subtotal = Array.isArray(cartItems) ? cartItems.reduce((total, item) => total + item.price * item.quantity, 0) : 0;
  const totalPrice = subtotal + taxAmount - couponDiscount;

  // Enhanced Empty Cart Component
  const EmptyCartComponent = () => (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* Main Empty Cart Illustration */}
          <div className="relative mb-8">
            <div className="w-48 h-48 mx-auto bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full flex items-center justify-center shadow-2xl">
              <div className="relative">
                <ShoppingBag className="w-24 h-24 text-blue-600 animate-bounce" />
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">0</span>
                </div>
              </div>
            </div>

            {/* Floating Elements */}
            <div className="absolute top-4 left-8 animate-float">
              <div className="w-12 h-12 bg-yellow-200 rounded-full flex items-center justify-center shadow-lg">
                <Sparkles className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
            <div className="absolute top-12 right-12 animate-float delay-300">
              <div className="w-10 h-10 bg-pink-200 rounded-full flex items-center justify-center shadow-lg">
                <Heart className="w-5 h-5 text-pink-600" />
              </div>
            </div>
            <div className="absolute bottom-8 left-16 animate-float delay-700">
              <div className="w-14 h-14 bg-green-200 rounded-full flex items-center justify-center shadow-lg">
                <Gift className="w-7 h-7 text-green-600" />
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="space-y-6">
            <div>
              <h1 className="text-4xl font-bold text-gray-800 mb-4">
                Your Cart is <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Empty</span>
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed">
                Looks like you haven't added anything to your cart yet.
                <br />
                Discover amazing products waiting for you!
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
              <button
                onClick={() => window.location.href = '/'}
                className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold text-lg shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3"
              >
                <ShoppingCart className="w-6 h-6" />
                Start Shopping
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>

              <button
                onClick={() => window.location.href = '/categories'}
                className="px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-full font-semibold text-lg hover:border-blue-500 hover:text-blue-600 transition-all duration-300 flex items-center gap-3"
              >
                <Package className="w-6 h-6" />
                Browse Categories
              </button>
            </div>

            {/* Feature Cards */}
            <div className="grid md:grid-cols-3 gap-6 mt-16">
              <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/50">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Truck className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">Free Shipping</h3>
                <p className="text-gray-600 text-sm">Free delivery on orders above ₹999</p>
              </div>

              <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/50">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Shield className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">Secure Payment</h3>
                <p className="text-gray-600 text-sm">100% secure payment gateway</p>
              </div>

              <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/50">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <Clock className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-800 mb-2">Quick Delivery</h3>
                <p className="text-gray-600 text-sm">Fast delivery within 2-3 days</p>
              </div>
            </div>

            {/* Popular Categories */}
            <div className="mt-16">
              <h2 className="text-2xl font-bold text-gray-800 mb-8">Popular Categories</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  { name: 'Electronics', icon: '📱', color: 'from-blue-500 to-cyan-500' },
                  { name: 'Fashion', icon: '👗', color: 'from-pink-500 to-rose-500' },
                  { name: 'Home & Garden', icon: '🏠', color: 'from-green-500 to-emerald-500' },
                  { name: 'Sports', icon: '⚽', color: 'from-orange-500 to-red-500' }
                ].map((category, index) => (
                  <button
                    key={index}
                    onClick={() => window.location.href = `/categories/${category.name.toLowerCase().replace(' & ', '-').replace(' ', '-')}`}
                    className={`group relative p-6 bg-gradient-to-br ${category.color} rounded-2xl text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300`}
                  >
                    <div className="text-3xl mb-2">{category.icon}</div>
                    <div className="font-semibold">{category.name}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {!Array.isArray(cartItems) || cartItems.length === 0 ? (
        <EmptyCartComponent />
      ) : (
        <div className="relative z-10 container mx-auto px-4 py-8">
          {/* Modern Cart Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-gray-800 mb-2">
                  Shopping <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Cart</span>
                </h1>
                <p className="text-gray-600">
                  {cartItems.length} item{cartItems.length !== 1 ? 's' : ''} in your cart
                </p>
              </div>
              <div className="hidden md:flex items-center space-x-4">
                <div className="flex items-center space-x-2 text-green-600">
                  <Shield className="w-5 h-5" />
                  <span className="text-sm font-medium">Secure Checkout</span>
                </div>
                <div className="flex items-center space-x-2 text-blue-600">
                  <Truck className="w-5 h-5" />
                  <span className="text-sm font-medium">Free Shipping</span>
                </div>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Cart Items Section */}
            <div className="lg:col-span-2">
              <div className="space-y-4">
                {cartItems.map((item, index) => (
                  <div key={item.id} className="group relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/50">
                    {/* Premium Badge for First Item */}
                    {index === 0 && (
                      <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                        <Star className="w-3 h-3" />
                        FEATURED
                      </div>
                    )}

                    <div className="flex items-start space-x-6">
                      {/* Product Image */}
                      <div className="relative flex-shrink-0">
                        <div className="w-32 h-32 rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 shadow-lg">
                          <img
                            src={getProductImageUrl(item)}
                            alt={item.product_name || 'Product Image'}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            onError={(e) => handleImageError(e)}
                          />
                        </div>

                        {/* Wishlist Button */}
                        <button className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-red-50 transition-colors group">
                          <Heart className="w-4 h-4 text-gray-400 group-hover:text-red-500 transition-colors" />
                        </button>
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h3 className="text-xl font-bold text-gray-800 mb-1 line-clamp-2">
                              {item.product_name}
                            </h3>
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <span>SKU: #{item.id}</span>
                              <span>•</span>
                              <span className="text-green-600 font-medium">In Stock</span>
                            </div>
                          </div>

                          {/* Remove Button */}
                          <button
                            onClick={() => handleRemoveItem(item.id)}
                            className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-all duration-200"
                            title="Remove item"
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </div>

                        {/* Price and Quantity */}
                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <div className="text-2xl font-bold text-gray-800">
                              {formatPrice(item.price)}
                            </div>
                            <div className="text-sm text-gray-500">
                              per item
                            </div>
                          </div>

                          {/* Quantity Controls */}
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center bg-gray-100 rounded-full p-1">
                              <button
                                onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                                disabled={item.quantity <= 1}
                                className="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                              >
                                <Minus className="w-4 h-4 text-gray-600" />
                              </button>

                              <div className="w-16 text-center">
                                <span className="text-lg font-semibold text-gray-800">
                                  {item.quantity}
                                </span>
                              </div>

                              <button
                                onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                                className="w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center hover:bg-gray-50 transition-all duration-200"
                              >
                                <Plus className="w-4 h-4 text-gray-600" />
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* Item Total */}
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Item Total:</span>
                            <span className="text-xl font-bold text-blue-600">
                              {formatPrice(item.price * item.quantity)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-6">
                {/* Order Summary Card */}
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-800">Order Summary</h2>
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <ShoppingBag className="w-5 h-5 text-white" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    {/* Subtotal */}
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600">Subtotal ({cartItems.length} items)</span>
                      <span className="font-semibold text-gray-800">{formatPrice(subtotal)}</span>
                    </div>

                    {/* Shipping */}
                    <div className="flex justify-between items-center py-2">
                      <div className="flex items-center space-x-2">
                        <Truck className="w-4 h-4 text-green-600" />
                        <span className="text-gray-600">Shipping</span>
                      </div>
                      <span className="font-semibold text-green-600">FREE</span>
                    </div>

                    {/* Coupon Discount */}
                    {couponDiscount > 0 && (
                      <div className="flex justify-between items-center py-2 text-green-600">
                        <div className="flex items-center space-x-2">
                          <Tag className="w-4 h-4" />
                          <span>Discount ({appliedCoupon?.code})</span>
                        </div>
                        <span className="font-semibold">-{formatPrice(couponDiscount)}</span>
                      </div>
                    )}

                    {/* Tax */}
                    {taxAmount > 0 && (
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600">Tax</span>
                        <span className="font-semibold text-gray-800">{formatPrice(taxAmount)}</span>
                      </div>
                    )}

                    {/* Divider */}
                    <div className="border-t border-gray-200 my-4"></div>

                    {/* Total */}
                    <div className="flex justify-between items-center py-2">
                      <span className="text-xl font-bold text-gray-800">Total</span>
                      <span className="text-2xl font-bold text-blue-600">{formatPrice(totalPrice)}</span>
                    </div>

                    {/* Savings Badge */}
                    {couponDiscount > 0 && (
                      <div className="bg-gradient-to-r from-green-100 to-emerald-100 border border-green-200 rounded-lg p-3 mt-4">
                        <div className="flex items-center space-x-2">
                          <Percent className="w-5 h-5 text-green-600" />
                          <span className="text-green-800 font-semibold">
                            You saved {formatPrice(couponDiscount)}!
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Checkout Options */}
                <div className="space-y-4">
                  {token ? (
                    <button
                      onClick={handleAuthenticatedCheckout}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-2xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center justify-center space-x-3"
                    >
                      <CreditCard className="w-6 h-6" />
                      <span>Proceed to Checkout</span>
                      <ArrowRight className="w-5 h-5" />
                    </button>
                  ) : (
                    <div className="space-y-4">
                      <button
                        onClick={handleGuestCheckout}
                        className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 px-6 rounded-2xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center justify-center space-x-3"
                      >
                        <ShoppingCart className="w-6 h-6" />
                        <span>Continue as Guest</span>
                        <ArrowRight className="w-5 h-5" />
                      </button>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <div className="w-full border-t border-gray-300"></div>
                        </div>
                        <div className="relative flex justify-center text-sm">
                          <span className="px-4 bg-white text-gray-500">or</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <button
                          onClick={() => window.location.href = '/login'}
                          className="bg-blue-600 text-white py-3 px-4 rounded-xl hover:bg-blue-700 transition-colors font-semibold text-sm"
                        >
                          Sign In
                        </button>
                        <button
                          onClick={() => window.location.href = '/signup'}
                          className="bg-gray-600 text-white py-3 px-4 rounded-xl hover:bg-gray-700 transition-colors font-semibold text-sm"
                        >
                          Sign Up
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Security Features */}
                <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-xl p-4 mt-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <Shield className="w-5 h-5 text-green-600" />
                    <span className="font-semibold text-green-800">Secure Checkout</span>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>SSL Encrypted</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>PCI Compliant</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Money Back</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>24/7 Support</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Features Section */}
          <div className="mt-12 space-y-8">
            {/* Saved Cart Manager - Only for authenticated users */}
            {token && (
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                    <Heart className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800">Saved Carts</h3>
                </div>
                <SavedCartManager token={token} />
              </div>
            )}

            {/* Coupon Manager - For both authenticated and guest users */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                  <Tag className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800">Apply Coupon</h3>
              </div>
              <CouponManager
                cartTotal={subtotal}
                userId={token ? JSON.parse(localStorage.getItem('user'))?.id : null}
                guestSessionId={guestSession}
                onCouponApplied={handleCouponApplied}
                appliedCoupon={appliedCoupon}
                onCouponRemoved={handleCouponRemoved}
              />
            </div>

            {/* Tax Calculator - Only for authenticated users with address */}
            {token && defaultAddress && (
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center">
                    <Percent className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800">Tax Information</h3>
                </div>
                <TaxCalculator
                  cartItems={cartItems}
                  selectedAddress={defaultAddress}
                  onTaxCalculated={handleTaxCalculated}
                />
              </div>
            )}

            {/* Smart Bundles - Show bundle suggestions */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                  <Package className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800">Recommended Bundles</h3>
              </div>
              <SmartBundles token={token} onBundleAdded={handleBundleAdded} />
            </div>
          </div>
        </div>
      )}

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }

        .animate-float {
          animation: float 3s ease-in-out infinite;
        }

        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default Cart;