#!/usr/bin/env python3
"""
Elasticsearch Setup and Initialization Script
Sets up Elasticsearch indices and performs initial data synchronization
"""

import os
import sys
import time
import logging
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from elasticsearch_config import get_elasticsearch_client, es_config
from elasticsearch_manager import get_elasticsearch_manager, get_elasticsearch_sync_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_elasticsearch_connection():
    """Check if Elasticsearch is running and accessible"""
    try:
        es = get_elasticsearch_client()
        if es.ping():
            info = es.info()
            logger.info(f"Connected to Elasticsearch {info['version']['number']}")
            return True
        else:
            logger.error("Cannot connect to Elasticsearch")
            return False
    except Exception as e:
        logger.error(f"Elasticsearch connection error: {e}")
        return False

def setup_indices():
    """Create all required Elasticsearch indices"""
    try:
        logger.info("Setting up Elasticsearch indices...")
        
        es_manager = get_elasticsearch_manager()
        
        # Create indices
        if es_manager.create_indices():
            logger.info("Successfully created all indices")
            
            # Get index statistics
            stats = es_manager.get_index_stats()
            for index_name, index_stats in stats.items():
                if 'document_count' in index_stats:
                    logger.info(f"Index {index_name}: {index_stats['document_count']} documents, "
                              f"Health: {index_stats['health']}")
                else:
                    logger.info(f"Index {index_name}: {index_stats.get('status', 'unknown')}")
            
            return True
        else:
            logger.error("Failed to create indices")
            return False
            
    except Exception as e:
        logger.error(f"Error setting up indices: {e}")
        return False

def initial_data_sync():
    """Perform initial data synchronization from PostgreSQL to Elasticsearch"""
    try:
        logger.info("Starting initial data synchronization...")
        
        es_manager = get_elasticsearch_manager()
        
        # Reindex all products
        if es_manager.reindex_products():
            logger.info("Successfully reindexed all products")
            
            # Update search suggestions
            if es_manager.update_suggestions():
                logger.info("Successfully updated search suggestions")
            else:
                logger.warning("Failed to update search suggestions")
            
            # Get final statistics
            stats = es_manager.get_index_stats()
            product_count = stats.get(es_manager.product_index, {}).get('document_count', 0)
            suggestions_count = stats.get(es_manager.suggestions_index, {}).get('document_count', 0)
            
            logger.info(f"Indexed {product_count} products and {suggestions_count} suggestions")
            return True
        else:
            logger.error("Failed to reindex products")
            return False
            
    except Exception as e:
        logger.error(f"Error during initial data sync: {e}")
        return False

def verify_setup():
    """Verify that Elasticsearch setup is working correctly"""
    try:
        logger.info("Verifying Elasticsearch setup...")
        
        es = get_elasticsearch_client()
        es_manager = get_elasticsearch_manager()
        
        # Check if indices exist and have data
        indices_to_check = [
            es_manager.product_index,
            es_manager.analytics_index,
            es_manager.suggestions_index
        ]
        
        all_good = True
        for index in indices_to_check:
            if es.indices.exists(index=index):
                count = es.count(index=index)['count']
                health = es.cluster.health(index=index)['status']
                logger.info(f"✓ Index {index}: {count} documents, health: {health}")
                
                if index == es_manager.product_index and count == 0:
                    logger.warning(f"⚠ Product index is empty - this might be expected for a new installation")
            else:
                logger.error(f"✗ Index {index} does not exist")
                all_good = False
        
        # Test a simple search
        try:
            search_result = es.search(
                index=es_manager.product_index,
                body={
                    "query": {"match_all": {}},
                    "size": 1
                }
            )
            logger.info(f"✓ Search test successful - found {search_result['hits']['total']['value']} total products")
        except Exception as e:
            logger.warning(f"⚠ Search test failed: {e}")
        
        return all_good
        
    except Exception as e:
        logger.error(f"Error during verification: {e}")
        return False

def reset_elasticsearch():
    """Reset Elasticsearch by deleting and recreating all indices"""
    try:
        logger.warning("Resetting Elasticsearch - this will delete all data!")
        
        # Ask for confirmation
        response = input("Are you sure you want to reset Elasticsearch? (yes/no): ")
        if response.lower() != 'yes':
            logger.info("Reset cancelled")
            return False
        
        es_manager = get_elasticsearch_manager()
        
        # Delete indices
        if es_manager.delete_indices():
            logger.info("Successfully deleted all indices")
            
            # Wait a moment for deletion to complete
            time.sleep(2)
            
            # Recreate indices
            if setup_indices():
                logger.info("Successfully recreated indices")
                return True
            else:
                logger.error("Failed to recreate indices")
                return False
        else:
            logger.error("Failed to delete indices")
            return False
            
    except Exception as e:
        logger.error(f"Error during reset: {e}")
        return False

def main():
    """Main setup function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Elasticsearch Setup for Allora')
    parser.add_argument('--reset', action='store_true', help='Reset Elasticsearch (delete and recreate indices)')
    parser.add_argument('--verify-only', action='store_true', help='Only verify the setup without making changes')
    parser.add_argument('--no-sync', action='store_true', help='Skip initial data synchronization')
    
    args = parser.parse_args()
    
    logger.info("Starting Elasticsearch setup for Allora...")
    
    # Check connection first
    if not check_elasticsearch_connection():
        logger.error("Cannot proceed without Elasticsearch connection")
        sys.exit(1)
    
    # Handle reset option
    if args.reset:
        if not reset_elasticsearch():
            logger.error("Reset failed")
            sys.exit(1)
        return
    
    # Handle verify-only option
    if args.verify_only:
        if verify_setup():
            logger.info("✓ Elasticsearch setup verification passed")
            sys.exit(0)
        else:
            logger.error("✗ Elasticsearch setup verification failed")
            sys.exit(1)
    
    # Normal setup process
    success = True
    
    # Setup indices
    if not setup_indices():
        success = False
    
    # Initial data sync (unless skipped)
    if success and not args.no_sync:
        if not initial_data_sync():
            success = False
    
    # Verify setup
    if success:
        if verify_setup():
            logger.info("🎉 Elasticsearch setup completed successfully!")
        else:
            logger.warning("⚠ Setup completed but verification found issues")
            success = False
    
    if success:
        logger.info("\nNext steps:")
        logger.info("1. Start your Flask application")
        logger.info("2. Test search functionality at /api/search")
        logger.info("3. Monitor search analytics at /api/admin/search-analytics")
        sys.exit(0)
    else:
        logger.error("❌ Elasticsearch setup failed")
        sys.exit(1)

if __name__ == '__main__':
    main()
