"""
Advanced ML Recommendation Models
=================================

Comprehensive machine learning models for product recommendations including:
- Collaborative Filtering (User-based & Item-based)
- Content-based Filtering
- Matrix Factorization
- Deep Learning Models
- Hybrid Recommendation System

Author: Allora Development Team
Date: 2025-07-06
"""

import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import NMF
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error
import pickle
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
from collections import defaultdict

# Import architecture components
from recommendation_system_architecture import (
    RecommendationAlgorithm, RecommendationResult, UserProfile
)

class CollaborativeFilteringModel:
    """
    Collaborative Filtering implementation with both user-based and item-based approaches
    """
    
    def __init__(self, method='item_based', similarity_metric='cosine', min_interactions=5):
        self.method = method  # 'user_based' or 'item_based'
        self.similarity_metric = similarity_metric
        self.min_interactions = min_interactions
        self.similarity_matrix = None
        self.user_item_matrix = None
        self.item_means = None
        self.user_means = None
        self.trained = False
    
    def prepare_data(self, interactions_df):
        """Prepare user-item interaction matrix"""
        # Create user-item matrix
        self.user_item_matrix = interactions_df.pivot_table(
            index='user_id', 
            columns='product_id', 
            values='rating',
            fill_value=0
        )
        
        # Calculate means for bias correction
        self.user_means = self.user_item_matrix.mean(axis=1)
        self.item_means = self.user_item_matrix.mean(axis=0)
        
        return self.user_item_matrix
    
    def train(self, interactions_df):
        """Train collaborative filtering model"""
        try:
            # Prepare data
            self.prepare_data(interactions_df)
            
            if self.method == 'user_based':
                # User-based collaborative filtering
                self.similarity_matrix = self._calculate_user_similarity()
            else:
                # Item-based collaborative filtering
                self.similarity_matrix = self._calculate_item_similarity()
            
            self.trained = True
            logging.info(f"Collaborative filtering model trained successfully ({self.method})")
            
        except Exception as e:
            logging.error(f"Error training collaborative filtering model: {e}")
            raise
    
    def _calculate_user_similarity(self):
        """Calculate user-user similarity matrix"""
        # Normalize by user mean
        user_matrix_normalized = self.user_item_matrix.sub(self.user_means, axis=0)
        
        # Calculate cosine similarity
        similarity_matrix = cosine_similarity(user_matrix_normalized.fillna(0))
        
        return pd.DataFrame(
            similarity_matrix,
            index=self.user_item_matrix.index,
            columns=self.user_item_matrix.index
        )
    
    def _calculate_item_similarity(self):
        """Calculate item-item similarity matrix"""
        # Normalize by item mean
        item_matrix_normalized = self.user_item_matrix.sub(self.item_means, axis=1)
        
        # Calculate cosine similarity
        similarity_matrix = cosine_similarity(item_matrix_normalized.fillna(0).T)
        
        return pd.DataFrame(
            similarity_matrix,
            index=self.user_item_matrix.columns,
            columns=self.user_item_matrix.columns
        )
    
    def predict(self, user_id, product_id):
        """Predict rating for user-item pair"""
        if not self.trained:
            raise ValueError("Model must be trained before making predictions")
        
        if self.method == 'user_based':
            return self._predict_user_based(user_id, product_id)
        else:
            return self._predict_item_based(user_id, product_id)
    
    def _predict_user_based(self, user_id, product_id):
        """User-based prediction"""
        if user_id not in self.user_item_matrix.index:
            return self.item_means.get(product_id, 3.0)  # Default rating
        
        if product_id not in self.user_item_matrix.columns:
            return self.user_means.get(user_id, 3.0)
        
        # Get users who rated this item
        item_ratings = self.user_item_matrix[product_id]
        rated_users = item_ratings[item_ratings > 0].index
        
        if len(rated_users) == 0:
            return self.user_means.get(user_id, 3.0)
        
        # Get similarities with these users
        user_similarities = self.similarity_matrix.loc[user_id, rated_users]
        
        # Calculate weighted average
        numerator = sum(user_similarities * (item_ratings[rated_users] - self.user_means[rated_users]))
        denominator = sum(abs(user_similarities))
        
        if denominator == 0:
            return self.user_means.get(user_id, 3.0)
        
        prediction = self.user_means[user_id] + (numerator / denominator)
        return max(1.0, min(5.0, prediction))  # Clamp to rating range
    
    def _predict_item_based(self, user_id, product_id):
        """Item-based prediction"""
        if user_id not in self.user_item_matrix.index:
            return self.item_means.get(product_id, 3.0)
        
        if product_id not in self.user_item_matrix.columns:
            return self.user_means.get(user_id, 3.0)
        
        # Get items rated by this user
        user_ratings = self.user_item_matrix.loc[user_id]
        rated_items = user_ratings[user_ratings > 0].index
        
        if len(rated_items) == 0:
            return self.item_means.get(product_id, 3.0)
        
        # Get similarities with these items
        item_similarities = self.similarity_matrix.loc[product_id, rated_items]
        
        # Calculate weighted average
        numerator = sum(item_similarities * (user_ratings[rated_items] - self.item_means[rated_items]))
        denominator = sum(abs(item_similarities))
        
        if denominator == 0:
            return self.item_means.get(product_id, 3.0)
        
        prediction = self.item_means[product_id] + (numerator / denominator)
        return max(1.0, min(5.0, prediction))
    
    def get_recommendations(self, user_id, n_recommendations=10, exclude_rated=True):
        """Get top N recommendations for a user"""
        if not self.trained:
            raise ValueError("Model must be trained before making recommendations")
        
        if user_id not in self.user_item_matrix.index:
            # Return popular items for new users
            popular_items = self.item_means.sort_values(ascending=False).head(n_recommendations)
            return [(item, score) for item, score in popular_items.items()]
        
        # Get all items
        all_items = self.user_item_matrix.columns
        user_ratings = self.user_item_matrix.loc[user_id]
        
        # Exclude already rated items if requested
        if exclude_rated:
            unrated_items = all_items[user_ratings == 0]
        else:
            unrated_items = all_items
        
        # Predict ratings for unrated items
        predictions = []
        for item in unrated_items:
            predicted_rating = self.predict(user_id, item)
            predictions.append((item, predicted_rating))
        
        # Sort by predicted rating
        predictions.sort(key=lambda x: x[1], reverse=True)
        
        return predictions[:n_recommendations]

class ContentBasedModel:
    """
    Content-based filtering using product features
    """
    
    def __init__(self, features=['name', 'description', 'category', 'brand']):
        self.features = features
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.feature_matrix = None
        self.product_features = None
        self.similarity_matrix = None
        self.trained = False
    
    def prepare_features(self, products_df):
        """Prepare product feature matrix"""
        # Combine text features
        text_features = []
        for _, product in products_df.iterrows():
            combined_text = ""
            for feature in self.features:
                if feature in product and pd.notna(product[feature]):
                    combined_text += str(product[feature]) + " "
            text_features.append(combined_text.strip())
        
        return text_features
    
    def train(self, products_df):
        """Train content-based model"""
        try:
            self.product_features = products_df.copy()
            
            # Prepare text features
            text_features = self.prepare_features(products_df)
            
            # Create TF-IDF matrix
            self.feature_matrix = self.tfidf_vectorizer.fit_transform(text_features)
            
            # Calculate similarity matrix
            self.similarity_matrix = cosine_similarity(self.feature_matrix)
            
            self.trained = True
            logging.info("Content-based model trained successfully")
            
        except Exception as e:
            logging.error(f"Error training content-based model: {e}")
            raise
    
    def get_similar_items(self, product_id, n_similar=10):
        """Get similar items to a given product"""
        if not self.trained:
            raise ValueError("Model must be trained before making recommendations")
        
        try:
            # Find product index
            product_idx = self.product_features[
                self.product_features['id'] == product_id
            ].index[0]
            
            # Get similarity scores
            similarity_scores = self.similarity_matrix[product_idx]
            
            # Get top similar items
            similar_indices = similarity_scores.argsort()[::-1][1:n_similar+1]  # Exclude self
            
            recommendations = []
            for idx in similar_indices:
                similar_product_id = self.product_features.iloc[idx]['id']
                similarity_score = similarity_scores[idx]
                recommendations.append((similar_product_id, similarity_score))
            
            return recommendations
            
        except Exception as e:
            logging.error(f"Error getting similar items: {e}")
            return []
    
    def get_recommendations(self, user_profile, n_recommendations=10):
        """Get recommendations based on user profile"""
        if not self.trained:
            raise ValueError("Model must be trained before making recommendations")
        
        # Get user preferences
        category_prefs = user_profile.get('categories', {})
        brand_prefs = user_profile.get('brands', {})
        
        # Score products based on preferences
        product_scores = []
        
        for _, product in self.product_features.iterrows():
            score = 0.0
            
            # Category preference
            if product.get('category') in category_prefs:
                score += category_prefs[product['category']] * 0.4
            
            # Brand preference
            if product.get('brand') in brand_prefs:
                score += brand_prefs[product['brand']] * 0.3
            
            # Add base popularity score
            score += product.get('average_rating', 3.0) * 0.3
            
            product_scores.append((product['id'], score))
        
        # Sort by score
        product_scores.sort(key=lambda x: x[1], reverse=True)
        
        return product_scores[:n_recommendations]

class MatrixFactorizationModel:
    """
    Matrix Factorization using Non-negative Matrix Factorization (NMF)
    """
    
    def __init__(self, n_factors=50, max_iter=200, random_state=42):
        self.n_factors = n_factors
        self.max_iter = max_iter
        self.random_state = random_state
        self.model = NMF(
            n_components=n_factors,
            max_iter=max_iter,
            random_state=random_state
        )
        self.user_factors = None
        self.item_factors = None
        self.user_item_matrix = None
        self.trained = False
    
    def train(self, interactions_df):
        """Train matrix factorization model"""
        try:
            # Create user-item matrix
            self.user_item_matrix = interactions_df.pivot_table(
                index='user_id',
                columns='product_id',
                values='rating',
                fill_value=0
            )
            
            # Fit NMF model
            self.user_factors = self.model.fit_transform(self.user_item_matrix)
            self.item_factors = self.model.components_
            
            self.trained = True
            logging.info("Matrix factorization model trained successfully")
            
        except Exception as e:
            logging.error(f"Error training matrix factorization model: {e}")
            raise
    
    def predict(self, user_id, product_id):
        """Predict rating for user-item pair"""
        if not self.trained:
            raise ValueError("Model must be trained before making predictions")
        
        try:
            user_idx = self.user_item_matrix.index.get_loc(user_id)
            item_idx = self.user_item_matrix.columns.get_loc(product_id)
            
            prediction = np.dot(self.user_factors[user_idx], self.item_factors[:, item_idx])
            return max(1.0, min(5.0, prediction))
            
        except (KeyError, ValueError):
            return 3.0  # Default rating for unknown user/item
    
    def get_recommendations(self, user_id, n_recommendations=10, exclude_rated=True):
        """Get top N recommendations for a user"""
        if not self.trained:
            raise ValueError("Model must be trained before making recommendations")
        
        try:
            user_idx = self.user_item_matrix.index.get_loc(user_id)
            user_ratings = self.user_item_matrix.iloc[user_idx]
            
            # Predict ratings for all items
            predictions = np.dot(self.user_factors[user_idx], self.item_factors)
            
            # Create list of (item_id, predicted_rating)
            recommendations = []
            for i, item_id in enumerate(self.user_item_matrix.columns):
                if exclude_rated and user_ratings.iloc[i] > 0:
                    continue  # Skip already rated items
                
                recommendations.append((item_id, predictions[i]))
            
            # Sort by predicted rating
            recommendations.sort(key=lambda x: x[1], reverse=True)
            
            return recommendations[:n_recommendations]
            
        except (KeyError, ValueError):
            # Return popular items for unknown users
            item_popularity = self.user_item_matrix.mean(axis=0).sort_values(ascending=False)
            return [(item, score) for item, score in item_popularity.head(n_recommendations).items()]

class HybridRecommendationModel:
    """
    Hybrid model combining multiple recommendation approaches
    """
    
    def __init__(self, weights=None):
        self.weights = weights or {
            'collaborative': 0.4,
            'content': 0.3,
            'matrix_factorization': 0.3
        }
        
        self.collaborative_model = CollaborativeFilteringModel()
        self.content_model = ContentBasedModel()
        self.matrix_factorization_model = MatrixFactorizationModel()
        
        self.trained = False
    
    def train(self, interactions_df, products_df):
        """Train all component models"""
        try:
            # Train collaborative filtering
            if len(interactions_df) >= 10:  # Minimum data requirement
                self.collaborative_model.train(interactions_df)
            
            # Train content-based model
            self.content_model.train(products_df)
            
            # Train matrix factorization
            if len(interactions_df) >= 20:  # Minimum data requirement
                self.matrix_factorization_model.train(interactions_df)
            
            self.trained = True
            logging.info("Hybrid recommendation model trained successfully")
            
        except Exception as e:
            logging.error(f"Error training hybrid model: {e}")
            raise
    
    def get_recommendations(self, user_id, user_profile=None, n_recommendations=10):
        """Get hybrid recommendations"""
        if not self.trained:
            raise ValueError("Model must be trained before making recommendations")
        
        all_recommendations = defaultdict(float)
        
        # Get recommendations from each model
        try:
            # Collaborative filtering recommendations
            if self.collaborative_model.trained:
                collab_recs = self.collaborative_model.get_recommendations(
                    user_id, n_recommendations * 2
                )
                for item_id, score in collab_recs:
                    all_recommendations[item_id] += score * self.weights['collaborative']
        except:
            pass
        
        try:
            # Content-based recommendations
            if user_profile and self.content_model.trained:
                content_recs = self.content_model.get_recommendations(
                    user_profile, n_recommendations * 2
                )
                for item_id, score in content_recs:
                    all_recommendations[item_id] += score * self.weights['content']
        except:
            pass
        
        try:
            # Matrix factorization recommendations
            if self.matrix_factorization_model.trained:
                mf_recs = self.matrix_factorization_model.get_recommendations(
                    user_id, n_recommendations * 2
                )
                for item_id, score in mf_recs:
                    all_recommendations[item_id] += score * self.weights['matrix_factorization']
        except:
            pass
        
        # Sort by combined score
        final_recommendations = sorted(
            all_recommendations.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return final_recommendations[:n_recommendations]
    
    def evaluate(self, test_interactions_df):
        """Evaluate model performance"""
        predictions = []
        actuals = []
        
        for _, interaction in test_interactions_df.iterrows():
            user_id = interaction['user_id']
            product_id = interaction['product_id']
            actual_rating = interaction['rating']
            
            # Get prediction from collaborative filtering (primary model)
            try:
                predicted_rating = self.collaborative_model.predict(user_id, product_id)
                predictions.append(predicted_rating)
                actuals.append(actual_rating)
            except:
                continue
        
        if len(predictions) > 0:
            mse = mean_squared_error(actuals, predictions)
            mae = mean_absolute_error(actuals, predictions)
            
            return {
                'mse': mse,
                'mae': mae,
                'rmse': np.sqrt(mse),
                'n_predictions': len(predictions)
            }
        
        return {'error': 'No valid predictions made'}

class RecommendationModelManager:
    """
    Manager for training, evaluating, and serving recommendation models
    """

    def __init__(self, model_dir='models'):
        self.model_dir = model_dir
        self.models = {}
        self.model_performance = {}
        self.last_training_time = {}

        # Initialize models
        self.models['collaborative_user'] = CollaborativeFilteringModel(method='user_based')
        self.models['collaborative_item'] = CollaborativeFilteringModel(method='item_based')
        self.models['content_based'] = ContentBasedModel()
        self.models['matrix_factorization'] = MatrixFactorizationModel()
        self.models['hybrid'] = HybridRecommendationModel()

    def prepare_training_data(self, db_session):
        """Prepare training data from database"""
        try:
            # Get interaction data
            from app import UserInteractionLog, Product, User

            # Query interactions with ratings
            interactions_query = db_session.query(
                UserInteractionLog.user_id,
                UserInteractionLog.product_id,
                UserInteractionLog.interaction_value.label('rating'),
                UserInteractionLog.timestamp
            ).filter(
                UserInteractionLog.interaction_type.in_(['rating', 'purchase']),
                UserInteractionLog.interaction_value.isnot(None)
            )

            interactions_df = pd.read_sql(interactions_query.statement, db_session.bind)

            # Get product data
            products_query = db_session.query(Product)
            products_df = pd.read_sql(products_query.statement, db_session.bind)

            return interactions_df, products_df

        except Exception as e:
            logging.error(f"Error preparing training data: {e}")
            return pd.DataFrame(), pd.DataFrame()

    def train_all_models(self, db_session):
        """Train all recommendation models"""
        try:
            # Prepare data
            interactions_df, products_df = self.prepare_training_data(db_session)

            if len(interactions_df) < 10:
                logging.warning("Insufficient interaction data for training")
                return False

            # Split data for evaluation
            train_interactions, test_interactions = train_test_split(
                interactions_df, test_size=0.2, random_state=42
            )

            training_results = {}

            # Train individual models
            for model_name, model in self.models.items():
                if model_name == 'hybrid':
                    continue  # Train hybrid model separately

                try:
                    start_time = datetime.now()

                    if model_name.startswith('collaborative') or model_name == 'matrix_factorization':
                        model.train(train_interactions)
                    elif model_name == 'content_based':
                        model.train(products_df)

                    training_time = (datetime.now() - start_time).total_seconds()

                    # Evaluate model
                    if hasattr(model, 'evaluate'):
                        performance = model.evaluate(test_interactions)
                    else:
                        performance = self._evaluate_model(model, test_interactions)

                    training_results[model_name] = {
                        'training_time': training_time,
                        'performance': performance,
                        'trained': True
                    }

                    self.last_training_time[model_name] = datetime.now()

                    logging.info(f"Model {model_name} trained successfully")

                except Exception as e:
                    logging.error(f"Error training model {model_name}: {e}")
                    training_results[model_name] = {
                        'error': str(e),
                        'trained': False
                    }

            # Train hybrid model
            try:
                self.models['hybrid'].train(train_interactions, products_df)
                hybrid_performance = self.models['hybrid'].evaluate(test_interactions)

                training_results['hybrid'] = {
                    'performance': hybrid_performance,
                    'trained': True
                }

                self.last_training_time['hybrid'] = datetime.now()

            except Exception as e:
                logging.error(f"Error training hybrid model: {e}")
                training_results['hybrid'] = {
                    'error': str(e),
                    'trained': False
                }

            # Save models
            self.save_models()

            return training_results

        except Exception as e:
            logging.error(f"Error in train_all_models: {e}")
            return False

    def _evaluate_model(self, model, test_interactions):
        """Evaluate a single model"""
        try:
            predictions = []
            actuals = []

            for _, interaction in test_interactions.iterrows():
                try:
                    predicted = model.predict(interaction['user_id'], interaction['product_id'])
                    predictions.append(predicted)
                    actuals.append(interaction['rating'])
                except:
                    continue

            if len(predictions) > 0:
                mse = mean_squared_error(actuals, predictions)
                mae = mean_absolute_error(actuals, predictions)

                return {
                    'mse': mse,
                    'mae': mae,
                    'rmse': np.sqrt(mse),
                    'n_predictions': len(predictions)
                }

            return {'error': 'No valid predictions'}

        except Exception as e:
            return {'error': str(e)}

    def get_recommendations(self, user_id, algorithm='hybrid', user_profile=None, n_recommendations=10):
        """Get recommendations using specified algorithm"""
        try:
            if algorithm not in self.models:
                algorithm = 'hybrid'  # Fallback

            model = self.models[algorithm]

            if not hasattr(model, 'trained') or not model.trained:
                # Fallback to popular items
                return self._get_popular_recommendations(n_recommendations)

            if algorithm == 'hybrid':
                return model.get_recommendations(user_id, user_profile, n_recommendations)
            elif algorithm == 'content_based':
                return model.get_recommendations(user_profile or {}, n_recommendations)
            else:
                return model.get_recommendations(user_id, n_recommendations)

        except Exception as e:
            logging.error(f"Error getting recommendations: {e}")
            return self._get_popular_recommendations(n_recommendations)

    def _get_popular_recommendations(self, n_recommendations=10):
        """Fallback to popular items"""
        try:
            # This would typically query the database for popular items
            # For now, return empty list
            return []
        except:
            return []

    def save_models(self):
        """Save trained models to disk"""
        try:
            import os
            os.makedirs(self.model_dir, exist_ok=True)

            for model_name, model in self.models.items():
                if hasattr(model, 'trained') and model.trained:
                    model_path = os.path.join(self.model_dir, f'{model_name}_model.pkl')
                    with open(model_path, 'wb') as f:
                        pickle.dump(model, f)

            # Save metadata
            metadata = {
                'last_training_time': {k: v.isoformat() for k, v in self.last_training_time.items()},
                'model_performance': self.model_performance
            }

            metadata_path = os.path.join(self.model_dir, 'model_metadata.json')
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)

            logging.info("Models saved successfully")

        except Exception as e:
            logging.error(f"Error saving models: {e}")

    def load_models(self):
        """Load trained models from disk"""
        try:
            import os

            for model_name in self.models.keys():
                model_path = os.path.join(self.model_dir, f'{model_name}_model.pkl')
                if os.path.exists(model_path):
                    with open(model_path, 'rb') as f:
                        self.models[model_name] = pickle.load(f)

            # Load metadata
            metadata_path = os.path.join(self.model_dir, 'model_metadata.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)

                self.last_training_time = {
                    k: datetime.fromisoformat(v)
                    for k, v in metadata.get('last_training_time', {}).items()
                }
                self.model_performance = metadata.get('model_performance', {})

            logging.info("Models loaded successfully")
            return True

        except Exception as e:
            logging.error(f"Error loading models: {e}")
            return False

    def get_model_status(self):
        """Get status of all models"""
        status = {}

        for model_name, model in self.models.items():
            status[model_name] = {
                'trained': hasattr(model, 'trained') and model.trained,
                'last_training': self.last_training_time.get(model_name),
                'performance': self.model_performance.get(model_name, {})
            }

        return status

    def needs_retraining(self, max_age_hours=24):
        """Check if models need retraining"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        for model_name, last_training in self.last_training_time.items():
            if last_training < cutoff_time:
                return True

        return False

# Export main classes
__all__ = [
    'CollaborativeFilteringModel',
    'ContentBasedModel',
    'MatrixFactorizationModel',
    'HybridRecommendationModel',
    'RecommendationModelManager'
]
