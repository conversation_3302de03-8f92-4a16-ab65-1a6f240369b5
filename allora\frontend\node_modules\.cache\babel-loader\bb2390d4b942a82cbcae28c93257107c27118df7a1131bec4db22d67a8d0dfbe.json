{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\components\\\\ErrorComponents.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useError } from '../contexts/ErrorContext';\nimport { AlertTriangle, X, RefreshCw, Info, AlertCircle, XCircle, CheckCircle } from 'lucide-react';\n\n// Error severity icons and colors\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ERROR_STYLES = {\n  low: {\n    icon: Info,\n    bgColor: 'bg-blue-50',\n    borderColor: 'border-blue-200',\n    textColor: 'text-blue-800',\n    iconColor: 'text-blue-500'\n  },\n  medium: {\n    icon: AlertCircle,\n    bgColor: 'bg-yellow-50',\n    borderColor: 'border-yellow-200',\n    textColor: 'text-yellow-800',\n    iconColor: 'text-yellow-500'\n  },\n  high: {\n    icon: AlertTriangle,\n    bgColor: 'bg-orange-50',\n    borderColor: 'border-orange-200',\n    textColor: 'text-orange-800',\n    iconColor: 'text-orange-500'\n  },\n  critical: {\n    icon: XCircle,\n    bgColor: 'bg-red-50',\n    borderColor: 'border-red-200',\n    textColor: 'text-red-800',\n    iconColor: 'text-red-500'\n  }\n};\n\n// Individual error display component\nexport const ErrorAlert = ({\n  error,\n  onDismiss,\n  onRetry,\n  showDetails = false,\n  className = ''\n}) => {\n  const {\n    severity = 'medium',\n    type,\n    message,\n    retryable\n  } = error;\n  const style = ERROR_STYLES[severity];\n  const Icon = style.icon;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${style.bgColor} ${style.borderColor} border rounded-lg p-4 ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        className: `${style.iconColor} w-5 h-5 mt-0.5 mr-3 flex-shrink-0`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: `${style.textColor} font-medium text-sm`,\n            children: type ? `${type.charAt(0).toUpperCase() + type.slice(1)} Error` : 'Error'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [retryable && onRetry && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onRetry,\n              className: `${style.textColor} hover:opacity-75 transition-opacity`,\n              title: \"Retry\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this), onDismiss && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onDismiss,\n              className: `${style.textColor} hover:opacity-75 transition-opacity`,\n              title: \"Dismiss\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `${style.textColor} text-sm mt-1`,\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), showDetails && error.context && /*#__PURE__*/_jsxDEV(\"details\", {\n          className: \"mt-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            className: `${style.textColor} text-xs cursor-pointer hover:underline`,\n            children: \"Show details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            className: `${style.textColor} text-xs mt-1 bg-white bg-opacity-50 p-2 rounded overflow-auto`,\n            children: JSON.stringify(error.context, null, 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n\n// Global error display component\n_c = ErrorAlert;\nexport const GlobalErrorDisplay = ({\n  position = 'top-right',\n  maxErrors = 5,\n  autoHide = true,\n  hideDelay = 5000\n}) => {\n  _s();\n  const {\n    errors,\n    dismissError,\n    retryError\n  } = useError();\n  const visibleErrors = errors.slice(0, maxErrors);\n  if (visibleErrors.length === 0) return null;\n  const positionClasses = {\n    'top-right': 'fixed top-4 right-4 z-50',\n    'top-left': 'fixed top-4 left-4 z-50',\n    'bottom-right': 'fixed bottom-4 right-4 z-50',\n    'bottom-left': 'fixed bottom-4 left-4 z-50',\n    'top-center': 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50',\n    'bottom-center': 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${positionClasses[position]} space-y-2 max-w-md`,\n    children: [visibleErrors.map(error => /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      error: error,\n      onDismiss: () => dismissError(error.id),\n      onRetry: error.retryable ? () => retryError(error.id) : null,\n      className: \"animate-slide-in-right shadow-lg\"\n    }, error.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this)), errors.length > maxErrors && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-100 border border-gray-200 rounded-lg p-2 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-600 text-sm\",\n        children: [\"+\", errors.length - maxErrors, \" more errors\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n\n// Error boundary fallback component\n_s(GlobalErrorDisplay, \"gBK9ITdrRe5Wt/GU7yFTgAUo1bA=\", false, function () {\n  return [useError];\n});\n_c2 = GlobalErrorDisplay;\nexport const ErrorBoundaryFallback = ({\n  error,\n  resetError,\n  componentStack\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-bold text-gray-900 mb-2\",\n          children: \"Something went wrong\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: \"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetError,\n            className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.reload(),\n            className: \"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors\",\n            children: \"Refresh Page\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), (error || componentStack) && /*#__PURE__*/_jsxDEV(\"details\", {\n          className: \"mt-6 text-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            className: \"text-sm text-gray-500 cursor-pointer hover:underline\",\n            children: \"Technical Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 p-3 bg-gray-100 rounded text-xs font-mono overflow-auto max-h-32\",\n            children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-red-600 mb-2\",\n              children: error.message || 'Unknown error occurred'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 19\n            }, this), componentStack && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: componentStack\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n\n// Page-level error component\n_c3 = ErrorBoundaryFallback;\nexport const PageError = ({\n  error,\n  onRetry,\n  title = 'Something went wrong',\n  description = 'An error occurred while loading this page.'\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        className: \"w-16 h-16 text-yellow-500 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900 mb-2\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-800 text-sm\",\n          children: error.message || 'Unknown error occurred'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [onRetry && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onRetry,\n          className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), \"Try Again\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.history.back(),\n          className: \"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors\",\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n\n// Inline error component for forms and sections\n_c4 = PageError;\nexport const InlineError = ({\n  error,\n  className = '',\n  showIcon = true\n}) => {\n  if (!error) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center text-red-600 text-sm mt-1 ${className}`,\n    children: [showIcon && /*#__PURE__*/_jsxDEV(AlertCircle, {\n      className: \"w-4 h-4 mr-1 flex-shrink-0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 20\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: error.message || error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 255,\n    columnNumber: 5\n  }, this);\n};\n\n// Success message component for consistency\n_c5 = InlineError;\nexport const SuccessAlert = ({\n  message,\n  onDismiss,\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-green-50 border border-green-200 rounded-lg p-4 ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start\",\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        className: \"text-green-500 w-5 h-5 mt-0.5 mr-3 flex-shrink-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-800 text-sm\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), onDismiss && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onDismiss,\n        className: \"text-green-800 hover:opacity-75 transition-opacity ml-3\",\n        children: /*#__PURE__*/_jsxDEV(X, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 5\n  }, this);\n};\n_c6 = SuccessAlert;\nexport default {\n  ErrorAlert,\n  GlobalErrorDisplay,\n  ErrorBoundaryFallback,\n  PageError,\n  InlineError,\n  SuccessAlert\n};\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"ErrorAlert\");\n$RefreshReg$(_c2, \"GlobalErrorDisplay\");\n$RefreshReg$(_c3, \"ErrorBoundaryFallback\");\n$RefreshReg$(_c4, \"PageError\");\n$RefreshReg$(_c5, \"InlineError\");\n$RefreshReg$(_c6, \"SuccessAlert\");", "map": {"version": 3, "names": ["React", "useError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "X", "RefreshCw", "Info", "AlertCircle", "XCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "ERROR_STYLES", "low", "icon", "bgColor", "borderColor", "textColor", "iconColor", "medium", "high", "critical", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "on<PERSON><PERSON><PERSON>", "onRetry", "showDetails", "className", "severity", "type", "message", "retryable", "style", "Icon", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "slice", "onClick", "title", "context", "JSON", "stringify", "_c", "GlobalErrorDisplay", "position", "maxErrors", "autoHide", "<PERSON><PERSON><PERSON><PERSON>", "_s", "errors", "dismissError", "retryError", "visibleErrors", "length", "positionClasses", "map", "id", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetError", "componentStack", "window", "location", "reload", "_c3", "Page<PERSON><PERSON><PERSON>", "description", "history", "back", "_c4", "InlineError", "showIcon", "_c5", "<PERSON><PERSON><PERSON><PERSON>", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/components/ErrorComponents.js"], "sourcesContent": ["import React from 'react';\nimport { useError } from '../contexts/ErrorContext';\nimport { AlertTriangle, X, RefreshCw, Info, AlertCircle, XCircle, CheckCircle } from 'lucide-react';\n\n// Error severity icons and colors\nconst ERROR_STYLES = {\n  low: {\n    icon: Info,\n    bgColor: 'bg-blue-50',\n    borderColor: 'border-blue-200',\n    textColor: 'text-blue-800',\n    iconColor: 'text-blue-500'\n  },\n  medium: {\n    icon: AlertCircle,\n    bgColor: 'bg-yellow-50',\n    borderColor: 'border-yellow-200',\n    textColor: 'text-yellow-800',\n    iconColor: 'text-yellow-500'\n  },\n  high: {\n    icon: AlertTriangle,\n    bgColor: 'bg-orange-50',\n    borderColor: 'border-orange-200',\n    textColor: 'text-orange-800',\n    iconColor: 'text-orange-500'\n  },\n  critical: {\n    icon: XCircle,\n    bgColor: 'bg-red-50',\n    borderColor: 'border-red-200',\n    textColor: 'text-red-800',\n    iconColor: 'text-red-500'\n  }\n};\n\n// Individual error display component\nexport const ErrorAlert = ({ \n  error, \n  onDismiss, \n  onRetry, \n  showDetails = false,\n  className = '' \n}) => {\n  const { severity = 'medium', type, message, retryable } = error;\n  const style = ERROR_STYLES[severity];\n  const Icon = style.icon;\n\n  return (\n    <div className={`${style.bgColor} ${style.borderColor} border rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <Icon className={`${style.iconColor} w-5 h-5 mt-0.5 mr-3 flex-shrink-0`} />\n        \n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className={`${style.textColor} font-medium text-sm`}>\n              {type ? `${type.charAt(0).toUpperCase() + type.slice(1)} Error` : 'Error'}\n            </h3>\n            \n            <div className=\"flex items-center space-x-2\">\n              {retryable && onRetry && (\n                <button\n                  onClick={onRetry}\n                  className={`${style.textColor} hover:opacity-75 transition-opacity`}\n                  title=\"Retry\"\n                >\n                  <RefreshCw className=\"w-4 h-4\" />\n                </button>\n              )}\n              \n              {onDismiss && (\n                <button\n                  onClick={onDismiss}\n                  className={`${style.textColor} hover:opacity-75 transition-opacity`}\n                  title=\"Dismiss\"\n                >\n                  <X className=\"w-4 h-4\" />\n                </button>\n              )}\n            </div>\n          </div>\n          \n          <p className={`${style.textColor} text-sm mt-1`}>\n            {message}\n          </p>\n          \n          {showDetails && error.context && (\n            <details className=\"mt-2\">\n              <summary className={`${style.textColor} text-xs cursor-pointer hover:underline`}>\n                Show details\n              </summary>\n              <pre className={`${style.textColor} text-xs mt-1 bg-white bg-opacity-50 p-2 rounded overflow-auto`}>\n                {JSON.stringify(error.context, null, 2)}\n              </pre>\n            </details>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Global error display component\nexport const GlobalErrorDisplay = ({ \n  position = 'top-right',\n  maxErrors = 5,\n  autoHide = true,\n  hideDelay = 5000 \n}) => {\n  const { errors, dismissError, retryError } = useError();\n  \n  const visibleErrors = errors.slice(0, maxErrors);\n  \n  if (visibleErrors.length === 0) return null;\n  \n  const positionClasses = {\n    'top-right': 'fixed top-4 right-4 z-50',\n    'top-left': 'fixed top-4 left-4 z-50',\n    'bottom-right': 'fixed bottom-4 right-4 z-50',\n    'bottom-left': 'fixed bottom-4 left-4 z-50',\n    'top-center': 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50',\n    'bottom-center': 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50'\n  };\n  \n  return (\n    <div className={`${positionClasses[position]} space-y-2 max-w-md`}>\n      {visibleErrors.map((error) => (\n        <ErrorAlert\n          key={error.id}\n          error={error}\n          onDismiss={() => dismissError(error.id)}\n          onRetry={error.retryable ? () => retryError(error.id) : null}\n          className=\"animate-slide-in-right shadow-lg\"\n        />\n      ))}\n      \n      {errors.length > maxErrors && (\n        <div className=\"bg-gray-100 border border-gray-200 rounded-lg p-2 text-center\">\n          <span className=\"text-gray-600 text-sm\">\n            +{errors.length - maxErrors} more errors\n          </span>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Error boundary fallback component\nexport const ErrorBoundaryFallback = ({ \n  error, \n  resetError, \n  componentStack \n}) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6\">\n        <div className=\"text-center\">\n          <XCircle className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\n          <h1 className=\"text-xl font-bold text-gray-900 mb-2\">\n            Something went wrong\n          </h1>\n          <p className=\"text-gray-600 mb-6\">\n            An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.\n          </p>\n          \n          <div className=\"space-y-3\">\n            <button\n              onClick={resetError}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Try Again\n            </button>\n            \n            <button\n              onClick={() => window.location.reload()}\n              className=\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors\"\n            >\n              Refresh Page\n            </button>\n          </div>\n          \n          {(error || componentStack) && (\n            <details className=\"mt-6 text-left\">\n              <summary className=\"text-sm text-gray-500 cursor-pointer hover:underline\">\n                Technical Details\n              </summary>\n              <div className=\"mt-2 p-3 bg-gray-100 rounded text-xs font-mono overflow-auto max-h-32\">\n                {error && (\n                  <div className=\"text-red-600 mb-2\">{error.message || 'Unknown error occurred'}</div>\n                )}\n                {componentStack && (\n                  <div className=\"text-gray-600\">{componentStack}</div>\n                )}\n              </div>\n            </details>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Page-level error component\nexport const PageError = ({ \n  error, \n  onRetry, \n  title = 'Something went wrong',\n  description = 'An error occurred while loading this page.' \n}) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\n      <div className=\"text-center max-w-md\">\n        <AlertTriangle className=\"w-16 h-16 text-yellow-500 mx-auto mb-4\" />\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">{title}</h1>\n        <p className=\"text-gray-600 mb-6\">{description}</p>\n        \n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left\">\n            <p className=\"text-red-800 text-sm\">{error.message || 'Unknown error occurred'}</p>\n          </div>\n        )}\n        \n        <div className=\"space-y-3\">\n          {onRetry && (\n            <button\n              onClick={onRetry}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\"\n            >\n              <RefreshCw className=\"w-4 h-4 mr-2\" />\n              Try Again\n            </button>\n          )}\n          \n          <button\n            onClick={() => window.history.back()}\n            className=\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors\"\n          >\n            Go Back\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Inline error component for forms and sections\nexport const InlineError = ({ \n  error, \n  className = '',\n  showIcon = true \n}) => {\n  if (!error) return null;\n  \n  return (\n    <div className={`flex items-center text-red-600 text-sm mt-1 ${className}`}>\n      {showIcon && <AlertCircle className=\"w-4 h-4 mr-1 flex-shrink-0\" />}\n      <span>{error.message || error}</span>\n    </div>\n  );\n};\n\n// Success message component for consistency\nexport const SuccessAlert = ({ \n  message, \n  onDismiss, \n  className = '' \n}) => {\n  return (\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <CheckCircle className=\"text-green-500 w-5 h-5 mt-0.5 mr-3 flex-shrink-0\" />\n        \n        <div className=\"flex-1 min-w-0\">\n          <p className=\"text-green-800 text-sm\">{message}</p>\n        </div>\n        \n        {onDismiss && (\n          <button\n            onClick={onDismiss}\n            className=\"text-green-800 hover:opacity-75 transition-opacity ml-3\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default {\n  ErrorAlert,\n  GlobalErrorDisplay,\n  ErrorBoundaryFallback,\n  PageError,\n  InlineError,\n  SuccessAlert\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,aAAa,EAAEC,CAAC,EAAEC,SAAS,EAAEC,IAAI,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,QAAQ,cAAc;;AAEnG;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,GAAG,EAAE;IACHC,IAAI,EAAER,IAAI;IACVS,OAAO,EAAE,YAAY;IACrBC,WAAW,EAAE,iBAAiB;IAC9BC,SAAS,EAAE,eAAe;IAC1BC,SAAS,EAAE;EACb,CAAC;EACDC,MAAM,EAAE;IACNL,IAAI,EAAEP,WAAW;IACjBQ,OAAO,EAAE,cAAc;IACvBC,WAAW,EAAE,mBAAmB;IAChCC,SAAS,EAAE,iBAAiB;IAC5BC,SAAS,EAAE;EACb,CAAC;EACDE,IAAI,EAAE;IACJN,IAAI,EAAEX,aAAa;IACnBY,OAAO,EAAE,cAAc;IACvBC,WAAW,EAAE,mBAAmB;IAChCC,SAAS,EAAE,iBAAiB;IAC5BC,SAAS,EAAE;EACb,CAAC;EACDG,QAAQ,EAAE;IACRP,IAAI,EAAEN,OAAO;IACbO,OAAO,EAAE,WAAW;IACpBC,WAAW,EAAE,gBAAgB;IAC7BC,SAAS,EAAE,cAAc;IACzBC,SAAS,EAAE;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,UAAU,GAAGA,CAAC;EACzBC,KAAK;EACLC,SAAS;EACTC,OAAO;EACPC,WAAW,GAAG,KAAK;EACnBC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAM;IAAEC,QAAQ,GAAG,QAAQ;IAAEC,IAAI;IAAEC,OAAO;IAAEC;EAAU,CAAC,GAAGR,KAAK;EAC/D,MAAMS,KAAK,GAAGpB,YAAY,CAACgB,QAAQ,CAAC;EACpC,MAAMK,IAAI,GAAGD,KAAK,CAAClB,IAAI;EAEvB,oBACEH,OAAA;IAAKgB,SAAS,EAAE,GAAGK,KAAK,CAACjB,OAAO,IAAIiB,KAAK,CAAChB,WAAW,0BAA0BW,SAAS,EAAG;IAAAO,QAAA,eACzFvB,OAAA;MAAKgB,SAAS,EAAC,kBAAkB;MAAAO,QAAA,gBAC/BvB,OAAA,CAACsB,IAAI;QAACN,SAAS,EAAE,GAAGK,KAAK,CAACd,SAAS;MAAqC;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE3E3B,OAAA;QAAKgB,SAAS,EAAC,gBAAgB;QAAAO,QAAA,gBAC7BvB,OAAA;UAAKgB,SAAS,EAAC,mCAAmC;UAAAO,QAAA,gBAChDvB,OAAA;YAAIgB,SAAS,EAAE,GAAGK,KAAK,CAACf,SAAS,sBAAuB;YAAAiB,QAAA,EACrDL,IAAI,GAAG,GAAGA,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGX,IAAI,CAACY,KAAK,CAAC,CAAC,CAAC,QAAQ,GAAG;UAAO;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEL3B,OAAA;YAAKgB,SAAS,EAAC,6BAA6B;YAAAO,QAAA,GACzCH,SAAS,IAAIN,OAAO,iBACnBd,OAAA;cACE+B,OAAO,EAAEjB,OAAQ;cACjBE,SAAS,EAAE,GAAGK,KAAK,CAACf,SAAS,sCAAuC;cACpE0B,KAAK,EAAC,OAAO;cAAAT,QAAA,eAEbvB,OAAA,CAACN,SAAS;gBAACsB,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CACT,EAEAd,SAAS,iBACRb,OAAA;cACE+B,OAAO,EAAElB,SAAU;cACnBG,SAAS,EAAE,GAAGK,KAAK,CAACf,SAAS,sCAAuC;cACpE0B,KAAK,EAAC,SAAS;cAAAT,QAAA,eAEfvB,OAAA,CAACP,CAAC;gBAACuB,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3B,OAAA;UAAGgB,SAAS,EAAE,GAAGK,KAAK,CAACf,SAAS,eAAgB;UAAAiB,QAAA,EAC7CJ;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EAEHZ,WAAW,IAAIH,KAAK,CAACqB,OAAO,iBAC3BjC,OAAA;UAASgB,SAAS,EAAC,MAAM;UAAAO,QAAA,gBACvBvB,OAAA;YAASgB,SAAS,EAAE,GAAGK,KAAK,CAACf,SAAS,yCAA0C;YAAAiB,QAAA,EAAC;UAEjF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACV3B,OAAA;YAAKgB,SAAS,EAAE,GAAGK,KAAK,CAACf,SAAS,gEAAiE;YAAAiB,QAAA,EAChGW,IAAI,CAACC,SAAS,CAACvB,KAAK,CAACqB,OAAO,EAAE,IAAI,EAAE,CAAC;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAS,EAAA,GAjEazB,UAAU;AAkEvB,OAAO,MAAM0B,kBAAkB,GAAGA,CAAC;EACjCC,QAAQ,GAAG,WAAW;EACtBC,SAAS,GAAG,CAAC;EACbC,QAAQ,GAAG,IAAI;EACfC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,MAAM;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAGtD,QAAQ,CAAC,CAAC;EAEvD,MAAMuD,aAAa,GAAGH,MAAM,CAACb,KAAK,CAAC,CAAC,EAAES,SAAS,CAAC;EAEhD,IAAIO,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE3C,MAAMC,eAAe,GAAG;IACtB,WAAW,EAAE,0BAA0B;IACvC,UAAU,EAAE,yBAAyB;IACrC,cAAc,EAAE,6BAA6B;IAC7C,aAAa,EAAE,4BAA4B;IAC3C,YAAY,EAAE,sDAAsD;IACpE,eAAe,EAAE;EACnB,CAAC;EAED,oBACEhD,OAAA;IAAKgB,SAAS,EAAE,GAAGgC,eAAe,CAACV,QAAQ,CAAC,qBAAsB;IAAAf,QAAA,GAC/DuB,aAAa,CAACG,GAAG,CAAErC,KAAK,iBACvBZ,OAAA,CAACW,UAAU;MAETC,KAAK,EAAEA,KAAM;MACbC,SAAS,EAAEA,CAAA,KAAM+B,YAAY,CAAChC,KAAK,CAACsC,EAAE,CAAE;MACxCpC,OAAO,EAAEF,KAAK,CAACQ,SAAS,GAAG,MAAMyB,UAAU,CAACjC,KAAK,CAACsC,EAAE,CAAC,GAAG,IAAK;MAC7DlC,SAAS,EAAC;IAAkC,GAJvCJ,KAAK,CAACsC,EAAE;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKd,CACF,CAAC,EAEDgB,MAAM,CAACI,MAAM,GAAGR,SAAS,iBACxBvC,OAAA;MAAKgB,SAAS,EAAC,+DAA+D;MAAAO,QAAA,eAC5EvB,OAAA;QAAMgB,SAAS,EAAC,uBAAuB;QAAAO,QAAA,GAAC,GACrC,EAACoB,MAAM,CAACI,MAAM,GAAGR,SAAS,EAAC,cAC9B;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAe,EAAA,CA5CaL,kBAAkB;EAAA,QAMgB9C,QAAQ;AAAA;AAAA4D,GAAA,GAN1Cd,kBAAkB;AA6C/B,OAAO,MAAMe,qBAAqB,GAAGA,CAAC;EACpCxC,KAAK;EACLyC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,oBACEtD,OAAA;IAAKgB,SAAS,EAAC,8DAA8D;IAAAO,QAAA,eAC3EvB,OAAA;MAAKgB,SAAS,EAAC,mDAAmD;MAAAO,QAAA,eAChEvB,OAAA;QAAKgB,SAAS,EAAC,aAAa;QAAAO,QAAA,gBAC1BvB,OAAA,CAACH,OAAO;UAACmB,SAAS,EAAC;QAAqC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3D3B,OAAA;UAAIgB,SAAS,EAAC,sCAAsC;UAAAO,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAGgB,SAAS,EAAC,oBAAoB;UAAAO,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJ3B,OAAA;UAAKgB,SAAS,EAAC,WAAW;UAAAO,QAAA,gBACxBvB,OAAA;YACE+B,OAAO,EAAEsB,UAAW;YACpBrC,SAAS,EAAC,wFAAwF;YAAAO,QAAA,EACnG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3B,OAAA;YACE+B,OAAO,EAAEA,CAAA,KAAMwB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCzC,SAAS,EAAC,2FAA2F;YAAAO,QAAA,EACtG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL,CAACf,KAAK,IAAI0C,cAAc,kBACvBtD,OAAA;UAASgB,SAAS,EAAC,gBAAgB;UAAAO,QAAA,gBACjCvB,OAAA;YAASgB,SAAS,EAAC,sDAAsD;YAAAO,QAAA,EAAC;UAE1E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACV3B,OAAA;YAAKgB,SAAS,EAAC,uEAAuE;YAAAO,QAAA,GACnFX,KAAK,iBACJZ,OAAA;cAAKgB,SAAS,EAAC,mBAAmB;cAAAO,QAAA,EAAEX,KAAK,CAACO,OAAO,IAAI;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACpF,EACA2B,cAAc,iBACbtD,OAAA;cAAKgB,SAAS,EAAC,eAAe;cAAAO,QAAA,EAAE+B;YAAc;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACrD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA+B,GAAA,GAtDaN,qBAAqB;AAuDlC,OAAO,MAAMO,SAAS,GAAGA,CAAC;EACxB/C,KAAK;EACLE,OAAO;EACPkB,KAAK,GAAG,sBAAsB;EAC9B4B,WAAW,GAAG;AAChB,CAAC,KAAK;EACJ,oBACE5D,OAAA;IAAKgB,SAAS,EAAC,8DAA8D;IAAAO,QAAA,eAC3EvB,OAAA;MAAKgB,SAAS,EAAC,sBAAsB;MAAAO,QAAA,gBACnCvB,OAAA,CAACR,aAAa;QAACwB,SAAS,EAAC;MAAwC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpE3B,OAAA;QAAIgB,SAAS,EAAC,uCAAuC;QAAAO,QAAA,EAAES;MAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClE3B,OAAA;QAAGgB,SAAS,EAAC,oBAAoB;QAAAO,QAAA,EAAEqC;MAAW;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAElDf,KAAK,iBACJZ,OAAA;QAAKgB,SAAS,EAAC,+DAA+D;QAAAO,QAAA,eAC5EvB,OAAA;UAAGgB,SAAS,EAAC,sBAAsB;UAAAO,QAAA,EAAEX,KAAK,CAACO,OAAO,IAAI;QAAwB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CACN,eAED3B,OAAA;QAAKgB,SAAS,EAAC,WAAW;QAAAO,QAAA,GACvBT,OAAO,iBACNd,OAAA;UACE+B,OAAO,EAAEjB,OAAQ;UACjBE,SAAS,EAAC,yHAAyH;UAAAO,QAAA,gBAEnIvB,OAAA,CAACN,SAAS;YAACsB,SAAS,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eAED3B,OAAA;UACE+B,OAAO,EAAEA,CAAA,KAAMwB,MAAM,CAACM,OAAO,CAACC,IAAI,CAAC,CAAE;UACrC9C,SAAS,EAAC,2FAA2F;UAAAO,QAAA,EACtG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAoC,GAAA,GA1CaJ,SAAS;AA2CtB,OAAO,MAAMK,WAAW,GAAGA,CAAC;EAC1BpD,KAAK;EACLI,SAAS,GAAG,EAAE;EACdiD,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,IAAI,CAACrD,KAAK,EAAE,OAAO,IAAI;EAEvB,oBACEZ,OAAA;IAAKgB,SAAS,EAAE,+CAA+CA,SAAS,EAAG;IAAAO,QAAA,GACxE0C,QAAQ,iBAAIjE,OAAA,CAACJ,WAAW;MAACoB,SAAS,EAAC;IAA4B;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnE3B,OAAA;MAAAuB,QAAA,EAAOX,KAAK,CAACO,OAAO,IAAIP;IAAK;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEV,CAAC;;AAED;AAAAuC,GAAA,GAfaF,WAAW;AAgBxB,OAAO,MAAMG,YAAY,GAAGA,CAAC;EAC3BhD,OAAO;EACPN,SAAS;EACTG,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,oBACEhB,OAAA;IAAKgB,SAAS,EAAE,sDAAsDA,SAAS,EAAG;IAAAO,QAAA,eAChFvB,OAAA;MAAKgB,SAAS,EAAC,kBAAkB;MAAAO,QAAA,gBAC/BvB,OAAA,CAACF,WAAW;QAACkB,SAAS,EAAC;MAAkD;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE5E3B,OAAA;QAAKgB,SAAS,EAAC,gBAAgB;QAAAO,QAAA,eAC7BvB,OAAA;UAAGgB,SAAS,EAAC,wBAAwB;UAAAO,QAAA,EAAEJ;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,EAELd,SAAS,iBACRb,OAAA;QACE+B,OAAO,EAAElB,SAAU;QACnBG,SAAS,EAAC,yDAAyD;QAAAO,QAAA,eAEnEvB,OAAA,CAACP,CAAC;UAACuB,SAAS,EAAC;QAAS;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACyC,GAAA,GAzBWD,YAAY;AA2BzB,eAAe;EACbxD,UAAU;EACV0B,kBAAkB;EAClBe,qBAAqB;EACrBO,SAAS;EACTK,WAAW;EACXG;AACF,CAAC;AAAC,IAAA/B,EAAA,EAAAe,GAAA,EAAAO,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAjC,EAAA;AAAAiC,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}