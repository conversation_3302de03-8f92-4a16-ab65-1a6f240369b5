import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { 
  Package, 
  Search, 
  AlertCircle,
  ArrowLeft,
  ExternalLink,
  Truck,
  CheckCircle,
  Clock,
  MapPin
} from 'lucide-react';
import OrderTracking from '../components/OrderTracking';
import { API_BASE_URL } from '../config/api';

const TrackOrder = () => {
  const { trackingNumber: urlTrackingNumber } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [trackingNumber, setTrackingNumber] = useState(urlTrackingNumber || searchParams.get('tracking') || '');
  const [showTracking, setShowTracking] = useState(false);
  const [recentOrders, setRecentOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // If tracking number is provided in URL, automatically show tracking
    if (urlTrackingNumber) {
      setShowTracking(true);
    }
    
    // Load recent orders for logged-in users
    loadRecentOrders();
  }, [urlTrackingNumber]);

  const loadRecentOrders = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/orders/recent?limit=5`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRecentOrders(data.orders || []);
      }
    } catch (err) {
      console.error('Failed to load recent orders:', err);
    }
  };

  const handleTrackSubmit = (e) => {
    e.preventDefault();
    if (!trackingNumber.trim()) {
      setError('Please enter a tracking number');
      return;
    }
    
    setError(null);
    setShowTracking(true);
    
    // Update URL without page reload
    navigate(`/track/${trackingNumber}`, { replace: true });
  };

  const handleOrderTrack = (order) => {
    if (order.tracking_number) {
      setTrackingNumber(order.tracking_number);
      setShowTracking(true);
      navigate(`/track/${order.tracking_number}`, { replace: true });
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'shipped':
      case 'out_for_delivery':
        return <Truck className="w-5 h-5 text-blue-500" />;
      case 'processing':
      case 'confirmed':
        return <Package className="w-5 h-5 text-yellow-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'shipped':
      case 'out_for_delivery':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
      case 'confirmed':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Package className="w-12 h-12 text-green-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Track Your Order</h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Enter your tracking number to get real-time updates on your order's journey
          </p>
        </div>

        {/* Tracking Number Input */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <form onSubmit={handleTrackSubmit} className="space-y-4">
            <div>
              <label htmlFor="tracking-number" className="block text-sm font-medium text-gray-700 mb-2">
                Tracking Number
              </label>
              <div className="flex space-x-3">
                <input
                  type="text"
                  id="tracking-number"
                  value={trackingNumber}
                  onChange={(e) => setTrackingNumber(e.target.value)}
                  placeholder="Enter your tracking number (e.g., 1Z999AA1234567890)"
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  <Search className="w-5 h-5 mr-2" />
                  Track
                </button>
              </div>
            </div>
            
            {error && (
              <div className="flex items-center text-red-600 text-sm">
                <AlertCircle className="w-4 h-4 mr-2" />
                {error}
              </div>
            )}
          </form>

          {/* Tracking Tips */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-medium text-blue-900 mb-2">Tracking Tips:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Tracking numbers are usually 10-20 characters long</li>
              <li>• You can find your tracking number in your order confirmation email</li>
              <li>• It may take 24-48 hours for tracking information to appear</li>
            </ul>
          </div>
        </div>

        {/* Recent Orders for Logged-in Users */}
        {recentOrders.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Recent Orders</h2>
            <div className="space-y-4">
              {recentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(order.status)}
                    <div>
                      <h3 className="font-medium text-gray-900">Order #{order.order_number}</h3>
                      <p className="text-sm text-gray-600">
                        Placed on {new Date(order.created_at).toLocaleDateString()}
                      </p>
                      {order.tracking_number && (
                        <p className="text-sm text-gray-500">
                          Tracking: {order.tracking_number}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </span>
                    {order.tracking_number ? (
                      <button
                        onClick={() => handleOrderTrack(order)}
                        className="px-3 py-2 text-green-600 hover:text-green-700 text-sm font-medium transition-colors"
                      >
                        Track Order
                      </button>
                    ) : (
                      <span className="px-3 py-2 text-gray-400 text-sm">
                        No tracking yet
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Need Help?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Can't find your tracking number?</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Check your order confirmation email</li>
                <li>• Look for shipping notification emails</li>
                <li>• Log in to your account to view order details</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Tracking not updating?</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Allow 24-48 hours for initial tracking</li>
                <li>• Check with the shipping carrier directly</li>
                <li>• Contact our customer support team</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">Still need assistance?</h3>
                <p className="text-sm text-gray-600">Our customer support team is here to help</p>
              </div>
              <div className="flex space-x-3">
                <a
                  href="mailto:<EMAIL>"
                  className="px-4 py-2 text-green-600 hover:text-green-700 text-sm font-medium transition-colors"
                >
                  Email Support
                </a>
                <a
                  href="tel:1-800-ALLORA"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm font-medium transition-colors"
                >
                  Call Support
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <button
            onClick={() => navigate('/')}
            className="inline-flex items-center text-green-600 hover:text-green-700 font-medium transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Shopping
          </button>
        </div>
      </div>

      {/* Order Tracking Modal */}
      {showTracking && (
        <OrderTracking
          trackingNumber={trackingNumber}
          onClose={() => {
            setShowTracking(false);
            navigate('/track', { replace: true });
          }}
        />
      )}
    </div>
  );
};

export default TrackOrder;
