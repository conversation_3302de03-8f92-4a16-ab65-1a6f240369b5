import React, { createContext, useContext, useState, useCallback } from 'react';
import { useNotification } from './NotificationContext';

const ErrorContext = createContext();

export const useError = () => {
  const context = useContext(ErrorContext);
  if (!context) {
    throw new Error('useError must be used within an ErrorProvider');
  }
  return context;
};

// Error types
export const ERROR_TYPES = {
  NETWORK: 'network',
  VALIDATION: 'validation',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  SERVER: 'server',
  CLIENT: 'client',
  UNKNOWN: 'unknown'
};

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

export const ErrorProvider = ({ children }) => {
  const [errors, setErrors] = useState({});
  const [errorHistory, setErrorHistory] = useState([]);
  const { error: showNotification } = useNotification();

  // Add error to state
  const addError = useCallback((key, error, options = {}) => {
    const errorObj = {
      id: `${key}_${Date.now()}`,
      key,
      message: error.message || error,
      type: options.type || ERROR_TYPES.UNKNOWN,
      severity: options.severity || ERROR_SEVERITY.MEDIUM,
      timestamp: new Date().toISOString(),
      stack: error.stack,
      context: options.context || {},
      retryable: options.retryable || false,
      showNotification: options.showNotification !== false,
      autoResolve: options.autoResolve || false,
      autoResolveDelay: options.autoResolveDelay || 5000
    };

    setErrors(prev => ({
      ...prev,
      [key]: errorObj
    }));

    setErrorHistory(prev => [...prev.slice(-99), errorObj]); // Keep last 100 errors

    // Show notification if enabled
    if (errorObj.showNotification) {
      const notificationOptions = {
        autoClose: errorObj.severity === ERROR_SEVERITY.CRITICAL ? false : 6000
      };
      showNotification(errorObj.message, notificationOptions);
    }

    // Auto-resolve if enabled
    if (errorObj.autoResolve) {
      setTimeout(() => {
        clearError(key);
      }, errorObj.autoResolveDelay);
    }

    // Report error to backend if critical
    if (errorObj.severity === ERROR_SEVERITY.CRITICAL) {
      reportError(errorObj);
    }

    return errorObj.id;
  }, [showNotification]);

  // Clear specific error
  const clearError = useCallback((key) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[key];
      return newErrors;
    });
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Get error by key
  const getError = useCallback((key) => {
    return errors[key];
  }, [errors]);

  // Check if error exists
  const hasError = useCallback((key) => {
    return !!errors[key];
  }, [errors]);

  // Get all errors
  const getAllErrors = useCallback(() => {
    return Object.values(errors);
  }, [errors]);

  // Get errors by type
  const getErrorsByType = useCallback((type) => {
    return Object.values(errors).filter(error => error.type === type);
  }, [errors]);

  // Get errors by severity
  const getErrorsBySeverity = useCallback((severity) => {
    return Object.values(errors).filter(error => error.severity === severity);
  }, [errors]);

  // Handle API errors
  const handleApiError = useCallback((error, context = {}) => {
    let errorType = ERROR_TYPES.UNKNOWN;
    let severity = ERROR_SEVERITY.MEDIUM;
    let message = 'An unexpected error occurred';

    if (error.response) {
      const status = error.response.status;
      
      if (status >= 400 && status < 500) {
        errorType = status === 401 ? ERROR_TYPES.AUTHENTICATION : 
                   status === 403 ? ERROR_TYPES.AUTHORIZATION : 
                   ERROR_TYPES.VALIDATION;
        severity = status === 401 || status === 403 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM;
      } else if (status >= 500) {
        errorType = ERROR_TYPES.SERVER;
        severity = ERROR_SEVERITY.HIGH;
      }

      message = error.response.data?.message || error.response.data?.error || message;
    } else if (error.request) {
      errorType = ERROR_TYPES.NETWORK;
      severity = ERROR_SEVERITY.HIGH;
      message = 'Network error. Please check your connection.';
    }

    return addError('api', error, {
      type: errorType,
      severity,
      context: { ...context, url: error.config?.url },
      retryable: errorType === ERROR_TYPES.NETWORK || errorType === ERROR_TYPES.SERVER
    });
  }, [addError]);

  // Report error to backend
  const reportError = useCallback(async (errorObj) => {
    try {
      const errorData = {
        message: errorObj.message,
        type: errorObj.type,
        severity: errorObj.severity,
        stack: errorObj.stack,
        context: errorObj.context,
        timestamp: errorObj.timestamp,
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('userId') || 'anonymous'
      };

      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData)
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }, []);

  // Retry function for retryable errors
  const retryError = useCallback(async (key, retryFunction) => {
    const error = getError(key);
    if (!error || !error.retryable) {
      return false;
    }

    try {
      clearError(key);
      await retryFunction();
      return true;
    } catch (retryError) {
      addError(key, retryError, {
        type: error.type,
        severity: error.severity,
        context: error.context,
        retryable: true
      });
      return false;
    }
  }, [getError, clearError, addError]);

  const value = {
    // State
    errors,
    errorHistory,
    
    // Error management
    addError,
    clearError,
    clearAllErrors,
    
    // Error queries
    getError,
    hasError,
    getAllErrors,
    getErrorsByType,
    getErrorsBySeverity,
    
    // Specialized handlers
    handleApiError,
    retryError,
    
    // Constants
    ERROR_TYPES,
    ERROR_SEVERITY
  };

  return (
    <ErrorContext.Provider value={value}>
      {children}
    </ErrorContext.Provider>
  );
};

export default ErrorContext;
