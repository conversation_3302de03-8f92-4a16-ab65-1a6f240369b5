import React, { useState, useEffect } from 'react';
import './RecommendationAnalytics.css';

const RecommendationAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [selectedAlgorithm, setSelectedAlgorithm] = useState('');

  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange, selectedAlgorithm]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      });
      
      if (selectedAlgorithm) {
        params.append('algorithm', selectedAlgorithm);
      }

      const response = await fetch(`/api/recommendations/analytics/performance?${params}`);
      const data = await response.json();
      
      if (data.error) {
        setError(data.error);
      } else {
        setAnalyticsData(data);
        setError(null);
      }
    } catch (err) {
      setError('Failed to fetch analytics data');
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(2)}%`;
  };

  const formatNumber = (value) => {
    return (value || 0).toLocaleString();
  };

  const formatCurrency = (value) => {
    return `₹${(value || 0).toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="analytics-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading analytics data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="analytics-container">
        <div className="error-message">
          <h3>Error Loading Analytics</h3>
          <p>{error}</p>
          <button onClick={fetchAnalyticsData} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  const { metrics } = analyticsData || {};

  return (
    <div className="analytics-container">
      <div className="analytics-header">
        <h1>Recommendation Analytics Dashboard</h1>
        <div className="analytics-controls">
          <div className="date-range-controls">
            <label>
              Start Date:
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </label>
            <label>
              End Date:
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </label>
          </div>
          <div className="algorithm-filter">
            <label>
              Algorithm:
              <select
                value={selectedAlgorithm}
                onChange={(e) => setSelectedAlgorithm(e.target.value)}
              >
                <option value="">All Algorithms</option>
                <option value="hybrid">Hybrid</option>
                <option value="collaborative_user">Collaborative (User)</option>
                <option value="collaborative_item">Collaborative (Item)</option>
                <option value="content_based">Content-Based</option>
                <option value="matrix_factorization">Matrix Factorization</option>
              </select>
            </label>
          </div>
        </div>
      </div>

      {metrics && (
        <div className="analytics-content">
          {/* Overview Metrics */}
          <div className="metrics-section">
            <h2>Overview</h2>
            <div className="metrics-grid">
              <div className="metric-card">
                <h3>Total Interactions</h3>
                <div className="metric-value">{formatNumber(metrics.overview?.total_interactions)}</div>
              </div>
              <div className="metric-card">
                <h3>Unique Users</h3>
                <div className="metric-value">{formatNumber(metrics.overview?.unique_users)}</div>
              </div>
              <div className="metric-card">
                <h3>Recommendations Served</h3>
                <div className="metric-value">{formatNumber(metrics.overview?.total_recommendations_served)}</div>
              </div>
              <div className="metric-card">
                <h3>Avg Interactions/User</h3>
                <div className="metric-value">{metrics.overview?.avg_interactions_per_user || 0}</div>
              </div>
            </div>
          </div>

          {/* Click-Through Rates */}
          {metrics.click_through_rates && (
            <div className="metrics-section">
              <h2>Click-Through Rates</h2>
              <div className="metrics-grid">
                <div className="metric-card highlight">
                  <h3>Overall CTR</h3>
                  <div className="metric-value">{formatPercentage(metrics.click_through_rates.overall_ctr)}</div>
                </div>
                <div className="metric-card">
                  <h3>Total Clicks</h3>
                  <div className="metric-value">{formatNumber(metrics.click_through_rates.total_clicks)}</div>
                </div>
              </div>
              
              {metrics.click_through_rates.ctr_by_algorithm && (
                <div className="algorithm-performance">
                  <h3>CTR by Algorithm</h3>
                  <div className="algorithm-grid">
                    {Object.entries(metrics.click_through_rates.ctr_by_algorithm).map(([algorithm, data]) => (
                      <div key={algorithm} className="algorithm-card">
                        <h4>{algorithm.replace('_', ' ').toUpperCase()}</h4>
                        <div className="algorithm-metrics">
                          <span>CTR: {formatPercentage(data.ctr)}</span>
                          <span>Clicks: {formatNumber(data.clicks)}</span>
                          <span>Recommendations: {formatNumber(data.recommendations)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Conversion Rates */}
          {metrics.conversion_rates && (
            <div className="metrics-section">
              <h2>Conversion Rates</h2>
              <div className="metrics-grid">
                <div className="metric-card highlight">
                  <h3>Overall Conversion Rate</h3>
                  <div className="metric-value">{formatPercentage(metrics.conversion_rates.overall_conversion_rate)}</div>
                </div>
                <div className="metric-card">
                  <h3>Total Revenue</h3>
                  <div className="metric-value">{formatCurrency(metrics.conversion_rates.total_revenue)}</div>
                </div>
                <div className="metric-card">
                  <h3>Average Order Value</h3>
                  <div className="metric-value">{formatCurrency(metrics.conversion_rates.average_order_value)}</div>
                </div>
                <div className="metric-card">
                  <h3>Revenue per Recommendation</h3>
                  <div className="metric-value">{formatCurrency(metrics.conversion_rates.revenue_per_recommendation)}</div>
                </div>
              </div>
            </div>
          )}

          {/* Algorithm Performance */}
          {metrics.algorithm_performance && (
            <div className="metrics-section">
              <h2>Algorithm Performance</h2>
              <div className="algorithm-comparison">
                {Object.entries(metrics.algorithm_performance).map(([algorithm, performance]) => (
                  <div key={algorithm} className="algorithm-performance-card">
                    <h3>{algorithm.replace('_', ' ').toUpperCase()}</h3>
                    <div className="performance-metrics">
                      <div className="performance-row">
                        <span>CTR:</span>
                        <span>{formatPercentage(performance.ctr)}</span>
                      </div>
                      <div className="performance-row">
                        <span>Conversion Rate:</span>
                        <span>{formatPercentage(performance.conversion_rate)}</span>
                      </div>
                      <div className="performance-row">
                        <span>Revenue/Request:</span>
                        <span>{formatCurrency(performance.revenue_per_request)}</span>
                      </div>
                      <div className="performance-row">
                        <span>Unique Users:</span>
                        <span>{formatNumber(performance.unique_users)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* User Engagement */}
          {metrics.user_engagement && (
            <div className="metrics-section">
              <h2>User Engagement</h2>
              <div className="metrics-grid">
                <div className="metric-card">
                  <h3>Total Users</h3>
                  <div className="metric-value">{formatNumber(metrics.user_engagement.total_users)}</div>
                </div>
                <div className="metric-card">
                  <h3>Avg Sessions/User</h3>
                  <div className="metric-value">{metrics.user_engagement.avg_sessions_per_user || 0}</div>
                </div>
                <div className="metric-card">
                  <h3>Avg Products/User</h3>
                  <div className="metric-value">{metrics.user_engagement.avg_products_per_user || 0}</div>
                </div>
              </div>
              
              {metrics.user_engagement.engagement_segments && (
                <div className="engagement-segments">
                  <h3>User Engagement Segments</h3>
                  <div className="segments-grid">
                    {Object.entries(metrics.user_engagement.engagement_segments).map(([segment, count]) => (
                      <div key={segment} className="segment-card">
                        <h4>{segment.replace('_', ' ').toUpperCase()}</h4>
                        <div className="segment-count">{formatNumber(count)}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Optimization Recommendations */}
          {metrics.recommendations && metrics.recommendations.length > 0 && (
            <div className="metrics-section">
              <h2>Optimization Recommendations</h2>
              <div className="recommendations-list">
                {metrics.recommendations.map((rec, index) => (
                  <div key={index} className={`recommendation-card priority-${rec.priority}`}>
                    <div className="recommendation-header">
                      <h3>{rec.title}</h3>
                      <span className={`priority-badge priority-${rec.priority}`}>
                        {rec.priority.toUpperCase()}
                      </span>
                    </div>
                    <p>{rec.description}</p>
                    <div className="recommendation-meta">
                      <span>Impact: {rec.impact}</span>
                      <span>Effort: {rec.effort}</span>
                      <span>Type: {rec.type.replace('_', ' ')}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Temporal Patterns */}
          {metrics.temporal_analysis && (
            <div className="metrics-section">
              <h2>Temporal Patterns</h2>
              <div className="temporal-insights">
                <div className="insight-card">
                  <h3>Peak Activity</h3>
                  <p>Peak Hour: {metrics.temporal_analysis.peak_hour}:00</p>
                  <p>Peak Day: {metrics.temporal_analysis.peak_day}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RecommendationAnalytics;
