{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\components\\\\LoadingComponents.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useLoading } from '../contexts/LoadingContext';\n\n// Basic loading spinner\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LoadingSpinner = ({\n  size = 'medium',\n  color = 'primary',\n  className = ''\n}) => {\n  const sizeClasses = {\n    small: 'w-4 h-4',\n    medium: 'w-8 h-8',\n    large: 'w-12 h-12',\n    xlarge: 'w-16 h-16'\n  };\n  const colorClasses = {\n    primary: 'border-green-500',\n    secondary: 'border-gray-500',\n    white: 'border-white',\n    dark: 'border-gray-800'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent ${sizeClasses[size]} ${colorClasses[color]} ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"sr-only\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n\n// Loading overlay for full screen\n_c = LoadingSpinner;\nexport const LoadingOverlay = ({\n  message = 'Loading...',\n  transparent = false\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `fixed inset-0 z-50 flex items-center justify-center ${transparent ? 'bg-black bg-opacity-30' : 'bg-white bg-opacity-90'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-4 text-lg font-medium text-gray-700\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n\n// Loading skeleton for content\n_c2 = LoadingOverlay;\nexport const LoadingSkeleton = ({\n  width = '100%',\n  height = '1rem',\n  className = '',\n  rounded = true,\n  animated = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-gray-200 ${rounded ? 'rounded' : ''} ${animated ? 'animate-pulse' : ''} ${className}`,\n    style: {\n      width,\n      height\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n\n// Product card skeleton\n_c3 = LoadingSkeleton;\nexport const ProductCardSkeleton = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-md p-4 animate-pulse\",\n    children: [/*#__PURE__*/_jsxDEV(LoadingSkeleton, {\n      height: \"200px\",\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingSkeleton, {\n      height: \"1.5rem\",\n      className: \"mb-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingSkeleton, {\n      height: \"1rem\",\n      width: \"60%\",\n      className: \"mb-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingSkeleton, {\n      height: \"1.25rem\",\n      width: \"40%\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n\n// List item skeleton\n_c4 = ProductCardSkeleton;\nexport const ListItemSkeleton = ({\n  showAvatar = false\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center space-x-4 p-4 animate-pulse\",\n    children: [showAvatar && /*#__PURE__*/_jsxDEV(LoadingSkeleton, {\n      width: \"3rem\",\n      height: \"3rem\",\n      className: \"rounded-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1\",\n      children: [/*#__PURE__*/_jsxDEV(LoadingSkeleton, {\n        height: \"1.25rem\",\n        className: \"mb-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LoadingSkeleton, {\n        height: \"1rem\",\n        width: \"70%\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n\n// Button loading state\n_c5 = ListItemSkeleton;\nexport const LoadingButton = ({\n  children,\n  loading = false,\n  disabled = false,\n  className = '',\n  loadingText = 'Loading...',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    ...props,\n    disabled: loading || disabled,\n    className: `relative ${className} ${loading ? 'cursor-not-allowed' : ''}`,\n    children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"small\",\n        color: \"white\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: loading ? 'opacity-0' : 'opacity-100',\n      children: loading ? loadingText : children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n\n// Page loading component\n_c6 = LoadingButton;\nexport const PageLoading = ({\n  message = 'Loading page...'\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4\",\n        children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"medium\",\n          color: \"white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900 mb-2\",\n        children: \"Please wait\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n\n// Section loading component\n_c7 = PageLoading;\nexport const SectionLoading = ({\n  message = 'Loading...',\n  height = 'auto'\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center p-8\",\n    style: {\n      minHeight: height\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-4 text-gray-600\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n\n// Global loading indicator (shows when any loading is active)\n_c8 = SectionLoading;\nexport const GlobalLoadingIndicator = () => {\n  _s();\n  const {\n    hasAnyLoading,\n    getActiveLoadingStates\n  } = useLoading();\n  if (!hasAnyLoading()) return null;\n  const activeStates = getActiveLoadingStates();\n  const primaryState = activeStates[0];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-0 left-0 right-0 z-50 bg-green-500 text-white px-4 py-2 text-sm font-medium\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"small\",\n        color: \"white\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: (primaryState === null || primaryState === void 0 ? void 0 : primaryState.message) || 'Loading...'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), activeStates.length > 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-green-200\",\n        children: [\"(+\", activeStates.length - 1, \" more)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n\n// Loading state wrapper component\n_s(GlobalLoadingIndicator, \"jGIu3NybhguGx7kkcUQRDSHqoyY=\", false, function () {\n  return [useLoading];\n});\n_c9 = GlobalLoadingIndicator;\nexport const LoadingWrapper = ({\n  loading,\n  error,\n  children,\n  loadingComponent: LoadingComponent = SectionLoading,\n  errorComponent: ErrorComponent = null,\n  loadingProps = {},\n  errorProps = {}\n}) => {\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingComponent, {\n      ...loadingProps\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 12\n    }, this);\n  }\n  if (error && ErrorComponent) {\n    return /*#__PURE__*/_jsxDEV(ErrorComponent, {\n      error: error,\n      ...errorProps\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n\n// Higher-order component for loading states\n_c0 = LoadingWrapper;\nexport const withLoading = (WrappedComponent, loadingKey) => {\n  var _s2 = $RefreshSig$();\n  return _s2(function LoadingWrappedComponent(props) {\n    _s2();\n    const {\n      isLoading,\n      getLoadingMessage\n    } = useLoading();\n    const loading = isLoading(loadingKey);\n    const loadingMessage = getLoadingMessage(loadingKey);\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(SectionLoading, {\n        message: loadingMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(WrappedComponent, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 12\n    }, this);\n  }, \"xRl6+YVemwQB5LoX48huvDWCjiA=\", false, function () {\n    return [useLoading];\n  });\n};\n\n// Lazy loading placeholder\nexport const LazyLoadingPlaceholder = ({\n  height = '200px'\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-100 animate-pulse flex items-center justify-center\",\n    style: {\n      height\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-gray-400\",\n      children: [/*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"medium\",\n        color: \"secondary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-sm\",\n        children: \"Loading content...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n_c1 = LazyLoadingPlaceholder;\nexport default {\n  LoadingSpinner,\n  LoadingOverlay,\n  LoadingSkeleton,\n  ProductCardSkeleton,\n  ListItemSkeleton,\n  LoadingButton,\n  PageLoading,\n  SectionLoading,\n  GlobalLoadingIndicator,\n  LoadingWrapper,\n  withLoading,\n  LazyLoadingPlaceholder\n};\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"LoadingSpinner\");\n$RefreshReg$(_c2, \"LoadingOverlay\");\n$RefreshReg$(_c3, \"LoadingSkeleton\");\n$RefreshReg$(_c4, \"ProductCardSkeleton\");\n$RefreshReg$(_c5, \"ListItemSkeleton\");\n$RefreshReg$(_c6, \"LoadingButton\");\n$RefreshReg$(_c7, \"PageLoading\");\n$RefreshReg$(_c8, \"SectionLoading\");\n$RefreshReg$(_c9, \"GlobalLoadingIndicator\");\n$RefreshReg$(_c0, \"LoadingWrapper\");\n$RefreshReg$(_c1, \"LazyLoadingPlaceholder\");", "map": {"version": 3, "names": ["React", "useLoading", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "color", "className", "sizeClasses", "small", "medium", "large", "xlarge", "colorClasses", "primary", "secondary", "white", "dark", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "LoadingOverlay", "message", "transparent", "_c2", "LoadingSkeleton", "width", "height", "rounded", "animated", "style", "_c3", "ProductCardSkeleton", "_c4", "ListItemSkeleton", "showAvatar", "_c5", "LoadingButton", "loading", "disabled", "loadingText", "props", "_c6", "PageLoading", "_c7", "SectionLoading", "minHeight", "_c8", "GlobalLoadingIndicator", "_s", "hasAnyLoading", "getActiveLoadingStates", "activeStates", "primaryState", "length", "_c9", "LoadingWrapper", "error", "loadingComponent", "LoadingComponent", "errorComponent", "ErrorComponent", "loadingProps", "errorProps", "_c0", "with<PERSON>oa<PERSON>", "WrappedComponent", "loadingKey", "_s2", "$RefreshSig$", "LoadingWrappedComponent", "isLoading", "getLoadingMessage", "loadingMessage", "LazyLoadingPlaceholder", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/components/LoadingComponents.js"], "sourcesContent": ["import React from 'react';\nimport { useLoading } from '../contexts/LoadingContext';\n\n// Basic loading spinner\nexport const LoadingSpinner = ({ size = 'medium', color = 'primary', className = '' }) => {\n  const sizeClasses = {\n    small: 'w-4 h-4',\n    medium: 'w-8 h-8',\n    large: 'w-12 h-12',\n    xlarge: 'w-16 h-16'\n  };\n\n  const colorClasses = {\n    primary: 'border-green-500',\n    secondary: 'border-gray-500',\n    white: 'border-white',\n    dark: 'border-gray-800'\n  };\n\n  return (\n    <div className={`inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent ${sizeClasses[size]} ${colorClasses[color]} ${className}`}>\n      <span className=\"sr-only\">Loading...</span>\n    </div>\n  );\n};\n\n// Loading overlay for full screen\nexport const LoadingOverlay = ({ message = 'Loading...', transparent = false }) => {\n  return (\n    <div className={`fixed inset-0 z-50 flex items-center justify-center ${transparent ? 'bg-black bg-opacity-30' : 'bg-white bg-opacity-90'}`}>\n      <div className=\"text-center\">\n        <LoadingSpinner size=\"large\" />\n        <p className=\"mt-4 text-lg font-medium text-gray-700\">{message}</p>\n      </div>\n    </div>\n  );\n};\n\n// Loading skeleton for content\nexport const LoadingSkeleton = ({ \n  width = '100%', \n  height = '1rem', \n  className = '',\n  rounded = true,\n  animated = true \n}) => {\n  return (\n    <div \n      className={`bg-gray-200 ${rounded ? 'rounded' : ''} ${animated ? 'animate-pulse' : ''} ${className}`}\n      style={{ width, height }}\n    />\n  );\n};\n\n// Product card skeleton\nexport const ProductCardSkeleton = () => {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-4 animate-pulse\">\n      <LoadingSkeleton height=\"200px\" className=\"mb-4\" />\n      <LoadingSkeleton height=\"1.5rem\" className=\"mb-2\" />\n      <LoadingSkeleton height=\"1rem\" width=\"60%\" className=\"mb-2\" />\n      <LoadingSkeleton height=\"1.25rem\" width=\"40%\" />\n    </div>\n  );\n};\n\n// List item skeleton\nexport const ListItemSkeleton = ({ showAvatar = false }) => {\n  return (\n    <div className=\"flex items-center space-x-4 p-4 animate-pulse\">\n      {showAvatar && (\n        <LoadingSkeleton width=\"3rem\" height=\"3rem\" className=\"rounded-full\" />\n      )}\n      <div className=\"flex-1\">\n        <LoadingSkeleton height=\"1.25rem\" className=\"mb-2\" />\n        <LoadingSkeleton height=\"1rem\" width=\"70%\" />\n      </div>\n    </div>\n  );\n};\n\n// Button loading state\nexport const LoadingButton = ({ \n  children, \n  loading = false, \n  disabled = false, \n  className = '',\n  loadingText = 'Loading...',\n  ...props \n}) => {\n  return (\n    <button\n      {...props}\n      disabled={loading || disabled}\n      className={`relative ${className} ${loading ? 'cursor-not-allowed' : ''}`}\n    >\n      {loading && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <LoadingSpinner size=\"small\" color=\"white\" />\n        </div>\n      )}\n      <span className={loading ? 'opacity-0' : 'opacity-100'}>\n        {loading ? loadingText : children}\n      </span>\n    </button>\n  );\n};\n\n// Page loading component\nexport const PageLoading = ({ message = 'Loading page...' }) => {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"text-center\">\n        <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4\">\n          <LoadingSpinner size=\"medium\" color=\"white\" />\n        </div>\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Please wait</h2>\n        <p className=\"text-gray-600\">{message}</p>\n      </div>\n    </div>\n  );\n};\n\n// Section loading component\nexport const SectionLoading = ({ message = 'Loading...', height = 'auto' }) => {\n  return (\n    <div className=\"flex items-center justify-center p-8\" style={{ minHeight: height }}>\n      <div className=\"text-center\">\n        <LoadingSpinner size=\"large\" />\n        <p className=\"mt-4 text-gray-600\">{message}</p>\n      </div>\n    </div>\n  );\n};\n\n// Global loading indicator (shows when any loading is active)\nexport const GlobalLoadingIndicator = () => {\n  const { hasAnyLoading, getActiveLoadingStates } = useLoading();\n  \n  if (!hasAnyLoading()) return null;\n  \n  const activeStates = getActiveLoadingStates();\n  const primaryState = activeStates[0];\n  \n  return (\n    <div className=\"fixed top-0 left-0 right-0 z-50 bg-green-500 text-white px-4 py-2 text-sm font-medium\">\n      <div className=\"flex items-center justify-center space-x-2\">\n        <LoadingSpinner size=\"small\" color=\"white\" />\n        <span>{primaryState?.message || 'Loading...'}</span>\n        {activeStates.length > 1 && (\n          <span className=\"text-green-200\">\n            (+{activeStates.length - 1} more)\n          </span>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Loading state wrapper component\nexport const LoadingWrapper = ({ \n  loading, \n  error, \n  children, \n  loadingComponent: LoadingComponent = SectionLoading,\n  errorComponent: ErrorComponent = null,\n  loadingProps = {},\n  errorProps = {}\n}) => {\n  if (loading) {\n    return <LoadingComponent {...loadingProps} />;\n  }\n  \n  if (error && ErrorComponent) {\n    return <ErrorComponent error={error} {...errorProps} />;\n  }\n  \n  return children;\n};\n\n// Higher-order component for loading states\nexport const withLoading = (WrappedComponent, loadingKey) => {\n  return function LoadingWrappedComponent(props) {\n    const { isLoading, getLoadingMessage } = useLoading();\n    \n    const loading = isLoading(loadingKey);\n    const loadingMessage = getLoadingMessage(loadingKey);\n    \n    if (loading) {\n      return <SectionLoading message={loadingMessage} />;\n    }\n    \n    return <WrappedComponent {...props} />;\n  };\n};\n\n// Lazy loading placeholder\nexport const LazyLoadingPlaceholder = ({ height = '200px' }) => {\n  return (\n    <div \n      className=\"bg-gray-100 animate-pulse flex items-center justify-center\"\n      style={{ height }}\n    >\n      <div className=\"text-center text-gray-400\">\n        <LoadingSpinner size=\"medium\" color=\"secondary\" />\n        <p className=\"mt-2 text-sm\">Loading content...</p>\n      </div>\n    </div>\n  );\n};\n\nexport default {\n  LoadingSpinner,\n  LoadingOverlay,\n  LoadingSkeleton,\n  ProductCardSkeleton,\n  ListItemSkeleton,\n  LoadingButton,\n  PageLoading,\n  SectionLoading,\n  GlobalLoadingIndicator,\n  LoadingWrapper,\n  withLoading,\n  LazyLoadingPlaceholder\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,4BAA4B;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI,GAAG,QAAQ;EAAEC,KAAK,GAAG,SAAS;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EACxF,MAAMC,WAAW,GAAG;IAClBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,WAAW;IAClBC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,OAAO,EAAE,kBAAkB;IAC3BC,SAAS,EAAE,iBAAiB;IAC5BC,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACR,CAAC;EAED,oBACEd,OAAA;IAAKI,SAAS,EAAE,oGAAoGC,WAAW,CAACH,IAAI,CAAC,IAAIQ,YAAY,CAACP,KAAK,CAAC,IAAIC,SAAS,EAAG;IAAAW,QAAA,eAC1Kf,OAAA;MAAMI,SAAS,EAAC,SAAS;MAAAW,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxC,CAAC;AAEV,CAAC;;AAED;AAAAC,EAAA,GAtBanB,cAAc;AAuB3B,OAAO,MAAMoB,cAAc,GAAGA,CAAC;EAAEC,OAAO,GAAG,YAAY;EAAEC,WAAW,GAAG;AAAM,CAAC,KAAK;EACjF,oBACEvB,OAAA;IAAKI,SAAS,EAAE,uDAAuDmB,WAAW,GAAG,wBAAwB,GAAG,wBAAwB,EAAG;IAAAR,QAAA,eACzIf,OAAA;MAAKI,SAAS,EAAC,aAAa;MAAAW,QAAA,gBAC1Bf,OAAA,CAACC,cAAc;QAACC,IAAI,EAAC;MAAO;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/BnB,OAAA;QAAGI,SAAS,EAAC,wCAAwC;QAAAW,QAAA,EAAEO;MAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAK,GAAA,GAXaH,cAAc;AAY3B,OAAO,MAAMI,eAAe,GAAGA,CAAC;EAC9BC,KAAK,GAAG,MAAM;EACdC,MAAM,GAAG,MAAM;EACfvB,SAAS,GAAG,EAAE;EACdwB,OAAO,GAAG,IAAI;EACdC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,oBACE7B,OAAA;IACEI,SAAS,EAAE,eAAewB,OAAO,GAAG,SAAS,GAAG,EAAE,IAAIC,QAAQ,GAAG,eAAe,GAAG,EAAE,IAAIzB,SAAS,EAAG;IACrG0B,KAAK,EAAE;MAAEJ,KAAK;MAAEC;IAAO;EAAE;IAAAX,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1B,CAAC;AAEN,CAAC;;AAED;AAAAY,GAAA,GAfaN,eAAe;AAgB5B,OAAO,MAAMO,mBAAmB,GAAGA,CAAA,KAAM;EACvC,oBACEhC,OAAA;IAAKI,SAAS,EAAC,iDAAiD;IAAAW,QAAA,gBAC9Df,OAAA,CAACyB,eAAe;MAACE,MAAM,EAAC,OAAO;MAACvB,SAAS,EAAC;IAAM;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnDnB,OAAA,CAACyB,eAAe;MAACE,MAAM,EAAC,QAAQ;MAACvB,SAAS,EAAC;IAAM;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpDnB,OAAA,CAACyB,eAAe;MAACE,MAAM,EAAC,MAAM;MAACD,KAAK,EAAC,KAAK;MAACtB,SAAS,EAAC;IAAM;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9DnB,OAAA,CAACyB,eAAe;MAACE,MAAM,EAAC,SAAS;MAACD,KAAK,EAAC;IAAK;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC;AAEV,CAAC;;AAED;AAAAc,GAAA,GAXaD,mBAAmB;AAYhC,OAAO,MAAME,gBAAgB,GAAGA,CAAC;EAAEC,UAAU,GAAG;AAAM,CAAC,KAAK;EAC1D,oBACEnC,OAAA;IAAKI,SAAS,EAAC,+CAA+C;IAAAW,QAAA,GAC3DoB,UAAU,iBACTnC,OAAA,CAACyB,eAAe;MAACC,KAAK,EAAC,MAAM;MAACC,MAAM,EAAC,MAAM;MAACvB,SAAS,EAAC;IAAc;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACvE,eACDnB,OAAA;MAAKI,SAAS,EAAC,QAAQ;MAAAW,QAAA,gBACrBf,OAAA,CAACyB,eAAe;QAACE,MAAM,EAAC,SAAS;QAACvB,SAAS,EAAC;MAAM;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDnB,OAAA,CAACyB,eAAe;QAACE,MAAM,EAAC,MAAM;QAACD,KAAK,EAAC;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAiB,GAAA,GAdaF,gBAAgB;AAe7B,OAAO,MAAMG,aAAa,GAAGA,CAAC;EAC5BtB,QAAQ;EACRuB,OAAO,GAAG,KAAK;EACfC,QAAQ,GAAG,KAAK;EAChBnC,SAAS,GAAG,EAAE;EACdoC,WAAW,GAAG,YAAY;EAC1B,GAAGC;AACL,CAAC,KAAK;EACJ,oBACEzC,OAAA;IAAA,GACMyC,KAAK;IACTF,QAAQ,EAAED,OAAO,IAAIC,QAAS;IAC9BnC,SAAS,EAAE,YAAYA,SAAS,IAAIkC,OAAO,GAAG,oBAAoB,GAAG,EAAE,EAAG;IAAAvB,QAAA,GAEzEuB,OAAO,iBACNtC,OAAA;MAAKI,SAAS,EAAC,mDAAmD;MAAAW,QAAA,eAChEf,OAAA,CAACC,cAAc;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAC;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACN,eACDnB,OAAA;MAAMI,SAAS,EAAEkC,OAAO,GAAG,WAAW,GAAG,aAAc;MAAAvB,QAAA,EACpDuB,OAAO,GAAGE,WAAW,GAAGzB;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEb,CAAC;;AAED;AAAAuB,GAAA,GA1BaL,aAAa;AA2B1B,OAAO,MAAMM,WAAW,GAAGA,CAAC;EAAErB,OAAO,GAAG;AAAkB,CAAC,KAAK;EAC9D,oBACEtB,OAAA;IAAKI,SAAS,EAAC,0DAA0D;IAAAW,QAAA,eACvEf,OAAA;MAAKI,SAAS,EAAC,aAAa;MAAAW,QAAA,gBAC1Bf,OAAA;QAAKI,SAAS,EAAC,oHAAoH;QAAAW,QAAA,eACjIf,OAAA,CAACC,cAAc;UAACC,IAAI,EAAC,QAAQ;UAACC,KAAK,EAAC;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACNnB,OAAA;QAAII,SAAS,EAAC,0CAA0C;QAAAW,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzEnB,OAAA;QAAGI,SAAS,EAAC,eAAe;QAAAW,QAAA,EAAEO;MAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAyB,GAAA,GAdaD,WAAW;AAexB,OAAO,MAAME,cAAc,GAAGA,CAAC;EAAEvB,OAAO,GAAG,YAAY;EAAEK,MAAM,GAAG;AAAO,CAAC,KAAK;EAC7E,oBACE3B,OAAA;IAAKI,SAAS,EAAC,sCAAsC;IAAC0B,KAAK,EAAE;MAAEgB,SAAS,EAAEnB;IAAO,CAAE;IAAAZ,QAAA,eACjFf,OAAA;MAAKI,SAAS,EAAC,aAAa;MAAAW,QAAA,gBAC1Bf,OAAA,CAACC,cAAc;QAACC,IAAI,EAAC;MAAO;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/BnB,OAAA;QAAGI,SAAS,EAAC,oBAAoB;QAAAW,QAAA,EAAEO;MAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA4B,GAAA,GAXaF,cAAc;AAY3B,OAAO,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM;IAAEC,aAAa;IAAEC;EAAuB,CAAC,GAAGrD,UAAU,CAAC,CAAC;EAE9D,IAAI,CAACoD,aAAa,CAAC,CAAC,EAAE,OAAO,IAAI;EAEjC,MAAME,YAAY,GAAGD,sBAAsB,CAAC,CAAC;EAC7C,MAAME,YAAY,GAAGD,YAAY,CAAC,CAAC,CAAC;EAEpC,oBACEpD,OAAA;IAAKI,SAAS,EAAC,uFAAuF;IAAAW,QAAA,eACpGf,OAAA;MAAKI,SAAS,EAAC,4CAA4C;MAAAW,QAAA,gBACzDf,OAAA,CAACC,cAAc;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAC;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CnB,OAAA;QAAAe,QAAA,EAAO,CAAAsC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE/B,OAAO,KAAI;MAAY;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACnDiC,YAAY,CAACE,MAAM,GAAG,CAAC,iBACtBtD,OAAA;QAAMI,SAAS,EAAC,gBAAgB;QAAAW,QAAA,GAAC,IAC7B,EAACqC,YAAY,CAACE,MAAM,GAAG,CAAC,EAAC,QAC7B;MAAA;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA8B,EAAA,CAvBaD,sBAAsB;EAAA,QACiBlD,UAAU;AAAA;AAAAyD,GAAA,GADjDP,sBAAsB;AAwBnC,OAAO,MAAMQ,cAAc,GAAGA,CAAC;EAC7BlB,OAAO;EACPmB,KAAK;EACL1C,QAAQ;EACR2C,gBAAgB,EAAEC,gBAAgB,GAAGd,cAAc;EACnDe,cAAc,EAAEC,cAAc,GAAG,IAAI;EACrCC,YAAY,GAAG,CAAC,CAAC;EACjBC,UAAU,GAAG,CAAC;AAChB,CAAC,KAAK;EACJ,IAAIzB,OAAO,EAAE;IACX,oBAAOtC,OAAA,CAAC2D,gBAAgB;MAAA,GAAKG;IAAY;MAAA9C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC/C;EAEA,IAAIsC,KAAK,IAAII,cAAc,EAAE;IAC3B,oBAAO7D,OAAA,CAAC6D,cAAc;MAACJ,KAAK,EAAEA,KAAM;MAAA,GAAKM;IAAU;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACzD;EAEA,OAAOJ,QAAQ;AACjB,CAAC;;AAED;AAAAiD,GAAA,GApBaR,cAAc;AAqB3B,OAAO,MAAMS,WAAW,GAAGA,CAACC,gBAAgB,EAAEC,UAAU,KAAK;EAAA,IAAAC,GAAA,GAAAC,YAAA;EAC3D,OAAAD,GAAA,CAAO,SAASE,uBAAuBA,CAAC7B,KAAK,EAAE;IAAA2B,GAAA;IAC7C,MAAM;MAAEG,SAAS;MAAEC;IAAkB,CAAC,GAAG1E,UAAU,CAAC,CAAC;IAErD,MAAMwC,OAAO,GAAGiC,SAAS,CAACJ,UAAU,CAAC;IACrC,MAAMM,cAAc,GAAGD,iBAAiB,CAACL,UAAU,CAAC;IAEpD,IAAI7B,OAAO,EAAE;MACX,oBAAOtC,OAAA,CAAC6C,cAAc;QAACvB,OAAO,EAAEmD;MAAe;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACpD;IAEA,oBAAOnB,OAAA,CAACkE,gBAAgB;MAAA,GAAKzB;IAAK;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACxC,CAAC;IAAA,QAV0CrB,UAAU;EAAA;AAWvD,CAAC;;AAED;AACA,OAAO,MAAM4E,sBAAsB,GAAGA,CAAC;EAAE/C,MAAM,GAAG;AAAQ,CAAC,KAAK;EAC9D,oBACE3B,OAAA;IACEI,SAAS,EAAC,4DAA4D;IACtE0B,KAAK,EAAE;MAAEH;IAAO,CAAE;IAAAZ,QAAA,eAElBf,OAAA;MAAKI,SAAS,EAAC,2BAA2B;MAAAW,QAAA,gBACxCf,OAAA,CAACC,cAAc;QAACC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAC;MAAW;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDnB,OAAA;QAAGI,SAAS,EAAC,cAAc;QAAAW,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACwD,GAAA,GAZWD,sBAAsB;AAcnC,eAAe;EACbzE,cAAc;EACdoB,cAAc;EACdI,eAAe;EACfO,mBAAmB;EACnBE,gBAAgB;EAChBG,aAAa;EACbM,WAAW;EACXE,cAAc;EACdG,sBAAsB;EACtBQ,cAAc;EACdS,WAAW;EACXS;AACF,CAAC;AAAC,IAAAtD,EAAA,EAAAI,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAQ,GAAA,EAAAS,GAAA,EAAAW,GAAA;AAAAC,YAAA,CAAAxD,EAAA;AAAAwD,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}