import React, { useEffect, useState, useRef } from 'react';
import { useImage } from '../contexts/ImageContext';
import { LoadingSkeleton, LoadingSpinner } from './LoadingComponents';

const EnhancedImage = ({
  src,
  alt = '',
  width,
  height,
  className = '',
  fallback,
  fallbackType = 'general',
  loading = 'lazy',
  priority = false,
  quality = 80,
  format = 'webp',
  fit = 'cover',
  onLoad,
  onError,
  showLoadingState = true,
  showErrorState = true,
  retryOnError = true,
  maxRetries = 3,
  placeholder = true,
  ...props
}) => {
  const { loadImage, getImageState, getOptimizedImageUrl } = useImage();
  const [imageKey] = useState(() => `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [retryCount, setRetryCount] = useState(0);
  const imgRef = useRef(null);
  
  const imageState = getImageState(imageKey);
  const { loading: isLoading, loaded, error, src: imageSrc, fallbackUsed } = imageState;

  // Load image when component mounts or src changes
  useEffect(() => {
    if (!src) return;

    const loadOptions = {
      width,
      height,
      quality,
      format,
      fit,
      fallback,
      fallbackType,
      maxRetries
    };

    loadImage(imageKey, src, loadOptions)
      .then((imageInfo) => {
        if (onLoad) {
          onLoad({
            target: imgRef.current,
            imageInfo,
            fallbackUsed: imageState.fallbackUsed
          });
        }
      })
      .catch((error) => {
        if (onError) {
          onError({
            target: imgRef.current,
            error,
            retryCount
          });
        }
      });
  }, [src, imageKey, width, height, quality, format, fit, fallback, fallbackType, maxRetries, loadImage, onLoad, onError, retryCount, imageState.fallbackUsed]);

  // Handle retry
  const handleRetry = () => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
      // Trigger reload by updating the image key
      const newKey = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      loadImage(newKey, src, {
        width,
        height,
        quality,
        format,
        fit,
        fallback,
        fallbackType,
        maxRetries
      });
    }
  };

  // Generate responsive srcSet if width/height provided
  const generateSrcSet = () => {
    if (!src || !width) return undefined;
    
    const sizes = [1, 1.5, 2, 3]; // 1x, 1.5x, 2x, 3x
    return sizes
      .map(multiplier => {
        const scaledWidth = Math.round(width * multiplier);
        const optimizedSrc = getOptimizedImageUrl(src, {
          width: scaledWidth,
          height: height ? Math.round(height * multiplier) : undefined,
          quality,
          format,
          fit
        });
        return `${optimizedSrc} ${multiplier}x`;
      })
      .join(', ');
  };

  // Generate sizes attribute
  const generateSizes = () => {
    if (!width) return undefined;
    return `(max-width: 768px) 100vw, ${width}px`;
  };

  // Loading state
  if (isLoading && showLoadingState) {
    if (placeholder) {
      return (
        <div 
          className={`relative ${className}`}
          style={{ width, height }}
        >
          <LoadingSkeleton 
            width="100%" 
            height="100%" 
            className="absolute inset-0"
          />
          <div className="absolute inset-0 flex items-center justify-center">
            <LoadingSpinner size="small" color="secondary" />
          </div>
        </div>
      );
    }
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 ${className}`}
        style={{ width, height }}
      >
        <LoadingSpinner size="medium" color="secondary" />
      </div>
    );
  }

  // Error state
  if (error && !loaded && showErrorState) {
    return (
      <div 
        className={`flex flex-col items-center justify-center bg-gray-100 text-gray-500 ${className}`}
        style={{ width, height }}
      >
        <div className="text-center p-4">
          <div className="text-2xl mb-2">⚠️</div>
          <p className="text-sm mb-2">Failed to load image</p>
          {retryOnError && retryCount < maxRetries && (
            <button
              onClick={handleRetry}
              className="text-xs bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded transition-colors"
            >
              Retry ({retryCount + 1}/{maxRetries})
            </button>
          )}
        </div>
      </div>
    );
  }

  // Success state - render image
  if (loaded && imageSrc) {
    return (
      <div className="relative">
        <img
          ref={imgRef}
          src={imageSrc}
          alt={alt}
          width={width}
          height={height}
          className={className}
          loading={priority ? 'eager' : loading}
          srcSet={generateSrcSet()}
          sizes={generateSizes()}
          {...props}
        />
        
        {/* Fallback indicator */}
        {fallbackUsed && (
          <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
            Fallback
          </div>
        )}
      </div>
    );
  }

  // Default fallback if no src provided
  if (!src) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 text-gray-400 ${className}`}
        style={{ width, height }}
      >
        <span className="text-sm">No image</span>
      </div>
    );
  }

  return null;
};

// Specialized image components
export const ProductImage = (props) => (
  <EnhancedImage
    {...props}
    fallbackType="product"
    quality={85}
    format="webp"
    fit="cover"
  />
);

export const UserAvatar = (props) => (
  <EnhancedImage
    {...props}
    fallbackType="user"
    quality={90}
    format="webp"
    fit="cover"
    className={`rounded-full ${props.className || ''}`}
  />
);

export const CategoryImage = (props) => (
  <EnhancedImage
    {...props}
    fallbackType="category"
    quality={80}
    format="webp"
    fit="cover"
  />
);

export const BrandImage = (props) => (
  <EnhancedImage
    {...props}
    fallbackType="brand"
    quality={85}
    format="webp"
    fit="contain"
  />
);

// Progressive image component with blur-up effect
export const ProgressiveImage = ({ 
  src, 
  placeholder, 
  className = '',
  ...props 
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  
  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Placeholder/blur image */}
      {placeholder && !imageLoaded && (
        <img
          src={placeholder}
          alt=""
          className="absolute inset-0 w-full h-full object-cover filter blur-sm scale-110 transition-opacity duration-300"
        />
      )}
      
      {/* Main image */}
      <EnhancedImage
        {...props}
        src={src}
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          imageLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={() => setImageLoaded(true)}
        showLoadingState={!placeholder}
      />
    </div>
  );
};

export default EnhancedImage;
