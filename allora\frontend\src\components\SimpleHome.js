import React, { useState, useEffect } from 'react';
import { useCart } from '../contexts/CartContext';
import { useNotification } from '../contexts/NotificationContext';
import { useLoading } from '../contexts/LoadingContext';
import { Link } from 'react-router-dom';
import { API_BASE_URL } from '../config/api';
import { ProductImage } from './EnhancedImage';
import { LoadingWrapper, ProductCardSkeleton } from './LoadingComponents';
import { formatPrice, calculateDiscountPercentage } from '../utils/currency';
import TrackingWidget from './TrackingWidget';

// Add custom CSS animations
const customStyles = `
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes slow-zoom {
    0% { transform: scale(1.1); }
    100% { transform: scale(1.2); }
  }

  @keyframes slide-up {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes slide-up-delayed {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes fade-in-up {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes fade-in-up-delayed {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  .animate-float { animation: float 6s ease-in-out infinite; }
  .animate-float-delayed { animation: float-delayed 6s ease-in-out infinite 2s; }
  .animate-slow-zoom { animation: slow-zoom 20s ease-in-out infinite alternate; }
  .animate-slide-up { animation: slide-up 1s ease-out forwards; }
  .animate-slide-up-delayed { animation: slide-up-delayed 1s ease-out 0.3s forwards; opacity: 0; }
  .animate-fade-in-up { animation: fade-in-up 0.8s ease-out forwards; }
  .animate-fade-in-up-delayed { animation: fade-in-up-delayed 0.8s ease-out 0.5s forwards; opacity: 0; }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = customStyles;
  document.head.appendChild(styleSheet);
}

// Modern Unique Product Card with Wishlist and Cart
const ProductCard = ({ product, onAddToCart, size = 'normal' }) => {
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Render star rating
  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="text-yellow-400">★</span>);
    }
    if (hasHalfStar) {
      stars.push(<span key="half" className="text-yellow-400">☆</span>);
    }
    for (let i = stars.length; i < 5; i++) {
      stars.push(<span key={i} className="text-gray-300">★</span>);
    }
    return stars;
  };

  const handleWishlistToggle = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    // Here you would typically call an API to add/remove from wishlist
  };

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    onAddToCart(product);
  };

  const cardClasses = size === 'small'
    ? "relative bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer group h-full flex flex-col transform hover:-translate-y-2"
    : "relative bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer group h-full flex flex-col transform hover:-translate-y-2";

  const imageHeight = size === 'small' ? 'h-40' : 'h-64';

  return (
    <div
      className={cardClasses}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Wishlist Button */}
      <button
        onClick={handleWishlistToggle}
        className={`absolute top-4 right-4 z-10 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${
          isWishlisted
            ? 'bg-red-500 text-white shadow-lg'
            : 'bg-white/80 backdrop-blur-sm text-gray-600 hover:bg-red-500 hover:text-white'
        } ${isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-75'}`}
      >
        <svg className="w-5 h-5" fill={isWishlisted ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      </button>

      <Link to={`/product/${product.id}`} className="block h-full flex flex-col">
        {/* Product Image with Gradient Overlay */}
        <div className={`relative overflow-hidden ${imageHeight}`}>
          <ProductImage
            src={product.image_url || product.image}
            alt={product.name || 'Product Image'}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
            width={300}
            height={200}
            quality={85}
            loading="lazy"
            showLoadingState={true}
            placeholder={true}
          />

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Floating Badges */}
          <div className="absolute top-4 left-4 flex flex-col space-y-2">
            {product.sustainabilityScore >= 80 && (
              <div className="bg-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full flex items-center space-x-1 shadow-lg">
                <span>🌱</span>
                <span>Eco-Friendly</span>
              </div>
            )}
            {product.original_price && product.original_price > product.price && (
              <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                {calculateDiscountPercentage(product.original_price, product.price)}% OFF
              </div>
            )}
          </div>

          {/* Stock Status */}
          {product.stockQuantity <= 5 && product.stockQuantity > 0 && (
            <div className="absolute bottom-4 left-4 bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse">
              Only {product.stockQuantity} left!
            </div>
          )}

          {/* Quick Action Buttons */}
          <div className={`absolute bottom-4 right-4 flex space-x-2 transition-all duration-300 ${
            isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
          }`}>
            <button
              onClick={handleAddToCart}
              disabled={product.stockQuantity === 0}
              className="bg-emerald-500 hover:bg-emerald-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4m16 0H4" />
              </svg>
            </button>
          </div>
        </div>

        {/* Product Info with Modern Design */}
        <div className="p-6 flex-1 flex flex-col">
          {/* Brand & Category */}
          <div className="flex items-center justify-between mb-2">
            {product.brand && (
              <span className="text-xs font-semibold text-emerald-600 uppercase tracking-wider bg-emerald-50 px-2 py-1 rounded-full">
                {product.brand}
              </span>
            )}
            {product.category && (
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                {product.category}
              </span>
            )}
          </div>

          {/* Product Name */}
          <h3 className={`font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors line-clamp-2 ${
            size === 'small' ? 'text-sm' : 'text-lg'
          }`}>
            {product.name}
          </h3>

          {/* Rating */}
          {(product.averageRating || product.average_rating) > 0 && (
            <div className="flex items-center space-x-2 mb-3">
              <div className="flex text-sm">
                {renderStars(product.averageRating || product.average_rating)}
              </div>
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                {product.totalReviews || product.total_reviews || 0} reviews
              </span>
            </div>
          )}

          {/* Price Section with Modern Design */}
          <div className="mt-auto">
            <div className="flex items-center space-x-3 mb-4">
              <span className={`font-bold text-emerald-600 ${size === 'small' ? 'text-xl' : 'text-2xl'}`}>
                {formatPrice(product.price)}
              </span>
              {product.original_price && product.original_price > product.price && (
                <span className="text-lg text-gray-400 line-through">
                  {formatPrice(product.original_price)}
                </span>
              )}
            </div>

            {/* Material/Features */}
            {product.material && (
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-xs text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                  {product.material}
                </span>
              </div>
            )}

            {/* Add to Cart Button - Always Visible */}
            <button
              onClick={handleAddToCart}
              disabled={product.stockQuantity === 0}
              className={`w-full py-3 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${
                product.stockQuantity === 0
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
              }`}
            >
              {product.stockQuantity === 0 ? (
                <span>Out of Stock</span>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4m16 0H4" />
                  </svg>
                  <span>Add to Cart</span>
                </>
              )}
            </button>
          </div>
        </div>
      </Link>
    </div>
  );
};

// Unique Floating Category Cards
const FloatingCategorySection = ({ categories, onCategoryClick }) => {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 bg-emerald-500 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-blue-500 rounded-full blur-3xl" />
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Explore Our
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-500 to-blue-500"> Universe</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover sustainable products across different categories, each carefully curated for quality and environmental impact.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {categories.map((category, index) => (
            <div
              key={index}
              onClick={() => onCategoryClick(category)}
              className="group cursor-pointer transform hover:scale-105 transition-all duration-500"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="relative bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden border-0 p-8">
                {/* Gradient Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 via-white to-blue-50 opacity-50" />

                {/* Content */}
                <div className="relative z-10 text-center">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <img
                      src={category.image}
                      alt={category.name}
                      className="w-12 h-12 object-cover rounded-lg filter brightness-0 invert"
                    />
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors">
                    {category.name}
                  </h3>

                  <p className="text-gray-600 text-sm leading-relaxed">
                    {category.description || "Sustainable and eco-friendly options"}
                  </p>

                  {/* Hover Arrow */}
                  <div className="mt-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                    <div className="inline-flex items-center text-emerald-600 font-semibold">
                      <span>Explore</span>
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Modern Product Showcase Section
const ModernProductShowcase = ({ title, products, onAddToCart, theme = 'emerald' }) => {
  const themeClasses = {
    emerald: 'from-emerald-500 to-emerald-600',
    blue: 'from-blue-500 to-blue-600',
    purple: 'from-purple-500 to-purple-600',
    pink: 'from-pink-500 to-pink-600'
  };

  return (
    <section className="py-20 bg-white relative overflow-hidden">
      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-500" />

      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {title}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-emerald-500 to-blue-500 mx-auto rounded-full" />
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {products.slice(0, 8).map((product, index) => (
            <div
              key={product.id}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <ProductCard
                product={product}
                onAddToCart={onAddToCart}
              />
            </div>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-16">
          <Link
            to="/search"
            className={`inline-flex items-center px-8 py-4 bg-gradient-to-r ${themeClasses[theme]} text-white font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300`}
          >
            <span>View All Products</span>
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

// Unique Modern Hero Section
const ModernHero = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const heroSlides = [
    {
      id: 1,
      title: "Sustainable Living",
      subtitle: "Made Simple",
      description: "Discover eco-friendly products that make a difference for you and the planet",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop",
      cta: "Shop Sustainable",
      link: "/search?sustainable=true",
      accent: "emerald"
    },
    {
      id: 2,
      title: "Green Technology",
      subtitle: "For Tomorrow",
      description: "Innovative eco-tech solutions that reduce your carbon footprint",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1200&h=600&fit=crop",
      cta: "Explore Tech",
      link: "/category/electronics",
      accent: "blue"
    },
    {
      id: 3,
      title: "Natural Beauty",
      subtitle: "Pure & Clean",
      description: "Organic beauty products that nurture your skin and the environment",
      image: "https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=1200&h=600&fit=crop",
      cta: "Discover Beauty",
      link: "/category/beauty",
      accent: "pink"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 6000);
    return () => clearInterval(timer);
  }, [heroSlides.length]);

  const currentHero = heroSlides[currentSlide];

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      {/* Background Image with Parallax Effect */}
      <div className="absolute inset-0">
        {heroSlides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={slide.image}
              alt={slide.title}
              className="w-full h-full object-cover scale-110 animate-slow-zoom"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent" />
          </div>
        ))}
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-emerald-500/10 rounded-full blur-xl animate-float" />
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-500/10 rounded-full blur-xl animate-float-delayed" />
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-pink-500/10 rounded-full blur-xl animate-float" />
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Animated Title */}
          <div className="mb-8">
            <h1 className="text-6xl md:text-8xl font-bold text-white mb-4 leading-tight">
              <span className="block animate-slide-up">{currentHero.title}</span>
              <span className={`block text-transparent bg-clip-text bg-gradient-to-r ${
                currentHero.accent === 'emerald' ? 'from-emerald-400 to-emerald-600' :
                currentHero.accent === 'blue' ? 'from-blue-400 to-blue-600' :
                'from-pink-400 to-pink-600'
              } animate-slide-up-delayed`}>
                {currentHero.subtitle}
              </span>
            </h1>
          </div>

          {/* Description */}
          <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed animate-fade-in-up">
            {currentHero.description}
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up-delayed">
            <Link
              to={currentHero.link}
              className={`group relative px-8 py-4 bg-gradient-to-r ${
                currentHero.accent === 'emerald' ? 'from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700' :
                currentHero.accent === 'blue' ? 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' :
                'from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700'
              } text-white font-semibold rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300`}
            >
              <span className="relative z-10 flex items-center space-x-2">
                <span>{currentHero.cta}</span>
                <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </span>
            </Link>

            <Link
              to="/categories"
              className="group px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-full hover:bg-white/10 hover:border-white/50 transition-all duration-300 backdrop-blur-sm"
            >
              <span className="flex items-center space-x-2">
                <span>Browse All</span>
                <svg className="w-5 h-5 group-hover:rotate-45 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </span>
            </Link>
          </div>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3">
        {heroSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? 'bg-white scale-125'
                : 'bg-white/40 hover:bg-white/60'
            }`}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 right-8 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  );
};

// Main Home Component
const SimpleHome = () => {
  const { addToCart } = useCart();
  const { success } = useNotification();
  const { setLoading: setGlobalLoading, clearLoading } = useLoading();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Modern unique categories
  const uniqueCategories = [
    {
      name: 'Eco Fashion',
      image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200&h=150&fit=crop',
      link: '/search?category=clothing',
      description: 'Sustainable clothing that looks good and feels great'
    },
    {
      name: 'Green Home',
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=150&fit=crop',
      link: '/search?category=home-garden',
      description: 'Transform your space with eco-friendly home essentials'
    },
    {
      name: 'Natural Beauty',
      image: 'https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?w=200&h=150&fit=crop',
      link: '/search?category=beauty',
      description: 'Pure, organic beauty products for radiant skin'
    },
    {
      name: 'Smart Tech',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=150&fit=crop',
      link: '/search?category=electronics',
      description: 'Innovative technology that respects the environment'
    },
    {
      name: 'Wellness',
      image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=200&h=150&fit=crop',
      link: '/search?category=wellness',
      description: 'Holistic products for mind, body, and soul'
    },
    {
      name: 'Outdoor Life',
      image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=200&h=150&fit=crop',
      link: '/search?category=outdoor',
      description: 'Gear for sustainable outdoor adventures'
    },
    {
      name: 'Zero Waste',
      image: 'https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?w=200&h=150&fit=crop',
      link: '/search?category=zero-waste',
      description: 'Products that help you live waste-free'
    },
    {
      name: 'Kids & Baby',
      image: 'https://images.unsplash.com/photo-1515488042361-ee00e0ddd4e4?w=200&h=150&fit=crop',
      link: '/search?category=kids',
      description: 'Safe, sustainable products for little ones'
    }
  ];

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setGlobalLoading('home_products', true, 'Loading featured products...');
        const response = await fetch(`${API_BASE_URL}/products?limit=20`);
        if (!response.ok) {
          throw new Error('Failed to fetch products');
        }
        const data = await response.json();
        setProducts(data.products || data);
        setLoading(false);
        clearLoading('home_products');
      } catch (err) {
        setError(err.message);
        setLoading(false);
        clearLoading('home_products');
      }
    };

    fetchProducts();
  }, [setGlobalLoading, clearLoading]);

  const handleAddToCart = async (product) => {
    try {
      await addToCart(product.id, 1);
      success(`${product.name} added to cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      error('Failed to add item to cart');
    }
  };

  const handleCategoryClick = (category) => {
    window.location.href = category.link;
  };

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Oops! Something went wrong</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors duration-200"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-64 bg-gray-200 rounded-lg mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <ProductCardSkeleton key={index} />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Modern Unique Hero Section */}
      <ModernHero />

      {/* Order Tracking Widget */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <TrackingWidget />
      </div>

      {/* Floating Category Section */}
      <FloatingCategorySection
        categories={uniqueCategories}
        onCategoryClick={handleCategoryClick}
      />

      {/* Featured Products Showcase */}
      <ModernProductShowcase
        title="Featured Sustainable Products"
        products={products.slice(0, 8)}
        onAddToCart={handleAddToCart}
        theme="emerald"
      />

      {/* Trending Products */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Trending Now
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover what's popular in sustainable living
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.slice(8, 14).map((product, index) => (
              <div
                key={product.id}
                className="animate-fade-in-up"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <ProductCard
                  product={product}
                  onAddToCart={handleAddToCart}
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Best Sellers with Unique Layout */}
      <section className="py-20 bg-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-blue-500/5" />

        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Customer Favorites
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-emerald-500 to-blue-500 mx-auto rounded-full mb-6" />
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Products our customers can't stop talking about
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {products.slice(14, 18).map((product, index) => (
              <div
                key={product.id}
                className="transform hover:scale-105 transition-all duration-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <ProductCard
                  product={product}
                  onAddToCart={handleAddToCart}
                />
              </div>
            ))}
          </div>
        </div>
      </section>



      {/* Sustainability Impact Section */}
      <section className="py-12 bg-emerald-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Your Impact Matters</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Every sustainable choice you make contributes to a better planet. See the collective impact of our community.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center bg-white rounded-xl p-6 shadow-sm">
              <div className="text-4xl font-bold text-emerald-600 mb-2">2.5M+</div>
              <div className="text-gray-600">CO₂ Saved (kg)</div>
            </div>
            <div className="text-center bg-white rounded-xl p-6 shadow-sm">
              <div className="text-4xl font-bold text-blue-600 mb-2">1.8M+</div>
              <div className="text-gray-600">Water Conserved (L)</div>
            </div>
            <div className="text-center bg-white rounded-xl p-6 shadow-sm">
              <div className="text-4xl font-bold text-orange-600 mb-2">950K+</div>
              <div className="text-gray-600">Waste Diverted (kg)</div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Reviews Section */}
      <section className="py-8 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">What Our Customers Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                name: "Priya Sharma",
                rating: 5,
                comment: "Amazing quality sustainable products! The bamboo t-shirts are so comfortable and eco-friendly.",
                product: "Eco-Friendly Bamboo T-Shirt"
              },
              {
                name: "Rahul Kumar",
                rating: 5,
                comment: "Fast delivery and excellent customer service. Love supporting sustainable brands through Allora.",
                product: "Organic Cotton Slim Jeans"
              },
              {
                name: "Anita Patel",
                rating: 4,
                comment: "Great variety of eco-friendly products. The packaging is also completely plastic-free!",
                product: "Recycled Canvas Backpack"
              }
            ].map((review, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center mb-3">
                  <div className="flex text-yellow-400 mr-2">
                    {[...Array(review.rating)].map((_, i) => (
                      <span key={i}>★</span>
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">{review.rating}/5</span>
                </div>
                <p className="text-gray-700 mb-3">"{review.comment}"</p>
                <div className="text-sm">
                  <div className="font-medium text-gray-900">{review.name}</div>
                  <div className="text-gray-500">Verified purchase: {review.product}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-12 bg-emerald-600">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Stay Updated with Sustainable Living</h2>
          <p className="text-emerald-100 mb-8 text-lg">
            Get the latest eco-friendly products, sustainability tips, and exclusive deals delivered to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-emerald-300 focus:outline-none"
            />
            <button className="bg-white text-emerald-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SimpleHome;
