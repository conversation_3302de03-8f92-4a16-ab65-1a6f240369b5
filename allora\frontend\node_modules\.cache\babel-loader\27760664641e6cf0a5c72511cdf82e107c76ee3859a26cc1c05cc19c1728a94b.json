{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\nimport { API_BASE_URL } from '../config/api';\nimport { useError } from '../contexts/ErrorContext';\nimport { useLoading } from '../contexts/LoadingContext';\n\n/**\r\n * Custom hook for API calls with loading, error, and success states\r\n * @param {string} baseUrl - The base URL for API calls\r\n * @returns {object} - API utilities and state\r\n */\nexport const useApi = (baseUrl = API_BASE_URL, options = {}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const {\n    handleApiError,\n    clearError\n  } = useError();\n  const {\n    setLoading: setGlobalLoading,\n    clearLoading\n  } = useLoading();\n  const {\n    useGlobalLoading = false,\n    loadingKey = 'api',\n    autoHandleErrors = true\n  } = options;\n\n  // Generic API call function with retry logic\n  const apiCall = useCallback(async (endpoint, requestOptions = {}, retryCount = 0) => {\n    const maxRetries = 3;\n    const baseDelay = 1000; // 1 second\n\n    setLoading(true);\n    setError(null);\n    if (useGlobalLoading) {\n      setGlobalLoading(loadingKey, true, `Loading ${endpoint}...`);\n    }\n    if (autoHandleErrors) {\n      clearError(loadingKey);\n    }\n    try {\n      const url = `${baseUrl}${endpoint}`;\n      const config = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...requestOptions.headers\n        },\n        ...requestOptions\n      };\n      const response = await fetch(url, config);\n      if (!response.ok) {\n        // Handle rate limiting with retry\n        if (response.status === 429 && retryCount < maxRetries) {\n          const retryAfter = parseInt(response.headers.get('Retry-After') || '60', 10);\n          const delay = Math.min(retryAfter * 1000, baseDelay * Math.pow(2, retryCount));\n          console.log(`Rate limited. Retrying in ${delay / 1000} seconds... (Attempt ${retryCount + 1}/${maxRetries})`);\n          await new Promise(resolve => setTimeout(resolve, delay));\n          return apiCall(endpoint, requestOptions, retryCount + 1);\n        }\n        const errorData = await response.json().catch(() => ({}));\n        if (response.status === 429) {\n          throw new Error('Rate limit exceeded. Please try again later.');\n        }\n        const error = new Error(errorData.error || `HTTP error! status: ${response.status}`);\n        error.response = {\n          status: response.status,\n          data: errorData\n        };\n        error.config = {\n          url\n        };\n        throw error;\n      }\n      const data = await response.json();\n      if (useGlobalLoading) {\n        clearLoading(loadingKey);\n      }\n      return data;\n    } catch (err) {\n      console.error('API call failed:', err);\n      setError(err.message);\n      if (autoHandleErrors) {\n        handleApiError(err, {\n          endpoint,\n          retryCount\n        });\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n      if (useGlobalLoading) {\n        clearLoading(loadingKey);\n      }\n    }\n  }, [baseUrl, useGlobalLoading, loadingKey, autoHandleErrors, setGlobalLoading, clearLoading, handleApiError, clearError]);\n\n  // GET request\n  const get = useCallback((endpoint, token = null) => {\n    const headers = token ? {\n      'Authorization': token\n    } : {};\n    return apiCall(endpoint, {\n      method: 'GET',\n      headers\n    });\n  }, [apiCall]);\n\n  // POST request\n  const post = useCallback((endpoint, data, token = null) => {\n    const headers = {\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': token\n      } : {})\n    };\n    return apiCall(endpoint, {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(data)\n    });\n  }, [apiCall]);\n\n  // POST request with FormData (for file uploads)\n  const postFormData = useCallback((endpoint, formData, token = null) => {\n    const headers = token ? {\n      'Authorization': token\n    } : {};\n    // Don't set Content-Type for FormData, let browser set it with boundary\n    delete headers['Content-Type'];\n    return apiCall(endpoint, {\n      method: 'POST',\n      headers,\n      body: formData\n    });\n  }, [apiCall]);\n\n  // PUT request\n  const put = useCallback((endpoint, data, token = null) => {\n    const headers = {\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': token\n      } : {})\n    };\n    return apiCall(endpoint, {\n      method: 'PUT',\n      headers,\n      body: JSON.stringify(data)\n    });\n  }, [apiCall]);\n\n  // PATCH request\n  const patch = useCallback((endpoint, data, token = null) => {\n    const headers = {\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': token\n      } : {})\n    };\n    return apiCall(endpoint, {\n      method: 'PATCH',\n      headers,\n      body: JSON.stringify(data)\n    });\n  }, [apiCall]);\n\n  // Clear local error\n  const clearLocalError = useCallback(() => {\n    setError(null);\n  }, []);\n  return {\n    loading,\n    error,\n    get,\n    post,\n    postFormData,\n    put,\n    patch,\n    clearError: clearLocalError\n  };\n};\n_s(useApi, \"bL2ZOYYWQ075IDEEvK7bfpbSHh0=\", false, function () {\n  return [useError, useLoading];\n});\nexport default useApi;", "map": {"version": 3, "names": ["useState", "useCallback", "API_BASE_URL", "useError", "useLoading", "useApi", "baseUrl", "options", "_s", "loading", "setLoading", "error", "setError", "handleApiError", "clearError", "setGlobalLoading", "clearLoading", "useGlobalLoading", "loadingKey", "autoHandleErrors", "apiCall", "endpoint", "requestOptions", "retryCount", "maxRetries", "baseDelay", "url", "config", "headers", "response", "fetch", "ok", "status", "retryAfter", "parseInt", "get", "delay", "Math", "min", "pow", "console", "log", "Promise", "resolve", "setTimeout", "errorData", "json", "catch", "Error", "data", "err", "message", "token", "method", "post", "body", "JSON", "stringify", "postFormData", "formData", "put", "patch", "clearLocalError"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/hooks/useApi.js"], "sourcesContent": ["import { useState, useCallback } from 'react';\r\nimport { API_BASE_URL } from '../config/api';\r\nimport { useError } from '../contexts/ErrorContext';\r\nimport { useLoading } from '../contexts/LoadingContext';\r\n\r\n/**\r\n * Custom hook for API calls with loading, error, and success states\r\n * @param {string} baseUrl - The base URL for API calls\r\n * @returns {object} - API utilities and state\r\n */\r\nexport const useApi = (baseUrl = API_BASE_URL, options = {}) => {\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const { handleApiError, clearError } = useError();\r\n  const { setLoading: setGlobalLoading, clearLoading } = useLoading();\r\n\r\n  const {\r\n    useGlobalLoading = false,\r\n    loadingKey = 'api',\r\n    autoHandleErrors = true\r\n  } = options;\r\n\r\n  // Generic API call function with retry logic\r\n  const apiCall = useCallback(async (endpoint, requestOptions = {}, retryCount = 0) => {\r\n    const maxRetries = 3;\r\n    const baseDelay = 1000; // 1 second\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    if (useGlobalLoading) {\r\n      setGlobalLoading(loadingKey, true, `Loading ${endpoint}...`);\r\n    }\r\n\r\n    if (autoHandleErrors) {\r\n      clearError(loadingKey);\r\n    }\r\n\r\n    try {\r\n      const url = `${baseUrl}${endpoint}`;\r\n      const config = {\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          ...requestOptions.headers,\r\n        },\r\n        ...requestOptions,\r\n      };\r\n\r\n      const response = await fetch(url, config);\r\n\r\n      if (!response.ok) {\r\n        // Handle rate limiting with retry\r\n        if (response.status === 429 && retryCount < maxRetries) {\r\n          const retryAfter = parseInt(response.headers.get('Retry-After') || '60', 10);\r\n          const delay = Math.min(retryAfter * 1000, baseDelay * Math.pow(2, retryCount));\r\n\r\n          console.log(`Rate limited. Retrying in ${delay/1000} seconds... (Attempt ${retryCount + 1}/${maxRetries})`);\r\n\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return apiCall(endpoint, requestOptions, retryCount + 1);\r\n        }\r\n\r\n        const errorData = await response.json().catch(() => ({}));\r\n\r\n        if (response.status === 429) {\r\n          throw new Error('Rate limit exceeded. Please try again later.');\r\n        }\r\n\r\n        const error = new Error(errorData.error || `HTTP error! status: ${response.status}`);\r\n        error.response = { status: response.status, data: errorData };\r\n        error.config = { url };\r\n        throw error;\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      if (useGlobalLoading) {\r\n        clearLoading(loadingKey);\r\n      }\r\n\r\n      return data;\r\n    } catch (err) {\r\n      console.error('API call failed:', err);\r\n      setError(err.message);\r\n\r\n      if (autoHandleErrors) {\r\n        handleApiError(err, { endpoint, retryCount });\r\n      }\r\n\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n\r\n      if (useGlobalLoading) {\r\n        clearLoading(loadingKey);\r\n      }\r\n    }\r\n  }, [baseUrl, useGlobalLoading, loadingKey, autoHandleErrors, setGlobalLoading, clearLoading, handleApiError, clearError]);\r\n\r\n  // GET request\r\n  const get = useCallback((endpoint, token = null) => {\r\n    const headers = token ? { 'Authorization': token } : {};\r\n    return apiCall(endpoint, { method: 'GET', headers });\r\n  }, [apiCall]);\r\n\r\n  // POST request\r\n  const post = useCallback((endpoint, data, token = null) => {\r\n    const headers = {\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': token } : {})\r\n    };\r\n    return apiCall(endpoint, {\r\n      method: 'POST',\r\n      headers,\r\n      body: JSON.stringify(data),\r\n    });\r\n  }, [apiCall]);\r\n\r\n  // POST request with FormData (for file uploads)\r\n  const postFormData = useCallback((endpoint, formData, token = null) => {\r\n    const headers = token ? { 'Authorization': token } : {};\r\n    // Don't set Content-Type for FormData, let browser set it with boundary\r\n    delete headers['Content-Type'];\r\n    return apiCall(endpoint, {\r\n      method: 'POST',\r\n      headers,\r\n      body: formData,\r\n    });\r\n  }, [apiCall]);\r\n\r\n  // PUT request\r\n  const put = useCallback((endpoint, data, token = null) => {\r\n    const headers = {\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': token } : {})\r\n    };\r\n    return apiCall(endpoint, {\r\n      method: 'PUT',\r\n      headers,\r\n      body: JSON.stringify(data),\r\n    });\r\n  }, [apiCall]);\r\n\r\n  // PATCH request\r\n  const patch = useCallback((endpoint, data, token = null) => {\r\n    const headers = {\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': token } : {})\r\n    };\r\n    return apiCall(endpoint, {\r\n      method: 'PATCH',\r\n      headers,\r\n      body: JSON.stringify(data),\r\n    });\r\n  }, [apiCall]);\r\n\r\n  // Clear local error\r\n  const clearLocalError = useCallback(() => {\r\n    setError(null);\r\n  }, []);\r\n\r\n  return {\r\n    loading,\r\n    error,\r\n    get,\r\n    post,\r\n    postFormData,\r\n    put,\r\n    patch,\r\n    clearError: clearLocalError,\r\n  };\r\n};\r\n\r\nexport default useApi;\r\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC7C,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,UAAU,QAAQ,4BAA4B;;AAEvD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAGA,CAACC,OAAO,GAAGJ,YAAY,EAAEK,OAAO,GAAG,CAAC,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM;IAAEa,cAAc;IAAEC;EAAW,CAAC,GAAGX,QAAQ,CAAC,CAAC;EACjD,MAAM;IAAEO,UAAU,EAAEK,gBAAgB;IAAEC;EAAa,CAAC,GAAGZ,UAAU,CAAC,CAAC;EAEnE,MAAM;IACJa,gBAAgB,GAAG,KAAK;IACxBC,UAAU,GAAG,KAAK;IAClBC,gBAAgB,GAAG;EACrB,CAAC,GAAGZ,OAAO;;EAEX;EACA,MAAMa,OAAO,GAAGnB,WAAW,CAAC,OAAOoB,QAAQ,EAAEC,cAAc,GAAG,CAAC,CAAC,EAAEC,UAAU,GAAG,CAAC,KAAK;IACnF,MAAMC,UAAU,GAAG,CAAC;IACpB,MAAMC,SAAS,GAAG,IAAI,CAAC,CAAC;;IAExBf,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAIK,gBAAgB,EAAE;MACpBF,gBAAgB,CAACG,UAAU,EAAE,IAAI,EAAE,WAAWG,QAAQ,KAAK,CAAC;IAC9D;IAEA,IAAIF,gBAAgB,EAAE;MACpBL,UAAU,CAACI,UAAU,CAAC;IACxB;IAEA,IAAI;MACF,MAAMQ,GAAG,GAAG,GAAGpB,OAAO,GAAGe,QAAQ,EAAE;MACnC,MAAMM,MAAM,GAAG;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,GAAGN,cAAc,CAACM;QACpB,CAAC;QACD,GAAGN;MACL,CAAC;MAED,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEC,MAAM,CAAC;MAEzC,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;QAChB;QACA,IAAIF,QAAQ,CAACG,MAAM,KAAK,GAAG,IAAIT,UAAU,GAAGC,UAAU,EAAE;UACtD,MAAMS,UAAU,GAAGC,QAAQ,CAACL,QAAQ,CAACD,OAAO,CAACO,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;UAC5E,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACL,UAAU,GAAG,IAAI,EAAER,SAAS,GAAGY,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEhB,UAAU,CAAC,CAAC;UAE9EiB,OAAO,CAACC,GAAG,CAAC,6BAA6BL,KAAK,GAAC,IAAI,wBAAwBb,UAAU,GAAG,CAAC,IAAIC,UAAU,GAAG,CAAC;UAE3G,MAAM,IAAIkB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEP,KAAK,CAAC,CAAC;UACxD,OAAOhB,OAAO,CAACC,QAAQ,EAAEC,cAAc,EAAEC,UAAU,GAAG,CAAC,CAAC;QAC1D;QAEA,MAAMsB,SAAS,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAIlB,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;UAC3B,MAAM,IAAIgB,KAAK,CAAC,8CAA8C,CAAC;QACjE;QAEA,MAAMrC,KAAK,GAAG,IAAIqC,KAAK,CAACH,SAAS,CAAClC,KAAK,IAAI,uBAAuBkB,QAAQ,CAACG,MAAM,EAAE,CAAC;QACpFrB,KAAK,CAACkB,QAAQ,GAAG;UAAEG,MAAM,EAAEH,QAAQ,CAACG,MAAM;UAAEiB,IAAI,EAAEJ;QAAU,CAAC;QAC7DlC,KAAK,CAACgB,MAAM,GAAG;UAAED;QAAI,CAAC;QACtB,MAAMf,KAAK;MACb;MAEA,MAAMsC,IAAI,GAAG,MAAMpB,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAElC,IAAI7B,gBAAgB,EAAE;QACpBD,YAAY,CAACE,UAAU,CAAC;MAC1B;MAEA,OAAO+B,IAAI;IACb,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZV,OAAO,CAAC7B,KAAK,CAAC,kBAAkB,EAAEuC,GAAG,CAAC;MACtCtC,QAAQ,CAACsC,GAAG,CAACC,OAAO,CAAC;MAErB,IAAIhC,gBAAgB,EAAE;QACpBN,cAAc,CAACqC,GAAG,EAAE;UAAE7B,QAAQ;UAAEE;QAAW,CAAC,CAAC;MAC/C;MAEA,MAAM2B,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;MAEjB,IAAIO,gBAAgB,EAAE;QACpBD,YAAY,CAACE,UAAU,CAAC;MAC1B;IACF;EACF,CAAC,EAAE,CAACZ,OAAO,EAAEW,gBAAgB,EAAEC,UAAU,EAAEC,gBAAgB,EAAEJ,gBAAgB,EAAEC,YAAY,EAAEH,cAAc,EAAEC,UAAU,CAAC,CAAC;;EAEzH;EACA,MAAMqB,GAAG,GAAGlC,WAAW,CAAC,CAACoB,QAAQ,EAAE+B,KAAK,GAAG,IAAI,KAAK;IAClD,MAAMxB,OAAO,GAAGwB,KAAK,GAAG;MAAE,eAAe,EAAEA;IAAM,CAAC,GAAG,CAAC,CAAC;IACvD,OAAOhC,OAAO,CAACC,QAAQ,EAAE;MAAEgC,MAAM,EAAE,KAAK;MAAEzB;IAAQ,CAAC,CAAC;EACtD,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMkC,IAAI,GAAGrD,WAAW,CAAC,CAACoB,QAAQ,EAAE4B,IAAI,EAAEG,KAAK,GAAG,IAAI,KAAK;IACzD,MAAMxB,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAEA;MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IACD,OAAOhC,OAAO,CAACC,QAAQ,EAAE;MACvBgC,MAAM,EAAE,MAAM;MACdzB,OAAO;MACP2B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMsC,YAAY,GAAGzD,WAAW,CAAC,CAACoB,QAAQ,EAAEsC,QAAQ,EAAEP,KAAK,GAAG,IAAI,KAAK;IACrE,MAAMxB,OAAO,GAAGwB,KAAK,GAAG;MAAE,eAAe,EAAEA;IAAM,CAAC,GAAG,CAAC,CAAC;IACvD;IACA,OAAOxB,OAAO,CAAC,cAAc,CAAC;IAC9B,OAAOR,OAAO,CAACC,QAAQ,EAAE;MACvBgC,MAAM,EAAE,MAAM;MACdzB,OAAO;MACP2B,IAAI,EAAEI;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvC,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMwC,GAAG,GAAG3D,WAAW,CAAC,CAACoB,QAAQ,EAAE4B,IAAI,EAAEG,KAAK,GAAG,IAAI,KAAK;IACxD,MAAMxB,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAEA;MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IACD,OAAOhC,OAAO,CAACC,QAAQ,EAAE;MACvBgC,MAAM,EAAE,KAAK;MACbzB,OAAO;MACP2B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMyC,KAAK,GAAG5D,WAAW,CAAC,CAACoB,QAAQ,EAAE4B,IAAI,EAAEG,KAAK,GAAG,IAAI,KAAK;IAC1D,MAAMxB,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAEA;MAAM,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IACD,OAAOhC,OAAO,CAACC,QAAQ,EAAE;MACvBgC,MAAM,EAAE,OAAO;MACfzB,OAAO;MACP2B,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,IAAI;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC;;EAEb;EACA,MAAM0C,eAAe,GAAG7D,WAAW,CAAC,MAAM;IACxCW,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLH,OAAO;IACPE,KAAK;IACLwB,GAAG;IACHmB,IAAI;IACJI,YAAY;IACZE,GAAG;IACHC,KAAK;IACL/C,UAAU,EAAEgD;EACd,CAAC;AACH,CAAC;AAACtD,EAAA,CAjKWH,MAAM;EAAA,QAGsBF,QAAQ,EACQC,UAAU;AAAA;AA+JnE,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}