import React from 'react';
import { Link } from 'react-router-dom';
import './SellerCard.css';

const SellerCard = ({ seller, showProducts = true }) => {
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short'
        });
    };

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(<span key={i} className="star filled">★</span>);
        }

        if (hasHalfStar) {
            stars.push(<span key="half" className="star half">★</span>);
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(<span key={`empty-${i}`} className="star empty">☆</span>);
        }

        return stars;
    };

    return (
        <div className="seller-card">
            <div className="seller-card-header">
                {seller.store_logo && (
                    <div className="seller-logo">
                        <img src={seller.store_logo} alt={seller.store_name} />
                    </div>
                )}
                <div className="seller-info">
                    <h3 className="seller-name">
                        <Link to={`/seller/${seller.store_slug}`}>
                            {seller.store_name || seller.business_name}
                        </Link>
                    </h3>
                    <p className="seller-business">{seller.business_name}</p>
                    {seller.contact_person && (
                        <p className="seller-contact">by {seller.contact_person}</p>
                    )}
                </div>
                {seller.is_verified && (
                    <div className="verified-badge">
                        <span className="verified-icon">✓</span>
                        <span className="verified-text">Verified</span>
                    </div>
                )}
            </div>

            <div className="seller-card-body">
                {seller.description && (
                    <p className="seller-description">{seller.description}</p>
                )}

                <div className="seller-stats">
                    <div className="stat-item">
                        <span className="stat-label">Rating</span>
                        <div className="rating-display">
                            <div className="stars">
                                {renderStars(seller.rating)}
                            </div>
                            <span className="rating-value">({seller.rating})</span>
                        </div>
                    </div>

                    {showProducts && (
                        <div className="stat-item">
                            <span className="stat-label">Products</span>
                            <span className="stat-value">{seller.total_products}</span>
                        </div>
                    )}

                    <div className="stat-item">
                        <span className="stat-label">Joined</span>
                        <span className="stat-value">{formatDate(seller.joined_date)}</span>
                    </div>

                    {seller.category && (
                        <div className="stat-item">
                            <span className="stat-label">Category</span>
                            <span className="stat-value">{seller.category}</span>
                        </div>
                    )}
                </div>
            </div>

            <div className="seller-card-footer">
                <Link to={`/seller/${seller.store_slug}`} className="view-store-btn">
                    View Store
                </Link>
                {showProducts && seller.total_products > 0 && (
                    <Link to={`/products?seller_id=${seller.id}`} className="view-products-btn">
                        View Products ({seller.total_products})
                    </Link>
                )}
            </div>
        </div>
    );
};

export default SellerCard;
