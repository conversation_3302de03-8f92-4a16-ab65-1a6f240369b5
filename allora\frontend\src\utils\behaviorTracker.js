/**
 * User Behavior Tracking Utility
 * ==============================
 * 
 * Frontend utility for tracking user interactions and behavior patterns.
 * Integrates with the backend behavior tracking API.
 * 
 * Features:
 * - Automatic interaction tracking
 * - Session management
 * - Batch request optimization
 * - Offline support with queue
 * - Privacy-compliant tracking
 * 
 * Author: Allora Development Team
 * Date: 2025-07-06
 */

class BehaviorTracker {
    constructor() {
        this.apiBaseUrl = '/api/behavior';
        this.sessionId = this.generateSessionId();
        this.userId = null;
        this.isEnabled = true;
        this.batchSize = 10;
        this.batchTimeout = 5000; // 5 seconds
        
        // Interaction queue for batch processing
        this.interactionQueue = [];
        this.batchTimer = null;
        
        // Session data
        this.sessionStartTime = Date.now();
        this.pageStartTime = Date.now();
        this.currentPage = window.location.pathname;
        
        // Initialize tracking
        this.initializeTracking();
    }
    
    /**
     * Initialize behavior tracking
     */
    initializeTracking() {
        // Get user ID from localStorage or session
        this.userId = localStorage.getItem('userId') || sessionStorage.getItem('userId');
        
        // Set up automatic tracking
        this.setupPageTracking();
        this.setupClickTracking();
        this.setupScrollTracking();
        this.setupFormTracking();
        
        // Handle page unload
        window.addEventListener('beforeunload', () => {
            this.flushQueue();
            this.trackPageView(this.currentPage, Date.now() - this.pageStartTime);
        });
        
        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.flushQueue();
            }
        });
    }
    
    /**
     * Set user ID for tracking
     */
    setUserId(userId) {
        this.userId = userId;
        localStorage.setItem('userId', userId);
    }
    
    /**
     * Enable or disable tracking
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        if (!enabled) {
            this.flushQueue();
        }
    }
    
    /**
     * Track a generic interaction
     */
    async trackInteraction(productId, interactionType, options = {}) {
        if (!this.isEnabled || !this.userId) return;
        
        const interaction = {
            user_id: this.userId,
            product_id: productId,
            interaction_type: interactionType,
            session_id: this.sessionId,
            value: options.value || null,
            context: {
                page: window.location.pathname,
                timestamp: Date.now(),
                user_agent: navigator.userAgent,
                screen_resolution: `${screen.width}x${screen.height}`,
                viewport_size: `${window.innerWidth}x${window.innerHeight}`,
                ...options.context
            },
            metadata: {
                referrer: document.referrer,
                ...options.metadata
            }
        };
        
        // Add to queue for batch processing
        this.addToQueue(interaction);
    }
    
    /**
     * Track product view
     */
    trackProductView(productId, options = {}) {
        this.trackInteraction(productId, 'view', {
            context: {
                view_duration: options.duration || 0,
                scroll_depth: this.getScrollDepth(),
                ...options.context
            }
        });
    }
    
    /**
     * Track product click
     */
    trackProductClick(productId, options = {}) {
        this.trackInteraction(productId, 'click', {
            context: {
                click_position: options.position || null,
                element_type: options.elementType || 'product',
                ...options.context
            }
        });
    }
    
    /**
     * Track add to cart
     */
    trackAddToCart(productId, quantity = 1, price = null) {
        this.trackInteraction(productId, 'add_to_cart', {
            value: quantity,
            context: {
                quantity: quantity,
                price: price,
                cart_size: this.getCartSize()
            }
        });
    }
    
    /**
     * Track remove from cart
     */
    trackRemoveFromCart(productId, quantity = 1) {
        this.trackInteraction(productId, 'remove_from_cart', {
            value: quantity,
            context: {
                quantity: quantity,
                cart_size: this.getCartSize()
            }
        });
    }
    
    /**
     * Track purchase
     */
    async trackPurchase(orderId, products, totalAmount) {
        if (!this.isEnabled || !this.userId) return;
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/track/purchase`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: this.userId,
                    order_id: orderId,
                    products: products,
                    total_amount: totalAmount,
                    session_id: this.sessionId
                })
            });
            
            if (!response.ok) {
                console.warn('Failed to track purchase:', response.statusText);
            }
        } catch (error) {
            console.warn('Error tracking purchase:', error);
        }
    }
    
    /**
     * Track search interaction
     */
    async trackSearch(searchQuery, resultsCount, clickedProducts = []) {
        if (!this.isEnabled || !this.userId) return;
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/track/search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: this.userId,
                    search_query: searchQuery,
                    results_count: resultsCount,
                    clicked_products: clickedProducts,
                    session_id: this.sessionId
                })
            });
            
            if (!response.ok) {
                console.warn('Failed to track search:', response.statusText);
            }
        } catch (error) {
            console.warn('Error tracking search:', error);
        }
    }
    
    /**
     * Track page view
     */
    async trackPageView(pageType, pageId, duration = null) {
        if (!this.isEnabled || !this.userId) return;
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/track/page-view`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: this.userId,
                    page_type: pageType,
                    page_id: pageId,
                    session_id: this.sessionId,
                    duration: duration,
                    context: {
                        url: window.location.href,
                        title: document.title,
                        scroll_depth: this.getScrollDepth()
                    }
                })
            });
            
            if (!response.ok) {
                console.warn('Failed to track page view:', response.statusText);
            }
        } catch (error) {
            console.warn('Error tracking page view:', error);
        }
    }
    
    /**
     * Track rating/review
     */
    trackRating(productId, rating, review = null) {
        this.trackInteraction(productId, 'rating', {
            value: rating,
            context: {
                rating: rating,
                has_review: !!review,
                review_length: review ? review.length : 0
            }
        });
    }
    
    /**
     * Track wishlist actions
     */
    trackWishlistAdd(productId) {
        this.trackInteraction(productId, 'wishlist_add');
    }
    
    trackWishlistRemove(productId) {
        this.trackInteraction(productId, 'wishlist_remove');
    }
    
    /**
     * Track product sharing
     */
    trackShare(productId, platform) {
        this.trackInteraction(productId, 'share', {
            context: {
                platform: platform,
                share_method: 'social'
            }
        });
    }
    
    /**
     * Track product comparison
     */
    trackCompare(productIds) {
        productIds.forEach(productId => {
            this.trackInteraction(productId, 'compare', {
                context: {
                    compared_products: productIds,
                    comparison_count: productIds.length
                }
            });
        });
    }
    
    /**
     * Add interaction to queue for batch processing
     */
    addToQueue(interaction) {
        this.interactionQueue.push(interaction);
        
        // Process queue if it reaches batch size
        if (this.interactionQueue.length >= this.batchSize) {
            this.flushQueue();
        } else {
            // Set timer for batch processing
            if (this.batchTimer) {
                clearTimeout(this.batchTimer);
            }
            this.batchTimer = setTimeout(() => {
                this.flushQueue();
            }, this.batchTimeout);
        }
    }
    
    /**
     * Flush interaction queue
     */
    async flushQueue() {
        if (this.interactionQueue.length === 0) return;
        
        const interactions = [...this.interactionQueue];
        this.interactionQueue = [];
        
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }
        
        // Send batch request
        try {
            for (const interaction of interactions) {
                await fetch(`${this.apiBaseUrl}/track`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(interaction)
                });
            }
        } catch (error) {
            console.warn('Error flushing interaction queue:', error);
            // Re-add failed interactions to queue for retry
            this.interactionQueue.unshift(...interactions);
        }
    }
    
    /**
     * Set up automatic page tracking
     */
    setupPageTracking() {
        // Track initial page load
        this.trackPageView('page', window.location.pathname);
        
        // Track page changes (for SPAs)
        let lastUrl = window.location.href;
        const observer = new MutationObserver(() => {
            if (window.location.href !== lastUrl) {
                // Track previous page duration
                this.trackPageView(this.currentPage, 'page', Date.now() - this.pageStartTime);
                
                // Update current page
                lastUrl = window.location.href;
                this.currentPage = window.location.pathname;
                this.pageStartTime = Date.now();
                
                // Track new page
                this.trackPageView('page', this.currentPage);
            }
        });
        
        observer.observe(document, { subtree: true, childList: true });
    }
    
    /**
     * Set up automatic click tracking
     */
    setupClickTracking() {
        document.addEventListener('click', (event) => {
            const element = event.target.closest('[data-product-id]');
            if (element) {
                const productId = element.getAttribute('data-product-id');
                const elementType = element.tagName.toLowerCase();
                
                this.trackProductClick(productId, {
                    elementType: elementType,
                    position: {
                        x: event.clientX,
                        y: event.clientY
                    }
                });
            }
        });
    }
    
    /**
     * Set up scroll tracking
     */
    setupScrollTracking() {
        let maxScrollDepth = 0;
        
        window.addEventListener('scroll', () => {
            const scrollDepth = this.getScrollDepth();
            if (scrollDepth > maxScrollDepth) {
                maxScrollDepth = scrollDepth;
            }
        });
        
        // Track scroll depth on page unload
        window.addEventListener('beforeunload', () => {
            if (maxScrollDepth > 0) {
                // Track as context in page view
                this.trackPageView(this.currentPage, 'page', Date.now() - this.pageStartTime);
            }
        });
    }
    
    /**
     * Set up form tracking
     */
    setupFormTracking() {
        document.addEventListener('submit', (event) => {
            const form = event.target;
            if (form.tagName === 'FORM') {
                const formType = form.getAttribute('data-form-type') || 'unknown';
                
                // Track form submission as interaction
                this.trackInteraction('form_submission', 'click', {
                    context: {
                        form_type: formType,
                        form_id: form.id || null,
                        field_count: form.elements.length
                    }
                });
            }
        });
    }
    
    /**
     * Utility functions
     */
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    getScrollDepth() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
        return documentHeight > 0 ? Math.round((scrollTop / documentHeight) * 100) : 0;
    }
    
    getCartSize() {
        // Try to get cart size from localStorage or global state
        try {
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            return cart.length;
        } catch {
            return 0;
        }
    }
}

// Create global instance
const behaviorTracker = new BehaviorTracker();

// Export for use in components
export default behaviorTracker;

// Also make available globally
window.behaviorTracker = behaviorTracker;
