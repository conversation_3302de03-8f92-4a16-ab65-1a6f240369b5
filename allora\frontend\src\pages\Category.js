import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, Link } from 'react-router-dom';
import VirtualizedProductGrid from '../components/VirtualizedProductGrid';
import SearchFilters from '../components/SearchFilters';
import SortControls from '../components/SortControls';
import { API_BASE_URL } from '../config/api';
import { Sparkles, Leaf, Award, TrendingUp, Filter, Grid, List, ChevronRight } from 'lucide-react';

const Category = ({ token }) => {
  const { categoryName } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();

  // State
  const [products, setProducts] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    pages: 0,
    has_next: false,
    has_prev: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Filter state (simplified for category page)
  const [filters, setFilters] = useState({
    categories: [],
    brands: [],
    sellers: [],
    priceRange: { min: 0, max: 10000 },
    ratingRange: { min: 0, max: 5 },
    sustainabilityRange: { min: 0, max: 100 },
    inStockOnly: false
  });

  // Sort and view state
  const [sortBy, setSortBy] = useState('price');
  const [sortOrder, setSortOrder] = useState('asc');
  const [viewMode, setViewMode] = useState('grid');

  // Decode category name for display
  const decodedCategoryName = categoryName ?
    categoryName.replace('-', ' ').replace('and', '&') : '';

  // Category configuration with real images and modern styling
  const getCategoryConfig = (categoryName) => {
    const configs = {
      'clothing': {
        title: 'Sustainable Clothing',
        subtitle: 'Eco-friendly fashion that makes a statement',
        heroImage: 'https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        gradient: 'from-emerald-600/90 via-teal-600/80 to-cyan-600/90',
        icon: '👗',
        features: ['Organic Materials', 'Fair Trade', 'Carbon Neutral', 'Recyclable'],
        description: 'Discover our curated collection of sustainable clothing made from organic and recycled materials.'
      },
      'footwear': {
        title: 'Eco-Friendly Footwear',
        subtitle: 'Step forward with sustainable style',
        heroImage: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        gradient: 'from-amber-600/90 via-orange-600/80 to-red-600/90',
        icon: '👟',
        features: ['Vegan Materials', 'Recycled Soles', 'Comfort Fit', 'Durable'],
        description: 'Walk confidently in shoes crafted from sustainable materials without compromising on style.'
      },
      'accessories': {
        title: 'Sustainable Accessories',
        subtitle: 'Complete your look responsibly',
        heroImage: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        gradient: 'from-purple-600/90 via-pink-600/80 to-rose-600/90',
        icon: '👜',
        features: ['Handcrafted', 'Eco Materials', 'Timeless Design', 'Versatile'],
        description: 'Elevate your style with accessories that reflect your commitment to sustainability.'
      },
      'kitchen-dining': {
        title: 'Eco Kitchen & Dining',
        subtitle: 'Sustainable solutions for your home',
        heroImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        gradient: 'from-green-600/90 via-emerald-600/80 to-teal-600/90',
        icon: '🍽️',
        features: ['BPA Free', 'Reusable', 'Dishwasher Safe', 'Eco-Friendly'],
        description: 'Transform your kitchen with sustainable products that reduce waste and environmental impact.'
      },
      'electronics': {
        title: 'Green Electronics',
        subtitle: 'Technology with a conscience',
        heroImage: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        gradient: 'from-blue-600/90 via-indigo-600/80 to-purple-600/90',
        icon: '📱',
        features: ['Energy Efficient', 'Recyclable', 'Long Lasting', 'Low Impact'],
        description: 'Discover electronics designed with sustainability in mind, from renewable materials to energy efficiency.'
      },
      'sports-fitness': {
        title: 'Eco Sports & Fitness',
        subtitle: 'Train sustainably, perform exceptionally',
        heroImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        gradient: 'from-cyan-600/90 via-blue-600/80 to-indigo-600/90',
        icon: '🏃‍♂️',
        features: ['Natural Materials', 'Performance Grade', 'Eco-Friendly', 'Durable'],
        description: 'Achieve your fitness goals with gear that supports both your performance and the planet.'
      }
    };

    const normalizedName = categoryName.toLowerCase().replace(/\s+/g, '-').replace('&', 'and');
    return configs[normalizedName] || {
      title: decodedCategoryName,
      subtitle: `Discover our ${decodedCategoryName.toLowerCase()} collection`,
      heroImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
      gradient: 'from-gray-600/90 via-slate-600/80 to-zinc-600/90',
      icon: '📦',
      features: ['Quality', 'Sustainable', 'Affordable', 'Reliable'],
      description: `Explore our carefully curated ${decodedCategoryName.toLowerCase()} products.`
    };
  };

  const categoryConfig = getCategoryConfig(decodedCategoryName);

  // Initialize from URL parameters
  useEffect(() => {
    const urlSortBy = searchParams.get('sort_by') || 'price';
    const urlSortOrder = searchParams.get('sort_order') || 'asc';
    const urlPage = parseInt(searchParams.get('page')) || 1;

    setSortBy(urlSortBy);
    setSortOrder(urlSortOrder);
    setPagination(prev => ({ ...prev, page: urlPage }));
  }, [searchParams]);

  // Fetch products when parameters change
  useEffect(() => {
    if (categoryName) {
      fetchCategoryProducts();
    }
  }, [categoryName, filters, sortBy, sortOrder, pagination.page]);

  const fetchCategoryProducts = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();

      if (filters.brands.length > 0) params.append('brand', filters.brands.join(','));
      if (filters.sellers && filters.sellers.length > 0) params.append('seller_id', filters.sellers.join(','));
      if (filters.priceRange.min > 0) params.append('min_price', filters.priceRange.min);
      if (filters.priceRange.max < 10000) params.append('max_price', filters.priceRange.max);
      if (filters.ratingRange.min > 0) params.append('min_rating', filters.ratingRange.min);
      if (filters.ratingRange.max < 5) params.append('max_rating', filters.ratingRange.max);
      if (filters.sustainabilityRange.min > 0) params.append('min_sustainability', filters.sustainabilityRange.min);
      if (filters.sustainabilityRange.max < 100) params.append('max_sustainability', filters.sustainabilityRange.max);
      if (filters.inStockOnly) params.append('in_stock_only', 'true');

      params.append('sort_by', sortBy);
      params.append('sort_order', sortOrder);
      params.append('page', pagination.page);
      params.append('per_page', pagination.per_page);

      const response = await fetch(`${API_BASE_URL}/categories/${categoryName}/products?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch category products');
      }

      const data = await response.json();
      setProducts(data.products || []);
      setPagination(data.pagination || {});

    } catch (err) {
      setError(err.message);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, page: 1 }));
    
    // Update URL
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', '1');
    setSearchParams(newParams);
  };

  const handleClearFilters = () => {
    // Reset filters to default state
    const defaultFilters = {
      categories: [],
      brands: [],
      sellers: [],
      priceRange: { min: 0, max: 10000 },
      ratingRange: { min: 0, max: 5 },
      sustainabilityRange: { min: 0, max: 100 },
      inStockOnly: false
    };

    setFilters(defaultFilters);
    setPagination(prev => ({ ...prev, page: 1 }));

    // Clear filter-related URL parameters while keeping sort and category
    const newParams = new URLSearchParams(searchParams);

    // Remove filter parameters
    newParams.delete('brand');
    newParams.delete('min_price');
    newParams.delete('max_price');
    newParams.delete('min_rating');
    newParams.delete('max_rating');
    newParams.delete('min_sustainability');
    newParams.delete('max_sustainability');
    newParams.delete('in_stock_only');

    // Reset to first page
    newParams.set('page', '1');

    setSearchParams(newParams);
  };

  const handleSortChange = (newSortBy, newSortOrder) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    
    // Update URL
    const newParams = new URLSearchParams(searchParams);
    newParams.set('sort_by', newSortBy);
    newParams.set('sort_order', newSortOrder);
    newParams.set('page', '1');
    setSearchParams(newParams);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
    
    // Update URL
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', newPage.toString());
    setSearchParams(newParams);
    
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center">
        <div className="text-center bg-white p-8 rounded-2xl shadow-xl max-w-md mx-4">
          <div className="text-6xl mb-4">😔</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Oops! Something went wrong</h2>
          <p className="text-red-500 text-lg mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all duration-200 font-semibold"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-gray-50">

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${categoryConfig.heroImage})` }}
        />
        <div className={`absolute inset-0 bg-gradient-to-r ${categoryConfig.gradient}`} />

        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000" />
        </div>

        <div className="relative z-10 container mx-auto px-4 py-20">
          {/* Breadcrumb Navigation */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-2">
              <li className="inline-flex items-center">
                <Link to="/" className="inline-flex items-center text-sm font-medium text-white/80 hover:text-white transition-colors">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                  </svg>
                  Home
                </Link>
              </li>
              <li>
                <ChevronRight className="w-4 h-4 text-white/60" />
              </li>
              <li>
                <Link to="/search" className="text-sm font-medium text-white/80 hover:text-white transition-colors">
                  Categories
                </Link>
              </li>
              <li>
                <ChevronRight className="w-4 h-4 text-white/60" />
              </li>
              <li aria-current="page">
                <span className="text-sm font-medium text-white">
                  {categoryConfig.title}
                </span>
              </li>
            </ol>
          </nav>

          {/* Hero Content */}
          <div className="text-center text-white max-w-4xl mx-auto">
            <div className="text-6xl mb-6 animate-bounce">
              {categoryConfig.icon}
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80">
              {categoryConfig.title}
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-white/90 font-light">
              {categoryConfig.subtitle}
            </p>
            <p className="text-lg mb-8 text-white/80 max-w-2xl mx-auto leading-relaxed">
              {categoryConfig.description}
            </p>

            {/* Feature Tags */}
            <div className="flex flex-wrap justify-center gap-3 mb-8">
              {categoryConfig.features.map((feature, index) => (
                <span
                  key={index}
                  className="px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium text-white border border-white/30 hover:bg-white/30 transition-all duration-200"
                >
                  {feature}
                </span>
              ))}
            </div>

            {/* Stats */}
            <div className="flex justify-center items-center space-x-8 text-white/90">
              <div className="text-center">
                <div className="text-2xl font-bold">{pagination.total || 0}</div>
                <div className="text-sm">Products</div>
              </div>
              <div className="w-px h-8 bg-white/30" />
              <div className="text-center flex items-center">
                <Leaf className="w-5 h-5 mr-2" />
                <span className="text-sm">100% Sustainable</span>
              </div>
              <div className="w-px h-8 bg-white/30" />
              <div className="text-center flex items-center">
                <Award className="w-5 h-5 mr-2" />
                <span className="text-sm">Premium Quality</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col xl:flex-row gap-8">

          {/* Modern Filters Sidebar */}
          <div className="xl:w-80 flex-shrink-0">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden sticky top-4">
              <div className="bg-gradient-to-r from-gray-50 to-white p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    <Filter className="w-5 h-5 mr-2 text-gray-600" />
                    Filters
                  </h3>
                  <button
                    onClick={handleClearFilters}
                    className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    Clear All
                  </button>
                </div>
              </div>
              <div className="p-6">
                <SearchFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onClearFilters={handleClearFilters}
                  isLoading={loading}
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="flex-1">

            {/* Modern Sort Controls */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center space-x-4">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {pagination.total || 0} Products Found
                  </h2>
                  {pagination.total > 0 && (
                    <div className="flex items-center text-sm text-gray-500">
                      <TrendingUp className="w-4 h-4 mr-1" />
                      Sorted by {sortBy === 'price' ? 'Price' : sortBy === 'name' ? 'Name' : 'Relevance'}
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-4">
                  <SortControls
                    sortBy={sortBy}
                    sortOrder={sortOrder}
                    onSortChange={handleSortChange}
                    resultCount={pagination.total}
                    viewMode={viewMode}
                    onViewModeChange={setViewMode}
                  />
                </div>
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-12">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mb-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent"></div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Products</h3>
                  <p className="text-gray-600">Finding the best {decodedCategoryName.toLowerCase()} for you...</p>
                </div>
              </div>
            )}

            {/* Products Grid */}
            {!loading && products.length > 0 && (
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <VirtualizedProductGrid
                  products={products}
                  height={600}
                  itemHeight={viewMode === 'grid' ? 320 : 200}
                  itemWidth={viewMode === 'grid' ? 300 : 800}
                  emptyMessage={`No ${decodedCategoryName.toLowerCase()} products found.`}
                  viewMode={viewMode}
                />
              </div>
            )}

            {/* No Results */}
            {!loading && products.length === 0 && (
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-12">
                <div className="text-center">
                  <div className="text-8xl mb-6 opacity-50">🔍</div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">
                    No products found in {categoryConfig.title}
                  </h3>
                  <p className="text-gray-600 mb-8 max-w-md mx-auto">
                    We couldn't find any products matching your criteria. Try adjusting your filters or explore other categories.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                      onClick={handleClearFilters}
                      className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200 font-medium"
                    >
                      Clear Filters
                    </button>
                    <Link
                      to="/search"
                      className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 font-medium"
                    >
                      Browse All Products
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {/* Modern Pagination */}
            {!loading && products.length > 0 && pagination.pages > 1 && (
              <div className="mt-8 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    Showing page {pagination.page} of {pagination.pages}
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.has_prev}
                      className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
                    >
                      Previous
                    </button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                        const pageNum = i + 1;
                        return (
                          <button
                            key={pageNum}
                            onClick={() => handlePageChange(pageNum)}
                            className={`w-10 h-10 rounded-lg font-medium transition-colors duration-200 ${
                              pageNum === pagination.page
                                ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.has_next}
                      className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}

          </div>
        </div>
      </div>
    </div>
  );
};

export default Category;
