import React, { useState } from 'react';
import { useCart } from '../contexts/CartContext';
import { useNotification } from '../contexts/NotificationContext';
import { formatPrice } from '../utils/currency';
import { getProductImageUrl, getLoadingPlaceholder, handleImageError } from '../utils/imageUtils';
import SellerInfo from './SellerInfo';

const ModernMinimalistProductCard = React.memo(({ product }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const { addToCart } = useCart();
  const { success, error } = useNotification();

  const handleAddToCart = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    setIsLoading(true);
    try {
      await addToCart(product.id, 1);
      success(`${product.name} added to cart!`, {
        title: 'Added to Cart'
      });
    } catch (err) {
      error('Failed to add item to cart', {
        title: 'Cart Error'
      });
      console.error('Add to cart error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleProductClick = () => {
    window.location.href = `/product/${product.id}`;
  };

  const handleWishlist = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist', {
      title: 'Wishlist Updated'
    });
  };

  // Calculate discount percentage if original price exists
  const discountPercentage = product.originalPrice && product.originalPrice > product.price
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : null;

  // Generate star rating
  const rating = product.rating || 4.2;
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;

  return (
    <div
      className="bg-white rounded-2xl hover:shadow-xl transition-all duration-300 p-4 h-full flex flex-col group cursor-pointer border border-gray-100 hover:border-gray-200 hover:-translate-y-1"
      onClick={handleProductClick}
    >
      {/* Product Image - Fixed Height & Fully Visible */}
      <div className="relative mb-4 overflow-hidden rounded-xl bg-gray-50">
        <img
          src={getProductImageUrl(product)}
          alt={product.name || 'Product Image'}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
          loading="lazy"
          onError={(e) => handleImageError(e)}
          style={{
            display: 'block',
            width: '100%',
            height: '192px', // Fixed height for consistency
            objectFit: 'cover'
          }}
        />
        
        {/* Wishlist Button */}
        <button
          onClick={handleWishlist}
          className={`absolute top-4 right-4 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm ${
            isWishlisted 
              ? 'bg-red-500 text-white shadow-lg' 
              : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500 hover:shadow-md'
          }`}
        >
          <svg className="w-5 h-5" fill={isWishlisted ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Discount Badge */}
        {discountPercentage && (
          <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg">
            -{discountPercentage}%
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="flex-1 flex flex-col">
        {/* Product Name */}
        <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
          {product.name || 'Unnamed Product'}
        </h3>

        {/* Rating */}
        <div className="flex items-center mb-2">
          <div className="flex items-center">
            {[...Array(fullStars)].map((_, i) => (
              <span key={i} className="text-yellow-400 text-sm">★</span>
            ))}
            {hasHalfStar && <span className="text-yellow-400 text-sm">☆</span>}
            {[...Array(5 - fullStars - (hasHalfStar ? 1 : 0))].map((_, i) => (
              <span key={i} className="text-gray-300 text-sm">★</span>
            ))}
          </div>
          <span className="text-xs text-gray-500 ml-2">({rating})</span>
        </div>

        {/* Price */}
        <div className="flex items-center mb-3">
          <span className="text-xl font-bold text-gray-900">
            {formatPrice(product.price || 0)}
          </span>
          {product.originalPrice && product.originalPrice > product.price && (
            <span className="text-sm text-gray-500 line-through ml-2">
              {formatPrice(product.originalPrice)}
            </span>
          )}
        </div>

        {/* Compact Product Details */}
        <div className="space-y-1 mb-3 flex-1">
          {/* Description - Shortened */}
          {product.description && (
            <p className="text-xs text-gray-600 line-clamp-2">
              {product.description}
            </p>
          )}

          {/* Brand & Category in one line */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            {product.brand && (
              <span className="truncate">
                <span className="font-medium">Brand:</span> {product.brand}
              </span>
            )}
            {product.category && (
              <span className="truncate ml-2">
                <span className="font-medium">Category:</span> {product.category}
              </span>
            )}
          </div>

          {/* Sustainability Score - Compact */}
          {(product.sustainabilityScore || product.sustainability_score) && (
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500 font-medium">Eco:</span>
              <div className="flex items-center flex-1">
                <div className="w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full rounded-full ${
                      (product.sustainabilityScore || product.sustainability_score) >= 80 ? 'bg-green-500' :
                      (product.sustainabilityScore || product.sustainability_score) >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${product.sustainabilityScore || product.sustainability_score}%` }}
                  ></div>
                </div>
                <span className="text-xs text-gray-600 ml-1">{product.sustainabilityScore || product.sustainability_score}/100</span>
              </div>
            </div>
          )}

          {/* Stock Status - Compact */}
          {(product.stockQuantity !== undefined || product.stock_quantity !== undefined) && (
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500 font-medium">Stock:</span>
              <span className={`text-xs font-medium ${
                (product.stockQuantity || product.stock_quantity || 0) > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {(product.stockQuantity || product.stock_quantity || 0) > 0 ? 'In Stock' : 'Out of Stock'}
              </span>
            </div>
          )}
        </div>

        {/* Seller Information - Compact */}
        {product.seller && (
          <SellerInfo seller={product.seller} compact={true} />
        )}

        {/* Add to Cart Button - Compact */}
        <button
          onClick={handleAddToCart}
          disabled={isLoading || (product.stockQuantity || product.stock_quantity || 0) <= 0}
          className={`w-full py-2 px-3 rounded-lg font-medium text-sm transition-all duration-200 ${
            isLoading
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : (product.stockQuantity || product.stock_quantity || 0) <= 0
              ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-md active:scale-95'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-3 w-3 border-2 border-gray-400 border-t-transparent mr-2"></div>
              Adding...
            </div>
          ) : (product.stockQuantity || product.stock_quantity || 0) <= 0 ? (
            'Out of Stock'
          ) : (
            'Add to Cart'
          )}
        </button>
      </div>
    </div>
  );
});

ModernMinimalistProductCard.displayName = 'ModernMinimalistProductCard';

export default ModernMinimalistProductCard;
