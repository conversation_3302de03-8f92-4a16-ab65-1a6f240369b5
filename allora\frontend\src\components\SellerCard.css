.seller-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #e0e0e0;
}

.seller-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.seller-card-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.seller-logo {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.seller-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.seller-info {
    flex: 1;
}

.seller-name {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.seller-name a {
    text-decoration: none;
    color: inherit;
    transition: color 0.2s ease;
}

.seller-name a:hover {
    color: #27ae60;
}

.seller-business {
    margin: 0 0 3px 0;
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

.seller-contact {
    margin: 0;
    font-size: 0.85rem;
    color: #95a5a6;
}

.verified-badge {
    display: flex;
    align-items: center;
    gap: 5px;
    background: #e8f5e8;
    color: #27ae60;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    flex-shrink: 0;
}

.verified-icon {
    background: #27ae60;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.seller-card-body {
    margin-bottom: 20px;
}

.seller-description {
    color: #5d6d7e;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.seller-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stat-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 0.9rem;
    color: #2c3e50;
    font-weight: 600;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    font-size: 14px;
    line-height: 1;
}

.star.filled {
    color: #f39c12;
}

.star.half {
    color: #f39c12;
    opacity: 0.6;
}

.star.empty {
    color: #bdc3c7;
}

.rating-value {
    font-size: 0.85rem;
    color: #7f8c8d;
    font-weight: 500;
}

.seller-card-footer {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.view-store-btn,
.view-products-btn {
    padding: 10px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
    flex: 1;
    min-width: 120px;
}

.view-store-btn {
    background: #27ae60;
    color: white;
    border: 2px solid #27ae60;
}

.view-store-btn:hover {
    background: #229954;
    border-color: #229954;
    transform: translateY(-1px);
}

.view-products-btn {
    background: transparent;
    color: #27ae60;
    border: 2px solid #27ae60;
}

.view-products-btn:hover {
    background: #27ae60;
    color: white;
    transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .seller-card {
        padding: 15px;
        margin-bottom: 15px;
    }

    .seller-card-header {
        gap: 12px;
    }

    .seller-logo {
        width: 50px;
        height: 50px;
    }

    .seller-name {
        font-size: 1.1rem;
    }

    .seller-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .seller-card-footer {
        flex-direction: column;
    }

    .view-store-btn,
    .view-products-btn {
        flex: none;
    }
}

@media (max-width: 480px) {
    .seller-card {
        padding: 12px;
    }

    .seller-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .rating-display {
        gap: 6px;
    }
}
