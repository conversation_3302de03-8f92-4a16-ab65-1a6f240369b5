/* Compact Seller Info for Product Cards */
.seller-info-compact {
    margin-top: 8px;
    padding: 8px 0;
    border-top: 1px solid #e0e0e0;
}

.seller-compact-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
    flex-wrap: wrap;
}

.sold-by-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
}

.seller-name-link {
    font-size: 0.85rem;
    color: #27ae60;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.seller-name-link:hover {
    color: #229954;
    text-decoration: underline;
}

.verified-badge-compact {
    background: #e8f5e8;
    color: #27ae60;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.verified-badge-compact .verified-icon {
    font-size: 10px;
    font-weight: bold;
}

.seller-compact-rating {
    display: flex;
    align-items: center;
    gap: 6px;
}

.seller-compact-rating .stars {
    display: flex;
    gap: 1px;
}

.seller-compact-rating .star {
    font-size: 12px;
}

.seller-compact-rating .rating-text {
    font-size: 0.75rem;
    color: #7f8c8d;
}

/* Full Seller Info for Product Details */
.seller-info {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.seller-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.seller-info-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

.verified-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #e8f5e8;
    color: #27ae60;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.verified-icon {
    background: #27ae60;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
}

.seller-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.seller-main-info {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.seller-logo {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e0e0e0;
}

.seller-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.seller-text-info {
    flex: 1;
}

.seller-store-name {
    margin: 0 0 8px 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
}

.seller-store-name a {
    text-decoration: none;
    color: inherit;
    transition: color 0.2s ease;
}

.seller-store-name a:hover {
    color: #27ae60;
}

.seller-business-name {
    margin: 0 0 6px 0;
    font-size: 1rem;
    color: #7f8c8d;
    font-weight: 500;
}

.seller-contact {
    margin: 0;
    font-size: 0.9rem;
    color: #95a5a6;
}

.seller-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.stat-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    font-size: 16px;
    line-height: 1;
}

.star.filled {
    color: #f39c12;
}

.star.half {
    color: #f39c12;
    opacity: 0.6;
}

.star.empty {
    color: #bdc3c7;
}

.rating-value {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

.seller-description {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
}

.seller-description p {
    margin: 0;
    color: #5d6d7e;
    line-height: 1.6;
}

.seller-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.view-store-btn,
.view-products-btn,
.contact-seller-btn {
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
    flex: 1;
    min-width: 140px;
    border: 2px solid transparent;
}

.view-store-btn {
    background: #27ae60;
    color: white;
    border-color: #27ae60;
}

.view-store-btn:hover {
    background: #229954;
    border-color: #229954;
    transform: translateY(-1px);
}

.view-products-btn {
    background: transparent;
    color: #27ae60;
    border-color: #27ae60;
}

.view-products-btn:hover {
    background: #27ae60;
    color: white;
    transform: translateY(-1px);
}

.contact-seller-btn {
    background: transparent;
    color: #3498db;
    border-color: #3498db;
}

.contact-seller-btn:hover {
    background: #3498db;
    color: white;
    transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .seller-info {
        padding: 15px;
        margin: 15px 0;
    }

    .seller-main-info {
        gap: 12px;
    }

    .seller-logo {
        width: 60px;
        height: 60px;
    }

    .seller-store-name {
        font-size: 1.1rem;
    }

    .seller-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 12px;
    }

    .seller-actions {
        flex-direction: column;
    }

    .view-store-btn,
    .view-products-btn,
    .contact-seller-btn {
        flex: none;
    }
}

@media (max-width: 480px) {
    .seller-info {
        padding: 12px;
    }

    .seller-info-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .seller-stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
}
