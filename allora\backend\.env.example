# Database Configuration
DATABASE_URL=sqlite:///allora.db

# Security
SECRET_KEY=your-super-secret-key-here

# OAuth Configuration
# Google OAuth (Get from: https://console.developers.google.com/)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Facebook OAuth (Get from: https://developers.facebook.com/)
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# GitHub OAuth (Get from: https://github.com/settings/applications/new)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Frontend URL (for CORS and redirects)
FRONTEND_URL=http://localhost:3000

# Optional: Disable HTTPS redirect for development
DISABLE_HTTPS_REDIRECT=true

# Elasticsearch Configuration
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_SCHEME=http
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# Search Configuration
SEARCH_RESULTS_PER_PAGE=20
MAX_SEARCH_RESULTS=1000
SEARCH_TIMEOUT_SECONDS=30

# Analytics Configuration
ENABLE_SEARCH_ANALYTICS=True
ANALYTICS_RETENTION_DAYS=90

# Performance Configuration
ELASTICSEARCH_TIMEOUT=30
ELASTICSEARCH_MAX_RETRIES=3
ELASTICSEARCH_RETRY_ON_TIMEOUT=True
