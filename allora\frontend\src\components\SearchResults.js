import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import OptimizedImage from './OptimizedImage';
import Pagination from './Pagination';
import SortControls from './SortControls';

const SearchResults = ({ 
  results, 
  loading = false, 
  onSortChange,
  onPageChange,
  currentPage = 1,
  showAnalytics = true 
}) => {
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin h-8 w-8 border-2 border-green-600 border-t-transparent rounded-full"></div>
        <span className="ml-3 text-gray-600">Searching...</span>
      </div>
    );
  }

  if (!results || results.error) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <svg className="h-16 w-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Search Error</h3>
        <p className="text-gray-600">{results?.error || 'Something went wrong with your search.'}</p>
      </div>
    );
  }

  if (!results.products || results.products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <svg className="h-16 w-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Found</h3>
        <p className="text-gray-600">Try adjusting your search terms or filters.</p>
      </div>
    );
  }

  const { products, total, total_pages, aggregations, performance } = results;

  const highlightText = (text, highlights) => {
    if (!highlights || highlights.length === 0) return text;
    
    // Combine all highlights and remove HTML tags, then re-highlight
    const highlightedText = highlights.join(' ... ');
    return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />;
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={i} className="h-4 w-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }

    if (hasHalfStar) {
      stars.push(
        <svg key="half" className="h-4 w-4 text-yellow-400" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="half-star">
              <stop offset="50%" stopColor="currentColor" />
              <stop offset="50%" stopColor="transparent" />
            </linearGradient>
          </defs>
          <path fill="url(#half-star)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <svg key={`empty-${i}`} className="h-4 w-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
        </svg>
      );
    }

    return stars;
  };

  const ProductCard = ({ product }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
      <Link to={`/product/${product.id}`} className="block">
        <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg bg-gray-200">
          <OptimizedImage
            src={product.image_url}
            alt={product.name}
            className="h-48 w-full object-cover object-center group-hover:opacity-75"
            fallbackSrc="/api/placeholder/300/300"
          />
        </div>
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2 mb-2">
            {product.highlight?.name ? 
              highlightText(product.name, product.highlight.name) : 
              product.name
            }
          </h3>
          
          {product.highlight?.description && (
            <p className="text-sm text-gray-600 line-clamp-2 mb-2">
              {highlightText(product.description, product.highlight.description)}
            </p>
          )}
          
          <div className="flex items-center justify-between mb-2">
            <span className="text-lg font-bold text-gray-900">
              {formatPrice(product.price)}
            </span>
            {product.original_price && product.original_price > product.price && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.original_price)}
              </span>
            )}
          </div>
          
          {product.average_rating && (
            <div className="flex items-center mb-2">
              <div className="flex items-center">
                {renderStars(product.average_rating)}
              </div>
              <span className="ml-2 text-sm text-gray-600">
                ({product.total_reviews || 0})
              </span>
            </div>
          )}
          
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{product.category}</span>
            {product.brand && <span>{product.brand}</span>}
          </div>
          
          {product.sustainability_score && (
            <div className="mt-2 flex items-center">
              <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                Eco Score: {product.sustainability_score}/100
              </span>
            </div>
          )}
        </div>
      </Link>
    </div>
  );

  const ProductListItem = ({ product }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 p-4">
      <Link to={`/product/${product.id}`} className="flex space-x-4">
        <div className="flex-shrink-0">
          <OptimizedImage
            src={product.image_url}
            alt={product.name}
            className="h-24 w-24 object-cover rounded-lg"
            fallbackSrc="/api/placeholder/150/150"
          />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-gray-900 line-clamp-2 mb-2">
            {product.highlight?.name ? 
              highlightText(product.name, product.highlight.name) : 
              product.name
            }
          </h3>
          
          {product.highlight?.description && (
            <p className="text-sm text-gray-600 line-clamp-3 mb-3">
              {highlightText(product.description, product.highlight.description)}
            </p>
          )}
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-xl font-bold text-gray-900">
                {formatPrice(product.price)}
              </span>
              {product.original_price && product.original_price > product.price && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.original_price)}
                </span>
              )}
            </div>
            
            {product.average_rating && (
              <div className="flex items-center">
                <div className="flex items-center">
                  {renderStars(product.average_rating)}
                </div>
                <span className="ml-2 text-sm text-gray-600">
                  ({product.total_reviews || 0})
                </span>
              </div>
            )}
          </div>
          
          <div className="mt-2 flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span>{product.category}</span>
              {product.brand && <span>{product.brand}</span>}
            </div>
            {product.sustainability_score && (
              <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                Eco Score: {product.sustainability_score}/100
              </span>
            )}
          </div>
        </div>
      </Link>
    </div>
  );

  return (
    <div className="search-results">
      {/* Results Header */}
      <div className="flex items-center justify-between mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-medium text-gray-900">
            {total.toLocaleString()} results found
          </h2>
          
          {showAnalytics && performance && (
            <span className="text-sm text-gray-500">
              ({performance.total_time_ms}ms)
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-4">
          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-md">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-2 text-sm font-medium ${
                viewMode === 'grid'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } rounded-l-md transition-colors`}
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-2 text-sm font-medium ${
                viewMode === 'list'
                  ? 'bg-green-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } rounded-r-md transition-colors`}
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          </div>
          
          {/* Sort Controls */}
          <SortControls onSortChange={onSortChange} />
        </div>
      </div>

      {/* Search Results */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      ) : (
        <div className="space-y-4 mb-8">
          {products.map((product) => (
            <ProductListItem key={product.id} product={product} />
          ))}
        </div>
      )}

      {/* Pagination */}
      {total_pages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={total_pages}
          onPageChange={onPageChange}
        />
      )}

      {/* Search Analytics */}
      {showAnalytics && aggregations && (
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Search Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {aggregations.categories && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Top Categories</h4>
                <div className="space-y-1">
                  {aggregations.categories.buckets.slice(0, 5).map((bucket) => (
                    <div key={bucket.key} className="flex justify-between text-sm">
                      <span className="text-gray-600">{bucket.key}</span>
                      <span className="text-gray-900 font-medium">{bucket.doc_count}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {aggregations.brands && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Top Brands</h4>
                <div className="space-y-1">
                  {aggregations.brands.buckets.slice(0, 5).map((bucket) => (
                    <div key={bucket.key} className="flex justify-between text-sm">
                      <span className="text-gray-600">{bucket.key}</span>
                      <span className="text-gray-900 font-medium">{bucket.doc_count}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {aggregations.price_stats && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Price Range</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Min:</span>
                    <span className="text-gray-900 font-medium">
                      {formatPrice(aggregations.price_stats.min)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max:</span>
                    <span className="text-gray-900 font-medium">
                      {formatPrice(aggregations.price_stats.max)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg:</span>
                    <span className="text-gray-900 font-medium">
                      {formatPrice(aggregations.price_stats.avg)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchResults;
