"""
RMA System Initialization Script
================================

Initialize the RMA (Return Merchandise Authorization) system with:
1. Database table creation
2. Default configuration values
3. Default business rules
4. Sample data for testing

Run this script after adding the RMA models to create the necessary
database structure and configuration.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from rma_architecture import DEFAULT_RMA_RULES, RMA_CONFIG

def init_rma_tables():
    """Create RMA database tables"""
    try:
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ RMA database tables created successfully")
            return True
    except Exception as e:
        print(f"❌ Error creating RMA tables: {e}")
        return False

def init_rma_configuration():
    """Initialize RMA system configuration"""
    try:
        with app.app_context():
            from app import RMAConfiguration
            
            # Default configuration values
            config_values = {
                'return_window_days': {'value': '30', 'type': 'integer', 'description': 'Number of days customers have to return items'},
                'exchange_window_days': {'value': '30', 'type': 'integer', 'description': 'Number of days customers have to exchange items'},
                'auto_approve_threshold': {'value': '500.0', 'type': 'float', 'description': 'Auto-approve returns under this amount (INR)'},
                'require_photos': {'value': 'true', 'type': 'boolean', 'description': 'Require photos for return requests'},
                'require_original_packaging': {'value': 'false', 'type': 'boolean', 'description': 'Require original packaging for returns'},
                'restocking_fee_percentage': {'value': '0.0', 'type': 'float', 'description': 'Restocking fee percentage (0-100)'},
                'free_return_shipping': {'value': 'true', 'type': 'boolean', 'description': 'Provide free return shipping labels'},
                'inspection_required': {'value': 'true', 'type': 'boolean', 'description': 'Require inspection of returned items'},
                'max_return_attempts': {'value': '3', 'type': 'integer', 'description': 'Maximum return attempts per order'},
                'return_center_address': {
                    'value': '{"name": "Allora Returns Center", "address": "123 Return Street", "city": "Mumbai", "state": "Maharashtra", "postal_code": "400001", "country": "India", "phone": "+91-22-1234-5678"}',
                    'type': 'json',
                    'description': 'Return center address for shipping labels'
                },
                'approval_required_reasons': {
                    'value': '["changed_mind", "better_price_found"]',
                    'type': 'json',
                    'description': 'Return reasons that require manual approval'
                },
                'auto_reject_reasons': {
                    'value': '[]',
                    'type': 'json',
                    'description': 'Return reasons that are automatically rejected'
                },
                'notification_emails': {
                    'value': '{"rma_team": "<EMAIL>", "customer_service": "<EMAIL>"}',
                    'type': 'json',
                    'description': 'Email addresses for RMA notifications'
                }
            }
            
            # Insert configuration values
            for key, config in config_values.items():
                existing = RMAConfiguration.query.filter_by(config_key=key).first()
                if not existing:
                    rma_config = RMAConfiguration(
                        config_key=key,
                        config_value=config['value'],
                        data_type=config['type'],
                        description=config['description'],
                        category='system',
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    db.session.add(rma_config)
            
            db.session.commit()
            print("✅ RMA configuration initialized successfully")
            return True
            
    except Exception as e:
        print(f"❌ Error initializing RMA configuration: {e}")
        db.session.rollback()
        return False

def init_rma_rules():
    """Initialize default RMA business rules"""
    try:
        with app.app_context():
            from app import RMARule
            
            # Insert default rules
            for rule in DEFAULT_RMA_RULES:
                existing = RMARule.query.filter_by(name=rule.name).first()
                if not existing:
                    rma_rule = RMARule(
                        name=rule.name,
                        description=f"Default rule: {rule.name}",
                        rule_type=rule.action,
                        condition_expression=rule.condition,
                        action=rule.action,
                        priority=rule.priority,
                        is_active=rule.active,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        created_by='system'
                    )
                    db.session.add(rma_rule)
            
            db.session.commit()
            print("✅ RMA business rules initialized successfully")
            return True
            
    except Exception as e:
        print(f"❌ Error initializing RMA rules: {e}")
        db.session.rollback()
        return False

def create_sample_rma_data():
    """Create sample RMA data for testing"""
    try:
        with app.app_context():
            from app import Order, OrderItem, Product, User, RMARequest, RMAItem
            
            # Check if we have orders to work with
            sample_order = Order.query.filter_by(status='delivered').first()
            if not sample_order:
                print("⚠️  No delivered orders found. Creating sample RMA data skipped.")
                return True
            
            # Check if sample RMA already exists
            existing_rma = RMARequest.query.filter_by(order_id=sample_order.id).first()
            if existing_rma:
                print("⚠️  Sample RMA data already exists")
                return True
            
            # Create sample RMA request
            rma_request = RMARequest(
                rma_number=f"RMA-{datetime.utcnow().strftime('%Y%m')}-SAMPLE01",
                order_id=sample_order.id,
                user_id=sample_order.user_id,
                rma_type='return_refund',
                status='pending',
                customer_email=sample_order.user.email if sample_order.user else '<EMAIL>',
                customer_notes='Sample RMA request for testing',
                total_refund_amount=0.0,
                requires_approval=True,
                deadline=datetime.utcnow() + timedelta(days=30),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.session.add(rma_request)
            db.session.flush()  # Get the ID
            
            # Create sample RMA items
            order_items = OrderItem.query.filter_by(order_id=sample_order.id).limit(2).all()
            total_refund = 0.0
            
            for order_item in order_items:
                rma_item = RMAItem(
                    rma_request_id=rma_request.id,
                    order_item_id=order_item.id,
                    product_id=order_item.product_id,
                    quantity=1,  # Return 1 item
                    unit_price=order_item.unit_price,
                    total_price=order_item.unit_price,
                    return_reason='defective',
                    condition_notes='Sample defective item for testing',
                    photos=[],
                    item_status='pending',
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                db.session.add(rma_item)
                total_refund += rma_item.total_price
            
            # Update total refund amount
            rma_request.total_refund_amount = total_refund
            
            db.session.commit()
            print("✅ Sample RMA data created successfully")
            return True
            
    except Exception as e:
        print(f"❌ Error creating sample RMA data: {e}")
        db.session.rollback()
        return False

def main():
    """Main initialization function"""
    print("🚀 Initializing RMA System...")
    print("=" * 50)
    
    success = True
    
    # Step 1: Create database tables
    print("1. Creating RMA database tables...")
    if not init_rma_tables():
        success = False
    
    # Step 2: Initialize configuration
    print("\n2. Initializing RMA configuration...")
    if not init_rma_configuration():
        success = False
    
    # Step 3: Initialize business rules
    print("\n3. Initializing RMA business rules...")
    if not init_rma_rules():
        success = False
    
    # Step 4: Create sample data
    print("\n4. Creating sample RMA data...")
    if not create_sample_rma_data():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 RMA System initialization completed successfully!")
        print("\nNext steps:")
        print("- Start the Flask application")
        print("- Test RMA functionality through the API")
        print("- Access admin dashboard for RMA management")
    else:
        print("❌ RMA System initialization failed!")
        print("Please check the error messages above and try again.")
    
    return success

if __name__ == "__main__":
    main()
