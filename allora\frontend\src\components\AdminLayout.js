import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import './AdminLayout.css';

const AdminLayout = ({ children }) => {
    const [sidebarOpen, setSidebarOpen] = useState(true);
    const navigate = useNavigate();
    const location = useLocation();

    const adminUser = JSON.parse(localStorage.getItem('adminUser') || '{}');

    const handleLogout = () => {
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');
        navigate('/admin/login');
    };

    const menuItems = [
        {
            path: '/admin/dashboard',
            icon: '📊',
            label: 'Dashboard',
            permission: null
        },
        {
            path: '/admin/products',
            icon: '📦',
            label: 'Products',
            permission: 'can_manage_products'
        },
        {
            path: '/admin/orders',
            icon: '🛒',
            label: 'Orders',
            permission: 'can_manage_orders'
        },
        {
            path: '/admin/users',
            icon: '👥',
            label: 'Users',
            permission: 'can_manage_users'
        },
        {
            path: '/admin/sellers',
            icon: '🏪',
            label: 'Sellers',
            permission: 'can_manage_sellers'
        },
        {
            path: '/admin/inventory',
            icon: '📋',
            label: 'Inventory',
            permission: 'can_manage_inventory'
        },
        {
            path: '/admin/fulfillment',
            icon: '🚚',
            label: 'Fulfillment',
            permission: 'can_manage_orders'
        },
        {
            path: '/admin/analytics',
            icon: '📈',
            label: 'Analytics',
            permission: 'can_view_analytics'
        },
        {
            path: '/admin/content',
            icon: '📝',
            label: 'Content',
            permission: 'can_manage_content'
        }
    ];

    const hasPermission = (permission) => {
        if (!permission) return true;
        return adminUser.permissions?.[permission] || false;
    };

    const filteredMenuItems = menuItems.filter(item => hasPermission(item.permission));

    return (
        <div className="admin-layout">
            {/* Sidebar */}
            <div className={`admin-sidebar ${sidebarOpen ? 'open' : 'closed'}`}>
                <div className="sidebar-header">
                    <div className="logo">
                        <h2>Allora Admin</h2>
                    </div>
                    <button 
                        className="sidebar-toggle"
                        onClick={() => setSidebarOpen(!sidebarOpen)}
                    >
                        {sidebarOpen ? '←' : '→'}
                    </button>
                </div>

                <nav className="sidebar-nav">
                    {filteredMenuItems.map(item => (
                        <button
                            key={item.path}
                            className={`nav-item ${location.pathname === item.path ? 'active' : ''}`}
                            onClick={() => navigate(item.path)}
                        >
                            <span className="nav-icon">{item.icon}</span>
                            {sidebarOpen && <span className="nav-label">{item.label}</span>}
                        </button>
                    ))}
                </nav>

                <div className="sidebar-footer">
                    <div className="admin-info">
                        {sidebarOpen && (
                            <>
                                <p className="admin-name">
                                    {adminUser.first_name} {adminUser.last_name}
                                </p>
                                <p className="admin-role">{adminUser.role}</p>
                            </>
                        )}
                    </div>
                    <button 
                        className="logout-btn"
                        onClick={handleLogout}
                        title="Logout"
                    >
                        🚪 {sidebarOpen && 'Logout'}
                    </button>
                </div>
            </div>

            {/* Main Content */}
            <div className={`admin-main ${sidebarOpen ? 'sidebar-open' : 'sidebar-closed'}`}>
                <header className="admin-header">
                    <div className="header-left">
                        <button 
                            className="mobile-menu-btn"
                            onClick={() => setSidebarOpen(!sidebarOpen)}
                        >
                            ☰
                        </button>
                        <h1>Admin Panel</h1>
                    </div>
                    
                    <div className="header-right">
                        <div className="admin-profile">
                            <span>{adminUser.first_name} {adminUser.last_name}</span>
                            <div className="admin-avatar">
                                {adminUser.first_name?.[0]}{adminUser.last_name?.[0]}
                            </div>
                        </div>
                        <button 
                            className="header-logout-btn"
                            onClick={handleLogout}
                        >
                            Logout
                        </button>
                    </div>
                </header>

                <main className="admin-content">
                    {children}
                </main>
            </div>
        </div>
    );
};

export default AdminLayout;
