import mysql.connector
from mysql.connector import Error

try:
    # Connect to MySQL database
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='Parul.2001',
        database='allora_db'
    )
    
    if connection.is_connected():
        print("✅ Connected to allora_db database")
        
        cursor = connection.cursor()
        
        # Show all tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print(f"\n📋 Found {len(tables)} tables in allora_db:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # Check products table structure
        if tables:
            print(f"\n🔍 Checking 'products' table structure:")
            cursor.execute("DESCRIBE products")
            columns = cursor.fetchall()
            
            print("Columns in products table:")
            for column in columns:
                print(f"  - {column[0]} ({column[1]}) - Null: {column[2]}, Default: {column[4]}")
            
            # Check if there are any products
            cursor.execute("SELECT COUNT(*) FROM products")
            count = cursor.fetchone()[0]
            print(f"\n📊 Products table has {count} records")
            
            if count > 0:
                cursor.execute("SELECT id, name, price FROM products LIMIT 5")
                products = cursor.fetchall()
                print("\nFirst 5 products:")
                for product in products:
                    print(f"  - ID: {product[0]}, Name: {product[1]}, Price: {product[2]}")
        
        cursor.close()
        
except Error as e:
    print(f"❌ Database error: {e}")
    
finally:
    if 'connection' in locals() and connection.is_connected():
        connection.close()
        print("\nMySQL connection closed")
