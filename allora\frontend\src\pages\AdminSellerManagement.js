import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './AdminSellerManagement.css';

const AdminSellerManagement = () => {
    const [sellers, setSellers] = useState([]);
    const [marketplaceStats, setMarketplaceStats] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [selectedSeller, setSelectedSeller] = useState(null);
    const [showSellerModal, setShowSellerModal] = useState(false);
    const [filters, setFilters] = useState({
        status: '',
        search: '',
        page: 1
    });
    const [pagination, setPagination] = useState(null);
    const navigate = useNavigate();

    useEffect(() => {
        fetchSellers();
        fetchMarketplaceStats();
    }, [filters]);

    const fetchSellers = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('token');
            
            if (!token) {
                navigate('/login');
                return;
            }

            const params = new URLSearchParams();
            if (filters.status) params.append('status', filters.status);
            if (filters.search) params.append('search', filters.search);
            params.append('page', filters.page);
            params.append('per_page', 20);

            const response = await fetch(`/api/admin/sellers?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch sellers');
            }

            const data = await response.json();
            setSellers(data.data.sellers);
            setPagination(data.data.pagination);

        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const fetchMarketplaceStats = async () => {
        try {
            const token = localStorage.getItem('token');
            
            const response = await fetch('/api/admin/marketplace/stats', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setMarketplaceStats(data.data);
            }
        } catch (err) {
            console.error('Failed to fetch marketplace stats:', err);
        }
    };

    const fetchSellerDetails = async (sellerId) => {
        try {
            const token = localStorage.getItem('token');
            
            const response = await fetch(`/api/admin/sellers/${sellerId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch seller details');
            }

            const data = await response.json();
            setSelectedSeller(data.data);
            setShowSellerModal(true);

        } catch (err) {
            setError(err.message);
        }
    };

    const updateSellerStatus = async (sellerId, newStatus, adminNotes = '') => {
        try {
            const token = localStorage.getItem('token');
            
            const response = await fetch(`/api/admin/sellers/${sellerId}/status`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    status: newStatus,
                    admin_notes: adminNotes
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update seller status');
            }

            // Refresh sellers list
            fetchSellers();
            setShowSellerModal(false);
            
            // Show success message
            alert(`Seller status updated to ${newStatus}`);

        } catch (err) {
            setError(err.message);
        }
    };

    const updateCommissionRate = async (sellerId, newRate) => {
        try {
            const token = localStorage.getItem('token');
            
            const response = await fetch(`/api/admin/sellers/${sellerId}/commission`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    commission_rate: newRate
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update commission rate');
            }

            // Refresh sellers list
            fetchSellers();
            
            // Show success message
            alert('Commission rate updated successfully');

        } catch (err) {
            setError(err.message);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'approved': return '#10b981';
            case 'pending': return '#f59e0b';
            case 'suspended': return '#ef4444';
            case 'rejected': return '#6b7280';
            default: return '#6b7280';
        }
    };

    const getStatusBadge = (status) => {
        return (
            <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(status) }}
            >
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
        );
    };

    if (loading && !sellers.length) {
        return (
            <div className="admin-seller-management">
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Loading sellers...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="admin-seller-management">
            <div className="management-header">
                <h1>Seller Management</h1>
                <button onClick={() => navigate('/admin')} className="back-btn">
                    Back to Admin
                </button>
            </div>

            {/* Marketplace Stats */}
            {marketplaceStats && (
                <div className="marketplace-stats">
                    <div className="stats-grid">
                        <div className="stat-card">
                            <h3>Total Sellers</h3>
                            <div className="stat-value">{marketplaceStats.sellers.total}</div>
                        </div>
                        <div className="stat-card">
                            <h3>Active Sellers</h3>
                            <div className="stat-value">{marketplaceStats.sellers.active}</div>
                        </div>
                        <div className="stat-card">
                            <h3>Pending Approval</h3>
                            <div className="stat-value">{marketplaceStats.sellers.pending}</div>
                        </div>
                        <div className="stat-card">
                            <h3>Total Products</h3>
                            <div className="stat-value">{marketplaceStats.products.total}</div>
                        </div>
                        <div className="stat-card">
                            <h3>Commission Earned</h3>
                            <div className="stat-value">
                                {formatCurrency(marketplaceStats.financials.total_commission_earned)}
                            </div>
                        </div>
                        <div className="stat-card">
                            <h3>Marketplace Volume</h3>
                            <div className="stat-value">
                                {formatCurrency(marketplaceStats.financials.total_marketplace_volume)}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Filters */}
            <div className="filters-section">
                <div className="filter-group">
                    <select 
                        value={filters.status} 
                        onChange={(e) => setFilters({...filters, status: e.target.value, page: 1})}
                        className="filter-select"
                    >
                        <option value="">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="suspended">Suspended</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
                <div className="filter-group">
                    <input
                        type="text"
                        placeholder="Search sellers..."
                        value={filters.search}
                        onChange={(e) => setFilters({...filters, search: e.target.value, page: 1})}
                        className="search-input"
                    />
                </div>
            </div>

            {error && (
                <div className="error-message">
                    {error}
                    <button onClick={() => setError('')} className="close-error">×</button>
                </div>
            )}

            {/* Sellers Table */}
            <div className="sellers-table">
                <div className="table-header">
                    <div>Business Name</div>
                    <div>Contact Person</div>
                    <div>Email</div>
                    <div>Status</div>
                    <div>Products</div>
                    <div>Total Earnings</div>
                    <div>Commission Rate</div>
                    <div>Actions</div>
                </div>
                
                {sellers.map((seller) => (
                    <div key={seller.id} className="table-row">
                        <div className="seller-info">
                            <div className="business-name">{seller.business_name}</div>
                            {seller.store_name && (
                                <div className="store-name">Store: {seller.store_name}</div>
                            )}
                        </div>
                        <div>{seller.contact_person}</div>
                        <div>{seller.email}</div>
                        <div>{getStatusBadge(seller.status)}</div>
                        <div>{seller.product_count}</div>
                        <div>{formatCurrency(seller.total_earnings)}</div>
                        <div>{seller.commission_rate}%</div>
                        <div className="actions">
                            <button 
                                onClick={() => fetchSellerDetails(seller.id)}
                                className="view-btn"
                            >
                                View Details
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {/* Pagination */}
            {pagination && pagination.pages > 1 && (
                <div className="pagination">
                    <button 
                        onClick={() => setFilters({...filters, page: filters.page - 1})}
                        disabled={!pagination.has_prev}
                        className="page-btn"
                    >
                        Previous
                    </button>
                    <span className="page-info">
                        Page {pagination.page} of {pagination.pages}
                    </span>
                    <button 
                        onClick={() => setFilters({...filters, page: filters.page + 1})}
                        disabled={!pagination.has_next}
                        className="page-btn"
                    >
                        Next
                    </button>
                </div>
            )}

            {/* Seller Details Modal */}
            {showSellerModal && selectedSeller && (
                <div className="modal-overlay" onClick={() => setShowSellerModal(false)}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <div className="modal-header">
                            <h2>{selectedSeller.business_name}</h2>
                            <button 
                                onClick={() => setShowSellerModal(false)}
                                className="close-modal"
                            >
                                ×
                            </button>
                        </div>
                        
                        <div className="modal-body">
                            <div className="seller-details-grid">
                                <div className="detail-section">
                                    <h3>Business Information</h3>
                                    <p><strong>Contact Person:</strong> {selectedSeller.contact_person}</p>
                                    <p><strong>Email:</strong> {selectedSeller.email}</p>
                                    <p><strong>Phone:</strong> {selectedSeller.phone}</p>
                                    <p><strong>Address:</strong> {selectedSeller.address}</p>
                                    <p><strong>GST Number:</strong> {selectedSeller.gst_number}</p>
                                    <p><strong>PAN Number:</strong> {selectedSeller.pan_number}</p>
                                </div>
                                
                                <div className="detail-section">
                                    <h3>Financial Information</h3>
                                    <p><strong>Total Earnings:</strong> {formatCurrency(selectedSeller.earnings_summary.total_earnings)}</p>
                                    <p><strong>Commission Paid:</strong> {formatCurrency(selectedSeller.earnings_summary.total_commission)}</p>
                                    <p><strong>Commission Rate:</strong> {selectedSeller.commission_rate}%</p>
                                    <p><strong>Total Transactions:</strong> {selectedSeller.earnings_summary.total_transactions}</p>
                                </div>
                            </div>
                            
                            <div className="action-buttons">
                                {selectedSeller.status === 'pending' && (
                                    <>
                                        <button 
                                            onClick={() => updateSellerStatus(selectedSeller.id, 'approved')}
                                            className="approve-btn"
                                        >
                                            Approve Seller
                                        </button>
                                        <button 
                                            onClick={() => updateSellerStatus(selectedSeller.id, 'rejected')}
                                            className="reject-btn"
                                        >
                                            Reject Seller
                                        </button>
                                    </>
                                )}
                                
                                {selectedSeller.status === 'approved' && (
                                    <button 
                                        onClick={() => updateSellerStatus(selectedSeller.id, 'suspended')}
                                        className="suspend-btn"
                                    >
                                        Suspend Seller
                                    </button>
                                )}
                                
                                {selectedSeller.status === 'suspended' && (
                                    <button 
                                        onClick={() => updateSellerStatus(selectedSeller.id, 'approved')}
                                        className="approve-btn"
                                    >
                                        Reactivate Seller
                                    </button>
                                )}
                                
                                <button 
                                    onClick={() => {
                                        const newRate = prompt('Enter new commission rate (0-50):', selectedSeller.commission_rate);
                                        if (newRate !== null && !isNaN(newRate) && newRate >= 0 && newRate <= 50) {
                                            updateCommissionRate(selectedSeller.id, parseFloat(newRate));
                                        }
                                    }}
                                    className="commission-btn"
                                >
                                    Update Commission
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AdminSellerManagement;
