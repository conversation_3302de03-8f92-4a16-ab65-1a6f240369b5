import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import './RMAStatus.css';

const RMAStatus = () => {
    const { rmaNumber } = useParams();
    const navigate = useNavigate();
    
    const [rmaStatus, setRmaStatus] = useState(null);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        fetchRMAStatus();
        
        // Set up auto-refresh every 30 seconds
        const interval = setInterval(() => {
            fetchRMAStatus(true);
        }, 30000);
        
        return () => clearInterval(interval);
    }, [rmaNumber]);

    const fetchRMAStatus = async (isRefresh = false) => {
        try {
            if (isRefresh) setRefreshing(true);
            
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/rma/${rmaNumber}/status`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setRmaStatus(data.rma_status);
            } else if (response.status === 404) {
                toast.error('RMA request not found');
                navigate('/orders');
            } else {
                toast.error('Failed to fetch RMA status');
            }
        } catch (error) {
            console.error('Error fetching RMA status:', error);
            if (!isRefresh) {
                toast.error('Error loading RMA status');
            }
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    const handleCancelRMA = async () => {
        if (!window.confirm('Are you sure you want to cancel this RMA request?')) {
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/rma/${rmaNumber}/cancel`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                toast.success('RMA request cancelled successfully');
                fetchRMAStatus();
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || 'Failed to cancel RMA request');
            }
        } catch (error) {
            console.error('Error cancelling RMA:', error);
            toast.error('Error cancelling RMA request');
        }
    };

    const getStatusColor = (status) => {
        const statusColors = {
            'pending': '#f39c12',
            'approved': '#27ae60',
            'rejected': '#e74c3c',
            'return_shipped': '#3498db',
            'return_received': '#9b59b6',
            'refund_completed': '#2ecc71',
            'cancelled': '#95a5a6',
            'exchange_completed': '#2ecc71'
        };
        return statusColors[status] || '#95a5a6';
    };

    const getStatusLabel = (status) => {
        const statusLabels = {
            'pending': 'Pending Review',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'return_shipped': 'Return Shipped',
            'return_received': 'Return Received',
            'refund_completed': 'Refund Completed',
            'cancelled': 'Cancelled',
            'exchange_completed': 'Exchange Completed'
        };
        return statusLabels[status] || status;
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString();
    };

    const formatCurrency = (amount) => {
        return `₹${parseFloat(amount).toFixed(2)}`;
    };

    if (loading) {
        return (
            <div className="rma-status-container">
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>Loading RMA status...</p>
                </div>
            </div>
        );
    }

    if (!rmaStatus) {
        return (
            <div className="rma-status-container">
                <div className="error-message">
                    <h2>RMA Request Not Found</h2>
                    <p>The requested RMA could not be found or you don't have access to it.</p>
                    <button onClick={() => navigate('/orders')} className="btn btn-primary">
                        Back to Orders
                    </button>
                </div>
            </div>
        );
    }

    const canCancel = ['pending', 'approved'].includes(rmaStatus.status);

    return (
        <div className="rma-status-container">
            {/* Header */}
            <div className="rma-status-header">
                <div className="header-content">
                    <h1>RMA Request Status</h1>
                    <div className="rma-number">#{rmaStatus.rma_number}</div>
                </div>
                <div className="header-actions">
                    <button 
                        onClick={() => fetchRMAStatus()} 
                        className="btn btn-secondary"
                        disabled={refreshing}
                    >
                        {refreshing ? 'Refreshing...' : 'Refresh'}
                    </button>
                    {canCancel && (
                        <button 
                            onClick={handleCancelRMA}
                            className="btn btn-danger"
                        >
                            Cancel Request
                        </button>
                    )}
                </div>
            </div>

            {/* Status Overview */}
            <div className="status-overview">
                <div className="status-badge" style={{ backgroundColor: getStatusColor(rmaStatus.status) }}>
                    {getStatusLabel(rmaStatus.status)}
                </div>
                <div className="status-details">
                    <div className="detail-item">
                        <span className="label">Request Type:</span>
                        <span className="value">{rmaStatus.rma_type?.replace('_', ' ').toUpperCase()}</span>
                    </div>
                    <div className="detail-item">
                        <span className="label">Created:</span>
                        <span className="value">{formatDate(rmaStatus.created_at)}</span>
                    </div>
                    <div className="detail-item">
                        <span className="label">Last Updated:</span>
                        <span className="value">{formatDate(rmaStatus.updated_at)}</span>
                    </div>
                    {rmaStatus.deadline && (
                        <div className="detail-item">
                            <span className="label">Deadline:</span>
                            <span className="value">{formatDate(rmaStatus.deadline)}</span>
                        </div>
                    )}
                </div>
            </div>

            {/* Order Information */}
            <div className="order-information">
                <h3>Order Information</h3>
                <div className="order-details">
                    <div className="detail-item">
                        <span className="label">Order Number:</span>
                        <span className="value">#{rmaStatus.order?.order_number}</span>
                    </div>
                    <div className="detail-item">
                        <span className="label">Order Date:</span>
                        <span className="value">{formatDate(rmaStatus.order?.created_at)}</span>
                    </div>
                    <div className="detail-item">
                        <span className="label">Order Total:</span>
                        <span className="value">{formatCurrency(rmaStatus.order?.total_amount)}</span>
                    </div>
                </div>
            </div>

            {/* Return Items */}
            <div className="return-items">
                <h3>Return Items</h3>
                <div className="items-list">
                    {rmaStatus.items?.map((item, index) => (
                        <div key={index} className="return-item">
                            <div className="item-image">
                                <img 
                                    src={item.product?.image_url || '/placeholder-product.jpg'} 
                                    alt={item.product?.name || 'Product'}
                                />
                            </div>
                            <div className="item-details">
                                <h4>{item.product?.name || 'Product'}</h4>
                                <p>Quantity: {item.quantity}</p>
                                <p>Reason: {item.return_reason?.replace('_', ' ')}</p>
                                {item.condition_notes && (
                                    <p>Notes: {item.condition_notes}</p>
                                )}
                            </div>
                            <div className="item-status">
                                <span className="status-badge small" style={{ backgroundColor: getStatusColor(item.status) }}>
                                    {getStatusLabel(item.status)}
                                </span>
                                {item.refund_amount && (
                                    <div className="refund-amount">
                                        Refund: {formatCurrency(item.refund_amount)}
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Refund Information */}
            {rmaStatus.total_refund_amount > 0 && (
                <div className="refund-information">
                    <h3>Refund Information</h3>
                    <div className="refund-details">
                        <div className="detail-item">
                            <span className="label">Total Refund Amount:</span>
                            <span className="value highlight">{formatCurrency(rmaStatus.total_refund_amount)}</span>
                        </div>
                        {rmaStatus.refund_method && (
                            <div className="detail-item">
                                <span className="label">Refund Method:</span>
                                <span className="value">{rmaStatus.refund_method}</span>
                            </div>
                        )}
                        {rmaStatus.refund_completed_at && (
                            <div className="detail-item">
                                <span className="label">Refund Completed:</span>
                                <span className="value">{formatDate(rmaStatus.refund_completed_at)}</span>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Shipping Information */}
            {rmaStatus.return_shipping && (
                <div className="shipping-information">
                    <h3>Return Shipping</h3>
                    <div className="shipping-details">
                        <div className="detail-item">
                            <span className="label">Carrier:</span>
                            <span className="value">{rmaStatus.return_shipping.carrier}</span>
                        </div>
                        <div className="detail-item">
                            <span className="label">Tracking Number:</span>
                            <span className="value">{rmaStatus.return_shipping.tracking_number}</span>
                        </div>
                        {rmaStatus.return_shipping.label_url && (
                            <div className="detail-item">
                                <span className="label">Return Label:</span>
                                <a 
                                    href={rmaStatus.return_shipping.label_url} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="btn btn-link"
                                >
                                    Download Label
                                </a>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Timeline */}
            {rmaStatus.timeline && rmaStatus.timeline.length > 0 && (
                <div className="rma-timeline">
                    <h3>Request Timeline</h3>
                    <div className="timeline">
                        {rmaStatus.timeline.map((event, index) => (
                            <div key={index} className="timeline-event">
                                <div className="timeline-marker"></div>
                                <div className="timeline-content">
                                    <div className="event-title">{event.event_type?.replace('_', ' ').toUpperCase()}</div>
                                    <div className="event-description">{event.event_description}</div>
                                    <div className="event-date">{formatDate(event.created_at)}</div>
                                    {event.actor_type && (
                                        <div className="event-actor">by {event.actor_type}</div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Actions */}
            <div className="rma-actions">
                <button 
                    onClick={() => navigate('/orders')}
                    className="btn btn-secondary"
                >
                    Back to Orders
                </button>
                <button 
                    onClick={() => navigate('/rma/my-requests')}
                    className="btn btn-primary"
                >
                    View All RMA Requests
                </button>
            </div>
        </div>
    );
};

export default RMAStatus;
