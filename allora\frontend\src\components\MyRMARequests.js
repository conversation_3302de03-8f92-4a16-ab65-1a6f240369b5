import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import './MyRMARequests.css';

const MyRMARequests = () => {
    const navigate = useNavigate();
    
    const [rmaRequests, setRmaRequests] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [statusFilter, setStatusFilter] = useState('');
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        fetchRMARequests();
    }, [currentPage, statusFilter]);

    const fetchRMARequests = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('token');
            
            const params = new URLSearchParams({
                page: currentPage.toString(),
                per_page: '10'
            });
            
            if (statusFilter) {
                params.append('status', statusFilter);
            }
            
            const response = await fetch(`/api/rma/my-requests?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setRmaRequests(data.requests);
                setTotalPages(data.pagination.pages);
            } else {
                toast.error('Failed to fetch RMA requests');
            }
        } catch (error) {
            console.error('Error fetching RMA requests:', error);
            toast.error('Error loading RMA requests');
        } finally {
            setLoading(false);
        }
    };

    const getStatusColor = (status) => {
        const statusColors = {
            'pending': '#f39c12',
            'approved': '#27ae60',
            'rejected': '#e74c3c',
            'return_shipped': '#3498db',
            'return_received': '#9b59b6',
            'refund_completed': '#2ecc71',
            'cancelled': '#95a5a6',
            'exchange_completed': '#2ecc71'
        };
        return statusColors[status] || '#95a5a6';
    };

    const getStatusLabel = (status) => {
        const statusLabels = {
            'pending': 'Pending Review',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'return_shipped': 'Return Shipped',
            'return_received': 'Return Received',
            'refund_completed': 'Refund Completed',
            'cancelled': 'Cancelled',
            'exchange_completed': 'Exchange Completed'
        };
        return statusLabels[status] || status;
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
    };

    const formatCurrency = (amount) => {
        return `₹${parseFloat(amount).toFixed(2)}`;
    };

    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };

    const handleStatusFilter = (status) => {
        setStatusFilter(status);
        setCurrentPage(1);
    };

    const filteredRequests = rmaRequests.filter(request => {
        if (!searchTerm) return true;
        return request.rma_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
               request.order_id.toString().includes(searchTerm);
    });

    if (loading) {
        return (
            <div className="my-rma-container">
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>Loading your RMA requests...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="my-rma-container">
            {/* Header */}
            <div className="my-rma-header">
                <h1>My Return/Exchange Requests</h1>
                <button 
                    onClick={() => navigate('/orders')}
                    className="btn btn-primary"
                >
                    Create New Request
                </button>
            </div>

            {/* Filters */}
            <div className="rma-filters">
                <div className="search-filter">
                    <input
                        type="text"
                        placeholder="Search by RMA number or Order ID..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="search-input"
                    />
                </div>
                
                <div className="status-filters">
                    <button 
                        className={`filter-btn ${statusFilter === '' ? 'active' : ''}`}
                        onClick={() => handleStatusFilter('')}
                    >
                        All
                    </button>
                    <button 
                        className={`filter-btn ${statusFilter === 'pending' ? 'active' : ''}`}
                        onClick={() => handleStatusFilter('pending')}
                    >
                        Pending
                    </button>
                    <button 
                        className={`filter-btn ${statusFilter === 'approved' ? 'active' : ''}`}
                        onClick={() => handleStatusFilter('approved')}
                    >
                        Approved
                    </button>
                    <button 
                        className={`filter-btn ${statusFilter === 'refund_completed' ? 'active' : ''}`}
                        onClick={() => handleStatusFilter('refund_completed')}
                    >
                        Completed
                    </button>
                </div>
            </div>

            {/* RMA Requests List */}
            {filteredRequests.length === 0 ? (
                <div className="empty-state">
                    <div className="empty-icon">📦</div>
                    <h3>No RMA Requests Found</h3>
                    <p>
                        {searchTerm || statusFilter 
                            ? 'No requests match your current filters.' 
                            : 'You haven\'t created any return or exchange requests yet.'
                        }
                    </p>
                    <button 
                        onClick={() => navigate('/orders')}
                        className="btn btn-primary"
                    >
                        Browse Your Orders
                    </button>
                </div>
            ) : (
                <div className="rma-requests-list">
                    {filteredRequests.map(request => (
                        <div key={request.rma_number} className="rma-request-card">
                            <div className="card-header">
                                <div className="rma-info">
                                    <h3>#{request.rma_number}</h3>
                                    <p>Order #{request.order_id}</p>
                                </div>
                                <div className="status-badge" style={{ backgroundColor: getStatusColor(request.status) }}>
                                    {getStatusLabel(request.status)}
                                </div>
                            </div>
                            
                            <div className="card-body">
                                <div className="request-details">
                                    <div className="detail-item">
                                        <span className="label">Request Type:</span>
                                        <span className="value">{request.rma_type?.replace('_', ' ').toUpperCase()}</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="label">Items:</span>
                                        <span className="value">{request.items_count} item(s)</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="label">Refund Amount:</span>
                                        <span className="value highlight">{formatCurrency(request.total_refund_amount)}</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="label">Created:</span>
                                        <span className="value">{formatDate(request.created_at)}</span>
                                    </div>
                                    {request.deadline && (
                                        <div className="detail-item">
                                            <span className="label">Deadline:</span>
                                            <span className="value">{formatDate(request.deadline)}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                            
                            <div className="card-actions">
                                <button 
                                    onClick={() => navigate(`/rma/${request.rma_number}`)}
                                    className="btn btn-primary"
                                >
                                    View Details
                                </button>
                                <button 
                                    onClick={() => navigate(`/orders/${request.order_id}`)}
                                    className="btn btn-secondary"
                                >
                                    View Order
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="pagination">
                    <button 
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="pagination-btn"
                    >
                        Previous
                    </button>
                    
                    <div className="page-numbers">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            let pageNum;
                            if (totalPages <= 5) {
                                pageNum = i + 1;
                            } else if (currentPage <= 3) {
                                pageNum = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                                pageNum = totalPages - 4 + i;
                            } else {
                                pageNum = currentPage - 2 + i;
                            }
                            
                            return (
                                <button
                                    key={pageNum}
                                    onClick={() => handlePageChange(pageNum)}
                                    className={`page-number ${currentPage === pageNum ? 'active' : ''}`}
                                >
                                    {pageNum}
                                </button>
                            );
                        })}
                    </div>
                    
                    <button 
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="pagination-btn"
                    >
                        Next
                    </button>
                </div>
            )}
        </div>
    );
};

export default MyRMARequests;
