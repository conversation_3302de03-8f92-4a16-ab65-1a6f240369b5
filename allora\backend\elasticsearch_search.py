"""
Advanced Elasticsearch Search Implementation
Provides comprehensive search functionality with full-text search, faceted search, 
autocomplete, suggestions, filters, sorting, and aggregations
"""

import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError

from elasticsearch_config import get_elasticsearch_client, get_product_index_name, get_suggestions_index_name
from elasticsearch_manager import get_elasticsearch_sync_manager
from search_query_builder import create_product_search_query, ProductSearchQueryBuilder, SearchField, BoolOperator

logger = logging.getLogger(__name__)

class AdvancedSearchEngine:
    """Advanced search engine with Elasticsearch backend"""
    
    def __init__(self):
        self.es = get_elasticsearch_client()
        self.product_index = get_product_index_name()
        self.suggestions_index = get_suggestions_index_name()
        self.sync_manager = get_elasticsearch_sync_manager()
    
    def search_products(
        self,
        query: str = "",
        filters: Dict[str, Any] = None,
        sort_by: str = "relevance",
        sort_order: str = "desc",
        page: int = 1,
        per_page: int = 20,
        include_aggregations: bool = True,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Comprehensive product search with filters, sorting, and aggregations
        """
        try:
            start_time = time.time()
            
            # Build Elasticsearch query
            es_query = self._build_search_query(query, filters)
            
            # Build sort configuration
            sort_config = self._build_sort_config(sort_by, sort_order)
            
            # Calculate pagination
            from_offset = (page - 1) * per_page
            
            # Build aggregations
            aggregations = self._build_aggregations() if include_aggregations else {}
            
            # Execute search
            search_body = {
                "query": es_query,
                "sort": sort_config,
                "from": from_offset,
                "size": per_page,
                "highlight": {
                    "fields": {
                        "name": {"pre_tags": ["<mark>"], "post_tags": ["</mark>"]},
                        "description": {"pre_tags": ["<mark>"], "post_tags": ["</mark>"]},
                        "search_keywords": {"pre_tags": ["<mark>"], "post_tags": ["</mark>"]}
                    }
                },
                "_source": {
                    "excludes": ["visual_features", "ai_generated_tags"]  # Exclude large fields
                }
            }
            
            if aggregations:
                search_body["aggs"] = aggregations
            
            # Execute search
            response = self.es.search(
                index=self.product_index,
                body=search_body,
                timeout="30s"
            )
            
            # Process results
            results = self._process_search_results(response, query)
            
            # Add performance metrics
            results["performance"] = {
                "took_ms": response.get("took", 0),
                "total_time_ms": int((time.time() - start_time) * 1000),
                "timed_out": response.get("timed_out", False)
            }
            
            # Log search for analytics
            self._log_search_analytics(query, filters, results, user_id)
            
            return results
            
        except Exception as e:
            logger.error(f"Search error: {e}")
            return {
                "products": [],
                "total": 0,
                "page": page,
                "per_page": per_page,
                "total_pages": 0,
                "aggregations": {},
                "error": str(e)
            }
    
    def _build_search_query(self, query: str, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Build comprehensive Elasticsearch query"""
        if not filters:
            filters = {}
        
        # Base query structure
        bool_query = {
            "bool": {
                "must": [],
                "filter": [],
                "should": [],
                "must_not": []
            }
        }
        
        # Text search query
        if query and query.strip():
            text_query = {
                "multi_match": {
                    "query": query.strip(),
                    "fields": [
                        "name^3",  # Boost product name
                        "name.autocomplete^2",
                        "description^1.5",
                        "search_keywords^2",
                        "category.text^2",
                        "brand.text^2",
                        "seller_name.text^1.5",
                        "tags.text"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO",
                    "operator": "or",
                    "minimum_should_match": "75%"
                }
            }
            bool_query["bool"]["must"].append(text_query)
            
            # Add boost for exact matches
            exact_match_boost = {
                "multi_match": {
                    "query": query.strip(),
                    "fields": ["name.keyword^5", "brand^3"],
                    "type": "phrase",
                    "boost": 2.0
                }
            }
            bool_query["bool"]["should"].append(exact_match_boost)
        else:
            # If no query, match all but boost featured/promoted products
            bool_query["bool"]["must"].append({"match_all": {}})
            
            # Boost featured products
            bool_query["bool"]["should"].extend([
                {"term": {"featured": {"value": True, "boost": 2.0}}},
                {"term": {"promoted": {"value": True, "boost": 1.5}}}
            ])
        
        # Apply filters
        self._apply_filters(bool_query, filters)
        
        # Function score for relevance boosting
        function_score_query = {
            "function_score": {
                "query": bool_query,
                "functions": [
                    # Boost by popularity score
                    {
                        "field_value_factor": {
                            "field": "popularity_score",
                            "factor": 0.1,
                            "modifier": "log1p",
                            "missing": 0
                        }
                    },
                    # Boost by rating
                    {
                        "field_value_factor": {
                            "field": "average_rating",
                            "factor": 0.2,
                            "modifier": "sqrt",
                            "missing": 0
                        }
                    },
                    # Boost by total reviews
                    {
                        "field_value_factor": {
                            "field": "total_reviews",
                            "factor": 0.05,
                            "modifier": "log1p",
                            "missing": 0
                        }
                    },
                    # Boost verified sellers
                    {
                        "filter": {"term": {"seller_verified": True}},
                        "weight": 1.2
                    },
                    # Boost in-stock products
                    {
                        "filter": {"term": {"is_in_stock": True}},
                        "weight": 1.1
                    }
                ],
                "score_mode": "multiply",
                "boost_mode": "multiply",
                "max_boost": 5.0
            }
        }
        
        return function_score_query
    
    def _apply_filters(self, bool_query: Dict[str, Any], filters: Dict[str, Any]):
        """Apply various filters to the search query"""
        filter_clauses = bool_query["bool"]["filter"]
        
        # Category filter
        if filters.get("categories"):
            categories = filters["categories"] if isinstance(filters["categories"], list) else [filters["categories"]]
            filter_clauses.append({"terms": {"category": categories}})
        
        # Brand filter
        if filters.get("brands"):
            brands = filters["brands"] if isinstance(filters["brands"], list) else [filters["brands"]]
            filter_clauses.append({"terms": {"brand": brands}})
        
        # Seller filter
        if filters.get("sellers"):
            sellers = filters["sellers"] if isinstance(filters["sellers"], list) else [filters["sellers"]]
            filter_clauses.append({"terms": {"seller_name": sellers}})
        
        # Price range filter
        if filters.get("price_min") is not None or filters.get("price_max") is not None:
            price_range = {"range": {"price": {}}}
            if filters.get("price_min") is not None:
                price_range["range"]["price"]["gte"] = float(filters["price_min"])
            if filters.get("price_max") is not None:
                price_range["range"]["price"]["lte"] = float(filters["price_max"])
            filter_clauses.append(price_range)
        
        # Rating filter
        if filters.get("min_rating") is not None:
            filter_clauses.append({
                "range": {"average_rating": {"gte": float(filters["min_rating"])}}
            })
        
        # Sustainability score filter
        if filters.get("min_sustainability") is not None:
            filter_clauses.append({
                "range": {"sustainability_score": {"gte": int(filters["min_sustainability"])}}
            })
        
        # Stock filter
        if filters.get("in_stock_only"):
            filter_clauses.append({"term": {"is_in_stock": True}})
        
        # Verified sellers only
        if filters.get("verified_sellers_only"):
            filter_clauses.append({"term": {"seller_verified": True}})
        
        # Free shipping filter
        if filters.get("free_shipping_only"):
            filter_clauses.append({"term": {"free_shipping": True}})
        
        # Eco-friendly filters
        if filters.get("organic_only"):
            filter_clauses.append({"term": {"organic": True}})
        
        if filters.get("fair_trade_only"):
            filter_clauses.append({"term": {"fair_trade": True}})
        
        if filters.get("recyclable_only"):
            filter_clauses.append({"term": {"recyclable": True}})
        
        # Discount filter
        if filters.get("on_sale_only"):
            filter_clauses.append({
                "range": {"discount_percentage": {"gt": 0}}
            })
    
    def _build_sort_config(self, sort_by: str, sort_order: str) -> List[Dict[str, Any]]:
        """Build sort configuration for Elasticsearch"""
        sort_config = []
        
        order = "desc" if sort_order.lower() == "desc" else "asc"
        
        if sort_by == "relevance":
            sort_config.append({"_score": {"order": "desc"}})
        elif sort_by == "price":
            sort_config.append({"price": {"order": order}})
        elif sort_by == "rating":
            sort_config.append({"average_rating": {"order": order}})
        elif sort_by == "popularity":
            sort_config.append({"popularity_score": {"order": order}})
        elif sort_by == "newest":
            sort_config.append({"created_at": {"order": "desc"}})
        elif sort_by == "name":
            sort_config.append({"name.keyword": {"order": order}})
        elif sort_by == "sustainability":
            sort_config.append({"sustainability_score": {"order": order}})
        elif sort_by == "reviews":
            sort_config.append({"total_reviews": {"order": order}})
        else:
            # Default to relevance
            sort_config.append({"_score": {"order": "desc"}})
        
        # Always add secondary sort by relevance score
        if sort_by != "relevance":
            sort_config.append({"_score": {"order": "desc"}})
        
        # Tertiary sort by product ID for consistent pagination
        sort_config.append({"id": {"order": "asc"}})
        
        return sort_config
    
    def _build_aggregations(self) -> Dict[str, Any]:
        """Build aggregations for faceted search"""
        return {
            "categories": {
                "terms": {
                    "field": "category",
                    "size": 20,
                    "order": {"_count": "desc"}
                }
            },
            "brands": {
                "terms": {
                    "field": "brand",
                    "size": 20,
                    "order": {"_count": "desc"}
                }
            },
            "sellers": {
                "terms": {
                    "field": "seller_name",
                    "size": 15,
                    "order": {"_count": "desc"}
                }
            },
            "price_ranges": {
                "range": {
                    "field": "price",
                    "ranges": [
                        {"key": "under_500", "to": 500},
                        {"key": "500_1000", "from": 500, "to": 1000},
                        {"key": "1000_2500", "from": 1000, "to": 2500},
                        {"key": "2500_5000", "from": 2500, "to": 5000},
                        {"key": "over_5000", "from": 5000}
                    ]
                }
            },
            "rating_ranges": {
                "range": {
                    "field": "average_rating",
                    "ranges": [
                        {"key": "4_plus", "from": 4.0},
                        {"key": "3_plus", "from": 3.0},
                        {"key": "2_plus", "from": 2.0},
                        {"key": "1_plus", "from": 1.0}
                    ]
                }
            },
            "sustainability_ranges": {
                "range": {
                    "field": "sustainability_score",
                    "ranges": [
                        {"key": "excellent", "from": 80},
                        {"key": "good", "from": 60, "to": 80},
                        {"key": "fair", "from": 40, "to": 60},
                        {"key": "poor", "to": 40}
                    ]
                }
            },
            "availability": {
                "terms": {
                    "field": "is_in_stock",
                    "size": 2
                }
            },
            "shipping": {
                "terms": {
                    "field": "free_shipping",
                    "size": 2
                }
            },
            "eco_features": {
                "filters": {
                    "filters": {
                        "organic": {"term": {"organic": True}},
                        "fair_trade": {"term": {"fair_trade": True}},
                        "recyclable": {"term": {"recyclable": True}}
                    }
                }
            },
            "price_stats": {
                "stats": {
                    "field": "price"
                }
            }
        }

    def _process_search_results(self, response: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Process Elasticsearch response into API format"""
        hits = response.get("hits", {})
        total_info = hits.get("total", {})

        # Handle different Elasticsearch versions
        if isinstance(total_info, dict):
            total_count = total_info.get("value", 0)
        else:
            total_count = total_info

        products = []
        for hit in hits.get("hits", []):
            source = hit["_source"]

            # Add search-specific fields
            product = {
                "id": source.get("id"),
                "name": source.get("name"),
                "description": source.get("description", ""),
                "category": source.get("category"),
                "brand": source.get("brand"),
                "price": source.get("price"),
                "original_price": source.get("original_price"),
                "discount_percentage": source.get("discount_percentage", 0),
                "currency": source.get("currency", "INR"),
                "primary_image": source.get("primary_image"),
                "image_urls": source.get("image_urls", []),
                "stock_quantity": source.get("stock_quantity"),
                "is_in_stock": source.get("is_in_stock"),
                "average_rating": source.get("average_rating", 0),
                "total_reviews": source.get("total_reviews", 0),
                "sustainability_score": source.get("sustainability_score"),
                "seller_name": source.get("seller_name"),
                "seller_verified": source.get("seller_verified"),
                "free_shipping": source.get("free_shipping"),
                "estimated_delivery_days": source.get("estimated_delivery_days"),
                "featured": source.get("featured", False),
                "promoted": source.get("promoted", False),

                # Search-specific metadata
                "search_score": hit.get("_score", 0),
                "highlights": hit.get("highlight", {}),
                "matched_fields": list(hit.get("highlight", {}).keys())
            }

            products.append(product)

        # Process aggregations
        aggregations = {}
        if "aggregations" in response:
            aggregations = self._process_aggregations(response["aggregations"])

        # Calculate pagination
        per_page = len(products) if products else 20
        current_page = 1  # Will be set by caller
        total_pages = (total_count + per_page - 1) // per_page if per_page > 0 else 0

        return {
            "products": products,
            "total": total_count,
            "page": current_page,
            "per_page": per_page,
            "total_pages": total_pages,
            "aggregations": aggregations,
            "query": query,
            "has_next": False,  # Will be calculated by caller
            "has_prev": False   # Will be calculated by caller
        }

    def _process_aggregations(self, aggs: Dict[str, Any]) -> Dict[str, Any]:
        """Process Elasticsearch aggregations into API format"""
        processed = {}

        for agg_name, agg_data in aggs.items():
            if "buckets" in agg_data:
                # Terms aggregation
                processed[agg_name] = [
                    {
                        "key": bucket["key"],
                        "count": bucket["doc_count"],
                        "selected": False  # Will be set based on current filters
                    }
                    for bucket in agg_data["buckets"]
                ]
            elif "value" in agg_data:
                # Stats aggregation
                processed[agg_name] = agg_data
            elif "filters" in agg_data:
                # Filters aggregation (for eco features)
                processed[agg_name] = {
                    filter_name: filter_data["doc_count"]
                    for filter_name, filter_data in agg_data["buckets"].items()
                }

        return processed

    def autocomplete_suggestions(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get autocomplete suggestions"""
        try:
            if not query or len(query.strip()) < 2:
                return []

            # Search in suggestions index
            search_body = {
                "suggest": {
                    "product_suggest": {
                        "prefix": query.strip().lower(),
                        "completion": {
                            "field": "text",
                            "size": limit,
                            "contexts": {
                                "category": []  # Can be filtered by category
                            }
                        }
                    }
                }
            }

            response = self.es.search(
                index=self.suggestions_index,
                body=search_body
            )

            suggestions = []
            for suggestion in response.get("suggest", {}).get("product_suggest", []):
                for option in suggestion.get("options", []):
                    suggestions.append({
                        "text": option["text"],
                        "score": option["_score"],
                        "type": option["_source"].get("type", "product"),
                        "payload": option["_source"].get("payload", {})
                    })

            return suggestions

        except Exception as e:
            logger.error(f"Autocomplete error: {e}")
            return []

    def search_suggestions(self, query: str, limit: int = 5) -> List[str]:
        """Get search query suggestions based on popular searches"""
        try:
            if not query or len(query.strip()) < 2:
                return []

            # For now, return basic suggestions based on product data
            return self._get_basic_suggestions(query, limit)

        except Exception as e:
            logger.error(f"Search suggestions error: {e}")
            return []

    def _get_basic_suggestions(self, query: str, limit: int) -> List[str]:
        """Get basic suggestions from product data"""
        try:
            search_body = {
                "query": {
                    "multi_match": {
                        "query": query,
                        "fields": ["name", "category", "brand"],
                        "type": "phrase_prefix"
                    }
                },
                "aggs": {
                    "suggestions": {
                        "terms": {
                            "script": {
                                "source": "doc['name.keyword'].value + ' in ' + doc['category'].value"
                            },
                            "size": limit
                        }
                    }
                },
                "size": 0
            }

            response = self.es.search(
                index=self.product_index,
                body=search_body
            )

            suggestions = []
            for bucket in response.get("aggregations", {}).get("suggestions", {}).get("buckets", []):
                suggestions.append(bucket["key"])

            return suggestions[:limit]

        except Exception as e:
            logger.error(f"Basic suggestions error: {e}")
            return []

    def similar_products(self, product_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Find similar products using More Like This query"""
        try:
            search_body = {
                "query": {
                    "more_like_this": {
                        "fields": ["name", "description", "category", "brand", "search_keywords"],
                        "like": [
                            {
                                "_index": self.product_index,
                                "_id": product_id
                            }
                        ],
                        "min_term_freq": 1,
                        "max_query_terms": 12,
                        "min_doc_freq": 1,
                        "analyzer": "product_search_analyzer"
                    }
                },
                "filter": {
                    "bool": {
                        "must": [
                            {"term": {"is_in_stock": True}}
                        ],
                        "must_not": [
                            {"term": {"id": product_id}}
                        ]
                    }
                },
                "size": limit
            }

            response = self.es.search(
                index=self.product_index,
                body=search_body
            )

            similar_products = []
            for hit in response.get("hits", {}).get("hits", []):
                source = hit["_source"]
                similar_products.append({
                    "id": source.get("id"),
                    "name": source.get("name"),
                    "price": source.get("price"),
                    "primary_image": source.get("primary_image"),
                    "average_rating": source.get("average_rating", 0),
                    "similarity_score": hit.get("_score", 0)
                })

            return similar_products

        except Exception as e:
            logger.error(f"Similar products error: {e}")
            return []

    def _log_search_analytics(self, query: str, filters: Dict[str, Any], results: Dict[str, Any], user_id: Optional[int]):
        """Log search analytics for monitoring and optimization"""
        try:
            # This would typically be done asynchronously
            analytics_doc = {
                "query": query,
                "normalized_query": query.lower().strip() if query else "",
                "user_id": user_id,
                "results_count": results.get("total", 0),
                "filters_applied": filters or {},
                "response_time_ms": results.get("performance", {}).get("total_time_ms", 0),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "search_type": "text"
            }

            # Index analytics document (async in production)
            # self.es.index(index=self.analytics_index, body=analytics_doc)

        except Exception as e:
            logger.error(f"Analytics logging error: {e}")

    def advanced_search_with_query_builder(
        self,
        query: str = "",
        filters: Dict[str, Any] = None,
        sort_by: str = "relevance",
        sort_order: str = "desc",
        page: int = 1,
        per_page: int = 20,
        include_aggregations: bool = True,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Advanced product search using the query builder system
        """
        start_time = time.time()

        try:
            if filters is None:
                filters = {}

            # Use the advanced query builder for better query construction
            search_body = create_product_search_query(
                query=query,
                categories=filters.get('categories', []),
                brands=filters.get('brands', []),
                min_price=filters.get('price_min'),
                max_price=filters.get('price_max'),
                min_rating=filters.get('min_rating'),
                in_stock_only=filters.get('in_stock_only', False),
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                per_page=per_page,
                include_highlights=True
            )

            # Add additional filters not covered by the query builder
            if filters.get('sellers'):
                # Add seller filter to the existing bool query
                if 'query' in search_body and 'bool' in search_body['query']:
                    if 'filter' not in search_body['query']['bool']:
                        search_body['query']['bool']['filter'] = []
                    search_body['query']['bool']['filter'].append({
                        'terms': {'seller_name': filters['sellers']}
                    })

            # Add sustainability filter
            if filters.get('min_sustainability'):
                if 'query' in search_body and 'bool' in search_body['query']:
                    if 'filter' not in search_body['query']['bool']:
                        search_body['query']['bool']['filter'] = []
                    search_body['query']['bool']['filter'].append({
                        'range': {'sustainability_score': {'gte': filters['min_sustainability']}}
                    })

            # Add eco-friendly filters
            eco_filters = ['organic_only', 'fair_trade_only', 'recyclable_only', 'free_shipping_only', 'verified_sellers_only']
            for eco_filter in eco_filters:
                if filters.get(eco_filter):
                    field_name = eco_filter.replace('_only', '')
                    if 'query' in search_body and 'bool' in search_body['query']:
                        if 'filter' not in search_body['query']['bool']:
                            search_body['query']['bool']['filter'] = []
                        search_body['query']['bool']['filter'].append({
                            'term': {field_name: True}
                        })

            # Add aggregations if requested
            if include_aggregations:
                search_body['aggs'] = self._build_aggregations()

            # Execute search
            response = self.es.search(
                index=self.product_index,
                body=search_body
            )

            # Process results
            results = self._process_search_results(response, page, per_page)

            # Add performance metrics
            total_time = time.time() - start_time
            results['performance'] = {
                'total_time_ms': round(total_time * 1000, 2),
                'elasticsearch_time_ms': response.get('took', 0),
                'query_builder': 'advanced'
            }

            # Log search analytics
            self._log_search_analytics(query, filters, results, user_id)

            return results

        except Exception as e:
            logger.error(f"Advanced search with query builder error: {e}")
            # Fallback to regular search
            return self.search_products(query, filters, sort_by, sort_order, page, per_page, include_aggregations, user_id)

    def complex_boolean_search(
        self,
        search_terms: List[Dict[str, Any]],
        filters: Dict[str, Any] = None,
        sort_by: str = "relevance",
        sort_order: str = "desc",
        page: int = 1,
        per_page: int = 20
    ) -> Dict[str, Any]:
        """
        Complex boolean search with multiple search terms and operators

        search_terms format:
        [
            {"query": "organic cotton", "operator": "must", "fields": ["name", "description"]},
            {"query": "sustainable", "operator": "should", "fields": ["category", "search_keywords"]},
            {"query": "plastic", "operator": "must_not", "fields": ["material"]}
        ]
        """
        try:
            builder = ProductSearchQueryBuilder()

            # Add each search term with its operator
            for term in search_terms:
                query_text = term.get('query', '')
                operator = term.get('operator', 'must')  # must, should, must_not
                fields = term.get('fields', ['name', 'description'])
                boost = term.get('boost', 1.0)

                if query_text:
                    search_fields = [SearchField(field, boost=boost) for field in fields]

                    if operator == 'must':
                        bool_op = BoolOperator.MUST
                    elif operator == 'should':
                        bool_op = BoolOperator.SHOULD
                    elif operator == 'must_not':
                        bool_op = BoolOperator.MUST_NOT
                    else:
                        bool_op = BoolOperator.MUST

                    builder.add_text_search(
                        query=query_text,
                        fields=search_fields,
                        bool_operator=bool_op
                    )

            # Add filters
            if filters:
                if filters.get('categories'):
                    builder.add_category_filter(filters['categories'])
                if filters.get('brands'):
                    builder.add_brand_filter(filters['brands'])
                builder.add_price_range(filters.get('price_min'), filters.get('price_max'))
                builder.add_rating_filter(filters.get('min_rating'))
                builder.add_stock_filter(filters.get('in_stock_only', False))

            # Add scoring boosts
            builder.add_popularity_boost()
            builder.add_rating_boost()

            # Set pagination
            builder.set_pagination(page, per_page)

            # Build and execute query
            search_body = builder.build()

            response = self.es.search(
                index=self.product_index,
                body=search_body
            )

            return self._process_search_results(response, page, per_page)

        except Exception as e:
            logger.error(f"Complex boolean search error: {e}")
            return {
                'products': [],
                'total': 0,
                'total_pages': 0,
                'error': str(e)
            }

# Global search engine instance
search_engine = AdvancedSearchEngine()

def get_search_engine() -> AdvancedSearchEngine:
    """Get global search engine instance"""
    return search_engine
