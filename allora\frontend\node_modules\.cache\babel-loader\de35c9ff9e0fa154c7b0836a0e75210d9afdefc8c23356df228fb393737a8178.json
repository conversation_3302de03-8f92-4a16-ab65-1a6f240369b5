{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\contexts\\\\ErrorContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport { useNotification } from './NotificationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ErrorContext = /*#__PURE__*/createContext();\nexport const useError = () => {\n  _s();\n  const context = useContext(ErrorContext);\n  if (!context) {\n    throw new Error('useError must be used within an ErrorProvider');\n  }\n  return context;\n};\n\n// Error types\n_s(useError, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ERROR_TYPES = {\n  NETWORK: 'network',\n  VALIDATION: 'validation',\n  AUTHENTICATION: 'authentication',\n  AUTHORIZATION: 'authorization',\n  SERVER: 'server',\n  CLIENT: 'client',\n  UNKNOWN: 'unknown'\n};\n\n// Error severity levels\nexport const ERROR_SEVERITY = {\n  LOW: 'low',\n  MEDIUM: 'medium',\n  HIGH: 'high',\n  CRITICAL: 'critical'\n};\nexport const ErrorProvider = ({\n  children\n}) => {\n  _s2();\n  const [errors, setErrors] = useState({});\n  const [errorHistory, setErrorHistory] = useState([]);\n  const {\n    error: showNotification\n  } = useNotification();\n\n  // Add error to state\n  const addError = useCallback((key, error, options = {}) => {\n    const errorObj = {\n      id: `${key}_${Date.now()}`,\n      key,\n      message: error.message || error,\n      type: options.type || ERROR_TYPES.UNKNOWN,\n      severity: options.severity || ERROR_SEVERITY.MEDIUM,\n      timestamp: new Date().toISOString(),\n      stack: error.stack,\n      context: options.context || {},\n      retryable: options.retryable || false,\n      showNotification: options.showNotification !== false,\n      autoResolve: options.autoResolve || false,\n      autoResolveDelay: options.autoResolveDelay || 5000\n    };\n    setErrors(prev => ({\n      ...prev,\n      [key]: errorObj\n    }));\n    setErrorHistory(prev => [...prev.slice(-99), errorObj]); // Keep last 100 errors\n\n    // Show notification if enabled\n    if (errorObj.showNotification) {\n      const notificationOptions = {\n        autoClose: errorObj.severity === ERROR_SEVERITY.CRITICAL ? false : 6000\n      };\n      showNotification(errorObj.message, notificationOptions);\n    }\n\n    // Auto-resolve if enabled\n    if (errorObj.autoResolve) {\n      setTimeout(() => {\n        clearError(key);\n      }, errorObj.autoResolveDelay);\n    }\n\n    // Report error to backend if critical\n    if (errorObj.severity === ERROR_SEVERITY.CRITICAL) {\n      reportError(errorObj);\n    }\n    return errorObj.id;\n  }, [showNotification]);\n\n  // Clear specific error\n  const clearError = useCallback(key => {\n    setErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[key];\n      return newErrors;\n    });\n  }, []);\n\n  // Clear all errors\n  const clearAllErrors = useCallback(() => {\n    setErrors({});\n  }, []);\n\n  // Get error by key\n  const getError = useCallback(key => {\n    return errors[key];\n  }, [errors]);\n\n  // Check if error exists\n  const hasError = useCallback(key => {\n    return !!errors[key];\n  }, [errors]);\n\n  // Get all errors\n  const getAllErrors = useCallback(() => {\n    return Object.values(errors);\n  }, [errors]);\n\n  // Get errors by type\n  const getErrorsByType = useCallback(type => {\n    return Object.values(errors).filter(error => error.type === type);\n  }, [errors]);\n\n  // Get errors by severity\n  const getErrorsBySeverity = useCallback(severity => {\n    return Object.values(errors).filter(error => error.severity === severity);\n  }, [errors]);\n\n  // Handle API errors\n  const handleApiError = useCallback((error, context = {}) => {\n    var _error$config;\n    let errorType = ERROR_TYPES.UNKNOWN;\n    let severity = ERROR_SEVERITY.MEDIUM;\n    let message = 'An unexpected error occurred';\n    if (error.response) {\n      var _error$response$data, _error$response$data2;\n      const status = error.response.status;\n      if (status >= 400 && status < 500) {\n        errorType = status === 401 ? ERROR_TYPES.AUTHENTICATION : status === 403 ? ERROR_TYPES.AUTHORIZATION : ERROR_TYPES.VALIDATION;\n        severity = status === 401 || status === 403 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM;\n      } else if (status >= 500) {\n        errorType = ERROR_TYPES.SERVER;\n        severity = ERROR_SEVERITY.HIGH;\n      }\n      message = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.error) || message;\n    } else if (error.request) {\n      errorType = ERROR_TYPES.NETWORK;\n      severity = ERROR_SEVERITY.HIGH;\n      message = 'Network error. Please check your connection.';\n    }\n    return addError('api', error, {\n      type: errorType,\n      severity,\n      context: {\n        ...context,\n        url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url\n      },\n      retryable: errorType === ERROR_TYPES.NETWORK || errorType === ERROR_TYPES.SERVER\n    });\n  }, [addError]);\n\n  // Report error to backend\n  const reportError = useCallback(async errorObj => {\n    try {\n      const errorData = {\n        message: errorObj.message,\n        type: errorObj.type,\n        severity: errorObj.severity,\n        stack: errorObj.stack,\n        context: errorObj.context,\n        timestamp: errorObj.timestamp,\n        userAgent: navigator.userAgent,\n        url: window.location.href,\n        userId: localStorage.getItem('userId') || 'anonymous'\n      };\n      await fetch('/api/errors', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(errorData)\n      });\n    } catch (reportingError) {\n      console.error('Failed to report error:', reportingError);\n    }\n  }, []);\n\n  // Retry function for retryable errors\n  const retryError = useCallback(async (key, retryFunction) => {\n    const error = getError(key);\n    if (!error || !error.retryable) {\n      return false;\n    }\n    try {\n      clearError(key);\n      await retryFunction();\n      return true;\n    } catch (retryError) {\n      addError(key, retryError, {\n        type: error.type,\n        severity: error.severity,\n        context: error.context,\n        retryable: true\n      });\n      return false;\n    }\n  }, [getError, clearError, addError]);\n  const value = {\n    // State\n    errors,\n    errorHistory,\n    // Error management\n    addError,\n    clearError,\n    clearAllErrors,\n    // Error queries\n    getError,\n    hasError,\n    getAllErrors,\n    getErrorsByType,\n    getErrorsBySeverity,\n    // Specialized handlers\n    handleApiError,\n    retryError,\n    // Constants\n    ERROR_TYPES,\n    ERROR_SEVERITY\n  };\n  return /*#__PURE__*/_jsxDEV(ErrorContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 5\n  }, this);\n};\n_s2(ErrorProvider, \"egFON3oJCfFPu0jTFJroAYRk6X8=\", false, function () {\n  return [useNotification];\n});\n_c = ErrorProvider;\nexport default ErrorContext;\nvar _c;\n$RefreshReg$(_c, \"ErrorProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useCallback", "useNotification", "jsxDEV", "_jsxDEV", "ErrorContext", "useError", "_s", "context", "Error", "ERROR_TYPES", "NETWORK", "VALIDATION", "AUTHENTICATION", "AUTHORIZATION", "SERVER", "CLIENT", "UNKNOWN", "ERROR_SEVERITY", "LOW", "MEDIUM", "HIGH", "CRITICAL", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s2", "errors", "setErrors", "errorHistory", "setErrorHistory", "error", "showNotification", "addError", "key", "options", "errorObj", "id", "Date", "now", "message", "type", "severity", "timestamp", "toISOString", "stack", "retryable", "autoResolve", "autoResolveDelay", "prev", "slice", "notificationOptions", "autoClose", "setTimeout", "clearError", "reportError", "newErrors", "clearAllErrors", "getError", "<PERSON><PERSON><PERSON><PERSON>", "getAllErrors", "Object", "values", "getErrorsByType", "filter", "getErrorsBySeverity", "handleApiError", "_error$config", "errorType", "response", "_error$response$data", "_error$response$data2", "status", "data", "request", "url", "config", "errorData", "userAgent", "navigator", "window", "location", "href", "userId", "localStorage", "getItem", "fetch", "method", "headers", "body", "JSON", "stringify", "reportingError", "console", "retryError", "retryFunction", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/contexts/ErrorContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useCallback } from 'react';\nimport { useNotification } from './NotificationContext';\n\nconst ErrorContext = createContext();\n\nexport const useError = () => {\n  const context = useContext(ErrorContext);\n  if (!context) {\n    throw new Error('useError must be used within an ErrorProvider');\n  }\n  return context;\n};\n\n// Error types\nexport const ERROR_TYPES = {\n  NETWORK: 'network',\n  VALIDATION: 'validation',\n  AUTHENTICATION: 'authentication',\n  AUTHORIZATION: 'authorization',\n  SERVER: 'server',\n  CLIENT: 'client',\n  UNKNOWN: 'unknown'\n};\n\n// Error severity levels\nexport const ERROR_SEVERITY = {\n  LOW: 'low',\n  MEDIUM: 'medium',\n  HIGH: 'high',\n  CRITICAL: 'critical'\n};\n\nexport const ErrorProvider = ({ children }) => {\n  const [errors, setErrors] = useState({});\n  const [errorHistory, setErrorHistory] = useState([]);\n  const { error: showNotification } = useNotification();\n\n  // Add error to state\n  const addError = useCallback((key, error, options = {}) => {\n    const errorObj = {\n      id: `${key}_${Date.now()}`,\n      key,\n      message: error.message || error,\n      type: options.type || ERROR_TYPES.UNKNOWN,\n      severity: options.severity || ERROR_SEVERITY.MEDIUM,\n      timestamp: new Date().toISOString(),\n      stack: error.stack,\n      context: options.context || {},\n      retryable: options.retryable || false,\n      showNotification: options.showNotification !== false,\n      autoResolve: options.autoResolve || false,\n      autoResolveDelay: options.autoResolveDelay || 5000\n    };\n\n    setErrors(prev => ({\n      ...prev,\n      [key]: errorObj\n    }));\n\n    setErrorHistory(prev => [...prev.slice(-99), errorObj]); // Keep last 100 errors\n\n    // Show notification if enabled\n    if (errorObj.showNotification) {\n      const notificationOptions = {\n        autoClose: errorObj.severity === ERROR_SEVERITY.CRITICAL ? false : 6000\n      };\n      showNotification(errorObj.message, notificationOptions);\n    }\n\n    // Auto-resolve if enabled\n    if (errorObj.autoResolve) {\n      setTimeout(() => {\n        clearError(key);\n      }, errorObj.autoResolveDelay);\n    }\n\n    // Report error to backend if critical\n    if (errorObj.severity === ERROR_SEVERITY.CRITICAL) {\n      reportError(errorObj);\n    }\n\n    return errorObj.id;\n  }, [showNotification]);\n\n  // Clear specific error\n  const clearError = useCallback((key) => {\n    setErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[key];\n      return newErrors;\n    });\n  }, []);\n\n  // Clear all errors\n  const clearAllErrors = useCallback(() => {\n    setErrors({});\n  }, []);\n\n  // Get error by key\n  const getError = useCallback((key) => {\n    return errors[key];\n  }, [errors]);\n\n  // Check if error exists\n  const hasError = useCallback((key) => {\n    return !!errors[key];\n  }, [errors]);\n\n  // Get all errors\n  const getAllErrors = useCallback(() => {\n    return Object.values(errors);\n  }, [errors]);\n\n  // Get errors by type\n  const getErrorsByType = useCallback((type) => {\n    return Object.values(errors).filter(error => error.type === type);\n  }, [errors]);\n\n  // Get errors by severity\n  const getErrorsBySeverity = useCallback((severity) => {\n    return Object.values(errors).filter(error => error.severity === severity);\n  }, [errors]);\n\n  // Handle API errors\n  const handleApiError = useCallback((error, context = {}) => {\n    let errorType = ERROR_TYPES.UNKNOWN;\n    let severity = ERROR_SEVERITY.MEDIUM;\n    let message = 'An unexpected error occurred';\n\n    if (error.response) {\n      const status = error.response.status;\n      \n      if (status >= 400 && status < 500) {\n        errorType = status === 401 ? ERROR_TYPES.AUTHENTICATION : \n                   status === 403 ? ERROR_TYPES.AUTHORIZATION : \n                   ERROR_TYPES.VALIDATION;\n        severity = status === 401 || status === 403 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM;\n      } else if (status >= 500) {\n        errorType = ERROR_TYPES.SERVER;\n        severity = ERROR_SEVERITY.HIGH;\n      }\n\n      message = error.response.data?.message || error.response.data?.error || message;\n    } else if (error.request) {\n      errorType = ERROR_TYPES.NETWORK;\n      severity = ERROR_SEVERITY.HIGH;\n      message = 'Network error. Please check your connection.';\n    }\n\n    return addError('api', error, {\n      type: errorType,\n      severity,\n      context: { ...context, url: error.config?.url },\n      retryable: errorType === ERROR_TYPES.NETWORK || errorType === ERROR_TYPES.SERVER\n    });\n  }, [addError]);\n\n  // Report error to backend\n  const reportError = useCallback(async (errorObj) => {\n    try {\n      const errorData = {\n        message: errorObj.message,\n        type: errorObj.type,\n        severity: errorObj.severity,\n        stack: errorObj.stack,\n        context: errorObj.context,\n        timestamp: errorObj.timestamp,\n        userAgent: navigator.userAgent,\n        url: window.location.href,\n        userId: localStorage.getItem('userId') || 'anonymous'\n      };\n\n      await fetch('/api/errors', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(errorData)\n      });\n    } catch (reportingError) {\n      console.error('Failed to report error:', reportingError);\n    }\n  }, []);\n\n  // Retry function for retryable errors\n  const retryError = useCallback(async (key, retryFunction) => {\n    const error = getError(key);\n    if (!error || !error.retryable) {\n      return false;\n    }\n\n    try {\n      clearError(key);\n      await retryFunction();\n      return true;\n    } catch (retryError) {\n      addError(key, retryError, {\n        type: error.type,\n        severity: error.severity,\n        context: error.context,\n        retryable: true\n      });\n      return false;\n    }\n  }, [getError, clearError, addError]);\n\n  const value = {\n    // State\n    errors,\n    errorHistory,\n    \n    // Error management\n    addError,\n    clearError,\n    clearAllErrors,\n    \n    // Error queries\n    getError,\n    hasError,\n    getAllErrors,\n    getErrorsByType,\n    getErrorsBySeverity,\n    \n    // Specialized handlers\n    handleApiError,\n    retryError,\n    \n    // Constants\n    ERROR_TYPES,\n    ERROR_SEVERITY\n  };\n\n  return (\n    <ErrorContext.Provider value={value}>\n      {children}\n    </ErrorContext.Provider>\n  );\n};\n\nexport default ErrorContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/E,SAASC,eAAe,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,YAAY,gBAAGP,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMQ,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGT,UAAU,CAACM,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,QAAQ;AASrB,OAAO,MAAMI,WAAW,GAAG;EACzBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,YAAY;EACxBC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM;IAAE8B,KAAK,EAAEC;EAAiB,CAAC,GAAG7B,eAAe,CAAC,CAAC;;EAErD;EACA,MAAM8B,QAAQ,GAAG/B,WAAW,CAAC,CAACgC,GAAG,EAAEH,KAAK,EAAEI,OAAO,GAAG,CAAC,CAAC,KAAK;IACzD,MAAMC,QAAQ,GAAG;MACfC,EAAE,EAAE,GAAGH,GAAG,IAAII,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC1BL,GAAG;MACHM,OAAO,EAAET,KAAK,CAACS,OAAO,IAAIT,KAAK;MAC/BU,IAAI,EAAEN,OAAO,CAACM,IAAI,IAAI9B,WAAW,CAACO,OAAO;MACzCwB,QAAQ,EAAEP,OAAO,CAACO,QAAQ,IAAIvB,cAAc,CAACE,MAAM;MACnDsB,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;MACnCC,KAAK,EAAEd,KAAK,CAACc,KAAK;MAClBpC,OAAO,EAAE0B,OAAO,CAAC1B,OAAO,IAAI,CAAC,CAAC;MAC9BqC,SAAS,EAAEX,OAAO,CAACW,SAAS,IAAI,KAAK;MACrCd,gBAAgB,EAAEG,OAAO,CAACH,gBAAgB,KAAK,KAAK;MACpDe,WAAW,EAAEZ,OAAO,CAACY,WAAW,IAAI,KAAK;MACzCC,gBAAgB,EAAEb,OAAO,CAACa,gBAAgB,IAAI;IAChD,CAAC;IAEDpB,SAAS,CAACqB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACf,GAAG,GAAGE;IACT,CAAC,CAAC,CAAC;IAEHN,eAAe,CAACmB,IAAI,IAAI,CAAC,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEd,QAAQ,CAAC,CAAC,CAAC,CAAC;;IAEzD;IACA,IAAIA,QAAQ,CAACJ,gBAAgB,EAAE;MAC7B,MAAMmB,mBAAmB,GAAG;QAC1BC,SAAS,EAAEhB,QAAQ,CAACM,QAAQ,KAAKvB,cAAc,CAACI,QAAQ,GAAG,KAAK,GAAG;MACrE,CAAC;MACDS,gBAAgB,CAACI,QAAQ,CAACI,OAAO,EAAEW,mBAAmB,CAAC;IACzD;;IAEA;IACA,IAAIf,QAAQ,CAACW,WAAW,EAAE;MACxBM,UAAU,CAAC,MAAM;QACfC,UAAU,CAACpB,GAAG,CAAC;MACjB,CAAC,EAAEE,QAAQ,CAACY,gBAAgB,CAAC;IAC/B;;IAEA;IACA,IAAIZ,QAAQ,CAACM,QAAQ,KAAKvB,cAAc,CAACI,QAAQ,EAAE;MACjDgC,WAAW,CAACnB,QAAQ,CAAC;IACvB;IAEA,OAAOA,QAAQ,CAACC,EAAE;EACpB,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMsB,UAAU,GAAGpD,WAAW,CAAEgC,GAAG,IAAK;IACtCN,SAAS,CAACqB,IAAI,IAAI;MAChB,MAAMO,SAAS,GAAG;QAAE,GAAGP;MAAK,CAAC;MAC7B,OAAOO,SAAS,CAACtB,GAAG,CAAC;MACrB,OAAOsB,SAAS;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,cAAc,GAAGvD,WAAW,CAAC,MAAM;IACvC0B,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM8B,QAAQ,GAAGxD,WAAW,CAAEgC,GAAG,IAAK;IACpC,OAAOP,MAAM,CAACO,GAAG,CAAC;EACpB,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMgC,QAAQ,GAAGzD,WAAW,CAAEgC,GAAG,IAAK;IACpC,OAAO,CAAC,CAACP,MAAM,CAACO,GAAG,CAAC;EACtB,CAAC,EAAE,CAACP,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMiC,YAAY,GAAG1D,WAAW,CAAC,MAAM;IACrC,OAAO2D,MAAM,CAACC,MAAM,CAACnC,MAAM,CAAC;EAC9B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMoC,eAAe,GAAG7D,WAAW,CAAEuC,IAAI,IAAK;IAC5C,OAAOoB,MAAM,CAACC,MAAM,CAACnC,MAAM,CAAC,CAACqC,MAAM,CAACjC,KAAK,IAAIA,KAAK,CAACU,IAAI,KAAKA,IAAI,CAAC;EACnE,CAAC,EAAE,CAACd,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMsC,mBAAmB,GAAG/D,WAAW,CAAEwC,QAAQ,IAAK;IACpD,OAAOmB,MAAM,CAACC,MAAM,CAACnC,MAAM,CAAC,CAACqC,MAAM,CAACjC,KAAK,IAAIA,KAAK,CAACW,QAAQ,KAAKA,QAAQ,CAAC;EAC3E,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMuC,cAAc,GAAGhE,WAAW,CAAC,CAAC6B,KAAK,EAAEtB,OAAO,GAAG,CAAC,CAAC,KAAK;IAAA,IAAA0D,aAAA;IAC1D,IAAIC,SAAS,GAAGzD,WAAW,CAACO,OAAO;IACnC,IAAIwB,QAAQ,GAAGvB,cAAc,CAACE,MAAM;IACpC,IAAImB,OAAO,GAAG,8BAA8B;IAE5C,IAAIT,KAAK,CAACsC,QAAQ,EAAE;MAAA,IAAAC,oBAAA,EAAAC,qBAAA;MAClB,MAAMC,MAAM,GAAGzC,KAAK,CAACsC,QAAQ,CAACG,MAAM;MAEpC,IAAIA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;QACjCJ,SAAS,GAAGI,MAAM,KAAK,GAAG,GAAG7D,WAAW,CAACG,cAAc,GAC5C0D,MAAM,KAAK,GAAG,GAAG7D,WAAW,CAACI,aAAa,GAC1CJ,WAAW,CAACE,UAAU;QACjC6B,QAAQ,GAAG8B,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,GAAG,GAAGrD,cAAc,CAACG,IAAI,GAAGH,cAAc,CAACE,MAAM;MAC3F,CAAC,MAAM,IAAImD,MAAM,IAAI,GAAG,EAAE;QACxBJ,SAAS,GAAGzD,WAAW,CAACK,MAAM;QAC9B0B,QAAQ,GAAGvB,cAAc,CAACG,IAAI;MAChC;MAEAkB,OAAO,GAAG,EAAA8B,oBAAA,GAAAvC,KAAK,CAACsC,QAAQ,CAACI,IAAI,cAAAH,oBAAA,uBAAnBA,oBAAA,CAAqB9B,OAAO,OAAA+B,qBAAA,GAAIxC,KAAK,CAACsC,QAAQ,CAACI,IAAI,cAAAF,qBAAA,uBAAnBA,qBAAA,CAAqBxC,KAAK,KAAIS,OAAO;IACjF,CAAC,MAAM,IAAIT,KAAK,CAAC2C,OAAO,EAAE;MACxBN,SAAS,GAAGzD,WAAW,CAACC,OAAO;MAC/B8B,QAAQ,GAAGvB,cAAc,CAACG,IAAI;MAC9BkB,OAAO,GAAG,8CAA8C;IAC1D;IAEA,OAAOP,QAAQ,CAAC,KAAK,EAAEF,KAAK,EAAE;MAC5BU,IAAI,EAAE2B,SAAS;MACf1B,QAAQ;MACRjC,OAAO,EAAE;QAAE,GAAGA,OAAO;QAAEkE,GAAG,GAAAR,aAAA,GAAEpC,KAAK,CAAC6C,MAAM,cAAAT,aAAA,uBAAZA,aAAA,CAAcQ;MAAI,CAAC;MAC/C7B,SAAS,EAAEsB,SAAS,KAAKzD,WAAW,CAACC,OAAO,IAAIwD,SAAS,KAAKzD,WAAW,CAACK;IAC5E,CAAC,CAAC;EACJ,CAAC,EAAE,CAACiB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMsB,WAAW,GAAGrD,WAAW,CAAC,MAAOkC,QAAQ,IAAK;IAClD,IAAI;MACF,MAAMyC,SAAS,GAAG;QAChBrC,OAAO,EAAEJ,QAAQ,CAACI,OAAO;QACzBC,IAAI,EAAEL,QAAQ,CAACK,IAAI;QACnBC,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;QAC3BG,KAAK,EAAET,QAAQ,CAACS,KAAK;QACrBpC,OAAO,EAAE2B,QAAQ,CAAC3B,OAAO;QACzBkC,SAAS,EAAEP,QAAQ,CAACO,SAAS;QAC7BmC,SAAS,EAAEC,SAAS,CAACD,SAAS;QAC9BH,GAAG,EAAEK,MAAM,CAACC,QAAQ,CAACC,IAAI;QACzBC,MAAM,EAAEC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI;MAC5C,CAAC;MAED,MAAMC,KAAK,CAAC,aAAa,EAAE;QACzBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACd,SAAS;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOe,cAAc,EAAE;MACvBC,OAAO,CAAC9D,KAAK,CAAC,yBAAyB,EAAE6D,cAAc,CAAC;IAC1D;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,UAAU,GAAG5F,WAAW,CAAC,OAAOgC,GAAG,EAAE6D,aAAa,KAAK;IAC3D,MAAMhE,KAAK,GAAG2B,QAAQ,CAACxB,GAAG,CAAC;IAC3B,IAAI,CAACH,KAAK,IAAI,CAACA,KAAK,CAACe,SAAS,EAAE;MAC9B,OAAO,KAAK;IACd;IAEA,IAAI;MACFQ,UAAU,CAACpB,GAAG,CAAC;MACf,MAAM6D,aAAa,CAAC,CAAC;MACrB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOD,UAAU,EAAE;MACnB7D,QAAQ,CAACC,GAAG,EAAE4D,UAAU,EAAE;QACxBrD,IAAI,EAAEV,KAAK,CAACU,IAAI;QAChBC,QAAQ,EAAEX,KAAK,CAACW,QAAQ;QACxBjC,OAAO,EAAEsB,KAAK,CAACtB,OAAO;QACtBqC,SAAS,EAAE;MACb,CAAC,CAAC;MACF,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACY,QAAQ,EAAEJ,UAAU,EAAErB,QAAQ,CAAC,CAAC;EAEpC,MAAM+D,KAAK,GAAG;IACZ;IACArE,MAAM;IACNE,YAAY;IAEZ;IACAI,QAAQ;IACRqB,UAAU;IACVG,cAAc;IAEd;IACAC,QAAQ;IACRC,QAAQ;IACRC,YAAY;IACZG,eAAe;IACfE,mBAAmB;IAEnB;IACAC,cAAc;IACd4B,UAAU;IAEV;IACAnF,WAAW;IACXQ;EACF,CAAC;EAED,oBACEd,OAAA,CAACC,YAAY,CAAC2F,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvE,QAAA,EACjCA;EAAQ;IAAAyE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAAC3E,GAAA,CA7MWF,aAAa;EAAA,QAGYrB,eAAe;AAAA;AAAAmG,EAAA,GAHxC9E,aAAa;AA+M1B,eAAelB,YAAY;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}