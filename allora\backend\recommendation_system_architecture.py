"""
Advanced Product Recommendation System Architecture
==================================================

This module defines the comprehensive architecture for the Allora e-commerce
recommendation system with real-time personalization capabilities.

Architecture Components:
1. User Behavior Tracking System
2. Multiple ML Recommendation Algorithms
3. Real-time Personalization Engine
4. Recommendation Caching Layer
5. Analytics and Optimization System
6. API Management System

Author: Allora Development Team
Date: 2025-07-06
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Union
from enum import Enum
from datetime import datetime
import json

class RecommendationAlgorithm(Enum):
    """Supported recommendation algorithms"""
    COLLABORATIVE_FILTERING_USER = "collaborative_filtering_user"
    COLLABORATIVE_FILTERING_ITEM = "collaborative_filtering_item"
    CONTENT_BASED = "content_based"
    MATRIX_FACTORIZATION = "matrix_factorization"
    DEEP_LEARNING = "deep_learning"
    HYBRID = "hybrid"
    TRENDING = "trending"
    POPULARITY_BASED = "popularity_based"

class RecommendationType(Enum):
    """Types of recommendations"""
    PERSONALIZED = "personalized"
    SIMILAR_PRODUCTS = "similar_products"
    CROSS_SELL = "cross_sell"
    UPSELL = "upsell"
    TRENDING = "trending"
    NEW_ARRIVALS = "new_arrivals"
    SEASONAL = "seasonal"
    CATEGORY_BASED = "category_based"
    BRAND_BASED = "brand_based"
    PRICE_BASED = "price_based"

class UserInteractionType(Enum):
    """Types of user interactions to track"""
    VIEW = "view"
    CLICK = "click"
    ADD_TO_CART = "add_to_cart"
    REMOVE_FROM_CART = "remove_from_cart"
    PURCHASE = "purchase"
    RATING = "rating"
    REVIEW = "review"
    WISHLIST_ADD = "wishlist_add"
    WISHLIST_REMOVE = "wishlist_remove"
    SEARCH = "search"
    FILTER = "filter"
    SHARE = "share"
    COMPARE = "compare"

@dataclass
class UserInteraction:
    """User interaction data structure"""
    user_id: str
    product_id: str
    interaction_type: UserInteractionType
    timestamp: datetime
    session_id: str
    value: Optional[float] = None  # For ratings, prices, etc.
    context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class UserProfile:
    """Comprehensive user profile for personalization"""
    user_id: str
    preferences: Dict[str, float] = field(default_factory=dict)
    categories: Dict[str, float] = field(default_factory=dict)
    brands: Dict[str, float] = field(default_factory=dict)
    price_range: Dict[str, float] = field(default_factory=dict)
    seasonal_preferences: Dict[str, float] = field(default_factory=dict)
    interaction_history: List[UserInteraction] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.utcnow)
    total_interactions: int = 0
    avg_rating: float = 0.0
    purchase_frequency: float = 0.0
    avg_order_value: float = 0.0

@dataclass
class RecommendationRequest:
    """Recommendation request structure"""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    recommendation_type: RecommendationType = RecommendationType.PERSONALIZED
    algorithm: Optional[RecommendationAlgorithm] = None
    context: Dict[str, Any] = field(default_factory=dict)
    filters: Dict[str, Any] = field(default_factory=dict)
    limit: int = 10
    include_explanation: bool = False
    exclude_products: List[str] = field(default_factory=list)

@dataclass
class RecommendationResult:
    """Recommendation result structure"""
    product_id: str
    score: float
    algorithm: RecommendationAlgorithm
    explanation: Optional[str] = None
    confidence: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RecommendationResponse:
    """Complete recommendation response"""
    recommendations: List[RecommendationResult]
    request_id: str
    user_id: Optional[str]
    algorithm_used: RecommendationAlgorithm
    total_candidates: int
    processing_time_ms: float
    cache_hit: bool = False
    explanation: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class RecommendationSystemConfig:
    """Configuration for the recommendation system"""
    
    # Algorithm weights for hybrid recommendations
    ALGORITHM_WEIGHTS = {
        RecommendationAlgorithm.COLLABORATIVE_FILTERING_USER: 0.3,
        RecommendationAlgorithm.COLLABORATIVE_FILTERING_ITEM: 0.25,
        RecommendationAlgorithm.CONTENT_BASED: 0.2,
        RecommendationAlgorithm.MATRIX_FACTORIZATION: 0.15,
        RecommendationAlgorithm.DEEP_LEARNING: 0.1
    }
    
    # Minimum data requirements
    MIN_INTERACTIONS_FOR_COLLABORATIVE = 5
    MIN_USERS_FOR_COLLABORATIVE = 10
    MIN_PRODUCTS_FOR_CONTENT_BASED = 20
    
    # Cache settings
    CACHE_TTL_SECONDS = 3600  # 1 hour
    CACHE_WARM_UP_USERS = 1000  # Top active users
    
    # Real-time settings
    REAL_TIME_UPDATE_THRESHOLD = 10  # Update after N interactions
    PROFILE_UPDATE_FREQUENCY = 300  # 5 minutes
    
    # Model training settings
    MODEL_RETRAIN_FREQUENCY = 86400  # 24 hours
    MODEL_EVALUATION_FREQUENCY = 3600  # 1 hour
    
    # Performance thresholds
    MAX_RESPONSE_TIME_MS = 200
    MIN_RECOMMENDATION_SCORE = 0.1
    MAX_RECOMMENDATIONS_PER_REQUEST = 50

class RecommendationSystemArchitecture:
    """
    Main architecture class defining the recommendation system structure
    """
    
    def __init__(self):
        self.config = RecommendationSystemConfig()
        self.algorithms = {}
        self.cache_layer = None
        self.behavior_tracker = None
        self.personalization_engine = None
        self.analytics_system = None
    
    def get_system_components(self) -> Dict[str, str]:
        """Get all system components and their descriptions"""
        return {
            "behavior_tracker": "Tracks and analyzes user interactions in real-time",
            "ml_models": "Multiple ML algorithms for different recommendation scenarios",
            "personalization_engine": "Real-time user profile management and preference learning",
            "cache_layer": "Redis-based caching for fast recommendation retrieval",
            "api_system": "RESTful APIs for recommendation requests and management",
            "analytics_system": "Performance monitoring and optimization recommendations",
            "model_manager": "ML model training, evaluation, and deployment management",
            "a_b_testing": "A/B testing framework for recommendation algorithm comparison"
        }
    
    def get_data_flow(self) -> List[Dict[str, str]]:
        """Get the data flow through the recommendation system"""
        return [
            {
                "step": 1,
                "component": "User Interaction",
                "description": "User performs action (view, click, purchase, etc.)",
                "output": "UserInteraction object"
            },
            {
                "step": 2,
                "component": "Behavior Tracker",
                "description": "Captures and processes user interaction",
                "output": "Processed interaction data"
            },
            {
                "step": 3,
                "component": "User Profile Update",
                "description": "Updates user preferences and profile",
                "output": "Updated UserProfile"
            },
            {
                "step": 4,
                "component": "Recommendation Request",
                "description": "API receives recommendation request",
                "output": "RecommendationRequest object"
            },
            {
                "step": 5,
                "component": "Cache Check",
                "description": "Check if recommendations exist in cache",
                "output": "Cache hit/miss result"
            },
            {
                "step": 6,
                "component": "Algorithm Selection",
                "description": "Select appropriate recommendation algorithm(s)",
                "output": "Selected algorithm(s)"
            },
            {
                "step": 7,
                "component": "Recommendation Generation",
                "description": "Generate recommendations using ML models",
                "output": "Raw recommendation scores"
            },
            {
                "step": 8,
                "component": "Personalization",
                "description": "Apply personalization and ranking",
                "output": "Personalized recommendations"
            },
            {
                "step": 9,
                "component": "Response Formatting",
                "description": "Format and return recommendations",
                "output": "RecommendationResponse"
            },
            {
                "step": 10,
                "component": "Analytics Tracking",
                "description": "Track recommendation performance",
                "output": "Analytics data"
            }
        ]
    
    def get_algorithm_details(self) -> Dict[str, Dict[str, Any]]:
        """Get details about each recommendation algorithm"""
        return {
            "collaborative_filtering_user": {
                "description": "Recommends products based on similar users' preferences",
                "use_case": "Users with sufficient interaction history",
                "pros": ["High accuracy for active users", "Discovers new interests"],
                "cons": ["Cold start problem", "Sparsity issues"],
                "data_requirements": "User-item interaction matrix"
            },
            "collaborative_filtering_item": {
                "description": "Recommends products similar to user's previous interactions",
                "use_case": "Products with sufficient interaction data",
                "pros": ["Stable recommendations", "Explainable results"],
                "cons": ["Limited diversity", "Popularity bias"],
                "data_requirements": "Item-item similarity matrix"
            },
            "content_based": {
                "description": "Recommends products based on product features and user preferences",
                "use_case": "New users or products with rich metadata",
                "pros": ["No cold start problem", "Transparent recommendations"],
                "cons": ["Limited serendipity", "Feature engineering required"],
                "data_requirements": "Product features and user preferences"
            },
            "matrix_factorization": {
                "description": "Uses latent factors to predict user-item preferences",
                "use_case": "Large datasets with sparse interactions",
                "pros": ["Handles sparsity well", "Scalable"],
                "cons": ["Less interpretable", "Requires tuning"],
                "data_requirements": "User-item rating matrix"
            },
            "deep_learning": {
                "description": "Neural networks for complex pattern recognition",
                "use_case": "Large datasets with complex user behavior",
                "pros": ["Captures complex patterns", "High accuracy potential"],
                "cons": ["Requires large datasets", "Computationally expensive"],
                "data_requirements": "Large-scale interaction and feature data"
            },
            "hybrid": {
                "description": "Combines multiple algorithms for optimal results",
                "use_case": "Production systems requiring robust performance",
                "pros": ["Best of all algorithms", "Robust performance"],
                "cons": ["Complex implementation", "Harder to debug"],
                "data_requirements": "All component algorithm requirements"
            }
        }

# Export main classes and enums
__all__ = [
    'RecommendationAlgorithm',
    'RecommendationType', 
    'UserInteractionType',
    'UserInteraction',
    'UserProfile',
    'RecommendationRequest',
    'RecommendationResult',
    'RecommendationResponse',
    'RecommendationSystemConfig',
    'RecommendationSystemArchitecture'
]
