"""
RMA Utility Functions
====================

Utility functions for RMA processing including:
1. Validation helpers
2. Calculation utilities
3. Notification helpers
4. Report generation
5. Data export/import
6. Integration helpers

These utilities support the main RMA engine and provide
reusable functionality across the RMA system.
"""

import logging
import json
import csv
import io
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
from decimal import Decimal, ROUND_HALF_UP

logger = logging.getLogger(__name__)

class RMAValidator:
    """RMA validation utilities"""
    
    @staticmethod
    def validate_return_eligibility(order, config) -> Tuple[bool, str]:
        """Validate if order is eligible for returns"""
        
        # Check order status
        if order.status not in ['delivered', 'shipped']:
            return False, "Order must be delivered or shipped to be eligible for returns"
        
        # Check time limits
        if order.delivered_at:
            days_since_delivery = (datetime.utcnow() - order.delivered_at).days
            if days_since_delivery > config.return_window_days:
                return False, f"Return window of {config.return_window_days} days has expired"
        elif order.shipped_at:
            days_since_shipped = (datetime.utcnow() - order.shipped_at).days
            if days_since_shipped > (config.return_window_days + 7):
                return False, "Return window has expired"
        else:
            return False, "Order must be shipped to be eligible for returns"
        
        # Check if order has existing RMA requests
        from app import RMARequest
        existing_rma = RMARequest.query.filter_by(order_id=order.id).first()
        if existing_rma and existing_rma.status not in ['rejected', 'cancelled']:
            return False, "Order already has an active RMA request"
        
        return True, "Order is eligible for returns"
    
    @staticmethod
    def validate_return_items(order_items: List, return_items: List[Dict]) -> Tuple[bool, str]:
        """Validate return items against order items"""
        
        order_items_dict = {item.id: item for item in order_items}
        
        for return_item in return_items:
            order_item_id = return_item.get('order_item_id')
            return_quantity = return_item.get('quantity', 0)
            
            # Check if order item exists
            if order_item_id not in order_items_dict:
                return False, f"Order item {order_item_id} not found"
            
            order_item = order_items_dict[order_item_id]
            
            # Check quantity
            if return_quantity <= 0:
                return False, "Return quantity must be greater than 0"
            
            if return_quantity > order_item.quantity:
                return False, f"Return quantity ({return_quantity}) exceeds ordered quantity ({order_item.quantity})"
            
            # Check return reason
            return_reason = return_item.get('return_reason')
            if not return_reason:
                return False, "Return reason is required"
            
            valid_reasons = [
                'defective', 'wrong_item', 'not_as_described', 'size_issue',
                'color_issue', 'damaged_shipping', 'changed_mind', 'better_price_found',
                'duplicate_order', 'quality_issue', 'missing_parts', 'expired_product', 'other'
            ]
            
            if return_reason not in valid_reasons:
                return False, f"Invalid return reason: {return_reason}"
        
        return True, "Return items are valid"
    
    @staticmethod
    def validate_exchange_items(return_items: List[Dict]) -> Tuple[bool, str]:
        """Validate exchange items"""
        
        from app import Product
        
        for return_item in return_items:
            exchange_product_id = return_item.get('exchange_product_id')
            exchange_quantity = return_item.get('exchange_quantity', 0)
            
            if exchange_product_id:
                # Check if exchange product exists
                exchange_product = Product.query.get(exchange_product_id)
                if not exchange_product:
                    return False, f"Exchange product {exchange_product_id} not found"
                
                # Check if exchange product is active
                if not exchange_product.is_active:
                    return False, f"Exchange product {exchange_product.name} is not available"
                
                # Check stock availability
                if exchange_product.stock_quantity < exchange_quantity:
                    return False, f"Insufficient stock for exchange product {exchange_product.name}"
        
        return True, "Exchange items are valid"

class RMACalculator:
    """RMA calculation utilities"""
    
    @staticmethod
    def calculate_refund_amount(rma_items: List, config) -> Dict[str, float]:
        """Calculate refund amounts"""
        
        subtotal = sum(item.total_price for item in rma_items)
        
        # Calculate restocking fee
        restocking_fee = 0.0
        if config.restocking_fee_percentage > 0:
            restocking_fee = subtotal * (config.restocking_fee_percentage / 100)
        
        # Calculate return shipping cost
        return_shipping_cost = 0.0
        if not config.free_return_shipping:
            # This would integrate with shipping calculator
            return_shipping_cost = 50.0  # Default shipping cost
        
        # Calculate net refund
        net_refund = subtotal - restocking_fee - return_shipping_cost
        net_refund = max(0, net_refund)  # Ensure non-negative
        
        return {
            'subtotal': round(subtotal, 2),
            'restocking_fee': round(restocking_fee, 2),
            'return_shipping_cost': round(return_shipping_cost, 2),
            'net_refund': round(net_refund, 2)
        }
    
    @staticmethod
    def calculate_exchange_price_difference(original_items: List, exchange_items: List) -> float:
        """Calculate price difference for exchanges"""
        
        original_total = sum(item.total_price for item in original_items)
        
        exchange_total = 0.0
        for item in exchange_items:
            if item.exchange_product_id and item.exchange_quantity:
                exchange_total += item.exchange_unit_price * item.exchange_quantity
        
        price_difference = exchange_total - original_total
        return round(price_difference, 2)

class RMANotificationHelper:
    """RMA notification utilities"""
    
    @staticmethod
    def get_notification_template(event_type: str) -> Dict[str, str]:
        """Get notification template for RMA events"""
        
        templates = {
            'created': {
                'subject': 'RMA Request Created - {rma_number}',
                'template': 'rma_created.html',
                'sms_template': 'Your return request {rma_number} has been created. We will review it within 24 hours.'
            },
            'approval_approved': {
                'subject': 'RMA Request Approved - {rma_number}',
                'template': 'rma_approved.html',
                'sms_template': 'Your return request {rma_number} has been approved. Return label will be sent shortly.'
            },
            'approval_rejected': {
                'subject': 'RMA Request Rejected - {rma_number}',
                'template': 'rma_rejected.html',
                'sms_template': 'Your return request {rma_number} has been rejected. Reason: {rejection_reason}'
            },
            'return_received': {
                'subject': 'Return Items Received - {rma_number}',
                'template': 'return_received.html',
                'sms_template': 'We have received your return items for {rma_number}. Processing refund now.'
            },
            'refund_completed': {
                'subject': 'Refund Completed - {rma_number}',
                'template': 'refund_completed.html',
                'sms_template': 'Refund of ₹{refund_amount} for {rma_number} has been processed to your original payment method.'
            },
            'exchange_processing': {
                'subject': 'Exchange Processing - {rma_number}',
                'template': 'exchange_processing.html',
                'sms_template': 'Your exchange request {rma_number} is being processed. New items will be shipped soon.'
            }
        }
        
        return templates.get(event_type, {
            'subject': 'RMA Update - {rma_number}',
            'template': 'rma_generic.html',
            'sms_template': 'Update on your RMA request {rma_number}.'
        })
    
    @staticmethod
    def format_notification_data(rma_request, event_type: str) -> Dict[str, Any]:
        """Format data for notifications"""
        
        return {
            'rma_number': rma_request.rma_number,
            'customer_email': rma_request.customer_email,
            'customer_phone': rma_request.customer_phone,
            'order_number': rma_request.order.order_number if rma_request.order else '',
            'total_refund_amount': rma_request.total_refund_amount,
            'net_refund_amount': rma_request.total_refund_amount - rma_request.restocking_fee - rma_request.return_shipping_cost,
            'status': rma_request.status,
            'rejection_reason': rma_request.rejection_reason,
            'return_tracking_number': rma_request.return_tracking_number,
            'return_label_url': rma_request.return_label_url,
            'event_type': event_type,
            'created_at': rma_request.created_at.strftime('%B %d, %Y'),
            'deadline': rma_request.deadline.strftime('%B %d, %Y') if rma_request.deadline else None
        }

class RMAReportGenerator:
    """RMA reporting utilities"""
    
    @staticmethod
    def generate_rma_summary_report(start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate RMA summary report"""
        
        from app import db, RMARequest, RMAItem
        
        # Get RMA requests in date range
        rma_requests = db.session.query(RMARequest).filter(
            RMARequest.created_at >= start_date,
            RMARequest.created_at <= end_date
        ).all()
        
        # Calculate statistics
        total_requests = len(rma_requests)
        approved_requests = len([r for r in rma_requests if r.status in ['approved', 'refund_completed', 'exchange_completed']])
        rejected_requests = len([r for r in rma_requests if r.status == 'rejected'])
        pending_requests = len([r for r in rma_requests if r.status == 'pending'])
        
        total_refund_amount = sum(r.total_refund_amount for r in rma_requests)
        total_restocking_fees = sum(r.restocking_fee for r in rma_requests)
        
        # Return reason breakdown
        reason_breakdown = {}
        for rma_request in rma_requests:
            for item in rma_request.rma_items:
                reason = item.return_reason
                reason_breakdown[reason] = reason_breakdown.get(reason, 0) + 1
        
        # Processing time analysis
        processing_times = []
        for rma_request in rma_requests:
            if rma_request.approved_at and rma_request.created_at:
                processing_time = (rma_request.approved_at - rma_request.created_at).total_seconds() / 3600
                processing_times.append(processing_time)
        
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        return {
            'period': {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            },
            'summary': {
                'total_requests': total_requests,
                'approved_requests': approved_requests,
                'rejected_requests': rejected_requests,
                'pending_requests': pending_requests,
                'approval_rate': (approved_requests / total_requests * 100) if total_requests > 0 else 0
            },
            'financial': {
                'total_refund_amount': round(total_refund_amount, 2),
                'total_restocking_fees': round(total_restocking_fees, 2),
                'net_refund_amount': round(total_refund_amount - total_restocking_fees, 2)
            },
            'return_reasons': reason_breakdown,
            'processing': {
                'avg_processing_time_hours': round(avg_processing_time, 2),
                'total_processed': len(processing_times)
            }
        }
    
    @staticmethod
    def export_rma_data_csv(rma_requests: List) -> str:
        """Export RMA data to CSV format"""
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'RMA Number', 'Order ID', 'Customer Email', 'Status', 'RMA Type',
            'Total Refund Amount', 'Restocking Fee', 'Net Refund',
            'Created At', 'Updated At', 'Approved At', 'Refunded At'
        ])
        
        # Write data
        for rma in rma_requests:
            writer.writerow([
                rma.rma_number,
                rma.order_id,
                rma.customer_email,
                rma.status,
                rma.rma_type,
                rma.total_refund_amount,
                rma.restocking_fee,
                rma.total_refund_amount - rma.restocking_fee - rma.return_shipping_cost,
                rma.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                rma.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                rma.approved_at.strftime('%Y-%m-%d %H:%M:%S') if rma.approved_at else '',
                rma.refunded_at.strftime('%Y-%m-%d %H:%M:%S') if rma.refunded_at else ''
            ])
        
        return output.getvalue()

class RMAIntegrationHelper:
    """Integration utilities for RMA system"""
    
    @staticmethod
    def sync_with_inventory_system(rma_items: List) -> bool:
        """Sync RMA items with inventory system"""
        try:
            from app import db, Product
            
            for rma_item in rma_items:
                if rma_item.item_status == 'received' and rma_item.inspection_result == 'passed':
                    # Add back to inventory
                    product = db.session.query(Product).get(rma_item.product_id)
                    if product:
                        product.stock_quantity += rma_item.quantity
                        product.updated_at = datetime.utcnow()
            
            db.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error syncing with inventory system: {e}")
            db.session.rollback()
            return False
    
    @staticmethod
    def integrate_with_shipping_system(rma_request, shipping_data: Dict) -> bool:
        """Integrate with shipping system for return labels"""
        try:
            # This would integrate with actual shipping APIs
            # For now, we'll simulate the integration
            
            rma_request.return_tracking_number = shipping_data.get('tracking_number')
            rma_request.return_carrier = shipping_data.get('carrier')
            rma_request.return_label_url = shipping_data.get('label_url')
            
            return True
            
        except Exception as e:
            logger.error(f"Error integrating with shipping system: {e}")
            return False
