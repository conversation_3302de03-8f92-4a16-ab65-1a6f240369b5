/**
 * Recommendation Tracking Utility
 * ===============================
 * 
 * Utility for tracking recommendation interactions including clicks,
 * conversions, and other engagement metrics for analytics.
 * 
 * Features:
 * - Click tracking
 * - Conversion tracking
 * - Batch processing
 * - Offline support
 * - Privacy compliance
 */

class RecommendationTracker {
  constructor() {
    this.trackingQueue = [];
    this.batchSize = 10;
    this.flushInterval = 5000; // 5 seconds
    this.maxRetries = 3;
    this.isOnline = navigator.onLine;
    
    // Initialize tracking
    this.init();
  }

  init() {
    // Set up periodic flushing
    setInterval(() => {
      this.flushQueue();
    }, this.flushInterval);

    // Handle online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Flush queue before page unload
    window.addEventListener('beforeunload', () => {
      this.flushQueue(true);
    });

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.flushQueue(true);
      }
    });
  }

  /**
   * Track when a user clicks on a recommended product
   */
  trackRecommendationClick(data) {
    const trackingData = {
      type: 'click',
      user_id: data.userId,
      product_id: data.productId,
      session_id: data.sessionId || this.getSessionId(),
      algorithm: data.algorithm,
      position: data.position,
      recommendation_context: {
        page_type: data.pageType || 'unknown',
        recommendation_type: data.recommendationType,
        category: data.category,
        search_query: data.searchQuery,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    };

    this.addToQueue(trackingData);
    
    // Also track with behavior tracker if available
    if (window.behaviorTracker) {
      window.behaviorTracker.trackProductView(data.productId, {
        source: 'recommendation',
        algorithm: data.algorithm,
        position: data.position
      });
    }
  }

  /**
   * Track when a recommended product leads to a conversion
   */
  trackRecommendationConversion(data) {
    const trackingData = {
      type: 'conversion',
      user_id: data.userId,
      product_id: data.productId,
      session_id: data.sessionId || this.getSessionId(),
      conversion_type: data.conversionType || 'purchase',
      conversion_value: data.conversionValue || 0,
      algorithm: data.algorithm,
      position: data.position,
      order_id: data.orderId,
      recommendation_context: {
        page_type: data.pageType || 'unknown',
        recommendation_type: data.recommendationType,
        category: data.category,
        original_recommendation_time: data.originalRecommendationTime,
        time_to_conversion: data.timeToConversion,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    };

    this.addToQueue(trackingData);

    // Also track with behavior tracker if available
    if (window.behaviorTracker) {
      const trackingMethod = data.conversionType === 'purchase' ? 'trackPurchase' : 'trackAddToCart';
      if (window.behaviorTracker[trackingMethod]) {
        window.behaviorTracker[trackingMethod](data.productId, {
          source: 'recommendation',
          algorithm: data.algorithm,
          value: data.conversionValue
        });
      }
    }
  }

  /**
   * Track recommendation impression (when recommendation is shown)
   */
  trackRecommendationImpression(data) {
    const trackingData = {
      type: 'impression',
      user_id: data.userId,
      product_ids: data.productIds, // Array of product IDs shown
      session_id: data.sessionId || this.getSessionId(),
      algorithm: data.algorithm,
      recommendation_context: {
        page_type: data.pageType || 'unknown',
        recommendation_type: data.recommendationType,
        category: data.category,
        total_recommendations: data.productIds.length,
        viewport_position: data.viewportPosition,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    };

    this.addToQueue(trackingData);
  }

  /**
   * Track recommendation dismissal (when user dismisses/hides recommendations)
   */
  trackRecommendationDismissal(data) {
    const trackingData = {
      type: 'dismissal',
      user_id: data.userId,
      session_id: data.sessionId || this.getSessionId(),
      algorithm: data.algorithm,
      dismissal_reason: data.reason || 'user_action',
      recommendation_context: {
        page_type: data.pageType || 'unknown',
        recommendation_type: data.recommendationType,
        products_shown: data.productsShown,
        time_visible: data.timeVisible,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    };

    this.addToQueue(trackingData);
  }

  /**
   * Add tracking data to queue
   */
  addToQueue(data) {
    // Add unique ID for deduplication
    data.tracking_id = this.generateTrackingId();
    
    this.trackingQueue.push(data);

    // Flush if queue is full
    if (this.trackingQueue.length >= this.batchSize) {
      this.flushQueue();
    }
  }

  /**
   * Flush tracking queue to server
   */
  async flushQueue(force = false) {
    if (!this.isOnline && !force) {
      return;
    }

    if (this.trackingQueue.length === 0) {
      return;
    }

    const dataToSend = [...this.trackingQueue];
    this.trackingQueue = [];

    try {
      await this.sendTrackingData(dataToSend);
    } catch (error) {
      console.warn('Failed to send tracking data:', error);
      
      // Re-add failed data to queue for retry (with retry limit)
      const retriableData = dataToSend.filter(item => 
        (item.retry_count || 0) < this.maxRetries
      ).map(item => ({
        ...item,
        retry_count: (item.retry_count || 0) + 1
      }));
      
      this.trackingQueue.unshift(...retriableData);
    }
  }

  /**
   * Send tracking data to server
   */
  async sendTrackingData(data) {
    const promises = data.map(async (item) => {
      const endpoint = item.type === 'conversion' 
        ? '/api/recommendations/track/conversion'
        : '/api/recommendations/track/click';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(item)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    });

    await Promise.all(promises);
  }

  /**
   * Get or create session ID
   */
  getSessionId() {
    let sessionId = sessionStorage.getItem('recommendation_session_id');
    
    if (!sessionId) {
      sessionId = this.generateSessionId();
      sessionStorage.setItem('recommendation_session_id', sessionId);
    }
    
    return sessionId;
  }

  /**
   * Generate unique session ID
   */
  generateSessionId() {
    return 'rec_session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Generate unique tracking ID
   */
  generateTrackingId() {
    return 'track_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get user ID from various sources
   */
  getUserId() {
    // Try to get user ID from various sources
    const sources = [
      () => localStorage.getItem('user_id'),
      () => sessionStorage.getItem('user_id'),
      () => window.currentUser?.id,
      () => document.querySelector('[data-user-id]')?.dataset.userId
    ];

    for (const source of sources) {
      try {
        const userId = source();
        if (userId) return userId;
      } catch (e) {
        // Continue to next source
      }
    }

    return null;
  }

  /**
   * Enhanced click tracking with automatic data collection
   */
  trackClick(element, additionalData = {}) {
    const productId = element.dataset.productId || 
                     element.closest('[data-product-id]')?.dataset.productId;
    
    if (!productId) {
      console.warn('No product ID found for recommendation click tracking');
      return;
    }

    const userId = this.getUserId();
    if (!userId) {
      console.warn('No user ID found for recommendation click tracking');
      return;
    }

    const trackingData = {
      userId: userId,
      productId: productId,
      algorithm: element.dataset.algorithm || additionalData.algorithm,
      position: element.dataset.position || additionalData.position,
      pageType: additionalData.pageType || this.getPageType(),
      recommendationType: element.dataset.recommendationType || additionalData.recommendationType,
      category: element.dataset.category || additionalData.category,
      ...additionalData
    };

    this.trackRecommendationClick(trackingData);
  }

  /**
   * Get current page type
   */
  getPageType() {
    const path = window.location.pathname;
    
    if (path === '/' || path === '/home') return 'home';
    if (path.includes('/product/')) return 'product_detail';
    if (path.includes('/category/')) return 'category';
    if (path.includes('/search')) return 'search';
    if (path.includes('/cart')) return 'cart';
    if (path.includes('/checkout')) return 'checkout';
    
    return 'other';
  }

  /**
   * Auto-setup click tracking for recommendation elements
   */
  setupAutoTracking() {
    // Set up click tracking for recommendation elements
    document.addEventListener('click', (event) => {
      const recommendationElement = event.target.closest('[data-recommendation-track]');
      
      if (recommendationElement) {
        this.trackClick(recommendationElement);
      }
    });

    // Set up intersection observer for impression tracking
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target;
            const productIds = element.dataset.productIds?.split(',') || [];
            
            if (productIds.length > 0) {
              const userId = this.getUserId();
              if (userId) {
                this.trackRecommendationImpression({
                  userId: userId,
                  productIds: productIds,
                  algorithm: element.dataset.algorithm,
                  recommendationType: element.dataset.recommendationType,
                  pageType: this.getPageType(),
                  viewportPosition: entry.boundingClientRect.top
                });
              }
            }
            
            // Stop observing after first impression
            observer.unobserve(element);
          }
        });
      }, {
        threshold: 0.5 // Track when 50% visible
      });

      // Observe recommendation containers
      document.querySelectorAll('[data-recommendation-impression]').forEach(element => {
        observer.observe(element);
      });
    }
  }

  /**
   * Clear tracking queue (for privacy compliance)
   */
  clearTrackingData() {
    this.trackingQueue = [];
    sessionStorage.removeItem('recommendation_session_id');
  }
}

// Create global instance
const recommendationTracker = new RecommendationTracker();

// Auto-setup when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    recommendationTracker.setupAutoTracking();
  });
} else {
  recommendationTracker.setupAutoTracking();
}

// Export for use in other modules
export default recommendationTracker;
