{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';\nimport { useMemo } from 'react';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { SellerAuthProvider } from './contexts/SellerAuthContext';\nimport { CartProvider } from './contexts/CartContext';\nimport { DialogProvider } from './contexts/DialogContext';\nimport { CookieProvider } from './contexts/CookieContext';\nimport NotificationProvider from './contexts/NotificationContext';\nimport { WebSocketProvider } from './contexts/WebSocketContext';\nimport { LoadingProvider } from './contexts/LoadingContext';\nimport { ErrorProvider } from './contexts/ErrorContext';\nimport { ImageProvider } from './contexts/ImageContext';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { GlobalErrorDisplay } from './components/ErrorComponents';\nimport { GlobalLoadingIndicator } from './components/LoadingComponents';\nimport { useSEO } from './utils/seo';\nimport ModernMinimalistNavbar from './components/ModernMinimalistNavbar';\nimport ModernMinimalistFooter from './components/ModernMinimalistFooter';\nimport DynamicTitle from './components/DynamicTitle';\nimport Home from './components/SimpleHome';\nimport ProductDetail from './pages/ProductDetails';\nimport Community from './pages/Community';\nimport Profile from './pages/Profile';\nimport Account from './pages/Account';\nimport Cart from './pages/Cart';\nimport Checkout from './pages/Checkout';\nimport CartRecovery from './pages/CartRecovery';\nimport Search from './pages/Search';\nimport Categories from './pages/Categories';\nimport Category from './pages/Category';\nimport InventoryAssistant from './pages/InventoryAssistant';\nimport PriceTrends from './pages/PriceTrends';\nimport AuthPage from './components/Login';\nimport PrivacyPolicy from './components/PrivacyPolicy';\nimport TermsOfService from './components/TermsOfService';\nimport CookiePolicy from './components/CookiePolicy';\nimport CookieConsentBanner from './components/CookieConsentBanner';\nimport CookiePreferenceCenter from './components/CookiePreferenceCenter';\nimport GuestCheckout from './components/GuestCheckout';\nimport OrderConfirmation from './pages/OrderConfirmation';\nimport TrackOrder from './pages/TrackOrder';\nimport SupportHub from './pages/SupportHub';\nimport FAQ from './pages/FAQ';\nimport ContactUs from './pages/ContactUs';\nimport ReturnPolicy from './pages/ReturnPolicy';\nimport SupportTickets from './pages/SupportTickets';\nimport Sell from './pages/Sell';\nimport SellerLogin from './pages/SellerLogin';\nimport SellerDashboard from './pages/SellerDashboard';\nimport SellerProducts from './pages/SellerProducts';\nimport SellerProductForm from './pages/SellerProductForm';\nimport SellerOrders from './pages/SellerOrders';\nimport SellerStoreProfile from './pages/SellerStoreProfile';\nimport PublicStorePage from './pages/PublicStorePage';\nimport SellerEarnings from './pages/SellerEarnings';\nimport SellerAnalytics from './pages/SellerAnalytics';\nimport SellerStorePage from './pages/SellerStorePage';\nimport AdminSellerManagement from './pages/AdminSellerManagement';\nimport AdminLogin from './pages/AdminLogin';\nimport AdminDashboard from './pages/AdminDashboard';\nimport AdminProducts from './pages/AdminProducts';\nimport NotFound from './pages/NotFound';\nimport ServerError from './pages/ServerError';\nimport ImageTest from './pages/ImageTest';\nimport TestPostCard from './components/TestPostCard';\nimport About from './pages/About';\nimport Sustainability from './pages/Sustainability';\nimport Careers from './pages/Careers';\nimport Press from './pages/Press';\nimport ShippingInfo from './pages/ShippingInfo';\nimport CommunityPage from './pages/CommunityPage';\nimport './styles.css';\nimport './styles/responsive.css';\nimport 'tailwindcss/tailwind.css';\nimport { HelmetProvider } from 'react-helmet-async';\n\n// Inner App component that uses AuthContext\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AppContent() {\n  _s();\n  const {\n    token,\n    user,\n    logout,\n    isAdmin\n  } = useAuth();\n\n  // Set global SEO defaults - use useMemo to prevent infinite re-renders\n  const currentUrl = useMemo(() => window.location.href, []);\n  useSEO({\n    title: 'Allora - Sustainable Shopping Platform',\n    description: 'Discover eco-friendly products and join our sustainable shopping community. Shop responsibly with Allora.',\n    keywords: 'sustainable shopping, eco-friendly products, green marketplace, sustainable living',\n    image: '/images/og-image.jpg',\n    url: currentUrl\n  });\n  const ProtectedRoute = ({\n    element\n  }) => {\n    return token ? element : /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 30\n    }, this);\n  };\n  const AdminProtectedRoute = ({\n    element\n  }) => {\n    return isAdmin ? element : /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 32\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(SellerAuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [/*#__PURE__*/_jsxDEV(DynamicTitle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(ModernMinimalistNavbar, {\n          token: token,\n          user: user,\n          onLogout: logout\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(CookieConsentBanner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(CookiePreferenceCenter, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Home, {\n                token: token\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 36\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/product/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProductDetail, {\n                token: token\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/community\",\n              element: /*#__PURE__*/_jsxDEV(Community, {\n                token: token\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/profile\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                element: /*#__PURE__*/_jsxDEV(Profile, {\n                  token: token\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 68\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/account\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                element: /*#__PURE__*/_jsxDEV(Account, {\n                  token: token\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 68\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cart\",\n              element: /*#__PURE__*/_jsxDEV(Cart, {\n                token: token\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/checkout\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                element: /*#__PURE__*/_jsxDEV(Checkout, {\n                  token: token\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 69\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/guest-checkout\",\n              element: /*#__PURE__*/_jsxDEV(GuestCheckout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cart-recovery/:recoveryToken\",\n              element: /*#__PURE__*/_jsxDEV(CartRecovery, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 64\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/order-confirmation/:orderNumber\",\n              element: /*#__PURE__*/_jsxDEV(OrderConfirmation, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 67\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/track\",\n              element: /*#__PURE__*/_jsxDEV(TrackOrder, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/track/:trackingNumber\",\n              element: /*#__PURE__*/_jsxDEV(TrackOrder, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 57\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/search\",\n              element: /*#__PURE__*/_jsxDEV(Search, {\n                token: token\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/categories\",\n              element: /*#__PURE__*/_jsxDEV(Categories, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/category/:categoryName\",\n              element: /*#__PURE__*/_jsxDEV(Category, {\n                token: token\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 58\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/inventory\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                element: /*#__PURE__*/_jsxDEV(InventoryAssistant, {\n                  token: token\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 70\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/price-trends\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                element: /*#__PURE__*/_jsxDEV(PriceTrends, {\n                  token: token\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 73\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/support\",\n              element: /*#__PURE__*/_jsxDEV(SupportHub, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/support/faq\",\n              element: /*#__PURE__*/_jsxDEV(FAQ, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/support/contact\",\n              element: /*#__PURE__*/_jsxDEV(ContactUs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/support/returns\",\n              element: /*#__PURE__*/_jsxDEV(ReturnPolicy, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/support/tickets\",\n              element: /*#__PURE__*/_jsxDEV(SupportTickets, {\n                token: token\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/about\",\n              element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/sustainability\",\n              element: /*#__PURE__*/_jsxDEV(Sustainability, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/careers\",\n              element: /*#__PURE__*/_jsxDEV(Careers, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/press\",\n              element: /*#__PURE__*/_jsxDEV(Press, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/shipping-info\",\n              element: /*#__PURE__*/_jsxDEV(ShippingInfo, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/company-community\",\n              element: /*#__PURE__*/_jsxDEV(CommunityPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/sell\",\n              element: /*#__PURE__*/_jsxDEV(Sell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/login\",\n              element: /*#__PURE__*/_jsxDEV(SellerLogin, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(SellerDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/products\",\n              element: /*#__PURE__*/_jsxDEV(SellerProducts, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/products/add\",\n              element: /*#__PURE__*/_jsxDEV(SellerProductForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 55\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/products/:productId/edit\",\n              element: /*#__PURE__*/_jsxDEV(SellerProductForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 67\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/orders\",\n              element: /*#__PURE__*/_jsxDEV(SellerOrders, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/store\",\n              element: /*#__PURE__*/_jsxDEV(SellerStoreProfile, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/store/:storeSlug\",\n              element: /*#__PURE__*/_jsxDEV(SellerStorePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/earnings\",\n              element: /*#__PURE__*/_jsxDEV(SellerEarnings, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/seller/analytics\",\n              element: /*#__PURE__*/_jsxDEV(SellerAnalytics, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/sellers\",\n              element: /*#__PURE__*/_jsxDEV(AdminSellerManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/signup\",\n              element: /*#__PURE__*/_jsxDEV(AuthPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(AuthPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/privacy\",\n              element: /*#__PURE__*/_jsxDEV(PrivacyPolicy, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/terms\",\n              element: /*#__PURE__*/_jsxDEV(TermsOfService, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cookies\",\n              element: /*#__PURE__*/_jsxDEV(CookiePolicy, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/image-test\",\n              element: /*#__PURE__*/_jsxDEV(ImageTest, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/test-post\",\n              element: /*#__PURE__*/_jsxDEV(TestPostCard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/login\",\n              element: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 81\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/products\",\n              element: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                element: /*#__PURE__*/_jsxDEV(AdminProducts, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 80\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/500\",\n              element: /*#__PURE__*/_jsxDEV(ServerError, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 39\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 36\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(ModernMinimalistFooter, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(GlobalErrorDisplay, {\n          position: \"top-right\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(GlobalLoadingIndicator, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 11\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 11\n  }, this);\n}\n\n// Main App component with providers\n_s(AppContent, \"61JSYAe9xA2Bs+EdwVvtc5Cd/Wg=\", false, function () {\n  return [useAuth, useSEO];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(HelmetProvider, {\n    children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n      children: /*#__PURE__*/_jsxDEV(ErrorProvider, {\n        children: /*#__PURE__*/_jsxDEV(LoadingProvider, {\n          children: /*#__PURE__*/_jsxDEV(ImageProvider, {\n            children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n              children: /*#__PURE__*/_jsxDEV(WebSocketProvider, {\n                children: /*#__PURE__*/_jsxDEV(CartProvider, {\n                  children: /*#__PURE__*/_jsxDEV(DialogProvider, {\n                    children: /*#__PURE__*/_jsxDEV(CookieProvider, {\n                      children: /*#__PURE__*/_jsxDEV(NotificationProvider, {\n                        children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 219,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Navigate", "useMemo", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CartProvider", "Dialog<PERSON><PERSON>", "<PERSON><PERSON>", "NotificationProvider", "WebSocketProvider", "LoadingProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ImageProvider", "Error<PERSON>ou<PERSON><PERSON>", "GlobalErrorDisplay", "GlobalLoadingIndicator", "useSEO", "ModernMinimalistNavbar", "ModernMinimalist<PERSON>ooter", "DynamicTitle", "Home", "ProductDetail", "Community", "Profile", "Account", "<PERSON><PERSON>", "Checkout", "CartRecovery", "Search", "Categories", "Category", "InventoryAssistant", "PriceTrends", "AuthPage", "PrivacyPolicy", "TermsOfService", "<PERSON>ie<PERSON><PERSON><PERSON>", "<PERSON>ieC<PERSON>entBanner", "CookiePreferenceCenter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OrderConfirmation", "TrackOrder", "SupportHub", "FAQ", "ContactUs", "ReturnPolicy", "SupportTickets", "<PERSON>ll", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerDashboard", "SellerProducts", "SellerProductForm", "SellerOrders", "SellerStoreProfile", "PublicStorePage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerAnalytics", "SellerStorePage", "AdminSellerManagement", "AdminLogin", "AdminDashboard", "AdminProducts", "NotFound", "ServerError", "ImageTest", "TestPostCard", "About", "Sustainability", "Careers", "Press", "ShippingInfo", "CommunityPage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "token", "user", "logout", "isAdmin", "currentUrl", "window", "location", "href", "title", "description", "keywords", "image", "url", "ProtectedRoute", "element", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "AdminProtectedRoute", "children", "className", "onLogout", "path", "position", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';\r\nimport { useMemo } from 'react';\r\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\r\nimport { SellerAuthProvider } from './contexts/SellerAuthContext';\r\nimport { CartProvider } from './contexts/CartContext';\r\nimport { DialogProvider } from './contexts/DialogContext';\r\nimport { CookieProvider } from './contexts/CookieContext';\r\nimport NotificationProvider from './contexts/NotificationContext';\r\nimport { WebSocketProvider } from './contexts/WebSocketContext';\r\nimport { LoadingProvider } from './contexts/LoadingContext';\r\nimport { ErrorProvider } from './contexts/ErrorContext';\r\nimport { ImageProvider } from './contexts/ImageContext';\r\nimport ErrorBoundary from './components/ErrorBoundary';\r\nimport { GlobalErrorDisplay } from './components/ErrorComponents';\r\nimport { GlobalLoadingIndicator } from './components/LoadingComponents';\r\n\r\n\r\n\r\nimport { useSEO } from './utils/seo';\r\nimport ModernMinimalistNavbar from './components/ModernMinimalistNavbar';\r\nimport ModernMinimalistFooter from './components/ModernMinimalistFooter';\r\nimport DynamicTitle from './components/DynamicTitle';\r\n\r\nimport Home from './components/SimpleHome';\r\nimport ProductDetail from './pages/ProductDetails';\r\nimport Community from './pages/Community';\r\nimport Profile from './pages/Profile';\r\nimport Account from './pages/Account';\r\nimport Cart from './pages/Cart';\r\nimport Checkout from './pages/Checkout';\r\nimport CartRecovery from './pages/CartRecovery';\r\nimport Search from './pages/Search';\r\nimport Categories from './pages/Categories';\r\nimport Category from './pages/Category';\r\nimport InventoryAssistant from './pages/InventoryAssistant';\r\nimport PriceTrends from './pages/PriceTrends';\r\nimport AuthPage from './components/Login';\r\nimport PrivacyPolicy from './components/PrivacyPolicy';\r\nimport TermsOfService from './components/TermsOfService';\r\nimport CookiePolicy from './components/CookiePolicy';\r\nimport CookieConsentBanner from './components/CookieConsentBanner';\r\nimport CookiePreferenceCenter from './components/CookiePreferenceCenter';\r\nimport GuestCheckout from './components/GuestCheckout';\r\nimport OrderConfirmation from './pages/OrderConfirmation';\r\nimport TrackOrder from './pages/TrackOrder';\r\nimport SupportHub from './pages/SupportHub';\r\nimport FAQ from './pages/FAQ';\r\nimport ContactUs from './pages/ContactUs';\r\nimport ReturnPolicy from './pages/ReturnPolicy';\r\nimport SupportTickets from './pages/SupportTickets';\r\nimport Sell from './pages/Sell';\r\nimport SellerLogin from './pages/SellerLogin';\r\nimport SellerDashboard from './pages/SellerDashboard';\r\nimport SellerProducts from './pages/SellerProducts';\r\nimport SellerProductForm from './pages/SellerProductForm';\r\nimport SellerOrders from './pages/SellerOrders';\r\nimport SellerStoreProfile from './pages/SellerStoreProfile';\r\nimport PublicStorePage from './pages/PublicStorePage';\r\nimport SellerEarnings from './pages/SellerEarnings';\r\nimport SellerAnalytics from './pages/SellerAnalytics';\r\nimport SellerStorePage from './pages/SellerStorePage';\r\nimport AdminSellerManagement from './pages/AdminSellerManagement';\r\n\r\nimport AdminLogin from './pages/AdminLogin';\r\nimport AdminDashboard from './pages/AdminDashboard';\r\nimport AdminProducts from './pages/AdminProducts';\r\nimport NotFound from './pages/NotFound';\r\nimport ServerError from './pages/ServerError';\r\nimport ImageTest from './pages/ImageTest';\r\nimport TestPostCard from './components/TestPostCard';\r\nimport About from './pages/About';\r\nimport Sustainability from './pages/Sustainability';\r\nimport Careers from './pages/Careers';\r\nimport Press from './pages/Press';\r\nimport ShippingInfo from './pages/ShippingInfo';\r\nimport CommunityPage from './pages/CommunityPage';\r\nimport './styles.css';\r\nimport './styles/responsive.css';\r\nimport 'tailwindcss/tailwind.css';\r\nimport { HelmetProvider } from 'react-helmet-async';\r\n\r\n// Inner App component that uses AuthContext\r\nfunction AppContent() {\r\n  const { token, user, logout, isAdmin } = useAuth();\r\n\r\n  // Set global SEO defaults - use useMemo to prevent infinite re-renders\r\n  const currentUrl = useMemo(() => window.location.href, []);\r\n\r\n  useSEO({\r\n    title: 'Allora - Sustainable Shopping Platform',\r\n    description: 'Discover eco-friendly products and join our sustainable shopping community. Shop responsibly with Allora.',\r\n    keywords: 'sustainable shopping, eco-friendly products, green marketplace, sustainable living',\r\n    image: '/images/og-image.jpg',\r\n    url: currentUrl\r\n  });\r\n\r\n  const ProtectedRoute = ({ element }) => {\r\n    return token ? element : <Navigate to=\"/login\" />;\r\n  };\r\n\r\n  const AdminProtectedRoute = ({ element }) => {\r\n    return isAdmin ? element : <Navigate to=\"/admin/login\" />;\r\n  };\r\n\r\n  return (\r\n          <Router>\r\n          <SellerAuthProvider>\r\n          <div className=\"min-h-screen bg-gray-50\">\r\n        {/* Dynamic Title Component */}\r\n        <DynamicTitle />\r\n\r\n        {/* Modern Minimalist Navigation Bar */}\r\n        <ModernMinimalistNavbar\r\n          token={token}\r\n          user={user}\r\n          onLogout={logout}\r\n        />\r\n\r\n        {/* Cookie Components */}\r\n        <CookieConsentBanner />\r\n        <CookiePreferenceCenter />\r\n\r\n        {/* Main Content */}\r\n        <div>\r\n        <Routes>\r\n          <Route path=\"/\" element={<Home token={token} />} />\r\n          <Route path=\"/product/:id\" element={<ProductDetail token={token} />} />\r\n          <Route path=\"/community\" element={<Community token={token} />} />\r\n          <Route path=\"/profile\" element={<ProtectedRoute element={<Profile token={token} />} />} />\r\n          <Route path=\"/account\" element={<ProtectedRoute element={<Account token={token} />} />} />\r\n          <Route path=\"/cart\" element={<Cart token={token} />} />\r\n          <Route path=\"/checkout\" element={<ProtectedRoute element={<Checkout token={token} />} />} />\r\n          <Route path=\"/guest-checkout\" element={<GuestCheckout />} />\r\n          <Route path=\"/cart-recovery/:recoveryToken\" element={<CartRecovery />} />\r\n          <Route path=\"/order-confirmation/:orderNumber\" element={<OrderConfirmation />} />\r\n          <Route path=\"/track\" element={<TrackOrder />} />\r\n          <Route path=\"/track/:trackingNumber\" element={<TrackOrder />} />\r\n          <Route path=\"/search\" element={<Search token={token} />} />\r\n          <Route path=\"/categories\" element={<Categories />} />\r\n          <Route path=\"/category/:categoryName\" element={<Category token={token} />} />\r\n          <Route path=\"/inventory\" element={<ProtectedRoute element={<InventoryAssistant token={token} />} />} />\r\n          <Route path=\"/price-trends\" element={<ProtectedRoute element={<PriceTrends token={token} />} />} />\r\n          <Route path=\"/support\" element={<SupportHub />} />\r\n          <Route path=\"/support/faq\" element={<FAQ />} />\r\n          <Route path=\"/support/contact\" element={<ContactUs />} />\r\n          <Route path=\"/support/returns\" element={<ReturnPolicy />} />\r\n          <Route path=\"/support/tickets\" element={<SupportTickets token={token} />} />\r\n          <Route path=\"/about\" element={<About />} />\r\n          <Route path=\"/sustainability\" element={<Sustainability />} />\r\n          <Route path=\"/careers\" element={<Careers />} />\r\n          <Route path=\"/press\" element={<Press />} />\r\n          <Route path=\"/shipping-info\" element={<ShippingInfo />} />\r\n          <Route path=\"/company-community\" element={<CommunityPage />} />\r\n          <Route path=\"/sell\" element={<Sell />} />\r\n          <Route path=\"/seller/login\" element={<SellerLogin />} />\r\n          <Route path=\"/seller/dashboard\" element={<SellerDashboard />} />\r\n          <Route path=\"/seller/products\" element={<SellerProducts />} />\r\n          <Route path=\"/seller/products/add\" element={<SellerProductForm />} />\r\n          <Route path=\"/seller/products/:productId/edit\" element={<SellerProductForm />} />\r\n          <Route path=\"/seller/orders\" element={<SellerOrders />} />\r\n          <Route path=\"/seller/store\" element={<SellerStoreProfile />} />\r\n          <Route path=\"/store/:storeSlug\" element={<SellerStorePage />} />\r\n          <Route path=\"/seller/earnings\" element={<SellerEarnings />} />\r\n          <Route path=\"/seller/analytics\" element={<SellerAnalytics />} />\r\n          <Route path=\"/admin/sellers\" element={<AdminSellerManagement />} />\r\n          <Route path=\"/signup\" element={<AuthPage />} />\r\n          <Route path=\"/login\" element={<AuthPage />} />\r\n\r\n          {/* Legal Pages */}\r\n          <Route path=\"/privacy\" element={<PrivacyPolicy />} />\r\n          <Route path=\"/terms\" element={<TermsOfService />} />\r\n          <Route path=\"/cookies\" element={<CookiePolicy />} />\r\n\r\n          {/* Test Routes */}\r\n          <Route path=\"/image-test\" element={<ImageTest />} />\r\n          <Route path=\"/test-post\" element={<TestPostCard />} />\r\n\r\n          {/* Admin Routes */}\r\n          <Route path=\"/admin/login\" element={<AdminLogin />} />\r\n          <Route path=\"/admin/dashboard\" element={<AdminProtectedRoute element={<AdminDashboard />} />} />\r\n          <Route path=\"/admin/products\" element={<AdminProtectedRoute element={<AdminProducts />} />} />\r\n\r\n          {/* Error Routes */}\r\n          <Route path=\"/500\" element={<ServerError />} />\r\n          <Route path=\"*\" element={<NotFound />} />\r\n        </Routes>\r\n\r\n        </div>\r\n\r\n\r\n\r\n        {/* Modern Minimalist Footer */}\r\n        <ModernMinimalistFooter />\r\n\r\n        {/* Global Components */}\r\n        <GlobalErrorDisplay position=\"top-right\" />\r\n        <GlobalLoadingIndicator />\r\n\r\n        </div>\r\n          </SellerAuthProvider>\r\n          </Router>\r\n  );\r\n}\r\n\r\n// Main App component with providers\r\nfunction App() {\r\n  return (\r\n    <HelmetProvider>\r\n      <ErrorBoundary>\r\n        <ErrorProvider>\r\n          <LoadingProvider>\r\n            <ImageProvider>\r\n              <AuthProvider>\r\n                <WebSocketProvider>\r\n                  <CartProvider>\r\n                    <DialogProvider>\r\n                      <CookieProvider>\r\n                        <NotificationProvider>\r\n                          <AppContent />\r\n                        </NotificationProvider>\r\n                      </CookieProvider>\r\n                    </DialogProvider>\r\n                  </CartProvider>\r\n                </WebSocketProvider>\r\n              </AuthProvider>\r\n            </ImageProvider>\r\n          </LoadingProvider>\r\n        </ErrorProvider>\r\n      </ErrorBoundary>\r\n    </HelmetProvider>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,sBAAsB,QAAQ,gCAAgC;AAIvE,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAOC,YAAY,MAAM,2BAA2B;AAEpD,OAAOC,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,qBAAqB,MAAM,+BAA+B;AAEjE,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAO,cAAc;AACrB,OAAO,yBAAyB;AAChC,OAAO,0BAA0B;AACjC,SAASC,cAAc,QAAQ,oBAAoB;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAQ,CAAC,GAAG7E,OAAO,CAAC,CAAC;;EAElD;EACA,MAAM8E,UAAU,GAAGhF,OAAO,CAAC,MAAMiF,MAAM,CAACC,QAAQ,CAACC,IAAI,EAAE,EAAE,CAAC;EAE1DpE,MAAM,CAAC;IACLqE,KAAK,EAAE,wCAAwC;IAC/CC,WAAW,EAAE,2GAA2G;IACxHC,QAAQ,EAAE,oFAAoF;IAC9FC,KAAK,EAAE,sBAAsB;IAC7BC,GAAG,EAAER;EACP,CAAC,CAAC;EAEF,MAAMS,cAAc,GAAGA,CAAC;IAAEC;EAAQ,CAAC,KAAK;IACtC,OAAOd,KAAK,GAAGc,OAAO,gBAAGjB,OAAA,CAAC1E,QAAQ;MAAC4F,EAAE,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnD,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAC;IAAEN;EAAQ,CAAC,KAAK;IAC3C,OAAOX,OAAO,GAAGW,OAAO,gBAAGjB,OAAA,CAAC1E,QAAQ;MAAC4F,EAAE,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3D,CAAC;EAED,oBACQtB,OAAA,CAAC7E,MAAM;IAAAqG,QAAA,eACPxB,OAAA,CAACtE,kBAAkB;MAAA8F,QAAA,eACnBxB,OAAA;QAAKyB,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBAE1CxB,OAAA,CAACvD,YAAY;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGhBtB,OAAA,CAACzD,sBAAsB;UACrB4D,KAAK,EAAEA,KAAM;UACbC,IAAI,EAAEA,IAAK;UACXsB,QAAQ,EAAErB;QAAO;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAGFtB,OAAA,CAACrC,mBAAmB;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvBtB,OAAA,CAACpC,sBAAsB;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BtB,OAAA;UAAAwB,QAAA,eACAxB,OAAA,CAAC3E,MAAM;YAAAmG,QAAA,gBACLxB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,GAAG;cAACV,OAAO,eAAEjB,OAAA,CAACtD,IAAI;gBAACyD,KAAK,EAAEA;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,cAAc;cAACV,OAAO,eAAEjB,OAAA,CAACrD,aAAa;gBAACwD,KAAK,EAAEA;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,YAAY;cAACV,OAAO,eAAEjB,OAAA,CAACpD,SAAS;gBAACuD,KAAK,EAAEA;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,UAAU;cAACV,OAAO,eAAEjB,OAAA,CAACgB,cAAc;gBAACC,OAAO,eAAEjB,OAAA,CAACnD,OAAO;kBAACsD,KAAK,EAAEA;gBAAM;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,UAAU;cAACV,OAAO,eAAEjB,OAAA,CAACgB,cAAc;gBAACC,OAAO,eAAEjB,OAAA,CAAClD,OAAO;kBAACqD,KAAK,EAAEA;gBAAM;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1FtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,OAAO;cAACV,OAAO,eAAEjB,OAAA,CAACjD,IAAI;gBAACoD,KAAK,EAAEA;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,WAAW;cAACV,OAAO,eAAEjB,OAAA,CAACgB,cAAc;gBAACC,OAAO,eAAEjB,OAAA,CAAChD,QAAQ;kBAACmD,KAAK,EAAEA;gBAAM;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5FtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,iBAAiB;cAACV,OAAO,eAAEjB,OAAA,CAACnC,aAAa;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,+BAA+B;cAACV,OAAO,eAAEjB,OAAA,CAAC/C,YAAY;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,kCAAkC;cAACV,OAAO,eAAEjB,OAAA,CAAClC,iBAAiB;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjFtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,QAAQ;cAACV,OAAO,eAAEjB,OAAA,CAACjC,UAAU;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,wBAAwB;cAACV,OAAO,eAAEjB,OAAA,CAACjC,UAAU;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,SAAS;cAACV,OAAO,eAAEjB,OAAA,CAAC9C,MAAM;gBAACiD,KAAK,EAAEA;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,aAAa;cAACV,OAAO,eAAEjB,OAAA,CAAC7C,UAAU;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,yBAAyB;cAACV,OAAO,eAAEjB,OAAA,CAAC5C,QAAQ;gBAAC+C,KAAK,EAAEA;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7EtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,YAAY;cAACV,OAAO,eAAEjB,OAAA,CAACgB,cAAc;gBAACC,OAAO,eAAEjB,OAAA,CAAC3C,kBAAkB;kBAAC8C,KAAK,EAAEA;gBAAM;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvGtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,eAAe;cAACV,OAAO,eAAEjB,OAAA,CAACgB,cAAc;gBAACC,OAAO,eAAEjB,OAAA,CAAC1C,WAAW;kBAAC6C,KAAK,EAAEA;gBAAM;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnGtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,UAAU;cAACV,OAAO,eAAEjB,OAAA,CAAChC,UAAU;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,cAAc;cAACV,OAAO,eAAEjB,OAAA,CAAC/B,GAAG;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,kBAAkB;cAACV,OAAO,eAAEjB,OAAA,CAAC9B,SAAS;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,kBAAkB;cAACV,OAAO,eAAEjB,OAAA,CAAC7B,YAAY;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,kBAAkB;cAACV,OAAO,eAAEjB,OAAA,CAAC5B,cAAc;gBAAC+B,KAAK,EAAEA;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5EtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,QAAQ;cAACV,OAAO,eAAEjB,OAAA,CAACR,KAAK;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,iBAAiB;cAACV,OAAO,eAAEjB,OAAA,CAACP,cAAc;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,UAAU;cAACV,OAAO,eAAEjB,OAAA,CAACN,OAAO;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,QAAQ;cAACV,OAAO,eAAEjB,OAAA,CAACL,KAAK;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,gBAAgB;cAACV,OAAO,eAAEjB,OAAA,CAACJ,YAAY;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,oBAAoB;cAACV,OAAO,eAAEjB,OAAA,CAACH,aAAa;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,OAAO;cAACV,OAAO,eAAEjB,OAAA,CAAC3B,IAAI;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,eAAe;cAACV,OAAO,eAAEjB,OAAA,CAAC1B,WAAW;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,mBAAmB;cAACV,OAAO,eAAEjB,OAAA,CAACzB,eAAe;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,kBAAkB;cAACV,OAAO,eAAEjB,OAAA,CAACxB,cAAc;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,sBAAsB;cAACV,OAAO,eAAEjB,OAAA,CAACvB,iBAAiB;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,kCAAkC;cAACV,OAAO,eAAEjB,OAAA,CAACvB,iBAAiB;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjFtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,gBAAgB;cAACV,OAAO,eAAEjB,OAAA,CAACtB,YAAY;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,eAAe;cAACV,OAAO,eAAEjB,OAAA,CAACrB,kBAAkB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,mBAAmB;cAACV,OAAO,eAAEjB,OAAA,CAACjB,eAAe;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,kBAAkB;cAACV,OAAO,eAAEjB,OAAA,CAACnB,cAAc;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,mBAAmB;cAACV,OAAO,eAAEjB,OAAA,CAAClB,eAAe;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,gBAAgB;cAACV,OAAO,eAAEjB,OAAA,CAAChB,qBAAqB;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnEtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,SAAS;cAACV,OAAO,eAAEjB,OAAA,CAACzC,QAAQ;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,QAAQ;cAACV,OAAO,eAAEjB,OAAA,CAACzC,QAAQ;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG9CtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,UAAU;cAACV,OAAO,eAAEjB,OAAA,CAACxC,aAAa;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,QAAQ;cAACV,OAAO,eAAEjB,OAAA,CAACvC,cAAc;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,UAAU;cAACV,OAAO,eAAEjB,OAAA,CAACtC,YAAY;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGpDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,aAAa;cAACV,OAAO,eAAEjB,OAAA,CAACV,SAAS;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,YAAY;cAACV,OAAO,eAAEjB,OAAA,CAACT,YAAY;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGtDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,cAAc;cAACV,OAAO,eAAEjB,OAAA,CAACf,UAAU;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,kBAAkB;cAACV,OAAO,eAAEjB,OAAA,CAACuB,mBAAmB;gBAACN,OAAO,eAAEjB,OAAA,CAACd,cAAc;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChGtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,iBAAiB;cAACV,OAAO,eAAEjB,OAAA,CAACuB,mBAAmB;gBAACN,OAAO,eAAEjB,OAAA,CAACb,aAAa;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG9FtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,MAAM;cAACV,OAAO,eAAEjB,OAAA,CAACX,WAAW;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CtB,OAAA,CAAC5E,KAAK;cAACuG,IAAI,EAAC,GAAG;cAACV,OAAO,eAAEjB,OAAA,CAACZ,QAAQ;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEJ,CAAC,eAKNtB,OAAA,CAACxD,sBAAsB;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BtB,OAAA,CAAC5D,kBAAkB;UAACwF,QAAQ,EAAC;QAAW;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CtB,OAAA,CAAC3D,sBAAsB;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAErB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACgB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEnB;;AAEA;AAAApB,EAAA,CA1HSD,UAAU;EAAA,QACwBxE,OAAO,EAKhDa,MAAM;AAAA;AAAAuF,EAAA,GANC5B,UAAU;AA2HnB,SAAS6B,GAAGA,CAAA,EAAG;EACb,oBACE9B,OAAA,CAACF,cAAc;IAAA0B,QAAA,eACbxB,OAAA,CAAC7D,aAAa;MAAAqF,QAAA,eACZxB,OAAA,CAAC/D,aAAa;QAAAuF,QAAA,eACZxB,OAAA,CAAChE,eAAe;UAAAwF,QAAA,eACdxB,OAAA,CAAC9D,aAAa;YAAAsF,QAAA,eACZxB,OAAA,CAACxE,YAAY;cAAAgG,QAAA,eACXxB,OAAA,CAACjE,iBAAiB;gBAAAyF,QAAA,eAChBxB,OAAA,CAACrE,YAAY;kBAAA6F,QAAA,eACXxB,OAAA,CAACpE,cAAc;oBAAA4F,QAAA,eACbxB,OAAA,CAACnE,cAAc;sBAAA2F,QAAA,eACbxB,OAAA,CAAClE,oBAAoB;wBAAA0F,QAAA,eACnBxB,OAAA,CAACC,UAAU;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAErB;AAACS,GAAA,GA1BQD,GAAG;AA4BZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}