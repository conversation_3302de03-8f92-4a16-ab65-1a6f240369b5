.admin-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h1 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
}

.dashboard-header p {
    color: #7f8c8d;
    margin: 0;
    font-size: 16px;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    flex-shrink: 0;
}

.stat-icon.products {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.orders {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.users {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.revenue {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.dashboard-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: 1px solid #e9ecef;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.section-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
}

.view-all-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background 0.3s ease;
}

.view-all-btn:hover {
    background: #2980b9;
}

/* Orders Table */
.orders-table table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th,
.orders-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.orders-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.orders-table td {
    font-size: 14px;
    color: #6c757d;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.confirmed {
    background: #d4edda;
    color: #155724;
}

.status-badge.shipped {
    background: #cce5ff;
    color: #004085;
}

.status-badge.delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Low Stock List */
.low-stock-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.low-stock-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
}

.product-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.product-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 14px;
}

.stock-level {
    width: 100px;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.stock-bar {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c 0%, #f39c12 50%, #27ae60 100%);
    transition: width 0.3s ease;
}

/* Loading and Error States */
.admin-loading,
.admin-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.retry-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 15px;
    transition: background 0.3s ease;
}

.retry-btn:hover {
    background: #2980b9;
}

.no-data {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 40px 20px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .stat-card {
        padding: 20px;
        gap: 15px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .stat-content h3 {
        font-size: 24px;
    }
    
    .dashboard-section {
        padding: 20px;
    }
    
    .orders-table {
        overflow-x: auto;
    }
    
    .low-stock-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .stock-level {
        width: 100%;
    }
}

/* Fulfillment Section Styles */
.fulfillment-section {
    grid-column: 1 / -1;
    margin-top: 20px;
}

.fulfillment-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.fulfillment-metric {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.fulfillment-metric h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.carrier-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.carrier-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.carrier-name {
    font-weight: 500;
    color: #495057;
}

.carrier-performance {
    font-weight: 600;
    color: #28a745;
}

.activity-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-label {
    color: #6c757d;
    font-size: 14px;
}

.activity-value {
    font-weight: 600;
    color: #2c3e50;
}

.fulfillment-alerts {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
}

.fulfillment-alerts h4 {
    margin: 0 0 15px 0;
    color: #856404;
    font-size: 16px;
    font-weight: 600;
}

.alert-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.alert-message {
    color: #856404;
    font-size: 14px;
}

.alert-action-btn {
    background: #ffc107;
    color: #212529;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: background 0.3s ease;
}

.alert-action-btn:hover {
    background: #e0a800;
}

.fulfillment-loading {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

/* Additional stat card styles for fulfillment */
.stat-icon.fulfillment {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.delivery {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.performance {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.exceptions {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

@media (max-width: 768px) {
    .fulfillment-overview {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .fulfillment-metric {
        padding: 15px;
    }

    .alert-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .alert-action-btn {
        align-self: flex-end;
    }
}
