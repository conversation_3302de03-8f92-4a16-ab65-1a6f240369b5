# Elasticsearch Setup Guide for Allora

This guide explains how to set up and configure Elasticsearch for advanced search functionality in the Allora e-commerce platform.

## Prerequisites

- Docker and Docker Compose (recommended)
- Python 3.8+
- At least 4GB RAM available for Elasticsearch

## Quick Start with Docker

### 1. Start Elasticsearch and Related Services

```bash
# Navigate to the project root
cd allora

# Start Elasticsearch, Kibana, and <PERSON><PERSON>
docker-compose -f docker-compose.elasticsearch.yml up -d

# Check if services are running
docker-compose -f docker-compose.elasticsearch.yml ps
```

### 2. Verify Elasticsearch is Running

```bash
# Check Elasticsearch health
curl http://localhost:9200/_cluster/health

# Expected response should show status: "green" or "yellow"
```

### 3. Install Python Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 4. Set up Environment Variables

```bash
# Copy the example environment file
cp .env.example .env

# Edit .env file with your Elasticsearch configuration
# The default values should work with Docker setup
```

### 5. Initialize Elasticsearch Indices

```bash
# Run the setup script
python setup_elasticsearch.py

# Or with options:
python setup_elasticsearch.py --help
```

## Manual Installation (Without Docker)

### 1. Install Elasticsearch

#### On Ubuntu/Debian:
```bash
# Import Elasticsearch GPG key
wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add -

# Add Elasticsearch repository
echo "deb https://artifacts.elastic.co/packages/8.x/apt stable main" | sudo tee /etc/apt/sources.list.d/elastic-8.x.list

# Install Elasticsearch
sudo apt update
sudo apt install elasticsearch
```

#### On macOS:
```bash
# Using Homebrew
brew tap elastic/tap
brew install elastic/tap/elasticsearch-full
```

#### On Windows:
Download and install from: https://www.elastic.co/downloads/elasticsearch

### 2. Configure Elasticsearch

Edit the Elasticsearch configuration file:

```bash
# Linux/macOS
sudo nano /etc/elasticsearch/elasticsearch.yml

# Add these configurations:
cluster.name: allora-cluster
node.name: allora-es-node
network.host: localhost
http.port: 9200
discovery.type: single-node
xpack.security.enabled: false
```

### 3. Start Elasticsearch Service

```bash
# Linux (systemd)
sudo systemctl start elasticsearch
sudo systemctl enable elasticsearch

# macOS
brew services start elasticsearch-full

# Windows
# Start from the installation directory
bin\elasticsearch.bat
```

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
# Elasticsearch Configuration
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_SCHEME=http
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# Search Configuration
SEARCH_RESULTS_PER_PAGE=20
MAX_SEARCH_RESULTS=1000
SEARCH_TIMEOUT_SECONDS=30

# Analytics Configuration
ENABLE_SEARCH_ANALYTICS=True
ANALYTICS_RETENTION_DAYS=90
```

### Index Configuration

The setup script creates three main indices:

1. **allora_products** - Product search index
2. **allora_search_analytics** - Search analytics and metrics
3. **allora_suggestions** - Autocomplete and search suggestions

## Setup Script Options

```bash
# Basic setup (creates indices and syncs data)
python setup_elasticsearch.py

# Reset everything (deletes and recreates indices)
python setup_elasticsearch.py --reset

# Only verify setup without changes
python setup_elasticsearch.py --verify-only

# Setup indices without data sync
python setup_elasticsearch.py --no-sync
```

## Verification

### 1. Check Elasticsearch Status

```bash
# Cluster health
curl http://localhost:9200/_cluster/health

# Node information
curl http://localhost:9200/_nodes

# Index statistics
curl http://localhost:9200/_cat/indices?v
```

### 2. Test Search Functionality

```bash
# Search all products
curl "http://localhost:9200/allora_products/_search?pretty"

# Search for specific term
curl -X GET "http://localhost:9200/allora_products/_search?pretty" -H 'Content-Type: application/json' -d'
{
  "query": {
    "match": {
      "name": "eco"
    }
  }
}'
```

### 3. Access Kibana (if using Docker)

Open http://localhost:5601 in your browser to access Kibana for data visualization and index management.

## Troubleshooting

### Common Issues

#### 1. Elasticsearch won't start
```bash
# Check logs
docker logs allora-elasticsearch

# Or for manual installation
sudo journalctl -u elasticsearch
```

#### 2. Out of memory errors
```bash
# Increase heap size in docker-compose.elasticsearch.yml
ES_JAVA_OPTS: "-Xms2g -Xmx2g"
```

#### 3. Connection refused
```bash
# Check if Elasticsearch is running
curl http://localhost:9200

# Check firewall settings
sudo ufw allow 9200
```

#### 4. Index creation fails
```bash
# Check Elasticsearch logs for detailed error messages
# Verify disk space availability
df -h
```

### Performance Tuning

#### 1. Memory Settings
```yaml
# In docker-compose.elasticsearch.yml
environment:
  - "ES_JAVA_OPTS=-Xms2g -Xmx2g"  # Adjust based on available RAM
```

#### 2. Index Settings
```bash
# Optimize for search performance
curl -X PUT "localhost:9200/allora_products/_settings" -H 'Content-Type: application/json' -d'
{
  "index": {
    "refresh_interval": "30s",
    "number_of_replicas": 0
  }
}'
```

## Monitoring

### 1. Health Checks

```bash
# Cluster health
curl http://localhost:9200/_cluster/health?pretty

# Index health
curl http://localhost:9200/_cat/indices?v&health=yellow
```

### 2. Performance Metrics

```bash
# Node stats
curl http://localhost:9200/_nodes/stats?pretty

# Index stats
curl http://localhost:9200/allora_products/_stats?pretty
```

## Backup and Recovery

### 1. Create Snapshot Repository

```bash
curl -X PUT "localhost:9200/_snapshot/allora_backup" -H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/usr/share/elasticsearch/backups"
  }
}'
```

### 2. Create Snapshot

```bash
curl -X PUT "localhost:9200/_snapshot/allora_backup/snapshot_1?wait_for_completion=true"
```

### 3. Restore Snapshot

```bash
curl -X POST "localhost:9200/_snapshot/allora_backup/snapshot_1/_restore"
```

## Next Steps

After successful setup:

1. Start your Flask application
2. Test search endpoints at `/api/search`
3. Monitor search analytics at `/api/admin/search-analytics`
4. Configure search suggestions and autocomplete
5. Set up monitoring and alerting

## Support

For issues and questions:
- Check Elasticsearch logs for detailed error messages
- Verify network connectivity and firewall settings
- Ensure sufficient system resources (RAM, disk space)
- Consult Elasticsearch documentation: https://www.elastic.co/guide/
