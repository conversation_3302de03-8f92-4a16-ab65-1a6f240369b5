import React from 'react';
import { useSEO } from '../utils/seo';
import { ErrorBoundaryFallback } from './ErrorComponents';
import './ErrorBoundary.css';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    this.setState({
      error,
      errorInfo
    });

    // Log to error reporting service
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error, errorInfo) => {
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: localStorage.getItem('userId') || 'anonymous',
      errorId: this.state.errorId
    };

    // Send to backend error logging endpoint
    try {
      fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData)
      }).catch(err => {
        console.error('Failed to log error to service:', err);
      });
    } catch (err) {
      console.error('Failed to send error log:', err);
    }

    // Log to console for development
    console.error('Error Boundary caught an error:', error, errorInfo);
  };

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    });
  };

  handleReportError = () => {
    const subject = encodeURIComponent(`Error Report - ${this.state.errorId}`);
    const body = encodeURIComponent(`
Error ID: ${this.state.errorId}
URL: ${window.location.href}
Error: ${this.state.error?.message}
Time: ${new Date().toISOString()}

Please describe what you were doing when this error occurred:
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided, otherwise use the new ErrorBoundaryFallback
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorBoundaryFallback
          error={this.state.error}
          resetError={this.handleRetry}
          componentStack={this.state.errorInfo?.componentStack}
        />
      );
    }

    return this.props.children;
  }
}

const ErrorFallback = ({ error, errorInfo, errorId, onRetry, onReport, fallback }) => {
  // Set SEO for error page
  useSEO({
    title: 'Something went wrong',
    description: 'An unexpected error occurred. Please try again or contact support.',
    robots: 'noindex, nofollow'
  });

  if (fallback) {
    return fallback;
  }

  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <div className="error-boundary">
      <div className="error-container">
        <div className="error-icon">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="#ef4444" strokeWidth="2"/>
            <line x1="15" y1="9" x2="9" y2="15" stroke="#ef4444" strokeWidth="2"/>
            <line x1="9" y1="9" x2="15" y2="15" stroke="#ef4444" strokeWidth="2"/>
          </svg>
        </div>
        
        <h1 className="error-title">Oops! Something went wrong</h1>
        
        <p className="error-description">
          We're sorry, but something unexpected happened. Our team has been notified and is working to fix this issue.
        </p>

        <div className="error-actions">
          <button 
            onClick={onRetry}
            className="retry-button"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4v6h6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Try Again
          </button>
          
          <button 
            onClick={() => window.location.href = '/'}
            className="home-button"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Go Home
          </button>
          
          <button 
            onClick={onReport}
            className="report-button"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Report Issue
          </button>
        </div>

        {errorId && (
          <div className="error-id">
            <p>Error ID: <code>{errorId}</code></p>
            <p className="error-id-help">
              Please include this ID when contacting support
            </p>
          </div>
        )}

        {isDevelopment && error && (
          <details className="error-details">
            <summary>Technical Details (Development Only)</summary>
            <div className="error-stack">
              <h3>Error Message:</h3>
              <pre>{error.message}</pre>
              
              <h3>Stack Trace:</h3>
              <pre>{error.stack}</pre>
              
              {errorInfo && (
                <>
                  <h3>Component Stack:</h3>
                  <pre>{errorInfo.componentStack}</pre>
                </>
              )}
            </div>
          </details>
        )}
      </div>
    </div>
  );
};

// Higher-order component for wrapping components with error boundaries
export const withErrorBoundary = (Component, fallback) => {
  return function WrappedComponent(props) {
    return (
      <ErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
};

// Hook for error reporting
export const useErrorHandler = () => {
  const reportError = (error, context = {}) => {
    const errorData = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: localStorage.getItem('userId') || 'anonymous'
    };

    try {
      fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData)
      }).catch(err => {
        console.error('Failed to report error:', err);
      });
    } catch (err) {
      console.error('Failed to send error report:', err);
    }
  };

  return { reportError };
};

// Error Display Component for API errors
const ErrorDisplay = ({ error, onRetry, showRetry = true }) => {
  const isRateLimit = error?.includes('Rate limit') || error?.includes('429');
  const isNetworkError = error?.includes('fetch') || error?.includes('Network');

  return (
    <div className="flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200">
      <div className="text-center max-w-md">
        {/* Error Icon */}
        <div className="mb-4">
          {isRateLimit ? (
            <div className="w-16 h-16 mx-auto bg-yellow-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          ) : isNetworkError ? (
            <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
              </svg>
            </div>
          ) : (
            <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          )}
        </div>

        {/* Error Title */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {isRateLimit ? 'Too Many Requests' : isNetworkError ? 'Connection Issue' : 'Something Went Wrong'}
        </h3>

        {/* Error Message */}
        <p className="text-gray-600 mb-6">
          {isRateLimit
            ? 'We\'re receiving a lot of requests right now. Please wait a moment and try again.'
            : isNetworkError
            ? 'Unable to connect to our servers. Please check your internet connection.'
            : error || 'An unexpected error occurred. Please try again.'
          }
        </p>

        {/* Action Buttons */}
        {showRetry && (
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {onRetry && (
              <button
                onClick={onRetry}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium"
              >
                {isRateLimit ? 'Try Again' : 'Retry'}
              </button>
            )}
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors duration-200 font-medium"
            >
              Refresh Page
            </button>
          </div>
        )}

        {/* Additional Help */}
        {isRateLimit && (
          <div className="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <p className="text-sm text-yellow-800">
              💡 <strong>Tip:</strong> Try refreshing the page in a few seconds, or browse our cached products below.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

const LoadingSpinner = ({ message = 'Loading...' }) => (
  <div className="flex flex-col items-center justify-center p-8">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mb-4"></div>
    <p className="text-gray-600 font-medium">{message}</p>
  </div>
);

export { ErrorDisplay, LoadingSpinner };
export default ErrorBoundary;
