import React, { useState, useEffect, useRef } from 'react';
import { API_BASE_URL } from '../config/api';
import SearchSuggestions from './SearchSuggestions';
import SearchFilters from './SearchFilters';
import { useDebounce } from '../hooks/useDebounce';

const AdvancedSearch = ({ 
  onSearchResults, 
  initialQuery = '', 
  initialFilters = {},
  showFilters = true,
  showComplexSearch = false 
}) => {
  const [query, setQuery] = useState(initialQuery);
  const [filters, setFilters] = useState(initialFilters);
  const [searchMode, setSearchMode] = useState('simple'); // 'simple', 'advanced', 'complex'
  const [complexTerms, setComplexTerms] = useState([
    { query: '', operator: 'must', fields: ['name', 'description'], boost: 1.0 }
  ]);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchHistory, setSearchHistory] = useState([]);
  const [savedSearches, setSavedSearches] = useState([]);
  
  const searchInputRef = useRef(null);
  const debouncedQuery = useDebounce(query, 300);

  // Load search history and saved searches on mount
  useEffect(() => {
    loadSearchHistory();
    loadSavedSearches();
  }, []);

  // Fetch autocomplete suggestions
  useEffect(() => {
    if (debouncedQuery && debouncedQuery.length >= 2 && searchMode === 'simple') {
      fetchAutocompleteSuggestions(debouncedQuery);
    } else {
      setSuggestions([]);
    }
  }, [debouncedQuery, searchMode]);

  const loadSearchHistory = () => {
    try {
      const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
      setSearchHistory(history.slice(0, 10)); // Keep only last 10 searches
    } catch (error) {
      console.error('Failed to load search history:', error);
    }
  };

  const loadSavedSearches = () => {
    try {
      const saved = JSON.parse(localStorage.getItem('savedSearches') || '[]');
      setSavedSearches(saved);
    } catch (error) {
      console.error('Failed to load saved searches:', error);
    }
  };

  const saveToHistory = (searchQuery, searchFilters) => {
    try {
      const historyItem = {
        query: searchQuery,
        filters: searchFilters,
        timestamp: new Date().toISOString()
      };
      
      const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
      const updatedHistory = [historyItem, ...history.filter(item => 
        item.query !== searchQuery || JSON.stringify(item.filters) !== JSON.stringify(searchFilters)
      )].slice(0, 10);
      
      localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
      setSearchHistory(updatedHistory);
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  };

  const fetchAutocompleteSuggestions = async (searchQuery) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/search/autocomplete?q=${encodeURIComponent(searchQuery)}&limit=8`
      );
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
        setShowSuggestions(true);
      }
    } catch (error) {
      console.error('Failed to fetch autocomplete suggestions:', error);
    }
  };

  const performSearch = async (searchQuery = query, searchFilters = filters, searchTerms = null) => {
    setLoading(true);
    setShowSuggestions(false);
    
    try {
      let response;
      
      if (searchMode === 'complex' && searchTerms) {
        // Complex boolean search
        response = await fetch(`${API_BASE_URL}/search/complex`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            search_terms: searchTerms,
            filters: searchFilters,
            sort_by: searchFilters.sort_by || 'relevance',
            sort_order: searchFilters.sort_order || 'desc',
            page: 1,
            per_page: 20
          })
        });
      } else {
        // Simple or advanced search
        const params = new URLSearchParams({
          q: searchQuery,
          page: '1',
          per_page: '20',
          sort_by: searchFilters.sort_by || 'relevance',
          sort_order: searchFilters.sort_order || 'desc'
        });

        // Add filters to params
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value) && value.length > 0) {
              params.append(key, value.join(','));
            } else if (!Array.isArray(value)) {
              params.append(key, value.toString());
            }
          }
        });

        response = await fetch(`${API_BASE_URL}/search?${params}`);
      }

      if (response.ok) {
        const data = await response.json();
        onSearchResults(data.data || data);
        
        // Save to search history
        if (searchQuery.trim()) {
          saveToHistory(searchQuery, searchFilters);
        }
      } else {
        console.error('Search failed:', response.statusText);
        onSearchResults({ products: [], total: 0, error: 'Search failed' });
      }
    } catch (error) {
      console.error('Search error:', error);
      onSearchResults({ products: [], total: 0, error: 'Search failed' });
    } finally {
      setLoading(false);
    }
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchMode === 'complex') {
      const validTerms = complexTerms.filter(term => term.query.trim());
      if (validTerms.length > 0) {
        performSearch('', filters, validTerms);
      }
    } else {
      performSearch();
    }
  };

  const handleSuggestionSelect = (suggestion) => {
    setQuery(suggestion.text || suggestion);
    setShowSuggestions(false);
    performSearch(suggestion.text || suggestion, filters);
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    if (query.trim() || searchMode === 'complex') {
      if (searchMode === 'complex') {
        const validTerms = complexTerms.filter(term => term.query.trim());
        if (validTerms.length > 0) {
          performSearch('', newFilters, validTerms);
        }
      } else {
        performSearch(query, newFilters);
      }
    }
  };

  const addComplexTerm = () => {
    setComplexTerms([...complexTerms, {
      query: '',
      operator: 'must',
      fields: ['name', 'description'],
      boost: 1.0
    }]);
  };

  const removeComplexTerm = (index) => {
    if (complexTerms.length > 1) {
      setComplexTerms(complexTerms.filter((_, i) => i !== index));
    }
  };

  const updateComplexTerm = (index, field, value) => {
    const updatedTerms = [...complexTerms];
    updatedTerms[index] = { ...updatedTerms[index], [field]: value };
    setComplexTerms(updatedTerms);
  };

  const saveCurrentSearch = () => {
    const searchName = prompt('Enter a name for this search:');
    if (searchName) {
      try {
        const searchItem = {
          name: searchName,
          query,
          filters,
          complexTerms: searchMode === 'complex' ? complexTerms : null,
          mode: searchMode,
          timestamp: new Date().toISOString()
        };
        
        const saved = JSON.parse(localStorage.getItem('savedSearches') || '[]');
        const updatedSaved = [searchItem, ...saved].slice(0, 20); // Keep only 20 saved searches
        
        localStorage.setItem('savedSearches', JSON.stringify(updatedSaved));
        setSavedSearches(updatedSaved);
      } catch (error) {
        console.error('Failed to save search:', error);
      }
    }
  };

  const loadSavedSearch = (savedSearch) => {
    setQuery(savedSearch.query);
    setFilters(savedSearch.filters);
    setSearchMode(savedSearch.mode);
    if (savedSearch.complexTerms) {
      setComplexTerms(savedSearch.complexTerms);
    }
    
    if (savedSearch.mode === 'complex' && savedSearch.complexTerms) {
      performSearch('', savedSearch.filters, savedSearch.complexTerms);
    } else {
      performSearch(savedSearch.query, savedSearch.filters);
    }
  };

  return (
    <div className="advanced-search bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      {/* Search Mode Toggle */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex space-x-2">
          <button
            onClick={() => setSearchMode('simple')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              searchMode === 'simple'
                ? 'bg-green-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Simple Search
          </button>
          <button
            onClick={() => setSearchMode('advanced')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              searchMode === 'advanced'
                ? 'bg-green-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Advanced Search
          </button>
          {showComplexSearch && (
            <button
              onClick={() => setSearchMode('complex')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                searchMode === 'complex'
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Complex Boolean
            </button>
          )}
        </div>
        
        <div className="flex space-x-2">
          {(query.trim() || (searchMode === 'complex' && complexTerms.some(t => t.query.trim()))) && (
            <button
              onClick={saveCurrentSearch}
              className="px-3 py-1 text-sm text-green-600 hover:text-green-700 border border-green-600 rounded-md hover:bg-green-50 transition-colors"
            >
              Save Search
            </button>
          )}
        </div>
      </div>

      {/* Search Form */}
      <form onSubmit={handleSearchSubmit} className="space-y-4">
        {searchMode !== 'complex' ? (
          /* Simple/Advanced Search Input */
          <div className="relative">
            <div className="relative">
              <input
                ref={searchInputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onFocus={() => setShowSuggestions(suggestions.length > 0)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                placeholder="Search for products..."
                className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-lg"
              />
              <button
                type="submit"
                disabled={loading}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-green-600 transition-colors"
              >
                {loading ? (
                  <div className="animate-spin h-5 w-5 border-2 border-green-600 border-t-transparent rounded-full"></div>
                ) : (
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                )}
              </button>
            </div>
            
            {/* Autocomplete Suggestions */}
            {showSuggestions && suggestions.length > 0 && (
              <SearchSuggestions
                query={query}
                onSuggestionSelect={handleSuggestionSelect}
                onClose={() => setShowSuggestions(false)}
                isVisible={showSuggestions}
              />
            )}
          </div>
        ) : (
          /* Complex Boolean Search */
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Complex Boolean Search</h3>
            {complexTerms.map((term, index) => (
              <div key={index} className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
                <select
                  value={term.operator}
                  onChange={(e) => updateComplexTerm(index, 'operator', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="must">MUST</option>
                  <option value="should">SHOULD</option>
                  <option value="must_not">MUST NOT</option>
                </select>
                
                <input
                  type="text"
                  value={term.query}
                  onChange={(e) => updateComplexTerm(index, 'query', e.target.value)}
                  placeholder="Search term..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
                
                <select
                  value={term.fields.join(',')}
                  onChange={(e) => updateComplexTerm(index, 'fields', e.target.value.split(','))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="name,description">Name & Description</option>
                  <option value="name">Name Only</option>
                  <option value="description">Description Only</option>
                  <option value="category">Category</option>
                  <option value="brand">Brand</option>
                  <option value="search_keywords">Keywords</option>
                  <option value="material">Material</option>
                </select>
                
                <input
                  type="number"
                  value={term.boost}
                  onChange={(e) => updateComplexTerm(index, 'boost', parseFloat(e.target.value) || 1.0)}
                  min="0.1"
                  max="10"
                  step="0.1"
                  className="w-20 px-2 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  title="Boost factor"
                />
                
                {complexTerms.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeComplexTerm(index)}
                    className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            ))}
            
            <div className="flex justify-between">
              <button
                type="button"
                onClick={addComplexTerm}
                className="px-4 py-2 text-green-600 hover:text-green-700 border border-green-600 rounded-md hover:bg-green-50 transition-colors"
              >
                Add Search Term
              </button>
              
              <button
                type="submit"
                disabled={loading || !complexTerms.some(term => term.query.trim())}
                className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Searching...' : 'Search'}
              </button>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default AdvancedSearch;
