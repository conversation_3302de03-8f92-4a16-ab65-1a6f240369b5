import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '../components/AdminLayout';
import './AdminDashboard.css';

const AdminDashboard = () => {
    const [dashboardData, setDashboardData] = useState(null);
    const [fulfillmentData, setFulfillmentData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const navigate = useNavigate();

    useEffect(() => {
        fetchDashboardData();
        fetchFulfillmentData();
    }, []);

    const fetchDashboardData = async () => {
        try {
            const token = localStorage.getItem('adminToken');
            if (!token) {
                navigate('/admin/login');
                return;
            }

            const response = await fetch('http://localhost:5000/api/admin/dashboard', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (response.ok) {
                const data = await response.json();
                setDashboardData(data);
            } else if (response.status === 401) {
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminUser');
                navigate('/admin/login');
            } else {
                setError('Failed to load dashboard data');
            }
        } catch (error) {
            setError('Network error. Please try again.');
            console.error('Dashboard fetch error:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchFulfillmentData = async () => {
        try {
            const token = localStorage.getItem('adminToken');
            if (!token) return;

            const response = await fetch('http://localhost:5000/api/fulfillment/dashboard/summary', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (response.ok) {
                const data = await response.json();
                setFulfillmentData(data.summary);
            }
        } catch (error) {
            console.error('Fulfillment data fetch error:', error);
        }
    };

    if (loading) {
        return (
            <AdminLayout>
                <div className="admin-loading">
                    <div className="loading-spinner"></div>
                    <p>Loading dashboard...</p>
                </div>
            </AdminLayout>
        );
    }

    if (error) {
        return (
            <AdminLayout>
                <div className="admin-error">
                    <p>{error}</p>
                    <button onClick={fetchDashboardData} className="retry-btn">
                        Retry
                    </button>
                </div>
            </AdminLayout>
        );
    }

    const { statistics, recent_orders, low_stock_products } = dashboardData || {};

    return (
        <AdminLayout>
            <div className="admin-dashboard">
                <div className="dashboard-header">
                    <h1>Dashboard Overview</h1>
                    <p>Welcome to the Allora Admin Panel</p>
                </div>

                {/* Statistics Cards */}
                <div className="stats-grid">
                    <div className="stat-card">
                        <div className="stat-icon products">📦</div>
                        <div className="stat-content">
                            <h3>{statistics?.total_products || 0}</h3>
                            <p>Total Products</p>
                        </div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="stat-icon orders">🛒</div>
                        <div className="stat-content">
                            <h3>{statistics?.total_orders || 0}</h3>
                            <p>Total Orders</p>
                        </div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="stat-icon users">👥</div>
                        <div className="stat-content">
                            <h3>{statistics?.total_users || 0}</h3>
                            <p>Total Users</p>
                        </div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="stat-icon revenue">💰</div>
                        <div className="stat-content">
                            <h3>₹{statistics?.total_revenue?.toLocaleString() || 0}</h3>
                            <p>Revenue (30 days)</p>
                        </div>
                    </div>

                    {/* Fulfillment Statistics */}
                    <div className="stat-card">
                        <div className="stat-icon fulfillment">🚚</div>
                        <div className="stat-content">
                            <h3>{fulfillmentData?.fulfillment_metrics?.total_shipments || 0}</h3>
                            <p>Active Shipments</p>
                        </div>
                    </div>

                    <div className="stat-card">
                        <div className="stat-icon delivery">📦</div>
                        <div className="stat-content">
                            <h3>{fulfillmentData?.fulfillment_metrics?.delivered_today || 0}</h3>
                            <p>Delivered Today</p>
                        </div>
                    </div>

                    <div className="stat-card">
                        <div className="stat-icon performance">⚡</div>
                        <div className="stat-content">
                            <h3>{fulfillmentData?.fulfillment_metrics?.on_time_delivery_rate || 0}%</h3>
                            <p>On-Time Delivery</p>
                        </div>
                    </div>

                    <div className="stat-card">
                        <div className="stat-icon exceptions">⚠️</div>
                        <div className="stat-content">
                            <h3>{fulfillmentData?.tracking_metrics?.exception_count || 0}</h3>
                            <p>Exceptions</p>
                        </div>
                    </div>
                </div>

                <div className="dashboard-content">
                    {/* Recent Orders */}
                    <div className="dashboard-section">
                        <div className="section-header">
                            <h2>Recent Orders</h2>
                            <button
                                onClick={() => navigate('/admin/orders')}
                                className="view-all-btn"
                            >
                                View All
                            </button>
                        </div>
                        
                        <div className="orders-table">
                            {recent_orders && recent_orders.length > 0 ? (
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Order #</th>
                                            <th>Customer</th>
                                            <th>Status</th>
                                            <th>Amount</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {recent_orders.map(order => (
                                            <tr key={order.id}>
                                                <td>#{order.order_number}</td>
                                                <td>{order.customer_email}</td>
                                                <td>
                                                    <span className={`status-badge ${order.status}`}>
                                                        {order.status}
                                                    </span>
                                                </td>
                                                <td>₹{order.total_amount.toLocaleString()}</td>
                                                <td>{new Date(order.created_at).toLocaleDateString()}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            ) : (
                                <p className="no-data">No recent orders</p>
                            )}
                        </div>
                    </div>

                    {/* Low Stock Alert */}
                    <div className="dashboard-section">
                        <div className="section-header">
                            <h2>Low Stock Alert</h2>
                            <button 
                                onClick={() => navigate('/admin/inventory')}
                                className="view-all-btn"
                            >
                                View Inventory
                            </button>
                        </div>
                        
                        <div className="low-stock-list">
                            {low_stock_products && low_stock_products.length > 0 ? (
                                low_stock_products.map(product => (
                                    <div key={product.id} className="low-stock-item">
                                        <div className="product-info">
                                            <h4>{product.name}</h4>
                                            <p>Stock: {product.stock_quantity} / Threshold: {product.low_stock_threshold}</p>
                                        </div>
                                        <div className="stock-level">
                                            <div 
                                                className="stock-bar"
                                                style={{
                                                    width: `${Math.min((product.stock_quantity / product.low_stock_threshold) * 100, 100)}%`
                                                }}
                                            ></div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="no-data">All products are well stocked</p>
                            )}
                        </div>
                    </div>

                    {/* Fulfillment Monitoring */}
                    <div className="dashboard-section fulfillment-section">
                        <div className="section-header">
                            <h2>Fulfillment Overview</h2>
                            <button
                                onClick={() => navigate('/admin/fulfillment')}
                                className="view-all-btn"
                            >
                                View Details
                            </button>
                        </div>

                        {fulfillmentData ? (
                            <div className="fulfillment-overview">
                                {/* Carrier Performance */}
                                <div className="fulfillment-metric">
                                    <h4>Carrier Performance</h4>
                                    <div className="carrier-stats">
                                        <div className="carrier-stat">
                                            <span className="carrier-name">Blue Dart</span>
                                            <span className="carrier-performance">
                                                {fulfillmentData.tracking_metrics?.carrier_performance?.blue_dart || 'N/A'}%
                                            </span>
                                        </div>
                                        <div className="carrier-stat">
                                            <span className="carrier-name">Delhivery</span>
                                            <span className="carrier-performance">
                                                {fulfillmentData.tracking_metrics?.carrier_performance?.delhivery || 'N/A'}%
                                            </span>
                                        </div>
                                        <div className="carrier-stat">
                                            <span className="carrier-name">FedEx</span>
                                            <span className="carrier-performance">
                                                {fulfillmentData.tracking_metrics?.carrier_performance?.fedex || 'N/A'}%
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Recent Fulfillment Activity */}
                                <div className="fulfillment-metric">
                                    <h4>Recent Activity</h4>
                                    <div className="activity-stats">
                                        <div className="activity-item">
                                            <span className="activity-label">Orders Processed Today</span>
                                            <span className="activity-value">
                                                {fulfillmentData.fulfillment_metrics?.orders_processed_today || 0}
                                            </span>
                                        </div>
                                        <div className="activity-item">
                                            <span className="activity-label">Shipments Created</span>
                                            <span className="activity-value">
                                                {fulfillmentData.fulfillment_metrics?.shipments_created_today || 0}
                                            </span>
                                        </div>
                                        <div className="activity-item">
                                            <span className="activity-label">Average Processing Time</span>
                                            <span className="activity-value">
                                                {fulfillmentData.fulfillment_metrics?.avg_processing_time || 'N/A'}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* System Alerts */}
                                {fulfillmentData.tracking_metrics?.exception_count > 0 && (
                                    <div className="fulfillment-alerts">
                                        <h4>⚠️ System Alerts</h4>
                                        <div className="alert-item">
                                            <span className="alert-message">
                                                {fulfillmentData.tracking_metrics.exception_count} shipment exceptions require attention
                                            </span>
                                            <button
                                                className="alert-action-btn"
                                                onClick={() => navigate('/admin/fulfillment/alerts')}
                                            >
                                                View Details
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="fulfillment-loading">
                                <p>Loading fulfillment data...</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
};

export default AdminDashboard;
