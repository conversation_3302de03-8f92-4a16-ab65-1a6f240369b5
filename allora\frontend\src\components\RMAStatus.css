.rma-status-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

/* Header */
.rma-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.header-content h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.2rem;
    font-weight: 600;
}

.rma-number {
    color: #3498db;
    font-size: 1.2rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* Status Overview */
.status-overview {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 30px;
}

.status-badge {
    padding: 15px 25px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.status-badge.small {
    padding: 8px 15px;
    font-size: 0.8rem;
    border-radius: 15px;
}

.status-details {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item .label {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

.detail-item .value {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.detail-item .value.highlight {
    color: #27ae60;
    font-size: 1.2rem;
}

/* Section Styles */
.order-information,
.return-items,
.refund-information,
.shipping-information,
.rma-timeline {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.order-information h3,
.return-items h3,
.refund-information h3,
.shipping-information h3,
.rma-timeline h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.4rem;
    font-weight: 600;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

.order-details,
.refund-details,
.shipping-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

/* Return Items */
.items-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.return-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.return-item:hover {
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
}

.item-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.1rem;
    font-weight: 600;
}

.item-details p {
    margin: 4px 0;
    color: #666;
    font-size: 0.9rem;
}

.item-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.refund-amount {
    color: #27ae60;
    font-weight: 600;
    font-size: 1rem;
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e0e0e0;
}

.timeline-event {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -37px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #3498db;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #3498db;
}

.timeline-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.event-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 5px;
}

.event-description {
    color: #666;
    margin-bottom: 10px;
    line-height: 1.5;
}

.event-date {
    color: #95a5a6;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.event-actor {
    color: #7f8c8d;
    font-size: 0.8rem;
    font-style: italic;
}

/* Actions */
.rma-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 30px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-link {
    background: transparent;
    color: #3498db;
    text-decoration: underline;
    padding: 5px 10px;
    min-width: auto;
}

.btn-link:hover {
    color: #2980b9;
    background: rgba(52, 152, 219, 0.1);
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading and Error States */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: #666;
    font-size: 1.1rem;
}

.error-message {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-message h2 {
    color: #e74c3c;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.error-message p {
    color: #666;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rma-status-container {
        padding: 15px;
    }
    
    .rma-status-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
    
    .status-overview {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .status-details {
        grid-template-columns: 1fr;
        text-align: left;
    }
    
    .return-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .item-status {
        align-items: center;
    }
    
    .order-details,
    .refund-details,
    .shipping-details {
        grid-template-columns: 1fr;
    }
    
    .rma-actions {
        flex-direction: column;
        gap: 15px;
    }
    
    .btn {
        width: 100%;
    }
    
    .timeline {
        padding-left: 20px;
    }
    
    .timeline-marker {
        left: -27px;
    }
}
