"""
Recommendation Analytics and Optimization System
===============================================

Comprehensive analytics system for tracking recommendation performance,
measuring effectiveness, and providing optimization insights.

Features:
- Click-through rate tracking
- Conversion rate analysis
- A/B testing analytics
- Model performance monitoring
- Recommendation effectiveness analysis
- Automated optimization recommendations

Author: Allora Development Team
Date: 2025-07-06
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
from collections import defaultdict, Counter
import json

# Import database models
from app import db, UserInteractionLog, Product, User, UserBehaviorProfile

class RecommendationAnalytics:
    """
    Analytics system for recommendation performance tracking
    """
    
    def __init__(self, db_session):
        self.db_session = db_session
        
        # Analytics configuration
        self.default_time_window = 30  # days
        self.min_interactions_for_analysis = 10
        self.conversion_events = ['purchase', 'add_to_cart']
        self.engagement_events = ['click', 'view', 'rating', 'wishlist_add']
    
    def get_recommendation_performance_report(self, 
                                            start_date: datetime = None,
                                            end_date: datetime = None,
                                            algorithm: str = None) -> Dict[str, Any]:
        """Generate comprehensive recommendation performance report"""
        try:
            # Set default date range
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=self.default_time_window)
            
            # Get recommendation interactions
            rec_interactions = self._get_recommendation_interactions(
                start_date, end_date, algorithm
            )
            
            if not rec_interactions:
                return {'error': 'No recommendation data found for the specified period'}
            
            # Calculate key metrics
            metrics = {
                'overview': self._calculate_overview_metrics(rec_interactions),
                'click_through_rates': self._calculate_ctr_metrics(rec_interactions),
                'conversion_rates': self._calculate_conversion_metrics(rec_interactions),
                'algorithm_performance': self._analyze_algorithm_performance(rec_interactions),
                'user_engagement': self._analyze_user_engagement(rec_interactions),
                'product_performance': self._analyze_product_performance(rec_interactions),
                'temporal_analysis': self._analyze_temporal_patterns(rec_interactions),
                'recommendations': self._generate_optimization_recommendations(rec_interactions)
            }
            
            return {
                'report_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days
                },
                'algorithm_filter': algorithm,
                'metrics': metrics,
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error generating recommendation performance report: {e}")
            return {'error': str(e)}
    
    def _get_recommendation_interactions(self, start_date: datetime, 
                                       end_date: datetime, 
                                       algorithm: str = None) -> pd.DataFrame:
        """Get recommendation-related interactions from database"""
        try:
            # Query recommendation requests and subsequent interactions
            query = self.db_session.query(UserInteractionLog).filter(
                UserInteractionLog.timestamp >= start_date,
                UserInteractionLog.timestamp <= end_date
            )
            
            # Filter by algorithm if specified
            if algorithm:
                query = query.filter(
                    UserInteractionLog.context_data.contains(f'"algorithm": "{algorithm}"')
                )
            
            interactions = query.all()
            
            # Convert to DataFrame for analysis
            data = []
            for interaction in interactions:
                data.append({
                    'user_id': interaction.user_id,
                    'product_id': interaction.product_id,
                    'interaction_type': interaction.interaction_type,
                    'timestamp': interaction.timestamp,
                    'session_id': interaction.session_id,
                    'value': interaction.interaction_value,
                    'context': interaction.context_data or {}
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logging.error(f"Error getting recommendation interactions: {e}")
            return pd.DataFrame()
    
    def _calculate_overview_metrics(self, interactions_df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate high-level overview metrics"""
        try:
            total_interactions = len(interactions_df)
            unique_users = interactions_df['user_id'].nunique()
            unique_products = interactions_df['product_id'].nunique()
            unique_sessions = interactions_df['session_id'].nunique()
            
            # Recommendation requests
            rec_requests = interactions_df[
                interactions_df['interaction_type'] == 'recommendation_request'
            ]
            total_recommendations = rec_requests['value'].sum() if not rec_requests.empty else 0
            
            # Engagement interactions
            engagement_interactions = interactions_df[
                interactions_df['interaction_type'].isin(self.engagement_events)
            ]
            
            # Conversion interactions
            conversion_interactions = interactions_df[
                interactions_df['interaction_type'].isin(self.conversion_events)
            ]
            
            return {
                'total_interactions': int(total_interactions),
                'unique_users': int(unique_users),
                'unique_products': int(unique_products),
                'unique_sessions': int(unique_sessions),
                'total_recommendations_served': int(total_recommendations),
                'engagement_interactions': len(engagement_interactions),
                'conversion_interactions': len(conversion_interactions),
                'avg_interactions_per_user': round(total_interactions / max(unique_users, 1), 2),
                'avg_interactions_per_session': round(total_interactions / max(unique_sessions, 1), 2)
            }
            
        except Exception as e:
            logging.error(f"Error calculating overview metrics: {e}")
            return {}
    
    def _calculate_ctr_metrics(self, interactions_df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate click-through rate metrics"""
        try:
            # Get recommendation requests and clicks
            rec_requests = interactions_df[
                interactions_df['interaction_type'] == 'recommendation_request'
            ]
            
            clicks = interactions_df[
                interactions_df['interaction_type'] == 'click'
            ]
            
            if rec_requests.empty:
                return {'error': 'No recommendation requests found'}
            
            # Calculate overall CTR
            total_recommendations = rec_requests['value'].sum()
            total_clicks = len(clicks)
            overall_ctr = (total_clicks / max(total_recommendations, 1)) * 100
            
            # CTR by algorithm
            ctr_by_algorithm = {}
            for _, rec in rec_requests.iterrows():
                context = rec.get('context', {})
                algorithm = context.get('recommendation_type', 'unknown')
                
                if algorithm not in ctr_by_algorithm:
                    ctr_by_algorithm[algorithm] = {'recommendations': 0, 'clicks': 0}
                
                ctr_by_algorithm[algorithm]['recommendations'] += rec['value'] or 0
                
                # Count clicks for this algorithm in the same session
                session_clicks = clicks[clicks['session_id'] == rec['session_id']]
                ctr_by_algorithm[algorithm]['clicks'] += len(session_clicks)
            
            # Calculate CTR percentages
            for algorithm in ctr_by_algorithm:
                recs = ctr_by_algorithm[algorithm]['recommendations']
                clicks_count = ctr_by_algorithm[algorithm]['clicks']
                ctr_by_algorithm[algorithm]['ctr'] = (clicks_count / max(recs, 1)) * 100
            
            return {
                'overall_ctr': round(overall_ctr, 2),
                'total_recommendations': int(total_recommendations),
                'total_clicks': int(total_clicks),
                'ctr_by_algorithm': ctr_by_algorithm
            }
            
        except Exception as e:
            logging.error(f"Error calculating CTR metrics: {e}")
            return {}
    
    def _calculate_conversion_metrics(self, interactions_df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate conversion rate metrics"""
        try:
            # Get recommendation requests and conversions
            rec_requests = interactions_df[
                interactions_df['interaction_type'] == 'recommendation_request'
            ]
            
            conversions = interactions_df[
                interactions_df['interaction_type'].isin(self.conversion_events)
            ]
            
            if rec_requests.empty:
                return {'error': 'No recommendation requests found'}
            
            # Calculate overall conversion rate
            total_recommendations = rec_requests['value'].sum()
            total_conversions = len(conversions)
            overall_conversion_rate = (total_conversions / max(total_recommendations, 1)) * 100
            
            # Conversion by type
            conversion_by_type = conversions['interaction_type'].value_counts().to_dict()
            
            # Revenue from conversions (if available)
            purchase_conversions = conversions[conversions['interaction_type'] == 'purchase']
            total_revenue = purchase_conversions['value'].sum() if not purchase_conversions.empty else 0
            
            # Average order value
            avg_order_value = (total_revenue / max(len(purchase_conversions), 1)) if total_revenue > 0 else 0
            
            return {
                'overall_conversion_rate': round(overall_conversion_rate, 2),
                'total_recommendations': int(total_recommendations),
                'total_conversions': int(total_conversions),
                'conversion_by_type': conversion_by_type,
                'total_revenue': float(total_revenue),
                'average_order_value': round(avg_order_value, 2),
                'revenue_per_recommendation': round(total_revenue / max(total_recommendations, 1), 2)
            }
            
        except Exception as e:
            logging.error(f"Error calculating conversion metrics: {e}")
            return {}
    
    def _analyze_algorithm_performance(self, interactions_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance by recommendation algorithm"""
        try:
            algorithm_stats = defaultdict(lambda: {
                'requests': 0,
                'clicks': 0,
                'conversions': 0,
                'revenue': 0,
                'unique_users': set(),
                'unique_products': set()
            })
            
            # Analyze each interaction
            for _, interaction in interactions_df.iterrows():
                context = interaction.get('context', {})
                algorithm = context.get('recommendation_type', 'unknown')
                
                if interaction['interaction_type'] == 'recommendation_request':
                    algorithm_stats[algorithm]['requests'] += interaction['value'] or 0
                elif interaction['interaction_type'] == 'click':
                    algorithm_stats[algorithm]['clicks'] += 1
                elif interaction['interaction_type'] in self.conversion_events:
                    algorithm_stats[algorithm]['conversions'] += 1
                    if interaction['interaction_type'] == 'purchase':
                        algorithm_stats[algorithm]['revenue'] += interaction['value'] or 0
                
                algorithm_stats[algorithm]['unique_users'].add(interaction['user_id'])
                if interaction['product_id']:
                    algorithm_stats[algorithm]['unique_products'].add(interaction['product_id'])
            
            # Calculate performance metrics for each algorithm
            performance = {}
            for algorithm, stats in algorithm_stats.items():
                requests = stats['requests']
                clicks = stats['clicks']
                conversions = stats['conversions']
                revenue = stats['revenue']
                
                performance[algorithm] = {
                    'requests': requests,
                    'clicks': clicks,
                    'conversions': conversions,
                    'revenue': round(revenue, 2),
                    'unique_users': len(stats['unique_users']),
                    'unique_products': len(stats['unique_products']),
                    'ctr': round((clicks / max(requests, 1)) * 100, 2),
                    'conversion_rate': round((conversions / max(requests, 1)) * 100, 2),
                    'revenue_per_request': round(revenue / max(requests, 1), 2)
                }
            
            return performance
            
        except Exception as e:
            logging.error(f"Error analyzing algorithm performance: {e}")
            return {}
    
    def _analyze_user_engagement(self, interactions_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze user engagement patterns"""
        try:
            # User engagement metrics
            user_stats = interactions_df.groupby('user_id').agg({
                'interaction_type': 'count',
                'timestamp': ['min', 'max'],
                'session_id': 'nunique',
                'product_id': 'nunique'
            }).round(2)
            
            # Flatten column names
            user_stats.columns = ['total_interactions', 'first_interaction', 'last_interaction', 'sessions', 'unique_products']
            
            # Calculate engagement scores
            user_stats['session_duration'] = (user_stats['last_interaction'] - user_stats['first_interaction']).dt.total_seconds() / 3600  # hours
            user_stats['interactions_per_session'] = user_stats['total_interactions'] / user_stats['sessions']
            
            # Engagement segments
            engagement_segments = {
                'high_engagement': len(user_stats[user_stats['total_interactions'] >= 20]),
                'medium_engagement': len(user_stats[(user_stats['total_interactions'] >= 5) & (user_stats['total_interactions'] < 20)]),
                'low_engagement': len(user_stats[user_stats['total_interactions'] < 5])
            }
            
            return {
                'total_users': len(user_stats),
                'avg_interactions_per_user': round(user_stats['total_interactions'].mean(), 2),
                'avg_sessions_per_user': round(user_stats['sessions'].mean(), 2),
                'avg_products_per_user': round(user_stats['unique_products'].mean(), 2),
                'avg_interactions_per_session': round(user_stats['interactions_per_session'].mean(), 2),
                'engagement_segments': engagement_segments
            }
            
        except Exception as e:
            logging.error(f"Error analyzing user engagement: {e}")
            return {}
    
    def _analyze_product_performance(self, interactions_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze product performance in recommendations"""
        try:
            # Filter out non-product interactions
            product_interactions = interactions_df[interactions_df['product_id'].notna()]
            
            if product_interactions.empty:
                return {'error': 'No product interactions found'}
            
            # Product performance metrics
            product_stats = product_interactions.groupby('product_id').agg({
                'interaction_type': 'count',
                'user_id': 'nunique',
                'session_id': 'nunique'
            }).round(2)
            
            product_stats.columns = ['total_interactions', 'unique_users', 'unique_sessions']
            
            # Get top performing products
            top_products = product_stats.sort_values('total_interactions', ascending=False).head(10)
            
            # Product categories performance
            category_performance = {}
            for product_id in product_interactions['product_id'].unique():
                try:
                    product = self.db_session.query(Product).filter(Product.id == product_id).first()
                    if product and product.category:
                        if product.category not in category_performance:
                            category_performance[product.category] = 0
                        category_performance[product.category] += len(
                            product_interactions[product_interactions['product_id'] == product_id]
                        )
                except:
                    continue
            
            return {
                'total_products_recommended': len(product_stats),
                'avg_interactions_per_product': round(product_stats['total_interactions'].mean(), 2),
                'avg_users_per_product': round(product_stats['unique_users'].mean(), 2),
                'top_products': top_products.to_dict('index'),
                'category_performance': dict(sorted(category_performance.items(), key=lambda x: x[1], reverse=True))
            }
            
        except Exception as e:
            logging.error(f"Error analyzing product performance: {e}")
            return {}
    
    def _analyze_temporal_patterns(self, interactions_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze temporal patterns in recommendation interactions"""
        try:
            # Convert timestamp to datetime if it's not already
            interactions_df['timestamp'] = pd.to_datetime(interactions_df['timestamp'])
            
            # Daily patterns
            interactions_df['hour'] = interactions_df['timestamp'].dt.hour
            interactions_df['day_of_week'] = interactions_df['timestamp'].dt.day_name()
            interactions_df['date'] = interactions_df['timestamp'].dt.date
            
            # Hourly distribution
            hourly_distribution = interactions_df['hour'].value_counts().sort_index().to_dict()
            
            # Daily distribution
            daily_distribution = interactions_df['day_of_week'].value_counts().to_dict()
            
            # Daily trend
            daily_trend = interactions_df.groupby('date').size().to_dict()
            daily_trend = {str(k): v for k, v in daily_trend.items()}  # Convert date to string
            
            return {
                'hourly_distribution': hourly_distribution,
                'daily_distribution': daily_distribution,
                'daily_trend': daily_trend,
                'peak_hour': max(hourly_distribution, key=hourly_distribution.get),
                'peak_day': max(daily_distribution, key=daily_distribution.get)
            }
            
        except Exception as e:
            logging.error(f"Error analyzing temporal patterns: {e}")
            return {}
    
    def _generate_optimization_recommendations(self, interactions_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Generate automated optimization recommendations"""
        try:
            recommendations = []
            
            # Analyze algorithm performance for recommendations
            algorithm_performance = self._analyze_algorithm_performance(interactions_df)
            
            if algorithm_performance:
                # Find best performing algorithm
                best_algorithm = max(
                    algorithm_performance.keys(),
                    key=lambda x: algorithm_performance[x].get('conversion_rate', 0)
                )
                
                best_ctr = algorithm_performance[best_algorithm].get('ctr', 0)
                
                recommendations.append({
                    'type': 'algorithm_optimization',
                    'priority': 'high',
                    'title': f'Optimize Algorithm Distribution',
                    'description': f'{best_algorithm} shows the best performance with {best_ctr}% CTR. Consider increasing its usage.',
                    'impact': 'high',
                    'effort': 'medium'
                })
            
            # Check for low engagement
            user_engagement = self._analyze_user_engagement(interactions_df)
            if user_engagement.get('avg_interactions_per_user', 0) < 5:
                recommendations.append({
                    'type': 'engagement_improvement',
                    'priority': 'medium',
                    'title': 'Improve User Engagement',
                    'description': 'Average user interactions are low. Consider improving recommendation relevance or UI/UX.',
                    'impact': 'high',
                    'effort': 'high'
                })
            
            # Check conversion rates
            conversion_metrics = self._calculate_conversion_metrics(interactions_df)
            if conversion_metrics.get('overall_conversion_rate', 0) < 2:
                recommendations.append({
                    'type': 'conversion_optimization',
                    'priority': 'high',
                    'title': 'Optimize Conversion Funnel',
                    'description': 'Conversion rate is below 2%. Review recommendation quality and checkout process.',
                    'impact': 'high',
                    'effort': 'medium'
                })
            
            # Temporal optimization
            temporal_patterns = self._analyze_temporal_patterns(interactions_df)
            if temporal_patterns.get('peak_hour'):
                recommendations.append({
                    'type': 'temporal_optimization',
                    'priority': 'low',
                    'title': 'Optimize for Peak Hours',
                    'description': f'Peak activity at hour {temporal_patterns["peak_hour"]}. Consider personalized timing for recommendations.',
                    'impact': 'medium',
                    'effort': 'low'
                })
            
            return recommendations
            
        except Exception as e:
            logging.error(f"Error generating optimization recommendations: {e}")
            return []
    
    def get_ab_test_results(self, test_name: str, start_date: datetime = None, 
                           end_date: datetime = None) -> Dict[str, Any]:
        """Analyze A/B test results for recommendation algorithms"""
        try:
            # Set default date range
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=self.default_time_window)
            
            # Get interactions for the test period
            interactions_df = self._get_recommendation_interactions(start_date, end_date)
            
            if interactions_df.empty:
                return {'error': 'No data found for A/B test analysis'}
            
            # Analyze by algorithm (variant)
            algorithm_performance = self._analyze_algorithm_performance(interactions_df)
            
            # Statistical significance testing would go here
            # For now, we'll provide basic comparison
            
            results = {
                'test_name': test_name,
                'test_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'variants': algorithm_performance,
                'winner': max(algorithm_performance.keys(), 
                            key=lambda x: algorithm_performance[x].get('conversion_rate', 0)) if algorithm_performance else None,
                'statistical_significance': 'Not calculated',  # Would implement proper statistical testing
                'recommendation': 'Continue monitoring for more data'
            }
            
            return results
            
        except Exception as e:
            logging.error(f"Error analyzing A/B test results: {e}")
            return {'error': str(e)}

# Export main class
__all__ = ['RecommendationAnalytics']
