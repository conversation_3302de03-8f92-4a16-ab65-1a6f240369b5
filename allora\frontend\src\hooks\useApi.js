import { useState, useCallback } from 'react';
import { API_BASE_URL } from '../config/api';
import { useError } from '../contexts/ErrorContext';
import { useLoading } from '../contexts/LoadingContext';

/**
 * Custom hook for API calls with loading, error, and success states
 * @param {string} baseUrl - The base URL for API calls
 * @returns {object} - API utilities and state
 */
export const useApi = (baseUrl = API_BASE_URL, options = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { handleApiError, clearError } = useError();
  const { setLoading: setGlobalLoading, clearLoading } = useLoading();

  const {
    useGlobalLoading = false,
    loadingKey = 'api',
    autoHandleErrors = true
  } = options;

  // Generic API call function with retry logic
  const apiCall = useCallback(async (endpoint, requestOptions = {}, retryCount = 0) => {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    setLoading(true);
    setError(null);

    if (useGlobalLoading) {
      setGlobalLoading(loadingKey, true, `Loading ${endpoint}...`);
    }

    if (autoHandleErrors) {
      clearError(loadingKey);
    }

    try {
      const url = `${baseUrl}${endpoint}`;
      const config = {
        headers: {
          'Content-Type': 'application/json',
          ...requestOptions.headers,
        },
        ...requestOptions,
      };

      const response = await fetch(url, config);

      if (!response.ok) {
        // Handle rate limiting with retry
        if (response.status === 429 && retryCount < maxRetries) {
          const retryAfter = parseInt(response.headers.get('Retry-After') || '60', 10);
          const delay = Math.min(retryAfter * 1000, baseDelay * Math.pow(2, retryCount));

          console.log(`Rate limited. Retrying in ${delay/1000} seconds... (Attempt ${retryCount + 1}/${maxRetries})`);

          await new Promise(resolve => setTimeout(resolve, delay));
          return apiCall(endpoint, requestOptions, retryCount + 1);
        }

        const errorData = await response.json().catch(() => ({}));

        if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }

        const error = new Error(errorData.error || `HTTP error! status: ${response.status}`);
        error.response = { status: response.status, data: errorData };
        error.config = { url };
        throw error;
      }

      const data = await response.json();

      if (useGlobalLoading) {
        clearLoading(loadingKey);
      }

      return data;
    } catch (err) {
      console.error('API call failed:', err);
      setError(err.message);

      if (autoHandleErrors) {
        handleApiError(err, { endpoint, retryCount });
      }

      throw err;
    } finally {
      setLoading(false);

      if (useGlobalLoading) {
        clearLoading(loadingKey);
      }
    }
  }, [baseUrl, useGlobalLoading, loadingKey, autoHandleErrors, setGlobalLoading, clearLoading, handleApiError, clearError]);

  // GET request
  const get = useCallback((endpoint, token = null) => {
    const headers = token ? { 'Authorization': token } : {};
    return apiCall(endpoint, { method: 'GET', headers });
  }, [apiCall]);

  // POST request
  const post = useCallback((endpoint, data, token = null) => {
    const headers = {
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': token } : {})
    };
    return apiCall(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });
  }, [apiCall]);

  // POST request with FormData (for file uploads)
  const postFormData = useCallback((endpoint, formData, token = null) => {
    const headers = token ? { 'Authorization': token } : {};
    // Don't set Content-Type for FormData, let browser set it with boundary
    delete headers['Content-Type'];
    return apiCall(endpoint, {
      method: 'POST',
      headers,
      body: formData,
    });
  }, [apiCall]);

  // PUT request
  const put = useCallback((endpoint, data, token = null) => {
    const headers = {
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': token } : {})
    };
    return apiCall(endpoint, {
      method: 'PUT',
      headers,
      body: JSON.stringify(data),
    });
  }, [apiCall]);

  // PATCH request
  const patch = useCallback((endpoint, data, token = null) => {
    const headers = {
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': token } : {})
    };
    return apiCall(endpoint, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(data),
    });
  }, [apiCall]);

  // Clear local error
  const clearLocalError = useCallback(() => {
    setError(null);
  }, []);

  return {
    loading,
    error,
    get,
    post,
    postFormData,
    put,
    patch,
    clearError: clearLocalError,
  };
};

export default useApi;
