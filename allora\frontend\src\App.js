import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import { useMemo } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SellerAuthProvider } from './contexts/SellerAuthContext';
import { CartProvider } from './contexts/CartContext';
import { DialogProvider } from './contexts/DialogContext';
import { CookieProvider } from './contexts/CookieContext';
import NotificationProvider from './contexts/NotificationContext';
import { WebSocketProvider } from './contexts/WebSocketContext';
import { LoadingProvider } from './contexts/LoadingContext';
import { ErrorProvider } from './contexts/ErrorContext';
import { ImageProvider } from './contexts/ImageContext';
import ErrorBoundary from './components/ErrorBoundary';
import { GlobalErrorDisplay } from './components/ErrorComponents';
import { GlobalLoadingIndicator } from './components/LoadingComponents';



import { useSEO } from './utils/seo';
import ModernMinimalistNavbar from './components/ModernMinimalistNavbar';
import ModernMinimalistFooter from './components/ModernMinimalistFooter';
import DynamicTitle from './components/DynamicTitle';

import Home from './components/SimpleHome';
import ProductDetail from './pages/ProductDetails';
import Community from './pages/Community';
import Profile from './pages/Profile';
import Account from './pages/Account';
import Cart from './pages/Cart';
import Checkout from './pages/Checkout';
import CartRecovery from './pages/CartRecovery';
import Search from './pages/Search';
import Categories from './pages/Categories';
import Category from './pages/Category';
import InventoryAssistant from './pages/InventoryAssistant';
import PriceTrends from './pages/PriceTrends';
import AuthPage from './components/Login';
import PrivacyPolicy from './components/PrivacyPolicy';
import TermsOfService from './components/TermsOfService';
import CookiePolicy from './components/CookiePolicy';
import CookieConsentBanner from './components/CookieConsentBanner';
import CookiePreferenceCenter from './components/CookiePreferenceCenter';
import GuestCheckout from './components/GuestCheckout';
import OrderConfirmation from './pages/OrderConfirmation';
import TrackOrder from './pages/TrackOrder';
import SupportHub from './pages/SupportHub';
import FAQ from './pages/FAQ';
import ContactUs from './pages/ContactUs';
import ReturnPolicy from './pages/ReturnPolicy';
import SupportTickets from './pages/SupportTickets';
import Sell from './pages/Sell';
import SellerLogin from './pages/SellerLogin';
import SellerDashboard from './pages/SellerDashboard';
import SellerProducts from './pages/SellerProducts';
import SellerProductForm from './pages/SellerProductForm';
import SellerOrders from './pages/SellerOrders';
import SellerStoreProfile from './pages/SellerStoreProfile';
import PublicStorePage from './pages/PublicStorePage';
import SellerEarnings from './pages/SellerEarnings';
import SellerAnalytics from './pages/SellerAnalytics';
import SellerStorePage from './pages/SellerStorePage';
import AdminSellerManagement from './pages/AdminSellerManagement';

import AdminLogin from './pages/AdminLogin';
import AdminDashboard from './pages/AdminDashboard';
import AdminProducts from './pages/AdminProducts';
import NotFound from './pages/NotFound';
import ServerError from './pages/ServerError';
import ImageTest from './pages/ImageTest';
import TestPostCard from './components/TestPostCard';
import About from './pages/About';
import Sustainability from './pages/Sustainability';
import Careers from './pages/Careers';
import Press from './pages/Press';
import ShippingInfo from './pages/ShippingInfo';
import CommunityPage from './pages/CommunityPage';
import './styles.css';
import './styles/responsive.css';
import './styles/tracking.css';
import 'tailwindcss/tailwind.css';
import { HelmetProvider } from 'react-helmet-async';

// Inner App component that uses AuthContext
function AppContent() {
  const { token, user, logout, isAdmin } = useAuth();

  // Set global SEO defaults - use useMemo to prevent infinite re-renders
  const currentUrl = useMemo(() => window.location.href, []);

  useSEO({
    title: 'Allora - Sustainable Shopping Platform',
    description: 'Discover eco-friendly products and join our sustainable shopping community. Shop responsibly with Allora.',
    keywords: 'sustainable shopping, eco-friendly products, green marketplace, sustainable living',
    image: '/images/og-image.jpg',
    url: currentUrl
  });

  const ProtectedRoute = ({ element }) => {
    return token ? element : <Navigate to="/login" />;
  };

  const AdminProtectedRoute = ({ element }) => {
    return isAdmin ? element : <Navigate to="/admin/login" />;
  };

  return (
          <Router>
          <SellerAuthProvider>
          <div className="min-h-screen bg-gray-50">
        {/* Dynamic Title Component */}
        <DynamicTitle />

        {/* Modern Minimalist Navigation Bar */}
        <ModernMinimalistNavbar
          token={token}
          user={user}
          onLogout={logout}
        />

        {/* Cookie Components */}
        <CookieConsentBanner />
        <CookiePreferenceCenter />

        {/* Main Content */}
        <div>
        <Routes>
          <Route path="/" element={<Home token={token} />} />
          <Route path="/product/:id" element={<ProductDetail token={token} />} />
          <Route path="/community" element={<Community token={token} />} />
          <Route path="/profile" element={<ProtectedRoute element={<Profile token={token} />} />} />
          <Route path="/account" element={<ProtectedRoute element={<Account token={token} />} />} />
          <Route path="/cart" element={<Cart token={token} />} />
          <Route path="/checkout" element={<ProtectedRoute element={<Checkout token={token} />} />} />
          <Route path="/guest-checkout" element={<GuestCheckout />} />
          <Route path="/cart-recovery/:recoveryToken" element={<CartRecovery />} />
          <Route path="/order-confirmation/:orderNumber" element={<OrderConfirmation />} />
          <Route path="/track" element={<TrackOrder />} />
          <Route path="/track/:trackingNumber" element={<TrackOrder />} />
          <Route path="/search" element={<Search token={token} />} />
          <Route path="/categories" element={<Categories />} />
          <Route path="/category/:categoryName" element={<Category token={token} />} />
          <Route path="/inventory" element={<ProtectedRoute element={<InventoryAssistant token={token} />} />} />
          <Route path="/price-trends" element={<ProtectedRoute element={<PriceTrends token={token} />} />} />
          <Route path="/support" element={<SupportHub />} />
          <Route path="/support/faq" element={<FAQ />} />
          <Route path="/support/contact" element={<ContactUs />} />
          <Route path="/support/returns" element={<ReturnPolicy />} />
          <Route path="/support/tickets" element={<SupportTickets token={token} />} />
          <Route path="/about" element={<About />} />
          <Route path="/sustainability" element={<Sustainability />} />
          <Route path="/careers" element={<Careers />} />
          <Route path="/press" element={<Press />} />
          <Route path="/shipping-info" element={<ShippingInfo />} />
          <Route path="/company-community" element={<CommunityPage />} />
          <Route path="/sell" element={<Sell />} />
          <Route path="/seller/login" element={<SellerLogin />} />
          <Route path="/seller/dashboard" element={<SellerDashboard />} />
          <Route path="/seller/products" element={<SellerProducts />} />
          <Route path="/seller/products/add" element={<SellerProductForm />} />
          <Route path="/seller/products/:productId/edit" element={<SellerProductForm />} />
          <Route path="/seller/orders" element={<SellerOrders />} />
          <Route path="/seller/store" element={<SellerStoreProfile />} />
          <Route path="/store/:storeSlug" element={<SellerStorePage />} />
          <Route path="/seller/earnings" element={<SellerEarnings />} />
          <Route path="/seller/analytics" element={<SellerAnalytics />} />
          <Route path="/admin/sellers" element={<AdminSellerManagement />} />
          <Route path="/signup" element={<AuthPage />} />
          <Route path="/login" element={<AuthPage />} />

          {/* Legal Pages */}
          <Route path="/privacy" element={<PrivacyPolicy />} />
          <Route path="/terms" element={<TermsOfService />} />
          <Route path="/cookies" element={<CookiePolicy />} />

          {/* Test Routes */}
          <Route path="/image-test" element={<ImageTest />} />
          <Route path="/test-post" element={<TestPostCard />} />

          {/* Admin Routes */}
          <Route path="/admin/login" element={<AdminLogin />} />
          <Route path="/admin/dashboard" element={<AdminProtectedRoute element={<AdminDashboard />} />} />
          <Route path="/admin/products" element={<AdminProtectedRoute element={<AdminProducts />} />} />

          {/* Error Routes */}
          <Route path="/500" element={<ServerError />} />
          <Route path="*" element={<NotFound />} />
        </Routes>

        </div>



        {/* Modern Minimalist Footer */}
        <ModernMinimalistFooter />

        {/* Global Components */}
        <GlobalErrorDisplay position="top-right" />
        <GlobalLoadingIndicator />

        </div>
          </SellerAuthProvider>
          </Router>
  );
}

// Main App component with providers
function App() {
  return (
    <HelmetProvider>
      <ErrorBoundary>
        <NotificationProvider>
          <ErrorProvider>
            <LoadingProvider>
              <ImageProvider>
                <AuthProvider>
                  <WebSocketProvider>
                    <CartProvider>
                      <DialogProvider>
                        <CookieProvider>
                          <AppContent />
                        </CookieProvider>
                      </DialogProvider>
                    </CartProvider>
                  </WebSocketProvider>
                </AuthProvider>
              </ImageProvider>
            </LoadingProvider>
          </ErrorProvider>
        </NotificationProvider>
      </ErrorBoundary>
    </HelmetProvider>
  );
}

export default App;