{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSEO } from '../utils/seo';\nimport { ErrorBoundaryFallback } from './ErrorComponents';\nimport './ErrorBoundary.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.logErrorToService = (error, errorInfo) => {\n      const errorData = {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        timestamp: new Date().toISOString(),\n        userAgent: navigator.userAgent,\n        url: window.location.href,\n        userId: localStorage.getItem('userId') || 'anonymous',\n        errorId: this.state.errorId\n      };\n\n      // Send to backend error logging endpoint\n      try {\n        fetch('/api/errors', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(errorData)\n        }).catch(err => {\n          console.error('Failed to log error to service:', err);\n        });\n      } catch (err) {\n        console.error('Failed to send error log:', err);\n      }\n\n      // Log to console for development\n      console.error('Error Boundary caught an error:', error, errorInfo);\n    };\n    this.handleRetry = () => {\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null,\n        errorId: null\n      });\n    };\n    this.handleReportError = () => {\n      var _this$state$error;\n      const subject = encodeURIComponent(`Error Report - ${this.state.errorId}`);\n      const body = encodeURIComponent(`\nError ID: ${this.state.errorId}\nURL: ${window.location.href}\nError: ${(_this$state$error = this.state.error) === null || _this$state$error === void 0 ? void 0 : _this$state$error.message}\nTime: ${new Date().toISOString()}\n\nPlease describe what you were doing when this error occurred:\n    `);\n      window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Log error details\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // Log to error reporting service\n    this.logErrorToService(error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      var _this$state$errorInfo;\n      // Use custom fallback if provided, otherwise use the new ErrorBoundaryFallback\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n      return /*#__PURE__*/_jsxDEV(ErrorBoundaryFallback, {\n        error: this.state.error,\n        resetError: this.handleRetry,\n        componentStack: (_this$state$errorInfo = this.state.errorInfo) === null || _this$state$errorInfo === void 0 ? void 0 : _this$state$errorInfo.componentStack\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nconst ErrorFallback = ({\n  error,\n  errorInfo,\n  errorId,\n  onRetry,\n  onReport,\n  fallback\n}) => {\n  _s();\n  // Set SEO for error page\n  useSEO({\n    title: 'Something went wrong',\n    description: 'An unexpected error occurred. Please try again or contact support.',\n    robots: 'noindex, nofollow'\n  });\n  if (fallback) {\n    return fallback;\n  }\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-boundary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-icon\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"64\",\n          height: \"64\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            stroke: \"#ef4444\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"15\",\n            y1: \"9\",\n            x2: \"9\",\n            y2: \"15\",\n            stroke: \"#ef4444\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"9\",\n            y1: \"9\",\n            x2: \"15\",\n            y2: \"15\",\n            stroke: \"#ef4444\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"error-title\",\n        children: \"Oops! Something went wrong\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error-description\",\n        children: \"We're sorry, but something unexpected happened. Our team has been notified and is working to fix this issue.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onRetry,\n          className: \"retry-button\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M1 4v6h6\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), \"Try Again\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.href = '/',\n          className: \"home-button\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n              points: \"9,22 9,12 15,12 15,22\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), \"Go Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onReport,\n          className: \"report-button\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"16\",\n            height: \"16\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n              points: \"22,6 12,13 2,6\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), \"Report Issue\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), errorId && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-id\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Error ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: errorId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 26\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"error-id-help\",\n          children: \"Please include this ID when contacting support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), isDevelopment && error && /*#__PURE__*/_jsxDEV(\"details\", {\n        className: \"error-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n          children: \"Technical Details (Development Only)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-stack\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Error Message:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            children: error.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Stack Trace:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            children: error.stack\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this), errorInfo && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Component Stack:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              children: errorInfo.componentStack\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n\n// Higher-order component for wrapping components with error boundaries\n_s(ErrorFallback, \"6rSydtRzpsPOyyHBRlWg+zmNlls=\", false, function () {\n  return [useSEO];\n});\n_c = ErrorFallback;\nexport const withErrorBoundary = (Component, fallback) => {\n  return function WrappedComponent(props) {\n    return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n      fallback: fallback,\n      children: /*#__PURE__*/_jsxDEV(Component, {\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this);\n  };\n};\n\n// Hook for error reporting\nexport const useErrorHandler = () => {\n  const reportError = (error, context = {}) => {\n    const errorData = {\n      message: error.message,\n      stack: error.stack,\n      context,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n      userId: localStorage.getItem('userId') || 'anonymous'\n    };\n    try {\n      fetch('/api/errors', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(errorData)\n      }).catch(err => {\n        console.error('Failed to report error:', err);\n      });\n    } catch (err) {\n      console.error('Failed to send error report:', err);\n    }\n  };\n  return {\n    reportError\n  };\n};\n\n// Error Display Component for API errors\nconst ErrorDisplay = ({\n  error,\n  onRetry,\n  showRetry = true\n}) => {\n  const isRateLimit = (error === null || error === void 0 ? void 0 : error.includes('Rate limit')) || (error === null || error === void 0 ? void 0 : error.includes('429'));\n  const isNetworkError = (error === null || error === void 0 ? void 0 : error.includes('fetch')) || (error === null || error === void 0 ? void 0 : error.includes('Network'));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: isRateLimit ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 mx-auto bg-yellow-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-yellow-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this) : isNetworkError ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-blue-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-red-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-2\",\n        children: isRateLimit ? 'Too Many Requests' : isNetworkError ? 'Connection Issue' : 'Something Went Wrong'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6\",\n        children: isRateLimit ? 'We\\'re receiving a lot of requests right now. Please wait a moment and try again.' : isNetworkError ? 'Unable to connect to our servers. Please check your internet connection.' : error || 'An unexpected error occurred. Please try again.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), showRetry && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n        children: [onRetry && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onRetry,\n          className: \"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\",\n          children: isRateLimit ? 'Try Again' : 'Retry'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors duration-200 font-medium\",\n          children: \"Refresh Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this), isRateLimit && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-yellow-800\",\n          children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Tip:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 18\n          }, this), \" Try refreshing the page in a few seconds, or browse our cached products below.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n_c2 = ErrorDisplay;\nconst LoadingSpinner = ({\n  message = 'Loading...'\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex flex-col items-center justify-center p-8\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mb-4\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 332,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"text-gray-600 font-medium\",\n    children: message\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 333,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 331,\n  columnNumber: 3\n}, this);\n_c3 = LoadingSpinner;\nexport { ErrorDisplay, LoadingSpinner };\nexport default ErrorBoundary;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ErrorFallback\");\n$RefreshReg$(_c2, \"ErrorDisplay\");\n$RefreshReg$(_c3, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "useSEO", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "logErrorToService", "error", "errorInfo", "errorData", "message", "stack", "componentStack", "timestamp", "Date", "toISOString", "userAgent", "navigator", "url", "window", "location", "href", "userId", "localStorage", "getItem", "errorId", "state", "fetch", "method", "headers", "body", "JSON", "stringify", "catch", "err", "console", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "handleReportError", "_this$state$error", "subject", "encodeURIComponent", "open", "getDerivedStateFromError", "now", "toString", "Math", "random", "substr", "componentDidCatch", "render", "_this$state$errorInfo", "fallback", "resetError", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onRetry", "onReport", "_s", "title", "description", "robots", "isDevelopment", "process", "env", "NODE_ENV", "className", "width", "height", "viewBox", "fill", "xmlns", "cx", "cy", "r", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "onClick", "d", "strokeLinecap", "strokeLinejoin", "points", "_c", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WrappedComponent", "useErrorHandler", "reportError", "context", "ErrorDisplay", "showRetry", "isRateLimit", "includes", "isNetworkError", "reload", "_c2", "LoadingSpinner", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/components/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\nimport { useSEO } from '../utils/seo';\nimport { ErrorBoundaryFallback } from './ErrorComponents';\nimport './ErrorBoundary.css';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { \n      hasError: false, \n      error: null, \n      errorInfo: null,\n      errorId: null\n    };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { \n      hasError: true,\n      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)\n    };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log error details\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // Log to error reporting service\n    this.logErrorToService(error, errorInfo);\n  }\n\n  logErrorToService = (error, errorInfo) => {\n    const errorData = {\n      message: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n      userId: localStorage.getItem('userId') || 'anonymous',\n      errorId: this.state.errorId\n    };\n\n    // Send to backend error logging endpoint\n    try {\n      fetch('/api/errors', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(errorData)\n      }).catch(err => {\n        console.error('Failed to log error to service:', err);\n      });\n    } catch (err) {\n      console.error('Failed to send error log:', err);\n    }\n\n    // Log to console for development\n    console.error('Error Boundary caught an error:', error, errorInfo);\n  };\n\n  handleRetry = () => {\n    this.setState({ \n      hasError: false, \n      error: null, \n      errorInfo: null,\n      errorId: null\n    });\n  };\n\n  handleReportError = () => {\n    const subject = encodeURIComponent(`Error Report - ${this.state.errorId}`);\n    const body = encodeURIComponent(`\nError ID: ${this.state.errorId}\nURL: ${window.location.href}\nError: ${this.state.error?.message}\nTime: ${new Date().toISOString()}\n\nPlease describe what you were doing when this error occurred:\n    `);\n    \n    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Use custom fallback if provided, otherwise use the new ErrorBoundaryFallback\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <ErrorBoundaryFallback\n          error={this.state.error}\n          resetError={this.handleRetry}\n          componentStack={this.state.errorInfo?.componentStack}\n        />\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nconst ErrorFallback = ({ error, errorInfo, errorId, onRetry, onReport, fallback }) => {\n  // Set SEO for error page\n  useSEO({\n    title: 'Something went wrong',\n    description: 'An unexpected error occurred. Please try again or contact support.',\n    robots: 'noindex, nofollow'\n  });\n\n  if (fallback) {\n    return fallback;\n  }\n\n  const isDevelopment = process.env.NODE_ENV === 'development';\n\n  return (\n    <div className=\"error-boundary\">\n      <div className=\"error-container\">\n        <div className=\"error-icon\">\n          <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#ef4444\" strokeWidth=\"2\"/>\n            <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"#ef4444\" strokeWidth=\"2\"/>\n            <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"#ef4444\" strokeWidth=\"2\"/>\n          </svg>\n        </div>\n        \n        <h1 className=\"error-title\">Oops! Something went wrong</h1>\n        \n        <p className=\"error-description\">\n          We're sorry, but something unexpected happened. Our team has been notified and is working to fix this issue.\n        </p>\n\n        <div className=\"error-actions\">\n          <button \n            onClick={onRetry}\n            className=\"retry-button\"\n          >\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M1 4v6h6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              <path d=\"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Try Again\n          </button>\n          \n          <button \n            onClick={() => window.location.href = '/'}\n            className=\"home-button\"\n          >\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              <polyline points=\"9,22 9,12 15,12 15,22\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Go Home\n          </button>\n          \n          <button \n            onClick={onReport}\n            className=\"report-button\"\n          >\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              <polyline points=\"22,6 12,13 2,6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Report Issue\n          </button>\n        </div>\n\n        {errorId && (\n          <div className=\"error-id\">\n            <p>Error ID: <code>{errorId}</code></p>\n            <p className=\"error-id-help\">\n              Please include this ID when contacting support\n            </p>\n          </div>\n        )}\n\n        {isDevelopment && error && (\n          <details className=\"error-details\">\n            <summary>Technical Details (Development Only)</summary>\n            <div className=\"error-stack\">\n              <h3>Error Message:</h3>\n              <pre>{error.message}</pre>\n              \n              <h3>Stack Trace:</h3>\n              <pre>{error.stack}</pre>\n              \n              {errorInfo && (\n                <>\n                  <h3>Component Stack:</h3>\n                  <pre>{errorInfo.componentStack}</pre>\n                </>\n              )}\n            </div>\n          </details>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Higher-order component for wrapping components with error boundaries\nexport const withErrorBoundary = (Component, fallback) => {\n  return function WrappedComponent(props) {\n    return (\n      <ErrorBoundary fallback={fallback}>\n        <Component {...props} />\n      </ErrorBoundary>\n    );\n  };\n};\n\n// Hook for error reporting\nexport const useErrorHandler = () => {\n  const reportError = (error, context = {}) => {\n    const errorData = {\n      message: error.message,\n      stack: error.stack,\n      context,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n      userId: localStorage.getItem('userId') || 'anonymous'\n    };\n\n    try {\n      fetch('/api/errors', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(errorData)\n      }).catch(err => {\n        console.error('Failed to report error:', err);\n      });\n    } catch (err) {\n      console.error('Failed to send error report:', err);\n    }\n  };\n\n  return { reportError };\n};\n\n// Error Display Component for API errors\nconst ErrorDisplay = ({ error, onRetry, showRetry = true }) => {\n  const isRateLimit = error?.includes('Rate limit') || error?.includes('429');\n  const isNetworkError = error?.includes('fetch') || error?.includes('Network');\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg border border-gray-200\">\n      <div className=\"text-center max-w-md\">\n        {/* Error Icon */}\n        <div className=\"mb-4\">\n          {isRateLimit ? (\n            <div className=\"w-16 h-16 mx-auto bg-yellow-100 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            </div>\n          ) : isNetworkError ? (\n            <div className=\"w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0\" />\n              </svg>\n            </div>\n          ) : (\n            <div className=\"w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          )}\n        </div>\n\n        {/* Error Title */}\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n          {isRateLimit ? 'Too Many Requests' : isNetworkError ? 'Connection Issue' : 'Something Went Wrong'}\n        </h3>\n\n        {/* Error Message */}\n        <p className=\"text-gray-600 mb-6\">\n          {isRateLimit\n            ? 'We\\'re receiving a lot of requests right now. Please wait a moment and try again.'\n            : isNetworkError\n            ? 'Unable to connect to our servers. Please check your internet connection.'\n            : error || 'An unexpected error occurred. Please try again.'\n          }\n        </p>\n\n        {/* Action Buttons */}\n        {showRetry && (\n          <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n            {onRetry && (\n              <button\n                onClick={onRetry}\n                className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\"\n              >\n                {isRateLimit ? 'Try Again' : 'Retry'}\n              </button>\n            )}\n            <button\n              onClick={() => window.location.reload()}\n              className=\"px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors duration-200 font-medium\"\n            >\n              Refresh Page\n            </button>\n          </div>\n        )}\n\n        {/* Additional Help */}\n        {isRateLimit && (\n          <div className=\"mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200\">\n            <p className=\"text-sm text-yellow-800\">\n              💡 <strong>Tip:</strong> Try refreshing the page in a few seconds, or browse our cached products below.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nconst LoadingSpinner = ({ message = 'Loading...' }) => (\n  <div className=\"flex flex-col items-center justify-center p-8\">\n    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mb-4\"></div>\n    <p className=\"text-gray-600 font-medium\">{message}</p>\n  </div>\n);\n\nexport { ErrorDisplay, LoadingSpinner };\nexport default ErrorBoundary;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,SAASP,KAAK,CAACQ,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAAC,KA4BfC,iBAAiB,GAAG,CAACC,KAAK,EAAEC,SAAS,KAAK;MACxC,MAAMC,SAAS,GAAG;QAChBC,OAAO,EAAEH,KAAK,CAACG,OAAO;QACtBC,KAAK,EAAEJ,KAAK,CAACI,KAAK;QAClBC,cAAc,EAAEJ,SAAS,CAACI,cAAc;QACxCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAEC,SAAS,CAACD,SAAS;QAC9BE,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI;QACzBC,MAAM,EAAEC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,WAAW;QACrDC,OAAO,EAAE,IAAI,CAACC,KAAK,CAACD;MACtB,CAAC;;MAED;MACA,IAAI;QACFE,KAAK,CAAC,aAAa,EAAE;UACnBC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACvB,SAAS;QAChC,CAAC,CAAC,CAACwB,KAAK,CAACC,GAAG,IAAI;UACdC,OAAO,CAAC5B,KAAK,CAAC,iCAAiC,EAAE2B,GAAG,CAAC;QACvD,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOA,GAAG,EAAE;QACZC,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,EAAE2B,GAAG,CAAC;MACjD;;MAEA;MACAC,OAAO,CAAC5B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,EAAEC,SAAS,CAAC;IACpE,CAAC;IAAA,KAED4B,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QACZC,QAAQ,EAAE,KAAK;QACf/B,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAI;QACfiB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDc,iBAAiB,GAAG,MAAM;MAAA,IAAAC,iBAAA;MACxB,MAAMC,OAAO,GAAGC,kBAAkB,CAAC,kBAAkB,IAAI,CAAChB,KAAK,CAACD,OAAO,EAAE,CAAC;MAC1E,MAAMK,IAAI,GAAGY,kBAAkB,CAAC;AACpC,YAAY,IAAI,CAAChB,KAAK,CAACD,OAAO;AAC9B,OAAON,MAAM,CAACC,QAAQ,CAACC,IAAI;AAC3B,SAD2B,CAAAmB,iBAAA,GAClB,IAAI,CAACd,KAAK,CAACnB,KAAK,cAAAiC,iBAAA,uBAAhBA,iBAAA,CAAkB9B,OAAO;AAClC,QAAQ,IAAII,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AAChC;AACA;AACA,KAAK,CAAC;MAEFI,MAAM,CAACwB,IAAI,CAAC,qCAAqCF,OAAO,SAASX,IAAI,EAAE,CAAC;IAC1E,CAAC;IA/EC,IAAI,CAACJ,KAAK,GAAG;MACXY,QAAQ,EAAE,KAAK;MACf/B,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfiB,OAAO,EAAE;IACX,CAAC;EACH;EAEA,OAAOmB,wBAAwBA,CAACrC,KAAK,EAAE;IACrC;IACA,OAAO;MACL+B,QAAQ,EAAE,IAAI;MACdb,OAAO,EAAEX,IAAI,CAAC+B,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC;IACxE,CAAC;EACH;EAEAC,iBAAiBA,CAAC3C,KAAK,EAAEC,SAAS,EAAE;IAClC;IACA,IAAI,CAAC6B,QAAQ,CAAC;MACZ9B,KAAK;MACLC;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACF,iBAAiB,CAACC,KAAK,EAAEC,SAAS,CAAC;EAC1C;EAwDA2C,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACzB,KAAK,CAACY,QAAQ,EAAE;MAAA,IAAAc,qBAAA;MACvB;MACA,IAAI,IAAI,CAAC/C,KAAK,CAACgD,QAAQ,EAAE;QACvB,OAAO,IAAI,CAAChD,KAAK,CAACgD,QAAQ;MAC5B;MAEA,oBACEtD,OAAA,CAACF,qBAAqB;QACpBU,KAAK,EAAE,IAAI,CAACmB,KAAK,CAACnB,KAAM;QACxB+C,UAAU,EAAE,IAAI,CAAClB,WAAY;QAC7BxB,cAAc,GAAAwC,qBAAA,GAAE,IAAI,CAAC1B,KAAK,CAAClB,SAAS,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBxC;MAAe;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAEN;IAEA,OAAO,IAAI,CAACrD,KAAK,CAACsD,QAAQ;EAC5B;AACF;AAEA,MAAMC,aAAa,GAAGA,CAAC;EAAErD,KAAK;EAAEC,SAAS;EAAEiB,OAAO;EAAEoC,OAAO;EAAEC,QAAQ;EAAET;AAAS,CAAC,KAAK;EAAAU,EAAA;EACpF;EACAnE,MAAM,CAAC;IACLoE,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,oEAAoE;IACjFC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,IAAIb,QAAQ,EAAE;IACZ,OAAOA,QAAQ;EACjB;EAEA,MAAMc,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;EAE5D,oBACEvE,OAAA;IAAKwE,SAAS,EAAC,gBAAgB;IAAAZ,QAAA,eAC7B5D,OAAA;MAAKwE,SAAS,EAAC,iBAAiB;MAAAZ,QAAA,gBAC9B5D,OAAA;QAAKwE,SAAS,EAAC,YAAY;QAAAZ,QAAA,eACzB5D,OAAA;UAAKyE,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,4BAA4B;UAAAjB,QAAA,gBAC5F5D,OAAA;YAAQ8E,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACjE3D,OAAA;YAAMmF,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACL,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACtE3D,OAAA;YAAMmF,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACL,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3D,OAAA;QAAIwE,SAAS,EAAC,aAAa;QAAAZ,QAAA,EAAC;MAA0B;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE3D3D,OAAA;QAAGwE,SAAS,EAAC,mBAAmB;QAAAZ,QAAA,EAAC;MAEjC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ3D,OAAA;QAAKwE,SAAS,EAAC,eAAe;QAAAZ,QAAA,gBAC5B5D,OAAA;UACEuF,OAAO,EAAEzB,OAAQ;UACjBU,SAAS,EAAC,cAAc;UAAAZ,QAAA,gBAExB5D,OAAA;YAAKyE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAAjB,QAAA,gBAC5F5D,OAAA;cAAMwF,CAAC,EAAC,UAAU;cAACP,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACO,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACvG3D,OAAA;cAAMwF,CAAC,EAAC,mCAAmC;cAACP,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACO,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7H,CAAC,aAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3D,OAAA;UACEuF,OAAO,EAAEA,CAAA,KAAMnE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAI;UAC1CkD,SAAS,EAAC,aAAa;UAAAZ,QAAA,gBAEvB5D,OAAA;YAAKyE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAAjB,QAAA,gBAC5F5D,OAAA;cAAMwF,CAAC,EAAC,gDAAgD;cAACP,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACO,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC7I3D,OAAA;cAAU2F,MAAM,EAAC,uBAAuB;cAACV,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACO,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1H,CAAC,WAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3D,OAAA;UACEuF,OAAO,EAAExB,QAAS;UAClBS,SAAS,EAAC,eAAe;UAAAZ,QAAA,gBAEzB5D,OAAA;YAAKyE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAAjB,QAAA,gBAC5F5D,OAAA;cAAMwF,CAAC,EAAC,6EAA6E;cAACP,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACO,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC1K3D,OAAA;cAAU2F,MAAM,EAAC,gBAAgB;cAACV,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACO,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC,gBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELjC,OAAO,iBACN1B,OAAA;QAAKwE,SAAS,EAAC,UAAU;QAAAZ,QAAA,gBACvB5D,OAAA;UAAA4D,QAAA,GAAG,YAAU,eAAA5D,OAAA;YAAA4D,QAAA,EAAOlC;UAAO;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvC3D,OAAA;UAAGwE,SAAS,EAAC,eAAe;UAAAZ,QAAA,EAAC;QAE7B;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN,EAEAS,aAAa,IAAI5D,KAAK,iBACrBR,OAAA;QAASwE,SAAS,EAAC,eAAe;QAAAZ,QAAA,gBAChC5D,OAAA;UAAA4D,QAAA,EAAS;QAAoC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACvD3D,OAAA;UAAKwE,SAAS,EAAC,aAAa;UAAAZ,QAAA,gBAC1B5D,OAAA;YAAA4D,QAAA,EAAI;UAAc;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB3D,OAAA;YAAA4D,QAAA,EAAMpD,KAAK,CAACG;UAAO;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAE1B3D,OAAA;YAAA4D,QAAA,EAAI;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB3D,OAAA;YAAA4D,QAAA,EAAMpD,KAAK,CAACI;UAAK;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAEvBlD,SAAS,iBACRT,OAAA,CAAAE,SAAA;YAAA0D,QAAA,gBACE5D,OAAA;cAAA4D,QAAA,EAAI;YAAgB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB3D,OAAA;cAAA4D,QAAA,EAAMnD,SAAS,CAACI;YAAc;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACrC,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAK,EAAA,CAnGMH,aAAa;EAAA,QAEjBhE,MAAM;AAAA;AAAA+F,EAAA,GAFF/B,aAAa;AAoGnB,OAAO,MAAMgC,iBAAiB,GAAGA,CAACzF,SAAS,EAAEkD,QAAQ,KAAK;EACxD,OAAO,SAASwC,gBAAgBA,CAACxF,KAAK,EAAE;IACtC,oBACEN,OAAA,CAACG,aAAa;MAACmD,QAAQ,EAAEA,QAAS;MAAAM,QAAA,eAChC5D,OAAA,CAACI,SAAS;QAAA,GAAKE;MAAK;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEpB,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMoC,eAAe,GAAGA,CAAA,KAAM;EACnC,MAAMC,WAAW,GAAGA,CAACxF,KAAK,EAAEyF,OAAO,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMvF,SAAS,GAAG;MAChBC,OAAO,EAAEH,KAAK,CAACG,OAAO;MACtBC,KAAK,EAAEJ,KAAK,CAACI,KAAK;MAClBqF,OAAO;MACPnF,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAEC,SAAS,CAACD,SAAS;MAC9BE,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI;MACzBC,MAAM,EAAEC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI;IAC5C,CAAC;IAED,IAAI;MACFG,KAAK,CAAC,aAAa,EAAE;QACnBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACvB,SAAS;MAChC,CAAC,CAAC,CAACwB,KAAK,CAACC,GAAG,IAAI;QACdC,OAAO,CAAC5B,KAAK,CAAC,yBAAyB,EAAE2B,GAAG,CAAC;MAC/C,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOA,GAAG,EAAE;MACZC,OAAO,CAAC5B,KAAK,CAAC,8BAA8B,EAAE2B,GAAG,CAAC;IACpD;EACF,CAAC;EAED,OAAO;IAAE6D;EAAY,CAAC;AACxB,CAAC;;AAED;AACA,MAAME,YAAY,GAAGA,CAAC;EAAE1F,KAAK;EAAEsD,OAAO;EAAEqC,SAAS,GAAG;AAAK,CAAC,KAAK;EAC7D,MAAMC,WAAW,GAAG,CAAA5F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,QAAQ,CAAC,YAAY,CAAC,MAAI7F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAMC,cAAc,GAAG,CAAA9F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,QAAQ,CAAC,OAAO,CAAC,MAAI7F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,QAAQ,CAAC,SAAS,CAAC;EAE7E,oBACErG,OAAA;IAAKwE,SAAS,EAAC,4FAA4F;IAAAZ,QAAA,eACzG5D,OAAA;MAAKwE,SAAS,EAAC,sBAAsB;MAAAZ,QAAA,gBAEnC5D,OAAA;QAAKwE,SAAS,EAAC,MAAM;QAAAZ,QAAA,EAClBwC,WAAW,gBACVpG,OAAA;UAAKwE,SAAS,EAAC,+EAA+E;UAAAZ,QAAA,eAC5F5D,OAAA;YAAKwE,SAAS,EAAC,yBAAyB;YAACI,IAAI,EAAC,MAAM;YAACK,MAAM,EAAC,cAAc;YAACN,OAAO,EAAC,WAAW;YAAAf,QAAA,eAC5F5D,OAAA;cAAMyF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACR,WAAW,EAAE,CAAE;cAACM,CAAC,EAAC;YAA2I;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ2C,cAAc,gBAChBtG,OAAA;UAAKwE,SAAS,EAAC,6EAA6E;UAAAZ,QAAA,eAC1F5D,OAAA;YAAKwE,SAAS,EAAC,uBAAuB;YAACI,IAAI,EAAC,MAAM;YAACK,MAAM,EAAC,cAAc;YAACN,OAAO,EAAC,WAAW;YAAAf,QAAA,eAC1F5D,OAAA;cAAMyF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACR,WAAW,EAAE,CAAE;cAACM,CAAC,EAAC;YAAyI;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN3D,OAAA;UAAKwE,SAAS,EAAC,4EAA4E;UAAAZ,QAAA,eACzF5D,OAAA;YAAKwE,SAAS,EAAC,sBAAsB;YAACI,IAAI,EAAC,MAAM;YAACK,MAAM,EAAC,cAAc;YAACN,OAAO,EAAC,WAAW;YAAAf,QAAA,eACzF5D,OAAA;cAAMyF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACR,WAAW,EAAE,CAAE;cAACM,CAAC,EAAC;YAAmD;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN3D,OAAA;QAAIwE,SAAS,EAAC,0CAA0C;QAAAZ,QAAA,EACrDwC,WAAW,GAAG,mBAAmB,GAAGE,cAAc,GAAG,kBAAkB,GAAG;MAAsB;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,eAGL3D,OAAA;QAAGwE,SAAS,EAAC,oBAAoB;QAAAZ,QAAA,EAC9BwC,WAAW,GACR,mFAAmF,GACnFE,cAAc,GACd,0EAA0E,GAC1E9F,KAAK,IAAI;MAAiD;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,EAGHwC,SAAS,iBACRnG,OAAA;QAAKwE,SAAS,EAAC,gDAAgD;QAAAZ,QAAA,GAC5DE,OAAO,iBACN9D,OAAA;UACEuF,OAAO,EAAEzB,OAAQ;UACjBU,SAAS,EAAC,4GAA4G;UAAAZ,QAAA,EAErHwC,WAAW,GAAG,WAAW,GAAG;QAAO;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACT,eACD3D,OAAA;UACEuF,OAAO,EAAEA,CAAA,KAAMnE,MAAM,CAACC,QAAQ,CAACkF,MAAM,CAAC,CAAE;UACxC/B,SAAS,EAAC,6GAA6G;UAAAZ,QAAA,EACxH;QAED;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAyC,WAAW,iBACVpG,OAAA;QAAKwE,SAAS,EAAC,2DAA2D;QAAAZ,QAAA,eACxE5D,OAAA;UAAGwE,SAAS,EAAC,yBAAyB;UAAAZ,QAAA,GAAC,eAClC,eAAA5D,OAAA;YAAA4D,QAAA,EAAQ;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,mFAC1B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC6C,GAAA,GA5EIN,YAAY;AA8ElB,MAAMO,cAAc,GAAGA,CAAC;EAAE9F,OAAO,GAAG;AAAa,CAAC,kBAChDX,OAAA;EAAKwE,SAAS,EAAC,+CAA+C;EAAAZ,QAAA,gBAC5D5D,OAAA;IAAKwE,SAAS,EAAC;EAAsE;IAAAhB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,eAC5F3D,OAAA;IAAGwE,SAAS,EAAC,2BAA2B;IAAAZ,QAAA,EAAEjD;EAAO;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACnD,CACN;AAAC+C,GAAA,GALID,cAAc;AAOpB,SAASP,YAAY,EAAEO,cAAc;AACrC,eAAetG,aAAa;AAAC,IAAAyF,EAAA,EAAAY,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAf,EAAA;AAAAe,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}