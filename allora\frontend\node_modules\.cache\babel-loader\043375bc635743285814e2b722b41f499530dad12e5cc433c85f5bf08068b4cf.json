{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\contexts\\\\WebSocketContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react';\nimport { useNotification } from './NotificationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WebSocketContext = /*#__PURE__*/createContext();\nexport const useWebSocket = () => {\n  _s();\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocket must be used within a WebSocketProvider');\n  }\n  return context;\n};\n_s(useWebSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const WebSocketProvider = ({\n  children\n}) => {\n  _s2();\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState('disconnected');\n  const [lastMessage, setLastMessage] = useState(null);\n  const [messageHistory, setMessageHistory] = useState([]);\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const reconnectAttempts = useRef(0);\n  const maxReconnectAttempts = 5;\n  const {\n    info,\n    error: showError\n  } = useNotification();\n\n  // Event listeners for different message types\n  const eventListeners = useRef({\n    cart_updated: [],\n    inventory_updated: [],\n    order_status_changed: [],\n    notification: [],\n    user_activity: []\n  });\n  const getWebSocketUrl = useCallback(() => {\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const host = window.location.host;\n    const token = localStorage.getItem('token');\n    const guestSession = localStorage.getItem('guest_session_id');\n    let url = `${protocol}//${host}/ws`;\n    const params = new URLSearchParams();\n    if (token) {\n      params.append('token', token);\n    } else if (guestSession) {\n      params.append('guest_session', guestSession);\n    }\n    if (params.toString()) {\n      url += `?${params.toString()}`;\n    }\n    return url;\n  }, []);\n  const connect = useCallback(() => {\n    var _wsRef$current;\n    if (((_wsRef$current = wsRef.current) === null || _wsRef$current === void 0 ? void 0 : _wsRef$current.readyState) === WebSocket.OPEN) {\n      return;\n    }\n    try {\n      setConnectionStatus('connecting');\n      const wsUrl = getWebSocketUrl();\n      wsRef.current = new WebSocket(wsUrl);\n      wsRef.current.onopen = () => {\n        var _wsRef$current2;\n        console.log('WebSocket connected');\n        setIsConnected(true);\n        setConnectionStatus('connected');\n        reconnectAttempts.current = 0;\n\n        // Send initial connection message\n        const connectionMessage = {\n          type: 'connection',\n          timestamp: new Date().toISOString(),\n          user_agent: navigator.userAgent\n        };\n        if (((_wsRef$current2 = wsRef.current) === null || _wsRef$current2 === void 0 ? void 0 : _wsRef$current2.readyState) === WebSocket.OPEN) {\n          wsRef.current.send(JSON.stringify(connectionMessage));\n        }\n      };\n      wsRef.current.onmessage = event => {\n        try {\n          const message = JSON.parse(event.data);\n          setLastMessage(message);\n          setMessageHistory(prev => [...prev.slice(-99), message]); // Keep last 100 messages\n\n          // Trigger event listeners\n          const messageType = message.type || message.event;\n          if (eventListeners.current[messageType]) {\n            eventListeners.current[messageType].forEach(listener => {\n              try {\n                listener(message);\n              } catch (error) {\n                console.error('Error in WebSocket event listener:', error);\n              }\n            });\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      wsRef.current.onclose = event => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setIsConnected(false);\n        setConnectionStatus('disconnected');\n\n        // Attempt to reconnect if not a normal closure\n        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {\n          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);\n          setConnectionStatus('reconnecting');\n          reconnectTimeoutRef.current = setTimeout(() => {\n            reconnectAttempts.current++;\n            connect();\n          }, delay);\n        } else if (reconnectAttempts.current >= maxReconnectAttempts) {\n          setConnectionStatus('failed');\n          showError('Connection lost. Please refresh the page to reconnect.');\n        }\n      };\n      wsRef.current.onerror = error => {\n        console.error('WebSocket error:', error);\n        setConnectionStatus('error');\n      };\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionStatus('error');\n    }\n  }, [getWebSocketUrl, showError]);\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'User disconnected');\n      wsRef.current = null;\n    }\n    setIsConnected(false);\n    setConnectionStatus('disconnected');\n  }, []);\n  const sendMessage = useCallback(message => {\n    var _wsRef$current3;\n    if (((_wsRef$current3 = wsRef.current) === null || _wsRef$current3 === void 0 ? void 0 : _wsRef$current3.readyState) === WebSocket.OPEN) {\n      try {\n        const messageWithTimestamp = {\n          ...message,\n          timestamp: new Date().toISOString()\n        };\n        wsRef.current.send(JSON.stringify(messageWithTimestamp));\n        return true;\n      } catch (error) {\n        console.error('Error sending WebSocket message:', error);\n        return false;\n      }\n    } else {\n      console.warn('WebSocket is not connected');\n      return false;\n    }\n  }, []);\n  const addEventListener = useCallback((eventType, listener) => {\n    if (!eventListeners.current[eventType]) {\n      eventListeners.current[eventType] = [];\n    }\n    eventListeners.current[eventType].push(listener);\n\n    // Return cleanup function\n    return () => {\n      eventListeners.current[eventType] = eventListeners.current[eventType].filter(l => l !== listener);\n    };\n  }, []);\n  const removeEventListener = useCallback((eventType, listener) => {\n    if (eventListeners.current[eventType]) {\n      eventListeners.current[eventType] = eventListeners.current[eventType].filter(l => l !== listener);\n    }\n  }, []);\n\n  // Auto-connect on mount and auth changes\n  useEffect(() => {\n    connect();\n\n    // Listen for auth changes\n    const handleStorageChange = e => {\n      if (e.key === 'token' || e.key === 'guest_session_id') {\n        disconnect();\n        setTimeout(connect, 1000); // Reconnect with new credentials\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      disconnect();\n    };\n  }, [connect, disconnect]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n  const value = {\n    isConnected,\n    connectionStatus,\n    lastMessage,\n    messageHistory,\n    connect,\n    disconnect,\n    sendMessage,\n    addEventListener,\n    removeEventListener\n  };\n  return /*#__PURE__*/_jsxDEV(WebSocketContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n};\n_s2(WebSocketProvider, \"rNxCrGPFGFa7/EDCWEFJ8AM9eho=\", false, function () {\n  return [useNotification];\n});\n_c = WebSocketProvider;\nexport default WebSocketContext;\nvar _c;\n$RefreshReg$(_c, \"WebSocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useRef", "useState", "useCallback", "useNotification", "jsxDEV", "_jsxDEV", "WebSocketContext", "useWebSocket", "_s", "context", "Error", "WebSocketProvider", "children", "_s2", "isConnected", "setIsConnected", "connectionStatus", "setConnectionStatus", "lastMessage", "setLastMessage", "messageHistory", "setMessageHistory", "wsRef", "reconnectTimeoutRef", "reconnectAttempts", "maxReconnectAttempts", "info", "error", "showError", "eventListeners", "cart_updated", "inventory_updated", "order_status_changed", "notification", "user_activity", "getWebSocketUrl", "protocol", "window", "location", "host", "token", "localStorage", "getItem", "guestSession", "url", "params", "URLSearchParams", "append", "toString", "connect", "_wsRef$current", "current", "readyState", "WebSocket", "OPEN", "wsUrl", "onopen", "_wsRef$current2", "console", "log", "connectionMessage", "type", "timestamp", "Date", "toISOString", "user_agent", "navigator", "userAgent", "send", "JSON", "stringify", "onmessage", "event", "message", "parse", "data", "prev", "slice", "messageType", "for<PERSON>ach", "listener", "onclose", "code", "reason", "delay", "Math", "min", "pow", "setTimeout", "onerror", "disconnect", "clearTimeout", "close", "sendMessage", "_wsRef$current3", "messageWithTimestamp", "warn", "addEventListener", "eventType", "push", "filter", "l", "removeEventListener", "handleStorageChange", "e", "key", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/contexts/WebSocketContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react';\nimport { useNotification } from './NotificationContext';\n\nconst WebSocketContext = createContext();\n\nexport const useWebSocket = () => {\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocket must be used within a WebSocketProvider');\n  }\n  return context;\n};\n\nexport const WebSocketProvider = ({ children }) => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState('disconnected');\n  const [lastMessage, setLastMessage] = useState(null);\n  const [messageHistory, setMessageHistory] = useState([]);\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const reconnectAttempts = useRef(0);\n  const maxReconnectAttempts = 5;\n  const { info, error: showError } = useNotification();\n\n  // Event listeners for different message types\n  const eventListeners = useRef({\n    cart_updated: [],\n    inventory_updated: [],\n    order_status_changed: [],\n    notification: [],\n    user_activity: []\n  });\n\n  const getWebSocketUrl = useCallback(() => {\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const host = window.location.host;\n    const token = localStorage.getItem('token');\n    const guestSession = localStorage.getItem('guest_session_id');\n    \n    let url = `${protocol}//${host}/ws`;\n    const params = new URLSearchParams();\n    \n    if (token) {\n      params.append('token', token);\n    } else if (guestSession) {\n      params.append('guest_session', guestSession);\n    }\n    \n    if (params.toString()) {\n      url += `?${params.toString()}`;\n    }\n    \n    return url;\n  }, []);\n\n  const connect = useCallback(() => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      return;\n    }\n\n    try {\n      setConnectionStatus('connecting');\n      const wsUrl = getWebSocketUrl();\n      wsRef.current = new WebSocket(wsUrl);\n\n      wsRef.current.onopen = () => {\n        console.log('WebSocket connected');\n        setIsConnected(true);\n        setConnectionStatus('connected');\n        reconnectAttempts.current = 0;\n        \n        // Send initial connection message\n        const connectionMessage = {\n          type: 'connection',\n          timestamp: new Date().toISOString(),\n          user_agent: navigator.userAgent\n        };\n        \n        if (wsRef.current?.readyState === WebSocket.OPEN) {\n          wsRef.current.send(JSON.stringify(connectionMessage));\n        }\n      };\n\n      wsRef.current.onmessage = (event) => {\n        try {\n          const message = JSON.parse(event.data);\n          setLastMessage(message);\n          setMessageHistory(prev => [...prev.slice(-99), message]); // Keep last 100 messages\n          \n          // Trigger event listeners\n          const messageType = message.type || message.event;\n          if (eventListeners.current[messageType]) {\n            eventListeners.current[messageType].forEach(listener => {\n              try {\n                listener(message);\n              } catch (error) {\n                console.error('Error in WebSocket event listener:', error);\n              }\n            });\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n\n      wsRef.current.onclose = (event) => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setIsConnected(false);\n        setConnectionStatus('disconnected');\n        \n        // Attempt to reconnect if not a normal closure\n        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {\n          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);\n          setConnectionStatus('reconnecting');\n          \n          reconnectTimeoutRef.current = setTimeout(() => {\n            reconnectAttempts.current++;\n            connect();\n          }, delay);\n        } else if (reconnectAttempts.current >= maxReconnectAttempts) {\n          setConnectionStatus('failed');\n          showError('Connection lost. Please refresh the page to reconnect.');\n        }\n      };\n\n      wsRef.current.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setConnectionStatus('error');\n      };\n\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionStatus('error');\n    }\n  }, [getWebSocketUrl, showError]);\n\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n    \n    if (wsRef.current) {\n      wsRef.current.close(1000, 'User disconnected');\n      wsRef.current = null;\n    }\n    \n    setIsConnected(false);\n    setConnectionStatus('disconnected');\n  }, []);\n\n  const sendMessage = useCallback((message) => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      try {\n        const messageWithTimestamp = {\n          ...message,\n          timestamp: new Date().toISOString()\n        };\n        wsRef.current.send(JSON.stringify(messageWithTimestamp));\n        return true;\n      } catch (error) {\n        console.error('Error sending WebSocket message:', error);\n        return false;\n      }\n    } else {\n      console.warn('WebSocket is not connected');\n      return false;\n    }\n  }, []);\n\n  const addEventListener = useCallback((eventType, listener) => {\n    if (!eventListeners.current[eventType]) {\n      eventListeners.current[eventType] = [];\n    }\n    eventListeners.current[eventType].push(listener);\n    \n    // Return cleanup function\n    return () => {\n      eventListeners.current[eventType] = eventListeners.current[eventType].filter(\n        l => l !== listener\n      );\n    };\n  }, []);\n\n  const removeEventListener = useCallback((eventType, listener) => {\n    if (eventListeners.current[eventType]) {\n      eventListeners.current[eventType] = eventListeners.current[eventType].filter(\n        l => l !== listener\n      );\n    }\n  }, []);\n\n  // Auto-connect on mount and auth changes\n  useEffect(() => {\n    connect();\n    \n    // Listen for auth changes\n    const handleStorageChange = (e) => {\n      if (e.key === 'token' || e.key === 'guest_session_id') {\n        disconnect();\n        setTimeout(connect, 1000); // Reconnect with new credentials\n      }\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    \n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      disconnect();\n    };\n  }, [connect, disconnect]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  const value = {\n    isConnected,\n    connectionStatus,\n    lastMessage,\n    messageHistory,\n    connect,\n    disconnect,\n    sendMessage,\n    addEventListener,\n    removeEventListener\n  };\n\n  return (\n    <WebSocketContext.Provider value={value}>\n      {children}\n    </WebSocketContext.Provider>\n  );\n};\n\nexport default WebSocketContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAClG,SAASC,eAAe,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,gBAAgB,gBAAGT,aAAa,CAAC,CAAC;AAExC,OAAO,MAAMU,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,OAAO,GAAGX,UAAU,CAACQ,gBAAgB,CAAC;EAC5C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,YAAY;AAQzB,OAAO,MAAMI,iBAAiB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACjD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAAC,cAAc,CAAC;EACxE,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMqB,KAAK,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMuB,mBAAmB,GAAGvB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMwB,iBAAiB,GAAGxB,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMyB,oBAAoB,GAAG,CAAC;EAC9B,MAAM;IAAEC,IAAI;IAAEC,KAAK,EAAEC;EAAU,CAAC,GAAGzB,eAAe,CAAC,CAAC;;EAEpD;EACA,MAAM0B,cAAc,GAAG7B,MAAM,CAAC;IAC5B8B,YAAY,EAAE,EAAE;IAChBC,iBAAiB,EAAE,EAAE;IACrBC,oBAAoB,EAAE,EAAE;IACxBC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAMC,eAAe,GAAGjC,WAAW,CAAC,MAAM;IACxC,MAAMkC,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACF,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;IACvE,MAAMG,IAAI,GAAGF,MAAM,CAACC,QAAQ,CAACC,IAAI;IACjC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IAE7D,IAAIE,GAAG,GAAG,GAAGR,QAAQ,KAAKG,IAAI,KAAK;IACnC,MAAMM,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpC,IAAIN,KAAK,EAAE;MACTK,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEP,KAAK,CAAC;IAC/B,CAAC,MAAM,IAAIG,YAAY,EAAE;MACvBE,MAAM,CAACE,MAAM,CAAC,eAAe,EAAEJ,YAAY,CAAC;IAC9C;IAEA,IAAIE,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE;MACrBJ,GAAG,IAAI,IAAIC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE;IAChC;IAEA,OAAOJ,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,OAAO,GAAG/C,WAAW,CAAC,MAAM;IAAA,IAAAgD,cAAA;IAChC,IAAI,EAAAA,cAAA,GAAA5B,KAAK,CAAC6B,OAAO,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;MAChD;IACF;IAEA,IAAI;MACFrC,mBAAmB,CAAC,YAAY,CAAC;MACjC,MAAMsC,KAAK,GAAGpB,eAAe,CAAC,CAAC;MAC/Bb,KAAK,CAAC6B,OAAO,GAAG,IAAIE,SAAS,CAACE,KAAK,CAAC;MAEpCjC,KAAK,CAAC6B,OAAO,CAACK,MAAM,GAAG,MAAM;QAAA,IAAAC,eAAA;QAC3BC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAClC5C,cAAc,CAAC,IAAI,CAAC;QACpBE,mBAAmB,CAAC,WAAW,CAAC;QAChCO,iBAAiB,CAAC2B,OAAO,GAAG,CAAC;;QAE7B;QACA,MAAMS,iBAAiB,GAAG;UACxBC,IAAI,EAAE,YAAY;UAClBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACnCC,UAAU,EAAEC,SAAS,CAACC;QACxB,CAAC;QAED,IAAI,EAAAV,eAAA,GAAAnC,KAAK,CAAC6B,OAAO,cAAAM,eAAA,uBAAbA,eAAA,CAAeL,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;UAChDhC,KAAK,CAAC6B,OAAO,CAACiB,IAAI,CAACC,IAAI,CAACC,SAAS,CAACV,iBAAiB,CAAC,CAAC;QACvD;MACF,CAAC;MAEDtC,KAAK,CAAC6B,OAAO,CAACoB,SAAS,GAAIC,KAAK,IAAK;QACnC,IAAI;UACF,MAAMC,OAAO,GAAGJ,IAAI,CAACK,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;UACtCxD,cAAc,CAACsD,OAAO,CAAC;UACvBpD,iBAAiB,CAACuD,IAAI,IAAI,CAAC,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEJ,OAAO,CAAC,CAAC,CAAC,CAAC;;UAE1D;UACA,MAAMK,WAAW,GAAGL,OAAO,CAACZ,IAAI,IAAIY,OAAO,CAACD,KAAK;UACjD,IAAI3C,cAAc,CAACsB,OAAO,CAAC2B,WAAW,CAAC,EAAE;YACvCjD,cAAc,CAACsB,OAAO,CAAC2B,WAAW,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;cACtD,IAAI;gBACFA,QAAQ,CAACP,OAAO,CAAC;cACnB,CAAC,CAAC,OAAO9C,KAAK,EAAE;gBACd+B,OAAO,CAAC/B,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;cAC5D;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOA,KAAK,EAAE;UACd+B,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF,CAAC;MAEDL,KAAK,CAAC6B,OAAO,CAAC8B,OAAO,GAAIT,KAAK,IAAK;QACjCd,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEa,KAAK,CAACU,IAAI,EAAEV,KAAK,CAACW,MAAM,CAAC;QAChEpE,cAAc,CAAC,KAAK,CAAC;QACrBE,mBAAmB,CAAC,cAAc,CAAC;;QAEnC;QACA,IAAIuD,KAAK,CAACU,IAAI,KAAK,IAAI,IAAI1D,iBAAiB,CAAC2B,OAAO,GAAG1B,oBAAoB,EAAE;UAC3E,MAAM2D,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE/D,iBAAiB,CAAC2B,OAAO,CAAC,EAAE,KAAK,CAAC;UAC5ElC,mBAAmB,CAAC,cAAc,CAAC;UAEnCM,mBAAmB,CAAC4B,OAAO,GAAGqC,UAAU,CAAC,MAAM;YAC7ChE,iBAAiB,CAAC2B,OAAO,EAAE;YAC3BF,OAAO,CAAC,CAAC;UACX,CAAC,EAAEmC,KAAK,CAAC;QACX,CAAC,MAAM,IAAI5D,iBAAiB,CAAC2B,OAAO,IAAI1B,oBAAoB,EAAE;UAC5DR,mBAAmB,CAAC,QAAQ,CAAC;UAC7BW,SAAS,CAAC,wDAAwD,CAAC;QACrE;MACF,CAAC;MAEDN,KAAK,CAAC6B,OAAO,CAACsC,OAAO,GAAI9D,KAAK,IAAK;QACjC+B,OAAO,CAAC/B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxCV,mBAAmB,CAAC,OAAO,CAAC;MAC9B,CAAC;IAEH,CAAC,CAAC,OAAOU,KAAK,EAAE;MACd+B,OAAO,CAAC/B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DV,mBAAmB,CAAC,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACkB,eAAe,EAAEP,SAAS,CAAC,CAAC;EAEhC,MAAM8D,UAAU,GAAGxF,WAAW,CAAC,MAAM;IACnC,IAAIqB,mBAAmB,CAAC4B,OAAO,EAAE;MAC/BwC,YAAY,CAACpE,mBAAmB,CAAC4B,OAAO,CAAC;MACzC5B,mBAAmB,CAAC4B,OAAO,GAAG,IAAI;IACpC;IAEA,IAAI7B,KAAK,CAAC6B,OAAO,EAAE;MACjB7B,KAAK,CAAC6B,OAAO,CAACyC,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;MAC9CtE,KAAK,CAAC6B,OAAO,GAAG,IAAI;IACtB;IAEApC,cAAc,CAAC,KAAK,CAAC;IACrBE,mBAAmB,CAAC,cAAc,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4E,WAAW,GAAG3F,WAAW,CAAEuE,OAAO,IAAK;IAAA,IAAAqB,eAAA;IAC3C,IAAI,EAAAA,eAAA,GAAAxE,KAAK,CAAC6B,OAAO,cAAA2C,eAAA,uBAAbA,eAAA,CAAe1C,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;MAChD,IAAI;QACF,MAAMyC,oBAAoB,GAAG;UAC3B,GAAGtB,OAAO;UACVX,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC;QACD1C,KAAK,CAAC6B,OAAO,CAACiB,IAAI,CAACC,IAAI,CAACC,SAAS,CAACyB,oBAAoB,CAAC,CAAC;QACxD,OAAO,IAAI;MACb,CAAC,CAAC,OAAOpE,KAAK,EAAE;QACd+B,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,OAAO,KAAK;MACd;IACF,CAAC,MAAM;MACL+B,OAAO,CAACsC,IAAI,CAAC,4BAA4B,CAAC;MAC1C,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAG/F,WAAW,CAAC,CAACgG,SAAS,EAAElB,QAAQ,KAAK;IAC5D,IAAI,CAACnD,cAAc,CAACsB,OAAO,CAAC+C,SAAS,CAAC,EAAE;MACtCrE,cAAc,CAACsB,OAAO,CAAC+C,SAAS,CAAC,GAAG,EAAE;IACxC;IACArE,cAAc,CAACsB,OAAO,CAAC+C,SAAS,CAAC,CAACC,IAAI,CAACnB,QAAQ,CAAC;;IAEhD;IACA,OAAO,MAAM;MACXnD,cAAc,CAACsB,OAAO,CAAC+C,SAAS,CAAC,GAAGrE,cAAc,CAACsB,OAAO,CAAC+C,SAAS,CAAC,CAACE,MAAM,CAC1EC,CAAC,IAAIA,CAAC,KAAKrB,QACb,CAAC;IACH,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMsB,mBAAmB,GAAGpG,WAAW,CAAC,CAACgG,SAAS,EAAElB,QAAQ,KAAK;IAC/D,IAAInD,cAAc,CAACsB,OAAO,CAAC+C,SAAS,CAAC,EAAE;MACrCrE,cAAc,CAACsB,OAAO,CAAC+C,SAAS,CAAC,GAAGrE,cAAc,CAACsB,OAAO,CAAC+C,SAAS,CAAC,CAACE,MAAM,CAC1EC,CAAC,IAAIA,CAAC,KAAKrB,QACb,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjF,SAAS,CAAC,MAAM;IACdkD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMsD,mBAAmB,GAAIC,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,kBAAkB,EAAE;QACrDf,UAAU,CAAC,CAAC;QACZF,UAAU,CAACvC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;MAC7B;IACF,CAAC;IAEDZ,MAAM,CAAC4D,gBAAgB,CAAC,SAAS,EAAEM,mBAAmB,CAAC;IAEvD,OAAO,MAAM;MACXlE,MAAM,CAACiE,mBAAmB,CAAC,SAAS,EAAEC,mBAAmB,CAAC;MAC1Db,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACzC,OAAO,EAAEyC,UAAU,CAAC,CAAC;;EAEzB;EACA3F,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX2F,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMgB,KAAK,GAAG;IACZ5F,WAAW;IACXE,gBAAgB;IAChBE,WAAW;IACXE,cAAc;IACd6B,OAAO;IACPyC,UAAU;IACVG,WAAW;IACXI,gBAAgB;IAChBK;EACF,CAAC;EAED,oBACEjG,OAAA,CAACC,gBAAgB,CAACqG,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA9F,QAAA,EACrCA;EAAQ;IAAAgG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;AAAClG,GAAA,CA/NWF,iBAAiB;EAAA,QASOR,eAAe;AAAA;AAAA6G,EAAA,GATvCrG,iBAAiB;AAiO9B,eAAeL,gBAAgB;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}