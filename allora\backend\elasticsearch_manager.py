"""
Elasticsearch Index Management and Data Synchronization
Handles index creation, data sync, and maintenance operations
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Generator
from elasticsearch import helpers
from elasticsearch.exceptions import NotFoundError, RequestError

from elasticsearch_config import (
    get_elasticsearch_client, 
    es_config,
    get_product_index_name,
    get_search_analytics_index_name,
    get_suggestions_index_name
)

logger = logging.getLogger(__name__)

class ElasticsearchManager:
    """Manages Elasticsearch operations and data synchronization"""
    
    def __init__(self):
        self.es = get_elasticsearch_client()
        self.product_index = get_product_index_name()
        self.analytics_index = get_search_analytics_index_name()
        self.suggestions_index = get_suggestions_index_name()
    
    def create_indices(self) -> bool:
        """Create all required Elasticsearch indices"""
        try:
            # Create product index
            if not self.es.indices.exists(index=self.product_index):
                self.es.indices.create(
                    index=self.product_index,
                    body=es_config.get_product_index_mapping()
                )
                logger.info(f"Created product index: {self.product_index}")
            
            # Create search analytics index
            if not self.es.indices.exists(index=self.analytics_index):
                self.es.indices.create(
                    index=self.analytics_index,
                    body=es_config.get_search_analytics_mapping()
                )
                logger.info(f"Created analytics index: {self.analytics_index}")
            
            # Create suggestions index
            if not self.es.indices.exists(index=self.suggestions_index):
                self.es.indices.create(
                    index=self.suggestions_index,
                    body=es_config.get_suggestions_mapping()
                )
                logger.info(f"Created suggestions index: {self.suggestions_index}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating indices: {e}")
            return False
    
    def delete_indices(self) -> bool:
        """Delete all Elasticsearch indices (use with caution)"""
        try:
            indices = [self.product_index, self.analytics_index, self.suggestions_index]
            for index in indices:
                if self.es.indices.exists(index=index):
                    self.es.indices.delete(index=index)
                    logger.info(f"Deleted index: {index}")
            return True
        except Exception as e:
            logger.error(f"Error deleting indices: {e}")
            return False
    
    def reindex_products(self) -> bool:
        """Reindex all products from database to Elasticsearch"""
        try:
            # Import here to avoid circular imports
            from app import Product, ProductReview, ProductImage, ProductVariant, Seller, SellerStore
            from app import db
            
            logger.info("Starting product reindexing...")
            
            # Get all products with related data
            products = db.session.query(Product).all()
            
            # Prepare bulk indexing data
            actions = []
            for product in products:
                doc = self._prepare_product_document(product)
                if doc:
                    actions.append({
                        '_index': self.product_index,
                        '_id': product.id,
                        '_source': doc
                    })
            
            # Bulk index products
            if actions:
                success_count, failed_items = helpers.bulk(
                    self.es,
                    actions,
                    chunk_size=100,
                    request_timeout=60
                )
                logger.info(f"Successfully indexed {success_count} products")
                
                if failed_items:
                    logger.warning(f"Failed to index {len(failed_items)} products")
                    for item in failed_items[:5]:  # Log first 5 failures
                        logger.warning(f"Failed item: {item}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error reindexing products: {e}")
            return False
    
    def _prepare_product_document(self, product) -> Optional[Dict[str, Any]]:
        """Prepare product document for Elasticsearch indexing"""
        try:
            # Import here to avoid circular imports
            from app import ProductReview, ProductImage, ProductVariant, Seller, SellerStore, db
            
            # Get seller information
            seller = None
            store = None
            if product.seller_id:
                seller = Seller.query.get(product.seller_id)
                if seller:
                    store = SellerStore.query.filter_by(seller_id=seller.id).first()
            
            # Get product reviews for ratings
            reviews = ProductReview.query.filter_by(product_id=product.id).all()
            total_reviews = len(reviews)
            average_rating = sum(r.rating for r in reviews) / total_reviews if total_reviews > 0 else 0
            
            # Calculate rating distribution
            rating_dist = {f"{i}_star": 0 for i in range(1, 6)}
            for review in reviews:
                rating_dist[f"{int(review.rating)}_star"] += 1
            
            # Get product images
            images = ProductImage.query.filter_by(product_id=product.id).all()
            primary_image = next((img.image_url for img in images if img.is_primary), None)
            if not primary_image and images:
                primary_image = images[0].image_url
            image_urls = [img.image_url for img in images]
            
            # Get product variants
            variants = ProductVariant.query.filter_by(product_id=product.id, is_available=True).all()
            variant_data = []
            for variant in variants:
                variant_data.append({
                    'id': variant.id,
                    'type': variant.variant_type,
                    'value': variant.variant_value,
                    'price_adjustment': float(variant.price_adjustment),
                    'stock_quantity': variant.stock_quantity
                })
            
            # Calculate derived fields
            discount_percentage = 0
            if hasattr(product, 'original_price') and product.original_price and product.original_price > product.price:
                discount_percentage = ((product.original_price - product.price) / product.original_price) * 100
            
            # Prepare search keywords
            search_keywords = []
            if product.name:
                search_keywords.extend(product.name.lower().split())
            if product.category:
                search_keywords.extend(product.category.lower().split())
            if hasattr(product, 'brand') and product.brand:
                search_keywords.extend(product.brand.lower().split())
            if hasattr(product, 'description') and product.description:
                search_keywords.extend(product.description.lower().split()[:20])  # Limit description words
            
            # Build document
            doc = {
                'id': product.id,
                'name': product.name,
                'description': getattr(product, 'description', ''),
                'category': getattr(product, 'category', ''),
                'brand': getattr(product, 'brand', ''),
                'tags': getattr(product, 'tags', []) if hasattr(product, 'tags') else [],
                
                # Pricing
                'price': float(product.price),
                'original_price': float(getattr(product, 'original_price', product.price)),
                'discount_percentage': discount_percentage,
                'currency': 'INR',
                'stock_quantity': product.stock_quantity,
                'is_in_stock': product.stock_quantity > 0,
                'low_stock_threshold': getattr(product, 'low_stock_threshold', 10),
                
                # Ratings
                'average_rating': round(average_rating, 2),
                'total_reviews': total_reviews,
                'rating_distribution': rating_dist,
                
                # Sustainability
                'sustainability_score': product.sustainability_score,
                'eco_certifications': getattr(product, 'eco_certifications', []),
                'carbon_footprint': getattr(product, 'carbon_footprint', 0),
                'recyclable': getattr(product, 'recyclable', False),
                'organic': getattr(product, 'organic', False),
                'fair_trade': getattr(product, 'fair_trade', False),
                
                # Seller info
                'seller_id': product.seller_id,
                'seller_name': seller.business_name if seller else 'Allora',
                'seller_rating': float(seller.rating) if seller and seller.rating else 0,
                'seller_verified': seller.status == 'approved' if seller else True,
                'store_name': store.store_name if store else 'Allora Store',
                
                # Product attributes and variants
                'attributes': [],  # Will be populated if attribute system exists
                'variants': variant_data,
                
                # Images
                'primary_image': primary_image or product.image,
                'image_urls': image_urls or [product.image],
                'has_video': False,  # Will be updated when video support is added
                
                # Sales metrics (mock data for now)
                'total_sales': getattr(product, 'total_sales', 0),
                'sales_rank': getattr(product, 'sales_rank', 0),
                'popularity_score': average_rating * total_reviews * 0.1,
                'trending_score': 0,  # Will be calculated based on recent sales
                'view_count': getattr(product, 'view_count', 0),
                'wishlist_count': getattr(product, 'wishlist_count', 0),
                
                # Shipping
                'free_shipping': getattr(product, 'free_shipping', True),
                'shipping_weight': getattr(product, 'shipping_weight', 0.5),
                'shipping_dimensions': {
                    'length': getattr(product, 'length', 10),
                    'width': getattr(product, 'width', 10),
                    'height': getattr(product, 'height', 5)
                },
                'estimated_delivery_days': getattr(product, 'estimated_delivery_days', 7),
                
                # Temporal
                'created_at': product.created_at.isoformat() if hasattr(product, 'created_at') and product.created_at else datetime.utcnow().isoformat(),
                'updated_at': product.updated_at.isoformat() if hasattr(product, 'updated_at') and product.updated_at else datetime.utcnow().isoformat(),
                'last_indexed_at': datetime.utcnow().isoformat(),
                
                # Search optimization
                'search_keywords': ' '.join(set(search_keywords)),
                'boost_score': 1.0,
                'featured': getattr(product, 'featured', False),
                'promoted': getattr(product, 'promoted', False),
                
                # Location
                'origin_country': getattr(product, 'origin_country', 'India'),
                'available_regions': getattr(product, 'available_regions', ['India']),
                
                # Advanced features
                'ai_generated_tags': [],  # Will be populated by AI tagging system
                # 'visual_features': []  # Will be populated by visual feature extraction
            }
            
            return doc
            
        except Exception as e:
            logger.error(f"Error preparing document for product {product.id}: {e}")
            return None
    
    def index_single_product(self, product_id: int) -> bool:
        """Index a single product"""
        try:
            from app import Product
            
            product = Product.query.get(product_id)
            if not product:
                logger.warning(f"Product {product_id} not found")
                return False
            
            doc = self._prepare_product_document(product)
            if doc:
                self.es.index(
                    index=self.product_index,
                    id=product_id,
                    body=doc
                )
                logger.info(f"Successfully indexed product {product_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error indexing product {product_id}: {e}")
            return False
    
    def delete_product(self, product_id: int) -> bool:
        """Delete a product from Elasticsearch"""
        try:
            self.es.delete(
                index=self.product_index,
                id=product_id,
                ignore=[404]
            )
            logger.info(f"Deleted product {product_id} from index")
            return True
        except Exception as e:
            logger.error(f"Error deleting product {product_id}: {e}")
            return False
    
    def update_suggestions(self) -> bool:
        """Update search suggestions based on product data and search analytics"""
        try:
            # Clear existing suggestions
            self.es.delete_by_query(
                index=self.suggestions_index,
                body={"query": {"match_all": {}}}
            )
            
            # Generate suggestions from products
            from app import Product, db
            
            suggestions = []
            
            # Product name suggestions
            products = db.session.query(Product.name, Product.category).distinct().all()
            for product in products:
                if product.name:
                    suggestions.append({
                        '_index': self.suggestions_index,
                        '_source': {
                            'text': {
                                'input': product.name,
                                'contexts': {'category': [product.category] if product.category else []}
                            },
                            'type': 'product',
                            'weight': 10,
                            'payload': {'name': product.name, 'category': product.category},
                            'created_at': datetime.utcnow().isoformat()
                        }
                    })
            
            # Category suggestions
            categories = db.session.query(Product.category).distinct().all()
            for category in categories:
                if category.category:
                    suggestions.append({
                        '_index': self.suggestions_index,
                        '_source': {
                            'text': {
                                'input': category.category,
                                'contexts': {'category': [category.category]}
                            },
                            'type': 'category',
                            'weight': 15,
                            'payload': {'category': category.category},
                            'created_at': datetime.utcnow().isoformat()
                        }
                    })
            
            # Bulk index suggestions
            if suggestions:
                helpers.bulk(self.es, suggestions, chunk_size=100)
                logger.info(f"Updated {len(suggestions)} search suggestions")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating suggestions: {e}")
            return False
    
    def get_index_stats(self) -> Dict[str, Any]:
        """Get statistics for all indices"""
        try:
            stats = {}
            indices = [self.product_index, self.analytics_index, self.suggestions_index]
            
            for index in indices:
                if self.es.indices.exists(index=index):
                    index_stats = self.es.indices.stats(index=index)
                    stats[index] = {
                        'document_count': index_stats['indices'][index]['total']['docs']['count'],
                        'size_in_bytes': index_stats['indices'][index]['total']['store']['size_in_bytes'],
                        'health': self.es.cluster.health(index=index)['status']
                    }
                else:
                    stats[index] = {'status': 'not_exists'}
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting index stats: {e}")
            return {}

# Global manager instance
es_manager = ElasticsearchManager()

def get_elasticsearch_manager() -> ElasticsearchManager:
    """Get global Elasticsearch manager instance"""
    return es_manager

class ElasticsearchSyncManager:
    """Handles real-time synchronization between PostgreSQL and Elasticsearch"""

    def __init__(self):
        self.es_manager = get_elasticsearch_manager()

    def sync_product_create(self, product_id: int) -> bool:
        """Sync product creation to Elasticsearch"""
        try:
            return self.es_manager.index_single_product(product_id)
        except Exception as e:
            logger.error(f"Error syncing product creation {product_id}: {e}")
            return False

    def sync_product_update(self, product_id: int) -> bool:
        """Sync product update to Elasticsearch"""
        try:
            return self.es_manager.index_single_product(product_id)
        except Exception as e:
            logger.error(f"Error syncing product update {product_id}: {e}")
            return False

    def sync_product_delete(self, product_id: int) -> bool:
        """Sync product deletion to Elasticsearch"""
        try:
            return self.es_manager.delete_product(product_id)
        except Exception as e:
            logger.error(f"Error syncing product deletion {product_id}: {e}")
            return False

    def sync_inventory_update(self, product_id: int, new_stock: int) -> bool:
        """Sync inventory updates to Elasticsearch"""
        try:
            # Update only stock-related fields
            doc = {
                'stock_quantity': new_stock,
                'is_in_stock': new_stock > 0,
                'last_indexed_at': datetime.utcnow().isoformat()
            }

            self.es_manager.es.update(
                index=self.es_manager.product_index,
                id=product_id,
                body={'doc': doc},
                ignore=[404]
            )

            logger.info(f"Updated inventory for product {product_id}: {new_stock}")
            return True

        except Exception as e:
            logger.error(f"Error syncing inventory update {product_id}: {e}")
            return False

    def sync_rating_update(self, product_id: int) -> bool:
        """Sync rating updates to Elasticsearch"""
        try:
            # Recalculate ratings from database
            from app import ProductReview

            reviews = ProductReview.query.filter_by(product_id=product_id).all()
            total_reviews = len(reviews)
            average_rating = sum(r.rating for r in reviews) / total_reviews if total_reviews > 0 else 0

            # Calculate rating distribution
            rating_dist = {f"{i}_star": 0 for i in range(1, 6)}
            for review in reviews:
                rating_dist[f"{int(review.rating)}_star"] += 1

            doc = {
                'average_rating': round(average_rating, 2),
                'total_reviews': total_reviews,
                'rating_distribution': rating_dist,
                'popularity_score': average_rating * total_reviews * 0.1,
                'last_indexed_at': datetime.utcnow().isoformat()
            }

            self.es_manager.es.update(
                index=self.es_manager.product_index,
                id=product_id,
                body={'doc': doc},
                ignore=[404]
            )

            logger.info(f"Updated ratings for product {product_id}")
            return True

        except Exception as e:
            logger.error(f"Error syncing rating update {product_id}: {e}")
            return False

    def bulk_sync_products(self, product_ids: List[int]) -> Dict[str, int]:
        """Bulk sync multiple products"""
        try:
            from app import Product

            results = {'success': 0, 'failed': 0}
            actions = []

            for product_id in product_ids:
                product = Product.query.get(product_id)
                if product:
                    doc = self.es_manager._prepare_product_document(product)
                    if doc:
                        actions.append({
                            '_index': self.es_manager.product_index,
                            '_id': product_id,
                            '_source': doc
                        })

            if actions:
                success_count, failed_items = helpers.bulk(
                    self.es_manager.es,
                    actions,
                    chunk_size=50,
                    request_timeout=30
                )
                results['success'] = success_count
                results['failed'] = len(failed_items)

            return results

        except Exception as e:
            logger.error(f"Error in bulk sync: {e}")
            return {'success': 0, 'failed': len(product_ids)}

# Global sync manager instance
es_sync_manager = ElasticsearchSyncManager()

def get_elasticsearch_sync_manager() -> ElasticsearchSyncManager:
    """Get global Elasticsearch sync manager instance"""
    return es_sync_manager
