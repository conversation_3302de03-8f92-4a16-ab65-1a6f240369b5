{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\SellerAnalytics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './SellerAnalytics.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SellerAnalytics = () => {\n  _s();\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [productAnalytics, setProductAnalytics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [selectedPeriod, setSelectedPeriod] = useState(30);\n  const [activeTab, setActiveTab] = useState('overview');\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchAnalyticsData();\n  }, [selectedPeriod]);\n  const fetchAnalyticsData = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('sellerToken');\n      if (!token) {\n        navigate('/seller/login');\n        return;\n      }\n\n      // Fetch overview analytics\n      const overviewResponse = await fetch(`/api/seller/analytics/overview?days=${selectedPeriod}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!overviewResponse.ok) {\n        throw new Error('Failed to fetch analytics data');\n      }\n      const overviewData = await overviewResponse.json();\n      setAnalyticsData(overviewData.data);\n\n      // Fetch product analytics\n      const productResponse = await fetch(`/api/seller/analytics/products?days=${selectedPeriod}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!productResponse.ok) {\n        throw new Error('Failed to fetch product analytics');\n      }\n      const productData = await productResponse.json();\n      setProductAnalytics(productData.data);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR'\n    }).format(amount);\n  };\n  const formatPercentage = value => {\n    const sign = value >= 0 ? '+' : '';\n    return `${sign}${value.toFixed(1)}%`;\n  };\n  const getGrowthColor = value => {\n    return value >= 0 ? '#10b981' : '#ef4444';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"seller-analytics\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading analytics...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"seller-analytics\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Error Loading Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchAnalyticsData,\n          className: \"retry-btn\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"seller-analytics\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analytics-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Analytics Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"period-selector\",\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedPeriod,\n          onChange: e => setSelectedPeriod(Number(e.target.value)),\n          className: \"period-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 7,\n            children: \"Last 7 days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 30,\n            children: \"Last 30 days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 90,\n            children: \"Last 90 days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 365,\n            children: \"Last year\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analytics-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'overview' ? 'active' : ''}`,\n        onClick: () => setActiveTab('overview'),\n        children: \"Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-btn ${activeTab === 'products' ? 'active' : ''}`,\n        onClick: () => setActiveTab('products'),\n        children: \"Product Performance\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this), activeTab === 'overview' && analyticsData && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overview-tab\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metrics-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Total Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"growth-indicator\",\n              style: {\n                color: getGrowthColor(analyticsData.overview.revenue_growth)\n              },\n              children: formatPercentage(analyticsData.overview.revenue_growth)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            children: formatCurrency(analyticsData.overview.total_revenue)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"growth-indicator\",\n              style: {\n                color: getGrowthColor(analyticsData.overview.orders_growth)\n              },\n              children: formatPercentage(analyticsData.overview.orders_growth)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            children: analyticsData.overview.total_orders\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Your Earnings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            children: formatCurrency(analyticsData.overview.total_earnings)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Avg Order Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            children: formatCurrency(analyticsData.overview.avg_order_value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Unique Customers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            children: analyticsData.overview.unique_customers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Commission Paid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            children: formatCurrency(analyticsData.overview.total_commission)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"top-products-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Top Performing Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"products-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Product Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Units Sold\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 29\n          }, this), analyticsData.top_products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-name\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: product.total_sold\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: formatCurrency(product.total_revenue)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: product.order_count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 37\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 33\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sales-chart-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Daily Sales Trend\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-placeholder\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Sales chart visualization would go here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"chart-data\",\n              children: analyticsData.daily_sales.map((day, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"chart-bar\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bar-label\",\n                  children: new Date(day.date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bar-value\",\n                  children: formatCurrency(day.revenue)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 45\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 17\n    }, this), activeTab === 'products' && productAnalytics && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"products-tab\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Product Performance Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-analytics-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Price\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Stock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Sold\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Revenue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Rating\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 25\n        }, this), productAnalytics.products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-name\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: product.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: formatCurrency(product.price)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: product.stock_quantity < 10 ? 'low-stock' : '',\n            children: product.stock_quantity\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: product.total_sold\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: formatCurrency(product.total_revenue)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"rating\",\n            children: product.avg_rating > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"\\u2B50 \", product.avg_rating, \" (\", product.review_count, \")\"]\n            }, void 0, true) : 'No reviews'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"performance-score\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"score-bar\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"score-fill\",\n                style: {\n                  width: `${product.performance_score}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [product.performance_score, \"/100\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 33\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 29\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analytics-actions\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/seller/dashboard'),\n        className: \"back-btn\",\n        children: \"Back to Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 9\n  }, this);\n};\n_s(SellerAnalytics, \"xWV6/HfpAyk1WBWi/ORjkwH4R1g=\", false, function () {\n  return [useNavigate];\n});\n_c = SellerAnalytics;\nexport default SellerAnalytics;\nvar _c;\n$RefreshReg$(_c, \"SellerAnalytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SellerAnalytics", "_s", "analyticsData", "setAnalyticsData", "productAnalytics", "setProductAnalytics", "loading", "setLoading", "error", "setError", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "activeTab", "setActiveTab", "navigate", "fetchAnalyticsData", "token", "localStorage", "getItem", "overviewResponse", "fetch", "headers", "ok", "Error", "overviewData", "json", "data", "productResponse", "productData", "err", "message", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatPercentage", "value", "sign", "toFixed", "getGrowthColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onChange", "e", "Number", "target", "color", "overview", "revenue_growth", "total_revenue", "orders_growth", "total_orders", "total_earnings", "avg_order_value", "unique_customers", "total_commission", "top_products", "map", "product", "name", "total_sold", "order_count", "id", "daily_sales", "day", "index", "Date", "date", "toLocaleDateString", "revenue", "products", "category", "price", "stock_quantity", "avg_rating", "review_count", "width", "performance_score", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/SellerAnalytics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './SellerAnalytics.css';\n\nconst SellerAnalytics = () => {\n    const [analyticsData, setAnalyticsData] = useState(null);\n    const [productAnalytics, setProductAnalytics] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [selectedPeriod, setSelectedPeriod] = useState(30);\n    const [activeTab, setActiveTab] = useState('overview');\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        fetchAnalyticsData();\n    }, [selectedPeriod]);\n\n    const fetchAnalyticsData = async () => {\n        try {\n            setLoading(true);\n            const token = localStorage.getItem('sellerToken');\n            \n            if (!token) {\n                navigate('/seller/login');\n                return;\n            }\n\n            // Fetch overview analytics\n            const overviewResponse = await fetch(`/api/seller/analytics/overview?days=${selectedPeriod}`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            if (!overviewResponse.ok) {\n                throw new Error('Failed to fetch analytics data');\n            }\n\n            const overviewData = await overviewResponse.json();\n            setAnalyticsData(overviewData.data);\n\n            // Fetch product analytics\n            const productResponse = await fetch(`/api/seller/analytics/products?days=${selectedPeriod}`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            if (!productResponse.ok) {\n                throw new Error('Failed to fetch product analytics');\n            }\n\n            const productData = await productResponse.json();\n            setProductAnalytics(productData.data);\n\n        } catch (err) {\n            setError(err.message);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const formatCurrency = (amount) => {\n        return new Intl.NumberFormat('en-IN', {\n            style: 'currency',\n            currency: 'INR'\n        }).format(amount);\n    };\n\n    const formatPercentage = (value) => {\n        const sign = value >= 0 ? '+' : '';\n        return `${sign}${value.toFixed(1)}%`;\n    };\n\n    const getGrowthColor = (value) => {\n        return value >= 0 ? '#10b981' : '#ef4444';\n    };\n\n    if (loading) {\n        return (\n            <div className=\"seller-analytics\">\n                <div className=\"loading-container\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading analytics...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"seller-analytics\">\n                <div className=\"error-container\">\n                    <h3>Error Loading Analytics</h3>\n                    <p>{error}</p>\n                    <button onClick={fetchAnalyticsData} className=\"retry-btn\">\n                        Try Again\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"seller-analytics\">\n            <div className=\"analytics-header\">\n                <h1>Analytics Dashboard</h1>\n                <div className=\"period-selector\">\n                    <select \n                        value={selectedPeriod} \n                        onChange={(e) => setSelectedPeriod(Number(e.target.value))}\n                        className=\"period-select\"\n                    >\n                        <option value={7}>Last 7 days</option>\n                        <option value={30}>Last 30 days</option>\n                        <option value={90}>Last 90 days</option>\n                        <option value={365}>Last year</option>\n                    </select>\n                </div>\n            </div>\n\n            <div className=\"analytics-tabs\">\n                <button \n                    className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}\n                    onClick={() => setActiveTab('overview')}\n                >\n                    Overview\n                </button>\n                <button \n                    className={`tab-btn ${activeTab === 'products' ? 'active' : ''}`}\n                    onClick={() => setActiveTab('products')}\n                >\n                    Product Performance\n                </button>\n            </div>\n\n            {activeTab === 'overview' && analyticsData && (\n                <div className=\"overview-tab\">\n                    {/* Key Metrics Cards */}\n                    <div className=\"metrics-grid\">\n                        <div className=\"metric-card\">\n                            <div className=\"metric-header\">\n                                <h3>Total Revenue</h3>\n                                <span \n                                    className=\"growth-indicator\"\n                                    style={{ color: getGrowthColor(analyticsData.overview.revenue_growth) }}\n                                >\n                                    {formatPercentage(analyticsData.overview.revenue_growth)}\n                                </span>\n                            </div>\n                            <div className=\"metric-value\">\n                                {formatCurrency(analyticsData.overview.total_revenue)}\n                            </div>\n                        </div>\n\n                        <div className=\"metric-card\">\n                            <div className=\"metric-header\">\n                                <h3>Total Orders</h3>\n                                <span \n                                    className=\"growth-indicator\"\n                                    style={{ color: getGrowthColor(analyticsData.overview.orders_growth) }}\n                                >\n                                    {formatPercentage(analyticsData.overview.orders_growth)}\n                                </span>\n                            </div>\n                            <div className=\"metric-value\">\n                                {analyticsData.overview.total_orders}\n                            </div>\n                        </div>\n\n                        <div className=\"metric-card\">\n                            <div className=\"metric-header\">\n                                <h3>Your Earnings</h3>\n                            </div>\n                            <div className=\"metric-value\">\n                                {formatCurrency(analyticsData.overview.total_earnings)}\n                            </div>\n                        </div>\n\n                        <div className=\"metric-card\">\n                            <div className=\"metric-header\">\n                                <h3>Avg Order Value</h3>\n                            </div>\n                            <div className=\"metric-value\">\n                                {formatCurrency(analyticsData.overview.avg_order_value)}\n                            </div>\n                        </div>\n\n                        <div className=\"metric-card\">\n                            <div className=\"metric-header\">\n                                <h3>Unique Customers</h3>\n                            </div>\n                            <div className=\"metric-value\">\n                                {analyticsData.overview.unique_customers}\n                            </div>\n                        </div>\n\n                        <div className=\"metric-card\">\n                            <div className=\"metric-header\">\n                                <h3>Commission Paid</h3>\n                            </div>\n                            <div className=\"metric-value\">\n                                {formatCurrency(analyticsData.overview.total_commission)}\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Top Products */}\n                    <div className=\"top-products-section\">\n                        <h2>Top Performing Products</h2>\n                        <div className=\"products-table\">\n                            <div className=\"table-header\">\n                                <div>Product Name</div>\n                                <div>Units Sold</div>\n                                <div>Revenue</div>\n                                <div>Orders</div>\n                            </div>\n                            {analyticsData.top_products.map((product) => (\n                                <div key={product.id} className=\"table-row\">\n                                    <div className=\"product-name\">{product.name}</div>\n                                    <div>{product.total_sold}</div>\n                                    <div>{formatCurrency(product.total_revenue)}</div>\n                                    <div>{product.order_count}</div>\n                                </div>\n                            ))}\n                        </div>\n                    </div>\n\n                    {/* Sales Chart */}\n                    <div className=\"sales-chart-section\">\n                        <h2>Daily Sales Trend</h2>\n                        <div className=\"chart-container\">\n                            <div className=\"chart-placeholder\">\n                                <p>Sales chart visualization would go here</p>\n                                <div className=\"chart-data\">\n                                    {analyticsData.daily_sales.map((day, index) => (\n                                        <div key={index} className=\"chart-bar\">\n                                            <div className=\"bar-label\">{new Date(day.date).toLocaleDateString()}</div>\n                                            <div className=\"bar-value\">{formatCurrency(day.revenue)}</div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {activeTab === 'products' && productAnalytics && (\n                <div className=\"products-tab\">\n                    <h2>Product Performance Analysis</h2>\n                    <div className=\"products-analytics-table\">\n                        <div className=\"table-header\">\n                            <div>Product</div>\n                            <div>Category</div>\n                            <div>Price</div>\n                            <div>Stock</div>\n                            <div>Sold</div>\n                            <div>Revenue</div>\n                            <div>Rating</div>\n                            <div>Performance</div>\n                        </div>\n                        {productAnalytics.products.map((product) => (\n                            <div key={product.id} className=\"table-row\">\n                                <div className=\"product-info\">\n                                    <div className=\"product-name\">{product.name}</div>\n                                </div>\n                                <div>{product.category}</div>\n                                <div>{formatCurrency(product.price)}</div>\n                                <div className={product.stock_quantity < 10 ? 'low-stock' : ''}>\n                                    {product.stock_quantity}\n                                </div>\n                                <div>{product.total_sold}</div>\n                                <div>{formatCurrency(product.total_revenue)}</div>\n                                <div className=\"rating\">\n                                    {product.avg_rating > 0 ? (\n                                        <>\n                                            ⭐ {product.avg_rating} ({product.review_count})\n                                        </>\n                                    ) : (\n                                        'No reviews'\n                                    )}\n                                </div>\n                                <div className=\"performance-score\">\n                                    <div className=\"score-bar\">\n                                        <div \n                                            className=\"score-fill\" \n                                            style={{ width: `${product.performance_score}%` }}\n                                        ></div>\n                                    </div>\n                                    <span>{product.performance_score}/100</span>\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n                </div>\n            )}\n\n            <div className=\"analytics-actions\">\n                <button onClick={() => navigate('/seller/dashboard')} className=\"back-btn\">\n                    Back to Dashboard\n                </button>\n            </div>\n        </div>\n    );\n};\n\nexport default SellerAnalytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAMqB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZqB,kBAAkB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACL,cAAc,CAAC,CAAC;EAEpB,MAAMK,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACAR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEjD,IAAI,CAACF,KAAK,EAAE;QACRF,QAAQ,CAAC,eAAe,CAAC;QACzB;MACJ;;MAEA;MACA,MAAMK,gBAAgB,GAAG,MAAMC,KAAK,CAAC,uCAAuCV,cAAc,EAAE,EAAE;QAC1FW,OAAO,EAAE;UACL,eAAe,EAAE,UAAUL,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACG,gBAAgB,CAACG,EAAE,EAAE;QACtB,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;MACrD;MAEA,MAAMC,YAAY,GAAG,MAAML,gBAAgB,CAACM,IAAI,CAAC,CAAC;MAClDtB,gBAAgB,CAACqB,YAAY,CAACE,IAAI,CAAC;;MAEnC;MACA,MAAMC,eAAe,GAAG,MAAMP,KAAK,CAAC,uCAAuCV,cAAc,EAAE,EAAE;QACzFW,OAAO,EAAE;UACL,eAAe,EAAE,UAAUL,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACW,eAAe,CAACL,EAAE,EAAE;QACrB,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;MACxD;MAEA,MAAMK,WAAW,GAAG,MAAMD,eAAe,CAACF,IAAI,CAAC,CAAC;MAChDpB,mBAAmB,CAACuB,WAAW,CAACF,IAAI,CAAC;IAEzC,CAAC,CAAC,OAAOG,GAAG,EAAE;MACVpB,QAAQ,CAACoB,GAAG,CAACC,OAAO,CAAC;IACzB,CAAC,SAAS;MACNvB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwB,cAAc,GAAIC,MAAM,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACrB,CAAC;EAED,MAAMM,gBAAgB,GAAIC,KAAK,IAAK;IAChC,MAAMC,IAAI,GAAGD,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;IAClC,OAAO,GAAGC,IAAI,GAAGD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,GAAG;EACxC,CAAC;EAED,MAAMC,cAAc,GAAIH,KAAK,IAAK;IAC9B,OAAOA,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;EAC7C,CAAC;EAED,IAAIjC,OAAO,EAAE;IACT,oBACIT,OAAA;MAAK8C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC7B/C,OAAA;QAAK8C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9B/C,OAAA;UAAK8C,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCnD,OAAA;UAAA+C,QAAA,EAAG;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIxC,KAAK,EAAE;IACP,oBACIX,OAAA;MAAK8C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC7B/C,OAAA;QAAK8C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5B/C,OAAA;UAAA+C,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCnD,OAAA;UAAA+C,QAAA,EAAIpC;QAAK;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdnD,OAAA;UAAQoD,OAAO,EAAElC,kBAAmB;UAAC4B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACInD,OAAA;IAAK8C,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC7B/C,OAAA;MAAK8C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7B/C,OAAA;QAAA+C,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BnD,OAAA;QAAK8C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5B/C,OAAA;UACI0C,KAAK,EAAE7B,cAAe;UACtBwC,QAAQ,EAAGC,CAAC,IAAKxC,iBAAiB,CAACyC,MAAM,CAACD,CAAC,CAACE,MAAM,CAACd,KAAK,CAAC,CAAE;UAC3DI,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzB/C,OAAA;YAAQ0C,KAAK,EAAE,CAAE;YAAAK,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCnD,OAAA;YAAQ0C,KAAK,EAAE,EAAG;YAAAK,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCnD,OAAA;YAAQ0C,KAAK,EAAE,EAAG;YAAAK,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCnD,OAAA;YAAQ0C,KAAK,EAAE,GAAI;YAAAK,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENnD,OAAA;MAAK8C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3B/C,OAAA;QACI8C,SAAS,EAAE,WAAW/B,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACjEqC,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAAC,UAAU,CAAE;QAAA+B,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnD,OAAA;QACI8C,SAAS,EAAE,WAAW/B,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QACjEqC,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAAC,UAAU,CAAE;QAAA+B,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAELpC,SAAS,KAAK,UAAU,IAAIV,aAAa,iBACtCL,OAAA;MAAK8C,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAEzB/C,OAAA;QAAK8C,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzB/C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/C,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B/C,OAAA;cAAA+C,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBnD,OAAA;cACI8C,SAAS,EAAC,kBAAkB;cAC5BR,KAAK,EAAE;gBAAEmB,KAAK,EAAEZ,cAAc,CAACxC,aAAa,CAACqD,QAAQ,CAACC,cAAc;cAAE,CAAE;cAAAZ,QAAA,EAEvEN,gBAAgB,CAACpC,aAAa,CAACqD,QAAQ,CAACC,cAAc;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxBb,cAAc,CAAC7B,aAAa,CAACqD,QAAQ,CAACE,aAAa;UAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/C,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B/C,OAAA;cAAA+C,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBnD,OAAA;cACI8C,SAAS,EAAC,kBAAkB;cAC5BR,KAAK,EAAE;gBAAEmB,KAAK,EAAEZ,cAAc,CAACxC,aAAa,CAACqD,QAAQ,CAACG,aAAa;cAAE,CAAE;cAAAd,QAAA,EAEtEN,gBAAgB,CAACpC,aAAa,CAACqD,QAAQ,CAACG,aAAa;YAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxB1C,aAAa,CAACqD,QAAQ,CAACI;UAAY;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/C,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1B/C,OAAA;cAAA+C,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxBb,cAAc,CAAC7B,aAAa,CAACqD,QAAQ,CAACK,cAAc;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/C,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1B/C,OAAA;cAAA+C,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxBb,cAAc,CAAC7B,aAAa,CAACqD,QAAQ,CAACM,eAAe;UAAC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/C,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1B/C,OAAA;cAAA+C,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxB1C,aAAa,CAACqD,QAAQ,CAACO;UAAgB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/C,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC1B/C,OAAA;cAAA+C,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxBb,cAAc,CAAC7B,aAAa,CAACqD,QAAQ,CAACQ,gBAAgB;UAAC;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNnD,OAAA;QAAK8C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjC/C,OAAA;UAAA+C,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCnD,OAAA;UAAK8C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3B/C,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB/C,OAAA;cAAA+C,QAAA,EAAK;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvBnD,OAAA;cAAA+C,QAAA,EAAK;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrBnD,OAAA;cAAA+C,QAAA,EAAK;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClBnD,OAAA;cAAA+C,QAAA,EAAK;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EACL9C,aAAa,CAAC8D,YAAY,CAACC,GAAG,CAAEC,OAAO,iBACpCrE,OAAA;YAAsB8C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvC/C,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEsB,OAAO,CAACC;YAAI;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDnD,OAAA;cAAA+C,QAAA,EAAMsB,OAAO,CAACE;YAAU;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/BnD,OAAA;cAAA+C,QAAA,EAAMb,cAAc,CAACmC,OAAO,CAACT,aAAa;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDnD,OAAA;cAAA+C,QAAA,EAAMsB,OAAO,CAACG;YAAW;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAJ1BkB,OAAO,CAACI,EAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKf,CACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNnD,OAAA;QAAK8C,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC/C,OAAA;UAAA+C,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BnD,OAAA;UAAK8C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC5B/C,OAAA;YAAK8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC9B/C,OAAA;cAAA+C,QAAA,EAAG;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9CnD,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAC,QAAA,EACtB1C,aAAa,CAACqE,WAAW,CAACN,GAAG,CAAC,CAACO,GAAG,EAAEC,KAAK,kBACtC5E,OAAA;gBAAiB8C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBAClC/C,OAAA;kBAAK8C,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAE,IAAI8B,IAAI,CAACF,GAAG,CAACG,IAAI,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1EnD,OAAA;kBAAK8C,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEb,cAAc,CAACyC,GAAG,CAACK,OAAO;gBAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAFxDyB,KAAK;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEApC,SAAS,KAAK,UAAU,IAAIR,gBAAgB,iBACzCP,OAAA;MAAK8C,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzB/C,OAAA;QAAA+C,QAAA,EAAI;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrCnD,OAAA;QAAK8C,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACrC/C,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzB/C,OAAA;YAAA+C,QAAA,EAAK;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClBnD,OAAA;YAAA+C,QAAA,EAAK;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnBnD,OAAA;YAAA+C,QAAA,EAAK;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChBnD,OAAA;YAAA+C,QAAA,EAAK;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChBnD,OAAA;YAAA+C,QAAA,EAAK;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACfnD,OAAA;YAAA+C,QAAA,EAAK;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClBnD,OAAA;YAAA+C,QAAA,EAAK;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjBnD,OAAA;YAAA+C,QAAA,EAAK;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,EACL5C,gBAAgB,CAAC0E,QAAQ,CAACb,GAAG,CAAEC,OAAO,iBACnCrE,OAAA;UAAsB8C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACvC/C,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,eACzB/C,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEsB,OAAO,CAACC;YAAI;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNnD,OAAA;YAAA+C,QAAA,EAAMsB,OAAO,CAACa;UAAQ;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7BnD,OAAA;YAAA+C,QAAA,EAAMb,cAAc,CAACmC,OAAO,CAACc,KAAK;UAAC;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1CnD,OAAA;YAAK8C,SAAS,EAAEuB,OAAO,CAACe,cAAc,GAAG,EAAE,GAAG,WAAW,GAAG,EAAG;YAAArC,QAAA,EAC1DsB,OAAO,CAACe;UAAc;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACNnD,OAAA;YAAA+C,QAAA,EAAMsB,OAAO,CAACE;UAAU;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BnD,OAAA;YAAA+C,QAAA,EAAMb,cAAc,CAACmC,OAAO,CAACT,aAAa;UAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDnD,OAAA;YAAK8C,SAAS,EAAC,QAAQ;YAAAC,QAAA,EAClBsB,OAAO,CAACgB,UAAU,GAAG,CAAC,gBACnBrF,OAAA,CAAAE,SAAA;cAAA6C,QAAA,GAAE,SACI,EAACsB,OAAO,CAACgB,UAAU,EAAC,IAAE,EAAChB,OAAO,CAACiB,YAAY,EAAC,GAClD;YAAA,eAAE,CAAC,GAEH;UACH;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC9B/C,OAAA;cAAK8C,SAAS,EAAC,WAAW;cAAAC,QAAA,eACtB/C,OAAA;gBACI8C,SAAS,EAAC,YAAY;gBACtBR,KAAK,EAAE;kBAAEiD,KAAK,EAAE,GAAGlB,OAAO,CAACmB,iBAAiB;gBAAI;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNnD,OAAA;cAAA+C,QAAA,GAAOsB,OAAO,CAACmB,iBAAiB,EAAC,MAAI;YAAA;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA,GA5BAkB,OAAO,CAACI,EAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6Bf,CACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAEDnD,OAAA;MAAK8C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAC9B/C,OAAA;QAAQoD,OAAO,EAAEA,CAAA,KAAMnC,QAAQ,CAAC,mBAAmB,CAAE;QAAC6B,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAE3E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC/C,EAAA,CA/SID,eAAe;EAAA,QAOAL,WAAW;AAAA;AAAA2F,EAAA,GAP1BtF,eAAe;AAiTrB,eAAeA,eAAe;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}