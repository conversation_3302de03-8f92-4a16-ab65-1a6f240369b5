import React, { useState, useEffect, useRef } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import VirtualizedProductGrid from '../components/VirtualizedProductGrid';
import SearchFilters from '../components/SearchFilters';
import SearchSuggestions from '../components/SearchSuggestions';
import SortControls from '../components/SortControls';
import Pagination from '../components/Pagination';
import VisualSearch from '../components/VisualSearch';
import { ErrorDisplay, LoadingSpinner } from '../components/ErrorBoundary';
import { SearchSEO } from '../components/SEOHead';
import { useAuth } from '../contexts/AuthContext';
import { API_BASE_URL } from '../config/api';
import { useDebounce } from '../hooks/useDebounce';
import { useNotification } from '../contexts/NotificationContext';

const Search = React.memo(({ token }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const searchInputRef = useRef(null);

  // Search state
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showVisualSearch, setShowVisualSearch] = useState(false);
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const { user } = useAuth();
  const { success, error } = useNotification();

  // Function to log search queries for analytics
  const logSearchQuery = async (query, resultsCount = 0, filters = {}) => {
    try {
      const token = localStorage.getItem('token');
      const guestSessionId = localStorage.getItem('guest_session_id');

      const searchData = {
        query: query.trim(),
        type: 'text',
        results_count: resultsCount,
        filters: filters,
        guest_session_id: guestSessionId,
        session_id: sessionStorage.getItem('session_id') || Date.now().toString()
      };

      await fetch(`${API_BASE_URL}/analytics/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
          ...(guestSessionId && { 'X-Guest-Session-ID': guestSessionId })
        },
        body: JSON.stringify(searchData)
      });
    } catch (error) {
      console.error('Failed to log search query:', error);
      // Don't show error to user - analytics logging should be silent
    }
  };

  // Products and pagination state
  const [products, setProducts] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    pages: 0,
    has_next: false,
    has_prev: false
  });
  const [loading, setLoading] = useState(false);
  const [errorState, setErrorState] = useState(null);

  // Filter state
  const [filters, setFilters] = useState({
    categories: [],
    brands: [],
    sellers: [],
    priceRange: { min: 0, max: 10000 },
    ratingRange: { min: 0, max: 5 },
    sustainabilityRange: { min: 0, max: 100 },
    inStockOnly: false
  });

  // Sort and view state
  const [sortBy, setSortBy] = useState('price');
  const [sortOrder, setSortOrder] = useState('asc');
  const [viewMode, setViewMode] = useState('grid');
  const [sortLoading, setSortLoading] = useState(false);

  // Initialize from URL parameters
  useEffect(() => {
    const urlQuery = searchParams.get('q') || '';
    const urlCategory = searchParams.get('category');
    const urlBrand = searchParams.get('brand');

    // Get sort preferences from URL or localStorage, with fallback to default
    const savedSortBy = localStorage.getItem('allora_preferred_sort_by') || 'price';
    const savedSortOrder = localStorage.getItem('allora_preferred_sort_order') || 'asc';
    const urlSortBy = searchParams.get('sort_by') || savedSortBy;
    const urlSortOrder = searchParams.get('sort_order') || savedSortOrder;
    const urlPage = parseInt(searchParams.get('page')) || 1;

    setSearchQuery(urlQuery);
    setSortBy(urlSortBy);
    setSortOrder(urlSortOrder);
    setPagination(prev => ({ ...prev, page: urlPage }));

    // Set filters from URL
    if (urlCategory || urlBrand) {
      setFilters(prev => ({
        ...prev,
        categories: urlCategory ? [urlCategory] : [],
        brands: urlBrand ? [urlBrand] : []
      }));
    }
  }, [searchParams]);

  // Initial load of products
  useEffect(() => {
    fetchProducts();
  }, []); // Run once on mount

  // Fetch products when search parameters change
  useEffect(() => {
    // Always fetch products - show all products by default, filtered by search/filters if provided
    fetchProducts();
  }, [debouncedSearchQuery, filters, sortBy, sortOrder, pagination.page]);

  const fetchProducts = async () => {
    setLoading(true);
    setErrorState(null);

    try {
      const params = new URLSearchParams();

      if (debouncedSearchQuery) params.append('search', debouncedSearchQuery);
      if (filters.categories.length > 0) params.append('category', filters.categories.join(','));
      if (filters.brands.length > 0) params.append('brand', filters.brands.join(','));
      if (filters.sellers && filters.sellers.length > 0) params.append('seller_id', filters.sellers.join(','));
      if (filters.priceRange.min > 0) params.append('min_price', filters.priceRange.min);
      if (filters.priceRange.max < 10000) params.append('max_price', filters.priceRange.max);
      if (filters.ratingRange.min > 0) params.append('min_rating', filters.ratingRange.min);
      if (filters.ratingRange.max < 5) params.append('max_rating', filters.ratingRange.max);
      if (filters.sustainabilityRange.min > 0) params.append('min_sustainability', filters.sustainabilityRange.min);
      if (filters.sustainabilityRange.max < 100) params.append('max_sustainability', filters.sustainabilityRange.max);
      if (filters.inStockOnly) params.append('in_stock_only', 'true');

      params.append('sort_by', sortBy);
      params.append('sort_order', sortOrder);
      params.append('page', pagination.page);
      params.append('per_page', pagination.per_page);

      console.log('Fetching products with params:', params.toString());
      const response = await fetch(`${API_BASE_URL}/products?${params.toString()}`);

      if (!response.ok) {
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After') || '60';
          throw new Error(`Rate limit exceeded. Please wait ${retryAfter} seconds before trying again.`);
        }
        throw new Error(`Failed to fetch products (Status: ${response.status})`);
      }

      const data = await response.json();
      console.log('Products API response:', data);

      // Validate and clean product data
      const validProducts = (data.products || []).filter(product => {
        if (!product) {
          console.warn('Invalid product found:', product);
          return false;
        }
        if (!product.name) {
          console.warn('Product missing name:', product);
        }
        if (product.price === null || product.price === undefined) {
          console.warn('Product missing price:', product);
          product.price = 0; // Set default price
        }
        if (!product.image) {
          console.warn('Product missing image:', product);
        }
        return true;
      });

      console.log(`Processed ${validProducts.length} valid products from ${(data.products || []).length} total`);

      setProducts(validProducts);
      setPagination(data.pagination || {
        page: 1,
        per_page: 20,
        total: 0,
        pages: 0,
        has_next: false,
        has_prev: false
      });

      // Log search query for analytics (only if there's a search query)
      if (debouncedSearchQuery && debouncedSearchQuery.trim()) {
        logSearchQuery(
          debouncedSearchQuery,
          validProducts.length,
          {
            category: filters.category,
            brand: filters.brand,
            min_price: filters.minPrice,
            max_price: filters.maxPrice,
            sort_by: sortBy,
            sort_order: sortOrder
          }
        );
      }

    } catch (err) {
      console.error('Search products error:', err);

      // Handle rate limiting gracefully
      if (err.message.includes('Rate limit exceeded')) {
        setErrorState('Too many requests. Please wait a moment and try again.');
      } else if (err.message.includes('429')) {
        setErrorState('Server is busy. Please try again in a few moments.');
      } else {
        setErrorState(err.message);
      }

      // Don't clear products on rate limit - keep showing existing results
      if (!err.message.includes('Rate limit') && !err.message.includes('429')) {
        setProducts([]);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (value) => {
    setSearchQuery(value);
    setShowSuggestions(value.length >= 2);

    // Update URL
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set('q', value);
    } else {
      newParams.delete('q');
    }
    newParams.set('page', '1'); // Reset to first page
    setSearchParams(newParams);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSuggestionSelect = (suggestion) => {
    if (suggestion.type === 'product') {
      setSearchQuery(suggestion.text);
    } else if (suggestion.type === 'category') {
      navigate(`/category/${suggestion.text.toLowerCase().replace(' ', '-')}`);
      return;
    } else if (suggestion.type === 'brand') {
      setFilters(prev => ({
        ...prev,
        brands: [suggestion.text]
      }));
    }
    setShowSuggestions(false);
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, page: 1 }));

    // Update URL with filter parameters
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', '1');
    setSearchParams(newParams);
  };

  const handleClearFilters = () => {
    // Reset filters to default state
    const defaultFilters = {
      categories: [],
      brands: [],
      sellers: [],
      priceRange: { min: 0, max: 10000 },
      ratingRange: { min: 0, max: 5 },
      sustainabilityRange: { min: 0, max: 100 },
      inStockOnly: false
    };

    setFilters(defaultFilters);
    setPagination(prev => ({ ...prev, page: 1 }));

    // Clear filter-related URL parameters while keeping search query and sort
    const newParams = new URLSearchParams(searchParams);

    // Remove filter parameters
    newParams.delete('category');
    newParams.delete('brand');
    newParams.delete('min_price');
    newParams.delete('max_price');
    newParams.delete('min_rating');
    newParams.delete('max_rating');
    newParams.delete('min_sustainability');
    newParams.delete('max_sustainability');
    newParams.delete('in_stock_only');

    // Reset to first page
    newParams.set('page', '1');

    setSearchParams(newParams);
  };

  const handleSortChange = (newSortBy, newSortOrder) => {
    setSortLoading(true);
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);

    // Save user's sort preference to localStorage
    localStorage.setItem('allora_preferred_sort_by', newSortBy);
    localStorage.setItem('allora_preferred_sort_order', newSortOrder);

    // Update URL
    const newParams = new URLSearchParams(searchParams);
    newParams.set('sort_by', newSortBy);
    newParams.set('sort_order', newSortOrder);
    newParams.set('page', '1');
    setSearchParams(newParams);
    setPagination(prev => ({ ...prev, page: 1 }));

    // Clear sort loading after a short delay
    setTimeout(() => setSortLoading(false), 500);
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));

    // Update URL
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', newPage.toString());
    setSearchParams(newParams);

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (errorState) {
    return (
      <div className="container-safe py-6">
        <ErrorDisplay
          error={errorState}
          onRetry={() => {
            setErrorState(null);
            fetchProducts();
          }}
        />
      </div>
    );
  }

  return (
    <>
      <SearchSEO query={searchQuery} results={products} />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Modern Header with Gradient Background */}
        <div className="relative mb-8 p-8 bg-gradient-to-r from-green-600 via-green-500 to-emerald-500 rounded-2xl shadow-xl overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-white mb-3 tracking-tight">
                  {searchQuery ? '🔍 Search Results' : '🛍️ Discover Products'}
                </h1>
                <p className="text-green-100 text-lg">
                  {searchQuery
                    ? `Found ${pagination.total} results for "${searchQuery}"`
                    : 'Explore our sustainable collection - use search and filters to find exactly what you need'
                  }
                </p>
              </div>
              <div className="hidden lg:block">
                <div className="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <span className="text-6xl">🌱</span>
                </div>
              </div>
            </div>
          </div>
          {/* Decorative Elements */}
          <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/5 rounded-full"></div>
          <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-white/5 rounded-full"></div>
        </div>

        {/* Enhanced Search Bar */}
        <div className="relative mb-8">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-stretch lg:items-center">
              <div className="relative flex-1">
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search for sustainable products, eco-friendly brands, categories..."
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  onFocus={() => setShowSuggestions(searchQuery.length >= 2)}
                  onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                  className="w-full px-6 py-4 pl-14 pr-6 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-green-500/20 focus:border-green-500 text-lg placeholder-gray-400 transition-all duration-200"
                />
                <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
                  <svg className="h-6 w-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                {searchQuery && (
                  <button
                    onClick={() => handleSearchChange('')}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                )}
              </div>

              {/* Enhanced Visual Search Button */}
              <button
                onClick={() => setShowVisualSearch(!showVisualSearch)}
                className={`px-6 py-4 rounded-xl border-2 transition-all duration-300 flex items-center gap-3 font-medium ${
                  showVisualSearch
                    ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-500 shadow-lg transform scale-105'
                    : 'bg-white text-gray-700 border-gray-300 hover:border-green-500 hover:text-green-600 hover:shadow-md'
                }`}
                title="AI Visual Search - Upload an image to find similar products"
              >
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
                <span className="hidden sm:inline">AI Visual Search</span>
                <span className="sm:hidden">📷</span>
              </button>
            </div>
          </div>

          <SearchSuggestions
            query={searchQuery}
            onSuggestionSelect={handleSuggestionSelect}
            onClose={() => setShowSuggestions(false)}
            isVisible={showSuggestions}
          />
        </div>

        {/* Enhanced Visual Search Component */}
        {showVisualSearch && (
          <div className="mb-8 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-200 shadow-lg overflow-hidden">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                    <svg className="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">🤖 AI Visual Search</h3>
                    <p className="text-sm text-gray-600">Upload an image to find similar sustainable products</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowVisualSearch(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-lg transition-all duration-200"
                >
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              <VisualSearch token={token} />
            </div>
          </div>
        )}

        {/* Main Content with Modern Layout */}
        <div className="flex flex-col xl:flex-row gap-8">

          {/* Enhanced Filters Sidebar */}
          <div className="xl:w-80 flex-shrink-0">
            <div className="sticky top-8">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
                <div className="p-6 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                  <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                    <span>🎯</span>
                    Refine Your Search
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">Find exactly what you're looking for</p>
                </div>
                <div className="p-6">
                  <SearchFilters
                    filters={filters}
                    onFiltersChange={handleFiltersChange}
                    onClearFilters={handleClearFilters}
                    isLoading={loading}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="flex-1 min-w-0">

            {/* Enhanced Sort Controls */}
            <div className="mb-8">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
                <SortControls
                  sortBy={sortBy}
                  sortOrder={sortOrder}
                  onSortChange={handleSortChange}
                  resultCount={pagination.total}
                  viewMode={viewMode}
                  onViewModeChange={setViewMode}
                  isLoading={sortLoading}
                />
              </div>
            </div>

            {/* Enhanced Loading State */}
            {loading && (
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-12">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Searching Products...</h3>
                  <p className="text-gray-600">Finding the best sustainable options for you</p>
                </div>
              </div>
            )}

            {/* Enhanced Products Grid */}
            {!loading && products.length > 0 && (
              <div className="space-y-8">
                {/* Sort Status Indicator */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">✓</span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-green-800">
                          Showing {products.length} products sorted by{' '}
                          <span className="font-semibold">
                            {sortBy === 'price' ? 'Price' :
                             sortBy === 'rating' ? 'Rating' :
                             sortBy === 'sustainability' ? 'Sustainability' :
                             sortBy === 'popularity' ? 'Popularity' :
                             sortBy === 'newest' ? 'Newest' : 'Name'}
                          </span>
                          {' '}({sortOrder === 'asc' ? 'Low to High' : 'High to Low'})
                        </p>
                        <p className="text-xs text-green-600">
                          {sortBy === 'price' && sortOrder === 'asc' && '💰 Best deals first'}
                          {sortBy === 'price' && sortOrder === 'desc' && '💎 Premium products first'}
                          {sortBy === 'rating' && sortOrder === 'desc' && '⭐ Highest rated first'}
                          {sortBy === 'sustainability' && sortOrder === 'desc' && '🌱 Most eco-friendly first'}
                          {sortBy === 'popularity' && sortOrder === 'desc' && '🔥 Most popular first'}
                          {sortBy === 'newest' && sortOrder === 'desc' && '🆕 Latest arrivals first'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
                  <div className="p-6">
                    <VirtualizedProductGrid
                      products={products}
                      emptyMessage="No products found matching your search criteria."
                      className="min-h-[400px]"
                      viewMode={viewMode}
                    />
                  </div>
                </div>

                {/* Enhanced Pagination */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
                  <Pagination
                    currentPage={pagination.page}
                    totalPages={pagination.pages}
                    hasNext={pagination.has_next}
                    hasPrev={pagination.has_prev}
                    onPageChange={handlePageChange}
                    loading={loading}
                  />
                </div>
              </div>
            )}

            {/* Enhanced No Results */}
            {!loading && products.length === 0 && (
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-12">
                <div className="text-center max-w-md mx-auto">
                  <div className="w-24 h-24 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span className="text-4xl">🔍</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">
                    {searchQuery || filters.categories.length > 0 || filters.brands.length > 0
                      ? 'No Products Found'
                      : 'No Products Available'
                    }
                  </h3>
                  <p className="text-gray-600 mb-8 leading-relaxed">
                    {searchQuery || filters.categories.length > 0 || filters.brands.length > 0
                      ? "We couldn't find any sustainable products matching your search criteria. Try adjusting your search or filters."
                      : "There are currently no products available in our sustainable catalog. Check back soon!"
                    }
                  </p>

                  {(searchQuery || filters.categories.length > 0 || filters.brands.length > 0) && (
                    <div className="bg-gray-50 rounded-xl p-6 mb-8">
                      <h4 className="font-semibold text-gray-900 mb-3">💡 Search Tips:</h4>
                      <ul className="text-sm text-gray-600 space-y-2 text-left">
                        <li className="flex items-center gap-2">
                          <span className="w-1.5 h-1.5 bg-green-500 rounded-full"></span>
                          Try different or more general keywords
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="w-1.5 h-1.5 bg-green-500 rounded-full"></span>
                          Check your spelling and try synonyms
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="w-1.5 h-1.5 bg-green-500 rounded-full"></span>
                          Remove some filters to broaden results
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="w-1.5 h-1.5 bg-green-500 rounded-full"></span>
                          Try our AI Visual Search feature
                        </li>
                      </ul>
                    </div>
                  )}

                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <button
                      onClick={handleClearFilters}
                      className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      🔄 Clear All Filters
                    </button>
                    <button
                      onClick={() => setShowVisualSearch(true)}
                      className="px-6 py-3 bg-white text-gray-700 border-2 border-gray-300 rounded-xl hover:border-green-500 hover:text-green-600 transition-all duration-200 font-medium"
                    >
                      📷 Try Visual Search
                    </button>
                  </div>
                </div>
              </div>
            )}

          </div>
        </div>
        </div>
      </div>
    </>
  );
});

export default Search;