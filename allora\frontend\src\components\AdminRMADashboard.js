import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import './AdminRMADashboard.css';

const AdminRMADashboard = () => {
    const navigate = useNavigate();
    
    const [rmaRequests, setRmaRequests] = useState([]);
    const [analytics, setAnalytics] = useState(null);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [filters, setFilters] = useState({
        status: '',
        rma_type: '',
        search: ''
    });
    const [selectedRequest, setSelectedRequest] = useState(null);
    const [showApprovalModal, setShowApprovalModal] = useState(false);
    const [approvalData, setApprovalData] = useState({
        decision: '',
        notes: ''
    });

    useEffect(() => {
        fetchRMARequests();
        fetchAnalytics();
    }, [currentPage, filters]);

    const fetchRMARequests = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('token');
            
            const params = new URLSearchParams({
                page: currentPage.toString(),
                per_page: '20'
            });
            
            if (filters.status) params.append('status', filters.status);
            if (filters.rma_type) params.append('rma_type', filters.rma_type);
            if (filters.search) params.append('search', filters.search);
            
            const response = await fetch(`/api/rma/admin/requests?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setRmaRequests(data.requests);
                setTotalPages(data.pagination.pages);
            } else {
                toast.error('Failed to fetch RMA requests');
            }
        } catch (error) {
            console.error('Error fetching RMA requests:', error);
            toast.error('Error loading RMA requests');
        } finally {
            setLoading(false);
        }
    };

    const fetchAnalytics = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/rma/admin/analytics/summary', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setAnalytics(data.analytics);
            }
        } catch (error) {
            console.error('Error fetching analytics:', error);
        }
    };

    const handleApproval = async () => {
        if (!selectedRequest || !approvalData.decision) return;

        try {
            const token = localStorage.getItem('token');
            const endpoint = approvalData.decision === 'approved' ? 'approve' : 'reject';
            
            const response = await fetch(`/api/rma/admin/${selectedRequest.rma_number}/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    notes: approvalData.notes,
                    reason: approvalData.decision === 'rejected' ? approvalData.notes : undefined
                })
            });

            if (response.ok) {
                toast.success(`RMA request ${approvalData.decision} successfully`);
                setShowApprovalModal(false);
                setSelectedRequest(null);
                setApprovalData({ decision: '', notes: '' });
                fetchRMARequests();
                fetchAnalytics();
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || `Failed to ${approvalData.decision} RMA request`);
            }
        } catch (error) {
            console.error('Error processing approval:', error);
            toast.error('Error processing approval');
        }
    };

    const handleProcessReturn = async (rmaNumber, receivedItems) => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/rma/admin/${rmaNumber}/process-return`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ received_items: receivedItems })
            });

            if (response.ok) {
                toast.success('Return processed successfully');
                fetchRMARequests();
                fetchAnalytics();
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || 'Failed to process return');
            }
        } catch (error) {
            console.error('Error processing return:', error);
            toast.error('Error processing return');
        }
    };

    const handleCompleteRefund = async (rmaNumber, refundDetails) => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/rma/admin/${rmaNumber}/complete-refund`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(refundDetails)
            });

            if (response.ok) {
                toast.success('Refund completed successfully');
                fetchRMARequests();
                fetchAnalytics();
            } else {
                const errorData = await response.json();
                toast.error(errorData.error || 'Failed to complete refund');
            }
        } catch (error) {
            console.error('Error completing refund:', error);
            toast.error('Error completing refund');
        }
    };

    const exportData = async () => {
        try {
            const token = localStorage.getItem('token');
            const params = new URLSearchParams();
            
            if (filters.status) params.append('status', filters.status);
            if (filters.start_date) params.append('start_date', filters.start_date);
            if (filters.end_date) params.append('end_date', filters.end_date);
            
            const response = await fetch(`/api/rma/admin/export/csv?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                
                // Create and download CSV file
                const blob = new Blob([data.csv_data], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = data.filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                toast.success('Data exported successfully');
            } else {
                toast.error('Failed to export data');
            }
        } catch (error) {
            console.error('Error exporting data:', error);
            toast.error('Error exporting data');
        }
    };

    const getStatusColor = (status) => {
        const statusColors = {
            'pending': '#f39c12',
            'approved': '#27ae60',
            'rejected': '#e74c3c',
            'return_shipped': '#3498db',
            'return_received': '#9b59b6',
            'refund_completed': '#2ecc71',
            'cancelled': '#95a5a6'
        };
        return statusColors[status] || '#95a5a6';
    };

    const getStatusLabel = (status) => {
        const statusLabels = {
            'pending': 'Pending Review',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'return_shipped': 'Return Shipped',
            'return_received': 'Return Received',
            'refund_completed': 'Refund Completed',
            'cancelled': 'Cancelled'
        };
        return statusLabels[status] || status;
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
    };

    const formatCurrency = (amount) => {
        return `₹${parseFloat(amount).toFixed(2)}`;
    };

    if (loading && !rmaRequests.length) {
        return (
            <div className="admin-rma-container">
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>Loading RMA dashboard...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="admin-rma-container">
            {/* Header */}
            <div className="admin-rma-header">
                <h1>RMA Management Dashboard</h1>
                <div className="header-actions">
                    <button onClick={exportData} className="btn btn-secondary">
                        Export Data
                    </button>
                    <button onClick={fetchRMARequests} className="btn btn-primary">
                        Refresh
                    </button>
                </div>
            </div>

            {/* Analytics Cards */}
            {analytics && (
                <div className="analytics-cards">
                    <div className="analytics-card">
                        <div className="card-icon">📋</div>
                        <div className="card-content">
                            <h3>{analytics.total_requests || 0}</h3>
                            <p>Total Requests</p>
                        </div>
                    </div>
                    <div className="analytics-card">
                        <div className="card-icon">⏳</div>
                        <div className="card-content">
                            <h3>{analytics.pending_requests || 0}</h3>
                            <p>Pending Review</p>
                        </div>
                    </div>
                    <div className="analytics-card">
                        <div className="card-icon">✅</div>
                        <div className="card-content">
                            <h3>{analytics.approved_requests || 0}</h3>
                            <p>Approved</p>
                        </div>
                    </div>
                    <div className="analytics-card">
                        <div className="card-icon">💰</div>
                        <div className="card-content">
                            <h3>{formatCurrency(analytics.total_refund_amount || 0)}</h3>
                            <p>Total Refunds</p>
                        </div>
                    </div>
                </div>
            )}

            {/* Filters */}
            <div className="rma-filters">
                <div className="filter-group">
                    <input
                        type="text"
                        placeholder="Search by RMA number or email..."
                        value={filters.search}
                        onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                        className="search-input"
                    />
                </div>
                
                <div className="filter-group">
                    <select
                        value={filters.status}
                        onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                        className="filter-select"
                    >
                        <option value="">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                        <option value="return_shipped">Return Shipped</option>
                        <option value="return_received">Return Received</option>
                        <option value="refund_completed">Refund Completed</option>
                    </select>
                </div>
                
                <div className="filter-group">
                    <select
                        value={filters.rma_type}
                        onChange={(e) => setFilters(prev => ({ ...prev, rma_type: e.target.value }))}
                        className="filter-select"
                    >
                        <option value="">All Types</option>
                        <option value="return_refund">Return for Refund</option>
                        <option value="exchange_same">Exchange Same</option>
                        <option value="exchange_different">Exchange Different</option>
                    </select>
                </div>
            </div>

            {/* RMA Requests Table */}
            <div className="rma-table-container">
                <table className="rma-table">
                    <thead>
                        <tr>
                            <th>RMA Number</th>
                            <th>Customer</th>
                            <th>Order ID</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Refund Amount</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {rmaRequests.map(request => (
                            <tr key={request.id}>
                                <td>
                                    <span className="rma-number">#{request.rma_number}</span>
                                </td>
                                <td>{request.customer_email}</td>
                                <td>#{request.order_id}</td>
                                <td>
                                    <span className="rma-type">
                                        {request.rma_type?.replace('_', ' ').toUpperCase()}
                                    </span>
                                </td>
                                <td>
                                    <span 
                                        className="status-badge small" 
                                        style={{ backgroundColor: getStatusColor(request.status) }}
                                    >
                                        {getStatusLabel(request.status)}
                                    </span>
                                </td>
                                <td>{formatCurrency(request.total_refund_amount)}</td>
                                <td>{formatDate(request.created_at)}</td>
                                <td>
                                    <div className="action-buttons">
                                        <button 
                                            onClick={() => navigate(`/admin/rma/${request.rma_number}`)}
                                            className="btn btn-sm btn-primary"
                                        >
                                            View
                                        </button>
                                        {request.status === 'pending' && (
                                            <button 
                                                onClick={() => {
                                                    setSelectedRequest(request);
                                                    setShowApprovalModal(true);
                                                }}
                                                className="btn btn-sm btn-success"
                                            >
                                                Review
                                            </button>
                                        )}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="pagination">
                    <button 
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="pagination-btn"
                    >
                        Previous
                    </button>
                    
                    <span className="page-info">
                        Page {currentPage} of {totalPages}
                    </span>
                    
                    <button 
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="pagination-btn"
                    >
                        Next
                    </button>
                </div>
            )}

            {/* Approval Modal */}
            {showApprovalModal && selectedRequest && (
                <div className="modal-overlay">
                    <div className="modal">
                        <div className="modal-header">
                            <h3>Review RMA Request</h3>
                            <button 
                                onClick={() => setShowApprovalModal(false)}
                                className="close-btn"
                            >
                                ×
                            </button>
                        </div>
                        
                        <div className="modal-body">
                            <div className="request-info">
                                <p><strong>RMA Number:</strong> #{selectedRequest.rma_number}</p>
                                <p><strong>Customer:</strong> {selectedRequest.customer_email}</p>
                                <p><strong>Order ID:</strong> #{selectedRequest.order_id}</p>
                                <p><strong>Refund Amount:</strong> {formatCurrency(selectedRequest.total_refund_amount)}</p>
                            </div>
                            
                            <div className="approval-form">
                                <div className="form-group">
                                    <label>Decision:</label>
                                    <select
                                        value={approvalData.decision}
                                        onChange={(e) => setApprovalData(prev => ({ ...prev, decision: e.target.value }))}
                                        className="form-control"
                                    >
                                        <option value="">Select Decision</option>
                                        <option value="approved">Approve</option>
                                        <option value="rejected">Reject</option>
                                    </select>
                                </div>
                                
                                <div className="form-group">
                                    <label>Notes:</label>
                                    <textarea
                                        value={approvalData.notes}
                                        onChange={(e) => setApprovalData(prev => ({ ...prev, notes: e.target.value }))}
                                        placeholder="Add notes or reason for rejection..."
                                        className="form-control"
                                        rows="4"
                                    />
                                </div>
                            </div>
                        </div>
                        
                        <div className="modal-footer">
                            <button 
                                onClick={() => setShowApprovalModal(false)}
                                className="btn btn-secondary"
                            >
                                Cancel
                            </button>
                            <button 
                                onClick={handleApproval}
                                disabled={!approvalData.decision}
                                className="btn btn-primary"
                            >
                                Submit Decision
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AdminRMADashboard;
