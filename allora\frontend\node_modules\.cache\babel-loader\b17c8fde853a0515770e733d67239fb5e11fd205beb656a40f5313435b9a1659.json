{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\pages\\\\PublicStorePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useError } from '../contexts/ErrorContext';\nimport { useLoading } from '../contexts/LoadingContext';\nimport { LoadingSpinner } from '../components/LoadingComponents';\nimport { ErrorAlert } from '../components/ErrorComponents';\nimport ProductCard from '../components/ProductCard';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\nconst PublicStorePage = () => {\n  _s();\n  const {\n    storeSlug\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    handleApiError\n  } = useError();\n  const {\n    isLoading,\n    withLoading\n  } = useLoading();\n  const [store, setStore] = useState(null);\n  const [products, setProducts] = useState([]);\n  const [pagination, setPagination] = useState({});\n  const [filters, setFilters] = useState({\n    category: '',\n    sort: 'newest',\n    page: 1\n  });\n  useEffect(() => {\n    fetchStoreProfile();\n  }, [storeSlug]);\n  useEffect(() => {\n    if (store) {\n      fetchStoreProducts();\n    }\n  }, [store, filters]);\n  const fetchStoreProfile = async () => {\n    try {\n      await withLoading('store_profile', async () => {\n        const response = await fetch(`${API_BASE_URL}/store/${storeSlug}`);\n        if (!response.ok) {\n          if (response.status === 404) {\n            navigate('/404');\n            return;\n          }\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to load store');\n        }\n        const data = await response.json();\n        setStore(data.data);\n      }, 'Loading store...');\n    } catch (error) {\n      handleApiError(error, {\n        action: 'fetch_store'\n      });\n    }\n  };\n  const fetchStoreProducts = async () => {\n    try {\n      await withLoading('store_products', async () => {\n        const queryParams = new URLSearchParams({\n          page: filters.page,\n          per_page: 20,\n          sort: filters.sort,\n          ...(filters.category && {\n            category: filters.category\n          })\n        });\n        const response = await fetch(`${API_BASE_URL}/store/${storeSlug}/products?${queryParams}`);\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to load products');\n        }\n        const data = await response.json();\n        setProducts(data.data.products);\n        setPagination(data.data.pagination);\n      }, 'Loading products...');\n    } catch (error) {\n      handleApiError(error, {\n        action: 'fetch_products'\n      });\n    }\n  };\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value,\n      page: 1 // Reset to first page when filtering\n    }));\n  };\n  const handlePageChange = newPage => {\n    setFilters(prev => ({\n      ...prev,\n      page: newPage\n    }));\n  };\n  if (isLoading('store_profile')) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this);\n  }\n  if (!store) {\n    return null;\n  }\n\n  // Get unique categories from products\n  const categories = [...new Set(products.map(product => product.category))].filter(Boolean);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm\",\n      children: [store.store_banner && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-48 md:h-64 overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: store.store_banner,\n          alt: `${store.store_name} banner`,\n          className: \"w-full h-full object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-6\",\n          children: [store.store_logo && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: store.store_logo,\n              alt: `${store.store_name} logo`,\n              className: \"h-20 w-20 rounded-lg object-cover border-2 border-white shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-gray-900\",\n              children: store.store_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mt-2\",\n              children: store.store_description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-6 mt-4 text-sm text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900\",\n                  children: store.total_products\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1\",\n                  children: \"Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900\",\n                  children: store.total_orders\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1\",\n                  children: \"Orders Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), store.rating > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: `h-4 w-4 ${i < Math.floor(store.rating) ? 'text-yellow-400' : 'text-gray-300'}`,\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this)\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 font-medium text-gray-900\",\n                  children: store.rating.toFixed(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1\",\n                  children: [\"(\", store.total_reviews, \" reviews)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n              children: [store.processing_time && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-4 w-4 mr-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this), \"Processing: \", store.processing_time]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), store.free_shipping_threshold > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-4 w-4 mr-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this), \"Free shipping on orders \\u20B9\", store.free_shipping_threshold, \"+\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), store.min_order_amount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-4 w-4 mr-2\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), \"Min order: \\u20B9\", store.min_order_amount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.category,\n              onChange: e => handleFilterChange('category', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category,\n                children: category\n              }, category, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Sort By\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.sort,\n              onChange: e => handleFilterChange('sort', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"price_low\",\n                children: \"Price: Low to High\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"price_high\",\n                children: \"Price: High to Low\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"rating\",\n                children: \"Highest Rated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setFilters({\n                category: '',\n                sort: 'newest',\n                page: 1\n              }),\n              className: \"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), isLoading('store_products') ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this) : products.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n          children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), pagination.pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-between sm:hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handlePageChange(pagination.page - 1),\n              disabled: !pagination.has_prev,\n              className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handlePageChange(pagination.page + 1),\n              disabled: !pagination.has_next,\n              className: \"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-700\",\n                children: [\"Showing page \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: pagination.page\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 36\n                }, this), \" of\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: pagination.pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this), \" (\", pagination.total, \" total products)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"nav\", {\n                className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePageChange(pagination.page - 1),\n                  disabled: !pagination.has_prev,\n                  className: \"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: \"Previous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePageChange(pagination.page + 1),\n                  disabled: !pagination.has_next,\n                  className: \"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: \"Next\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto h-12 w-12 text-gray-400\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"No products found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: filters.category || filters.sort !== 'newest' ? 'Try adjusting your filters.' : 'This store hasn\\'t added any products yet.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), (store.return_policy || store.shipping_policy || store.terms_conditions) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-6\",\n          children: \"Store Policies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: [store.return_policy && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-3\",\n              children: \"Return Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 whitespace-pre-line\",\n              children: store.return_policy\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 17\n          }, this), store.shipping_policy && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-3\",\n              children: \"Shipping Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 whitespace-pre-line\",\n              children: store.shipping_policy\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 17\n          }, this), store.terms_conditions && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-3\",\n              children: \"Terms & Conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 whitespace-pre-line\",\n              children: store.terms_conditions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ErrorAlert, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(PublicStorePage, \"AgT7rNv/RGJsCHEQ9NEIWzKP+l0=\", false, function () {\n  return [useParams, useNavigate, useError, useLoading];\n});\n_c = PublicStorePage;\nexport default PublicStorePage;\nvar _c;\n$RefreshReg$(_c, \"PublicStorePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useError", "useLoading", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ProductCard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "PublicStorePage", "_s", "storeSlug", "navigate", "handleApiError", "isLoading", "with<PERSON>oa<PERSON>", "store", "setStore", "products", "setProducts", "pagination", "setPagination", "filters", "setFilters", "category", "sort", "page", "fetchStoreProfile", "fetchStoreProducts", "response", "fetch", "ok", "status", "errorData", "json", "Error", "error", "data", "action", "queryParams", "URLSearchParams", "per_page", "handleFilterChange", "key", "value", "prev", "handlePageChange", "newPage", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "categories", "Set", "map", "product", "filter", "Boolean", "store_banner", "src", "alt", "store_name", "store_logo", "store_description", "total_products", "total_orders", "rating", "Array", "_", "i", "Math", "floor", "fill", "viewBox", "d", "toFixed", "total_reviews", "processing_time", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "free_shipping_threshold", "min_order_amount", "onChange", "e", "target", "onClick", "length", "id", "pages", "disabled", "has_prev", "has_next", "total", "return_policy", "shipping_policy", "terms_conditions", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/pages/PublicStorePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useError } from '../contexts/ErrorContext';\nimport { useLoading } from '../contexts/LoadingContext';\nimport { LoadingSpinner } from '../components/LoadingComponents';\nimport { ErrorAlert } from '../components/ErrorComponents';\nimport ProductCard from '../components/ProductCard';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nconst PublicStorePage = () => {\n  const { storeSlug } = useParams();\n  const navigate = useNavigate();\n  const { handleApiError } = useError();\n  const { isLoading, withLoading } = useLoading();\n\n  const [store, setStore] = useState(null);\n  const [products, setProducts] = useState([]);\n  const [pagination, setPagination] = useState({});\n  const [filters, setFilters] = useState({\n    category: '',\n    sort: 'newest',\n    page: 1\n  });\n\n  useEffect(() => {\n    fetchStoreProfile();\n  }, [storeSlug]);\n\n  useEffect(() => {\n    if (store) {\n      fetchStoreProducts();\n    }\n  }, [store, filters]);\n\n  const fetchStoreProfile = async () => {\n    try {\n      await withLoading('store_profile', async () => {\n        const response = await fetch(`${API_BASE_URL}/store/${storeSlug}`);\n\n        if (!response.ok) {\n          if (response.status === 404) {\n            navigate('/404');\n            return;\n          }\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to load store');\n        }\n\n        const data = await response.json();\n        setStore(data.data);\n      }, 'Loading store...');\n    } catch (error) {\n      handleApiError(error, { action: 'fetch_store' });\n    }\n  };\n\n  const fetchStoreProducts = async () => {\n    try {\n      await withLoading('store_products', async () => {\n        const queryParams = new URLSearchParams({\n          page: filters.page,\n          per_page: 20,\n          sort: filters.sort,\n          ...(filters.category && { category: filters.category })\n        });\n\n        const response = await fetch(`${API_BASE_URL}/store/${storeSlug}/products?${queryParams}`);\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to load products');\n        }\n\n        const data = await response.json();\n        setProducts(data.data.products);\n        setPagination(data.data.pagination);\n      }, 'Loading products...');\n    } catch (error) {\n      handleApiError(error, { action: 'fetch_products' });\n    }\n  };\n\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value,\n      page: 1 // Reset to first page when filtering\n    }));\n  };\n\n  const handlePageChange = (newPage) => {\n    setFilters(prev => ({\n      ...prev,\n      page: newPage\n    }));\n  };\n\n  if (isLoading('store_profile')) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <LoadingSpinner size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!store) {\n    return null;\n  }\n\n  // Get unique categories from products\n  const categories = [...new Set(products.map(product => product.category))].filter(Boolean);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Store Header */}\n      <div className=\"bg-white shadow-sm\">\n        {/* Store Banner */}\n        {store.store_banner && (\n          <div className=\"h-48 md:h-64 overflow-hidden\">\n            <img\n              src={store.store_banner}\n              alt={`${store.store_name} banner`}\n              className=\"w-full h-full object-cover\"\n            />\n          </div>\n        )}\n\n        {/* Store Info */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-start space-x-6\">\n            {/* Store Logo */}\n            {store.store_logo && (\n              <div className=\"flex-shrink-0\">\n                <img\n                  src={store.store_logo}\n                  alt={`${store.store_name} logo`}\n                  className=\"h-20 w-20 rounded-lg object-cover border-2 border-white shadow-lg\"\n                />\n              </div>\n            )}\n\n            {/* Store Details */}\n            <div className=\"flex-1\">\n              <h1 className=\"text-3xl font-bold text-gray-900\">{store.store_name}</h1>\n              <p className=\"text-gray-600 mt-2\">{store.store_description}</p>\n              \n              {/* Store Stats */}\n              <div className=\"flex items-center space-x-6 mt-4 text-sm text-gray-500\">\n                <div className=\"flex items-center\">\n                  <span className=\"font-medium text-gray-900\">{store.total_products}</span>\n                  <span className=\"ml-1\">Products</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <span className=\"font-medium text-gray-900\">{store.total_orders}</span>\n                  <span className=\"ml-1\">Orders Completed</span>\n                </div>\n                {store.rating > 0 && (\n                  <div className=\"flex items-center\">\n                    <div className=\"flex items-center\">\n                      {[...Array(5)].map((_, i) => (\n                        <svg\n                          key={i}\n                          className={`h-4 w-4 ${i < Math.floor(store.rating) ? 'text-yellow-400' : 'text-gray-300'}`}\n                          fill=\"currentColor\"\n                          viewBox=\"0 0 20 20\"\n                        >\n                          <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                        </svg>\n                      ))}\n                    </div>\n                    <span className=\"ml-1 font-medium text-gray-900\">{store.rating.toFixed(1)}</span>\n                    <span className=\"ml-1\">({store.total_reviews} reviews)</span>\n                  </div>\n                )}\n              </div>\n\n              {/* Store Policies */}\n              <div className=\"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                {store.processing_time && (\n                  <div className=\"flex items-center text-gray-600\">\n                    <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    Processing: {store.processing_time}\n                  </div>\n                )}\n                {store.free_shipping_threshold > 0 && (\n                  <div className=\"flex items-center text-gray-600\">\n                    <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                    </svg>\n                    Free shipping on orders ₹{store.free_shipping_threshold}+\n                  </div>\n                )}\n                {store.min_order_amount > 0 && (\n                  <div className=\"flex items-center text-gray-600\">\n                    <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\" />\n                    </svg>\n                    Min order: ₹{store.min_order_amount}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Products Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category\n              </label>\n              <select\n                value={filters.category}\n                onChange={(e) => handleFilterChange('category', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>{category}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Sort By\n              </label>\n              <select\n                value={filters.sort}\n                onChange={(e) => handleFilterChange('sort', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"price_low\">Price: Low to High</option>\n                <option value=\"price_high\">Price: High to Low</option>\n                <option value=\"rating\">Highest Rated</option>\n              </select>\n            </div>\n            <div className=\"flex items-end\">\n              <button\n                onClick={() => setFilters({ category: '', sort: 'newest', page: 1 })}\n                className=\"px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n              >\n                Clear Filters\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Products Grid */}\n        {isLoading('store_products') ? (\n          <div className=\"flex justify-center py-12\">\n            <LoadingSpinner size=\"large\" />\n          </div>\n        ) : products.length > 0 ? (\n          <>\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {products.map((product) => (\n                <ProductCard key={product.id} product={product} />\n              ))}\n            </div>\n\n            {/* Pagination */}\n            {pagination.pages > 1 && (\n              <div className=\"mt-8 bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow\">\n                <div className=\"flex-1 flex justify-between sm:hidden\">\n                  <button\n                    onClick={() => handlePageChange(pagination.page - 1)}\n                    disabled={!pagination.has_prev}\n                    className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    Previous\n                  </button>\n                  <button\n                    onClick={() => handlePageChange(pagination.page + 1)}\n                    disabled={!pagination.has_next}\n                    className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    Next\n                  </button>\n                </div>\n                <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-700\">\n                      Showing page <span className=\"font-medium\">{pagination.page}</span> of{' '}\n                      <span className=\"font-medium\">{pagination.pages}</span> ({pagination.total} total products)\n                    </p>\n                  </div>\n                  <div>\n                    <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                      <button\n                        onClick={() => handlePageChange(pagination.page - 1)}\n                        disabled={!pagination.has_prev}\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        Previous\n                      </button>\n                      <button\n                        onClick={() => handlePageChange(pagination.page + 1)}\n                        disabled={!pagination.has_next}\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        Next\n                      </button>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            )}\n          </>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"mx-auto h-12 w-12 text-gray-400\">\n              <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n              </svg>\n            </div>\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No products found</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {filters.category || filters.sort !== 'newest' ? 'Try adjusting your filters.' : 'This store hasn\\'t added any products yet.'}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Store Policies Section */}\n      {(store.return_policy || store.shipping_policy || store.terms_conditions) && (\n        <div className=\"bg-white border-t\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Store Policies</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {store.return_policy && (\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-3\">Return Policy</h3>\n                  <p className=\"text-gray-600 whitespace-pre-line\">{store.return_policy}</p>\n                </div>\n              )}\n              {store.shipping_policy && (\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-3\">Shipping Policy</h3>\n                  <p className=\"text-gray-600 whitespace-pre-line\">{store.shipping_policy}</p>\n                </div>\n              )}\n              {store.terms_conditions && (\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-3\">Terms & Conditions</h3>\n                  <p className=\"text-gray-600 whitespace-pre-line\">{store.terms_conditions}</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Global Error Display */}\n      <ErrorAlert />\n    </div>\n  );\n};\n\nexport default PublicStorePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAU,CAAC,GAAGjB,SAAS,CAAC,CAAC;EACjC,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAe,CAAC,GAAGjB,QAAQ,CAAC,CAAC;EACrC,MAAM;IAAEkB,SAAS;IAAEC;EAAY,CAAC,GAAGlB,UAAU,CAAC,CAAC;EAE/C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC;IACrCgC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE;EACR,CAAC,CAAC;EAEFjC,SAAS,CAAC,MAAM;IACdkC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;EAEflB,SAAS,CAAC,MAAM;IACd,IAAIuB,KAAK,EAAE;MACTY,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACZ,KAAK,EAAEM,OAAO,CAAC,CAAC;EAEpB,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMZ,WAAW,CAAC,eAAe,EAAE,YAAY;QAC7C,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGzB,YAAY,UAAUM,SAAS,EAAE,CAAC;QAElE,IAAI,CAACkB,QAAQ,CAACE,EAAE,EAAE;UAChB,IAAIF,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;YAC3BpB,QAAQ,CAAC,MAAM,CAAC;YAChB;UACF;UACA,MAAMqB,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,KAAK,IAAI,sBAAsB,CAAC;QAC5D;QAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCjB,QAAQ,CAACoB,IAAI,CAACA,IAAI,CAAC;MACrB,CAAC,EAAE,kBAAkB,CAAC;IACxB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdvB,cAAc,CAACuB,KAAK,EAAE;QAAEE,MAAM,EAAE;MAAc,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMV,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMb,WAAW,CAAC,gBAAgB,EAAE,YAAY;QAC9C,MAAMwB,WAAW,GAAG,IAAIC,eAAe,CAAC;UACtCd,IAAI,EAAEJ,OAAO,CAACI,IAAI;UAClBe,QAAQ,EAAE,EAAE;UACZhB,IAAI,EAAEH,OAAO,CAACG,IAAI;UAClB,IAAIH,OAAO,CAACE,QAAQ,IAAI;YAAEA,QAAQ,EAAEF,OAAO,CAACE;UAAS,CAAC;QACxD,CAAC,CAAC;QAEF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGzB,YAAY,UAAUM,SAAS,aAAa4B,WAAW,EAAE,CAAC;QAE1F,IAAI,CAACV,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAME,SAAS,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,KAAK,IAAI,yBAAyB,CAAC;QAC/D;QAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCf,WAAW,CAACkB,IAAI,CAACA,IAAI,CAACnB,QAAQ,CAAC;QAC/BG,aAAa,CAACgB,IAAI,CAACA,IAAI,CAACjB,UAAU,CAAC;MACrC,CAAC,EAAE,qBAAqB,CAAC;IAC3B,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdvB,cAAc,CAACuB,KAAK,EAAE;QAAEE,MAAM,EAAE;MAAiB,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMI,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACzCrB,UAAU,CAACsB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC,KAAK;MACZlB,IAAI,EAAE,CAAC,CAAC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMoB,gBAAgB,GAAIC,OAAO,IAAK;IACpCxB,UAAU,CAACsB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPnB,IAAI,EAAEqB;IACR,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAIjC,SAAS,CAAC,eAAe,CAAC,EAAE;IAC9B,oBACEZ,OAAA;MAAK8C,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE/C,OAAA,CAACJ,cAAc;QAACoD,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAEV;EAEA,IAAI,CAACtC,KAAK,EAAE;IACV,OAAO,IAAI;EACb;;EAEA;EACA,MAAMuC,UAAU,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACtC,QAAQ,CAACuC,GAAG,CAACC,OAAO,IAAIA,OAAO,CAAClC,QAAQ,CAAC,CAAC,CAAC,CAACmC,MAAM,CAACC,OAAO,CAAC;EAE1F,oBACE1D,OAAA;IAAK8C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtC/C,OAAA;MAAK8C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,GAEhCjC,KAAK,CAAC6C,YAAY,iBACjB3D,OAAA;QAAK8C,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3C/C,OAAA;UACE4D,GAAG,EAAE9C,KAAK,CAAC6C,YAAa;UACxBE,GAAG,EAAE,GAAG/C,KAAK,CAACgD,UAAU,SAAU;UAClChB,SAAS,EAAC;QAA4B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAGDpD,OAAA;QAAK8C,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D/C,OAAA;UAAK8C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAExCjC,KAAK,CAACiD,UAAU,iBACf/D,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B/C,OAAA;cACE4D,GAAG,EAAE9C,KAAK,CAACiD,UAAW;cACtBF,GAAG,EAAE,GAAG/C,KAAK,CAACgD,UAAU,OAAQ;cAChChB,SAAS,EAAC;YAAmE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAGDpD,OAAA;YAAK8C,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB/C,OAAA;cAAI8C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEjC,KAAK,CAACgD;YAAU;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxEpD,OAAA;cAAG8C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEjC,KAAK,CAACkD;YAAiB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAG/DpD,OAAA;cAAK8C,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACrE/C,OAAA;gBAAK8C,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/C,OAAA;kBAAM8C,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEjC,KAAK,CAACmD;gBAAc;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEpD,OAAA;kBAAM8C,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNpD,OAAA;gBAAK8C,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/C,OAAA;kBAAM8C,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEjC,KAAK,CAACoD;gBAAY;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvEpD,OAAA;kBAAM8C,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,EACLtC,KAAK,CAACqD,MAAM,GAAG,CAAC,iBACfnE,OAAA;gBAAK8C,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/C,OAAA;kBAAK8C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAC/B,CAAC,GAAGqB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACb,GAAG,CAAC,CAACc,CAAC,EAAEC,CAAC,kBACtBtE,OAAA;oBAEE8C,SAAS,EAAE,WAAWwB,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC1D,KAAK,CAACqD,MAAM,CAAC,GAAG,iBAAiB,GAAG,eAAe,EAAG;oBAC3FM,IAAI,EAAC,cAAc;oBACnBC,OAAO,EAAC,WAAW;oBAAA3B,QAAA,eAEnB/C,OAAA;sBAAM2E,CAAC,EAAC;oBAA0V;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC,GALhWkB,CAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMH,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpD,OAAA;kBAAM8C,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAEjC,KAAK,CAACqD,MAAM,CAACS,OAAO,CAAC,CAAC;gBAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjFpD,OAAA;kBAAM8C,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAC,GAAC,EAACjC,KAAK,CAAC+D,aAAa,EAAC,WAAS;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNpD,OAAA;cAAK8C,SAAS,EAAC,oDAAoD;cAAAC,QAAA,GAChEjC,KAAK,CAACgE,eAAe,iBACpB9E,OAAA;gBAAK8C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C/C,OAAA;kBAAK8C,SAAS,EAAC,cAAc;kBAAC2B,IAAI,EAAC,MAAM;kBAACM,MAAM,EAAC,cAAc;kBAACL,OAAO,EAAC,WAAW;kBAAA3B,QAAA,eACjF/C,OAAA;oBAAMgF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACP,CAAC,EAAC;kBAA6C;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH,CAAC,gBACM,EAACtC,KAAK,CAACgE,eAAe;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CACN,EACAtC,KAAK,CAACqE,uBAAuB,GAAG,CAAC,iBAChCnF,OAAA;gBAAK8C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C/C,OAAA;kBAAK8C,SAAS,EAAC,cAAc;kBAAC2B,IAAI,EAAC,MAAM;kBAACM,MAAM,EAAC,cAAc;kBAACL,OAAO,EAAC,WAAW;kBAAA3B,QAAA,eACjF/C,OAAA;oBAAMgF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACP,CAAC,EAAC;kBAAiE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtI,CAAC,kCACmB,EAACtC,KAAK,CAACqE,uBAAuB,EAAC,GAC1D;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACAtC,KAAK,CAACsE,gBAAgB,GAAG,CAAC,iBACzBpF,OAAA;gBAAK8C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C/C,OAAA;kBAAK8C,SAAS,EAAC,cAAc;kBAAC2B,IAAI,EAAC,MAAM;kBAACM,MAAM,EAAC,cAAc;kBAACL,OAAO,EAAC,WAAW;kBAAA3B,QAAA,eACjF/C,OAAA;oBAAMgF,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACP,CAAC,EAAC;kBAAoJ;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzN,CAAC,qBACM,EAACtC,KAAK,CAACsE,gBAAgB;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MAAK8C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D/C,OAAA;QAAK8C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClD/C,OAAA;UAAK8C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD/C,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAO8C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpD,OAAA;cACE0C,KAAK,EAAEtB,OAAO,CAACE,QAAS;cACxB+D,QAAQ,EAAGC,CAAC,IAAK9C,kBAAkB,CAAC,UAAU,EAAE8C,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;cAChEI,SAAS,EAAC,6HAA6H;cAAAC,QAAA,gBAEvI/C,OAAA;gBAAQ0C,KAAK,EAAC,EAAE;gBAAAK,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvCC,UAAU,CAACE,GAAG,CAACjC,QAAQ,iBACtBtB,OAAA;gBAAuB0C,KAAK,EAAEpB,QAAS;gBAAAyB,QAAA,EAAEzB;cAAQ,GAApCA,QAAQ;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNpD,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAO8C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpD,OAAA;cACE0C,KAAK,EAAEtB,OAAO,CAACG,IAAK;cACpB8D,QAAQ,EAAGC,CAAC,IAAK9C,kBAAkB,CAAC,MAAM,EAAE8C,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;cAC5DI,SAAS,EAAC,6HAA6H;cAAAC,QAAA,gBAEvI/C,OAAA;gBAAQ0C,KAAK,EAAC,QAAQ;gBAAAK,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CpD,OAAA;gBAAQ0C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrDpD,OAAA;gBAAQ0C,KAAK,EAAC,YAAY;gBAAAK,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDpD,OAAA;gBAAQ0C,KAAK,EAAC,QAAQ;gBAAAK,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNpD,OAAA;YAAK8C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B/C,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAMnE,UAAU,CAAC;gBAAEC,QAAQ,EAAE,EAAE;gBAAEC,IAAI,EAAE,QAAQ;gBAAEC,IAAI,EAAE;cAAE,CAAC,CAAE;cACrEsB,SAAS,EAAC,kLAAkL;cAAAC,QAAA,EAC7L;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxC,SAAS,CAAC,gBAAgB,CAAC,gBAC1BZ,OAAA;QAAK8C,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACxC/C,OAAA,CAACJ,cAAc;UAACoD,IAAI,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,GACJpC,QAAQ,CAACyE,MAAM,GAAG,CAAC,gBACrBzF,OAAA,CAAAE,SAAA;QAAA6C,QAAA,gBACE/C,OAAA;UAAK8C,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjF/B,QAAQ,CAACuC,GAAG,CAAEC,OAAO,iBACpBxD,OAAA,CAACF,WAAW;YAAkB0D,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAACkC,EAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGLlC,UAAU,CAACyE,KAAK,GAAG,CAAC,iBACnB3F,OAAA;UAAK8C,SAAS,EAAC,8GAA8G;UAAAC,QAAA,gBAC3H/C,OAAA;YAAK8C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD/C,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAAC1B,UAAU,CAACM,IAAI,GAAG,CAAC,CAAE;cACrDoE,QAAQ,EAAE,CAAC1E,UAAU,CAAC2E,QAAS;cAC/B/C,SAAS,EAAC,2LAA2L;cAAAC,QAAA,EACtM;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpD,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAAC1B,UAAU,CAACM,IAAI,GAAG,CAAC,CAAE;cACrDoE,QAAQ,EAAE,CAAC1E,UAAU,CAAC4E,QAAS;cAC/BhD,SAAS,EAAC,gMAAgM;cAAAC,QAAA,EAC3M;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNpD,OAAA;YAAK8C,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1E/C,OAAA;cAAA+C,QAAA,eACE/C,OAAA;gBAAG8C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,eACtB,eAAA/C,OAAA;kBAAM8C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE7B,UAAU,CAACM;gBAAI;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,OAAG,EAAC,GAAG,eAC1EpD,OAAA;kBAAM8C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE7B,UAAU,CAACyE;gBAAK;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,MAAE,EAAClC,UAAU,CAAC6E,KAAK,EAAC,kBAC7E;cAAA;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpD,OAAA;cAAA+C,QAAA,eACE/C,OAAA;gBAAK8C,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBACxE/C,OAAA;kBACEwF,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAAC1B,UAAU,CAACM,IAAI,GAAG,CAAC,CAAE;kBACrDoE,QAAQ,EAAE,CAAC1E,UAAU,CAAC2E,QAAS;kBAC/B/C,SAAS,EAAC,6LAA6L;kBAAAC,QAAA,EACxM;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpD,OAAA;kBACEwF,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAAC1B,UAAU,CAACM,IAAI,GAAG,CAAC,CAAE;kBACrDoE,QAAQ,EAAE,CAAC1E,UAAU,CAAC4E,QAAS;kBAC/BhD,SAAS,EAAC,6LAA6L;kBAAAC,QAAA,EACxM;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,eACD,CAAC,gBAEHpD,OAAA;QAAK8C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/C,OAAA;UAAK8C,SAAS,EAAC,iCAAiC;UAAAC,QAAA,eAC9C/C,OAAA;YAAKyE,IAAI,EAAC,MAAM;YAACM,MAAM,EAAC,cAAc;YAACL,OAAO,EAAC,WAAW;YAAA3B,QAAA,eACxD/C,OAAA;cAAMgF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACP,CAAC,EAAC;YAAiE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpD,OAAA;UAAI8C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EpD,OAAA;UAAG8C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtC3B,OAAO,CAACE,QAAQ,IAAIF,OAAO,CAACG,IAAI,KAAK,QAAQ,GAAG,6BAA6B,GAAG;QAA4C;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5H,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAACtC,KAAK,CAACkF,aAAa,IAAIlF,KAAK,CAACmF,eAAe,IAAInF,KAAK,CAACoF,gBAAgB,kBACtElG,OAAA;MAAK8C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC/C,OAAA;QAAK8C,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D/C,OAAA;UAAI8C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEpD,OAAA;UAAK8C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GACnDjC,KAAK,CAACkF,aAAa,iBAClBhG,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAI8C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEpD,OAAA;cAAG8C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEjC,KAAK,CAACkF;YAAa;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CACN,EACAtC,KAAK,CAACmF,eAAe,iBACpBjG,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAI8C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EpD,OAAA;cAAG8C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEjC,KAAK,CAACmF;YAAe;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CACN,EACAtC,KAAK,CAACoF,gBAAgB,iBACrBlG,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAI8C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9EpD,OAAA;cAAG8C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEjC,KAAK,CAACoF;YAAgB;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDpD,OAAA,CAACH,UAAU;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAlWID,eAAe;EAAA,QACGf,SAAS,EACdC,WAAW,EACDC,QAAQ,EACAC,UAAU;AAAA;AAAAwG,EAAA,GAJzC5F,eAAe;AAoWrB,eAAeA,eAAe;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}