{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\components\\\\ModernMinimalistProductCard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useCart } from '../contexts/CartContext';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { formatPrice } from '../utils/currency';\nimport { getProductImageUrl, getLoadingPlaceholder, handleImageError } from '../utils/imageUtils';\nimport SellerInfo from './SellerInfo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernMinimalistProductCard = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  product\n}) => {\n  _s();\n  const [isLoading, setIsLoading] = useState(false);\n  const [isWishlisted, setIsWishlisted] = useState(false);\n  const {\n    addToCart\n  } = useCart();\n  const {\n    success,\n    error\n  } = useNotification();\n  const handleAddToCart = async e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsLoading(true);\n    try {\n      await addToCart(product.id, 1);\n      success(`${product.name} added to cart!`, {\n        title: 'Added to Cart'\n      });\n    } catch (err) {\n      error('Failed to add item to cart', {\n        title: 'Cart Error'\n      });\n      console.error('Add to cart error:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleProductClick = () => {\n    window.location.href = `/product/${product.id}`;\n  };\n  const handleWishlist = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsWishlisted(!isWishlisted);\n    success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist', {\n      title: 'Wishlist Updated'\n    });\n  };\n\n  // Calculate discount percentage if original price exists\n  const discountPercentage = product.originalPrice && product.originalPrice > product.price ? Math.round((product.originalPrice - product.price) / product.originalPrice * 100) : null;\n\n  // Generate star rating\n  const rating = product.rating || 4.2;\n  const fullStars = Math.floor(rating);\n  const hasHalfStar = rating % 1 >= 0.5;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-2xl hover:shadow-xl transition-all duration-300 p-4 h-full flex flex-col group cursor-pointer border border-gray-100 hover:border-gray-200 hover:-translate-y-1\",\n    onClick: handleProductClick,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative mb-4 overflow-hidden rounded-xl bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: getProductImageUrl(product),\n        alt: product.name || 'Product Image',\n        className: \"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500\",\n        loading: \"lazy\",\n        onError: e => handleImageError(e),\n        style: {\n          display: 'block',\n          width: '100%',\n          height: '192px',\n          // Fixed height for consistency\n          objectFit: 'cover'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleWishlist,\n        className: `absolute top-4 right-4 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm ${isWishlisted ? 'bg-red-500 text-white shadow-lg' : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500 hover:shadow-md'}`,\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5\",\n          fill: isWishlisted ? 'currentColor' : 'none',\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), discountPercentage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg\",\n        children: [\"-\", discountPercentage, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-base font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200\",\n        children: product.name || 'Unnamed Product'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [[...Array(fullStars)].map((_, i) => /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400 text-sm\",\n            children: \"\\u2605\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this)), hasHalfStar && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400 text-sm\",\n            children: \"\\u2606\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 29\n          }, this), [...Array(5 - fullStars - (hasHalfStar ? 1 : 0))].map((_, i) => /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-300 text-sm\",\n            children: \"\\u2605\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-500 ml-2\",\n          children: [\"(\", rating, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xl font-bold text-gray-900\",\n          children: formatPrice(product.price || 0)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500 line-through ml-2\",\n          children: formatPrice(product.originalPrice)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1 mb-3 flex-1\",\n        children: [product.description && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 line-clamp-2\",\n          children: product.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between text-xs text-gray-500\",\n          children: [product.brand && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"truncate\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Brand:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), \" \", product.brand]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this), product.category && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"truncate ml-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Category:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), \" \", product.category]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), (product.sustainabilityScore || product.sustainability_score) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500 font-medium\",\n            children: \"Eco:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `h-full rounded-full ${(product.sustainabilityScore || product.sustainability_score) >= 80 ? 'bg-green-500' : (product.sustainabilityScore || product.sustainability_score) >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`,\n                style: {\n                  width: `${product.sustainabilityScore || product.sustainability_score}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-600 ml-1\",\n              children: [product.sustainabilityScore || product.sustainability_score, \"/100\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), (product.stockQuantity !== undefined || product.stock_quantity !== undefined) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500 font-medium\",\n            children: \"Stock:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-xs font-medium ${(product.stockQuantity || product.stock_quantity || 0) > 0 ? 'text-green-600' : 'text-red-600'}`,\n            children: (product.stockQuantity || product.stock_quantity || 0) > 0 ? 'In Stock' : 'Out of Stock'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), product.seller && /*#__PURE__*/_jsxDEV(SellerInfo, {\n        seller: product.seller,\n        compact: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddToCart,\n        disabled: isLoading || (product.stockQuantity || product.stock_quantity || 0) <= 0,\n        className: `w-full py-2 px-3 rounded-lg font-medium text-sm transition-all duration-200 ${isLoading ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : (product.stockQuantity || product.stock_quantity || 0) <= 0 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-md active:scale-95'}`,\n        children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-3 w-3 border-2 border-gray-400 border-t-transparent mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), \"Adding...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this) : (product.stockQuantity || product.stock_quantity || 0) <= 0 ? 'Out of Stock' : 'Add to Cart'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n}, \"GNlKEkp/w6AI/AKc0+YM7xevWew=\", false, function () {\n  return [useCart, useNotification];\n})), \"GNlKEkp/w6AI/AKc0+YM7xevWew=\", false, function () {\n  return [useCart, useNotification];\n});\n_c2 = ModernMinimalistProductCard;\nModernMinimalistProductCard.displayName = 'ModernMinimalistProductCard';\nexport default ModernMinimalistProductCard;\nvar _c, _c2;\n$RefreshReg$(_c, \"ModernMinimalistProductCard$React.memo\");\n$RefreshReg$(_c2, \"ModernMinimalistProductCard\");", "map": {"version": 3, "names": ["React", "useState", "useCart", "useNotification", "formatPrice", "getProductImageUrl", "getLoadingPlaceholder", "handleImageError", "SellerInfo", "jsxDEV", "_jsxDEV", "ModernMinimalistProductCard", "_s", "memo", "_c", "product", "isLoading", "setIsLoading", "isWishlisted", "setIsWishlisted", "addToCart", "success", "error", "handleAddToCart", "e", "preventDefault", "stopPropagation", "id", "name", "title", "err", "console", "handleProductClick", "window", "location", "href", "handleWishlist", "discountPercentage", "originalPrice", "price", "Math", "round", "rating", "fullStars", "floor", "hasHalfStar", "className", "onClick", "children", "src", "alt", "loading", "onError", "style", "display", "width", "height", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "Array", "map", "_", "i", "description", "brand", "category", "sustainabilityScore", "sustainability_score", "stockQuantity", "undefined", "stock_quantity", "seller", "compact", "disabled", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/components/ModernMinimalistProductCard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useCart } from '../contexts/CartContext';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { formatPrice } from '../utils/currency';\nimport { getProductImageUrl, getLoadingPlaceholder, handleImageError } from '../utils/imageUtils';\nimport SellerInfo from './SellerInfo';\n\nconst ModernMinimalistProductCard = React.memo(({ product }) => {\n  const [isLoading, setIsLoading] = useState(false);\n  const [isWishlisted, setIsWishlisted] = useState(false);\n  const { addToCart } = useCart();\n  const { success, error } = useNotification();\n\n  const handleAddToCart = async (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    setIsLoading(true);\n    try {\n      await addToCart(product.id, 1);\n      success(`${product.name} added to cart!`, {\n        title: 'Added to Cart'\n      });\n    } catch (err) {\n      error('Failed to add item to cart', {\n        title: 'Cart Error'\n      });\n      console.error('Add to cart error:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleProductClick = () => {\n    window.location.href = `/product/${product.id}`;\n  };\n\n  const handleWishlist = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsWishlisted(!isWishlisted);\n    success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist', {\n      title: 'Wishlist Updated'\n    });\n  };\n\n  // Calculate discount percentage if original price exists\n  const discountPercentage = product.originalPrice && product.originalPrice > product.price\n    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)\n    : null;\n\n  // Generate star rating\n  const rating = product.rating || 4.2;\n  const fullStars = Math.floor(rating);\n  const hasHalfStar = rating % 1 >= 0.5;\n\n  return (\n    <div\n      className=\"bg-white rounded-2xl hover:shadow-xl transition-all duration-300 p-4 h-full flex flex-col group cursor-pointer border border-gray-100 hover:border-gray-200 hover:-translate-y-1\"\n      onClick={handleProductClick}\n    >\n      {/* Product Image - Fixed Height & Fully Visible */}\n      <div className=\"relative mb-4 overflow-hidden rounded-xl bg-gray-50\">\n        <img\n          src={getProductImageUrl(product)}\n          alt={product.name || 'Product Image'}\n          className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500\"\n          loading=\"lazy\"\n          onError={(e) => handleImageError(e)}\n          style={{\n            display: 'block',\n            width: '100%',\n            height: '192px', // Fixed height for consistency\n            objectFit: 'cover'\n          }}\n        />\n        \n        {/* Wishlist Button */}\n        <button\n          onClick={handleWishlist}\n          className={`absolute top-4 right-4 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm ${\n            isWishlisted \n              ? 'bg-red-500 text-white shadow-lg' \n              : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500 hover:shadow-md'\n          }`}\n        >\n          <svg className=\"w-5 h-5\" fill={isWishlisted ? 'currentColor' : 'none'} stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n          </svg>\n        </button>\n\n        {/* Discount Badge */}\n        {discountPercentage && (\n          <div className=\"absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg\">\n            -{discountPercentage}%\n          </div>\n        )}\n      </div>\n\n      {/* Product Info */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Product Name */}\n        <h3 className=\"text-base font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200\">\n          {product.name || 'Unnamed Product'}\n        </h3>\n\n        {/* Rating */}\n        <div className=\"flex items-center mb-2\">\n          <div className=\"flex items-center\">\n            {[...Array(fullStars)].map((_, i) => (\n              <span key={i} className=\"text-yellow-400 text-sm\">★</span>\n            ))}\n            {hasHalfStar && <span className=\"text-yellow-400 text-sm\">☆</span>}\n            {[...Array(5 - fullStars - (hasHalfStar ? 1 : 0))].map((_, i) => (\n              <span key={i} className=\"text-gray-300 text-sm\">★</span>\n            ))}\n          </div>\n          <span className=\"text-xs text-gray-500 ml-2\">({rating})</span>\n        </div>\n\n        {/* Price */}\n        <div className=\"flex items-center mb-3\">\n          <span className=\"text-xl font-bold text-gray-900\">\n            {formatPrice(product.price || 0)}\n          </span>\n          {product.originalPrice && product.originalPrice > product.price && (\n            <span className=\"text-sm text-gray-500 line-through ml-2\">\n              {formatPrice(product.originalPrice)}\n            </span>\n          )}\n        </div>\n\n        {/* Compact Product Details */}\n        <div className=\"space-y-1 mb-3 flex-1\">\n          {/* Description - Shortened */}\n          {product.description && (\n            <p className=\"text-xs text-gray-600 line-clamp-2\">\n              {product.description}\n            </p>\n          )}\n\n          {/* Brand & Category in one line */}\n          <div className=\"flex items-center justify-between text-xs text-gray-500\">\n            {product.brand && (\n              <span className=\"truncate\">\n                <span className=\"font-medium\">Brand:</span> {product.brand}\n              </span>\n            )}\n            {product.category && (\n              <span className=\"truncate ml-2\">\n                <span className=\"font-medium\">Category:</span> {product.category}\n              </span>\n            )}\n          </div>\n\n          {/* Sustainability Score - Compact */}\n          {(product.sustainabilityScore || product.sustainability_score) && (\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-xs text-gray-500 font-medium\">Eco:</span>\n              <div className=\"flex items-center flex-1\">\n                <div className=\"w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden\">\n                  <div\n                    className={`h-full rounded-full ${\n                      (product.sustainabilityScore || product.sustainability_score) >= 80 ? 'bg-green-500' :\n                      (product.sustainabilityScore || product.sustainability_score) >= 60 ? 'bg-yellow-500' : 'bg-red-500'\n                    }`}\n                    style={{ width: `${product.sustainabilityScore || product.sustainability_score}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-xs text-gray-600 ml-1\">{product.sustainabilityScore || product.sustainability_score}/100</span>\n              </div>\n            </div>\n          )}\n\n          {/* Stock Status - Compact */}\n          {(product.stockQuantity !== undefined || product.stock_quantity !== undefined) && (\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-xs text-gray-500 font-medium\">Stock:</span>\n              <span className={`text-xs font-medium ${\n                (product.stockQuantity || product.stock_quantity || 0) > 0 ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {(product.stockQuantity || product.stock_quantity || 0) > 0 ? 'In Stock' : 'Out of Stock'}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Seller Information - Compact */}\n        {product.seller && (\n          <SellerInfo seller={product.seller} compact={true} />\n        )}\n\n        {/* Add to Cart Button - Compact */}\n        <button\n          onClick={handleAddToCart}\n          disabled={isLoading || (product.stockQuantity || product.stock_quantity || 0) <= 0}\n          className={`w-full py-2 px-3 rounded-lg font-medium text-sm transition-all duration-200 ${\n            isLoading\n              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n              : (product.stockQuantity || product.stock_quantity || 0) <= 0\n              ? 'bg-gray-200 text-gray-500 cursor-not-allowed'\n              : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-md active:scale-95'\n          }`}\n        >\n          {isLoading ? (\n            <div className=\"flex items-center justify-center\">\n              <div className=\"animate-spin rounded-full h-3 w-3 border-2 border-gray-400 border-t-transparent mr-2\"></div>\n              Adding...\n            </div>\n          ) : (product.stockQuantity || product.stock_quantity || 0) <= 0 ? (\n            'Out of Stock'\n          ) : (\n            'Add to Cart'\n          )}\n        </button>\n      </div>\n    </div>\n  );\n});\n\nModernMinimalistProductCard.displayName = 'ModernMinimalistProductCard';\n\nexport default ModernMinimalistProductCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,kBAAkB,EAAEC,qBAAqB,EAAEC,gBAAgB,QAAQ,qBAAqB;AACjG,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,2BAA2B,gBAAAC,EAAA,cAAGZ,KAAK,CAACa,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG;AAAQ,CAAC,KAAK;EAAAH,EAAA;EAC9D,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAEmB;EAAU,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC/B,MAAM;IAAEmB,OAAO;IAAEC;EAAM,CAAC,GAAGnB,eAAe,CAAC,CAAC;EAE5C,MAAMoB,eAAe,GAAG,MAAOC,CAAC,IAAK;IACnCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IAEnBT,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMG,SAAS,CAACL,OAAO,CAACY,EAAE,EAAE,CAAC,CAAC;MAC9BN,OAAO,CAAC,GAAGN,OAAO,CAACa,IAAI,iBAAiB,EAAE;QACxCC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZR,KAAK,CAAC,4BAA4B,EAAE;QAClCO,KAAK,EAAE;MACT,CAAC,CAAC;MACFE,OAAO,CAACT,KAAK,CAAC,oBAAoB,EAAEQ,GAAG,CAAC;IAC1C,CAAC,SAAS;MACRb,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMe,kBAAkB,GAAGA,CAAA,KAAM;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAYpB,OAAO,CAACY,EAAE,EAAE;EACjD,CAAC;EAED,MAAMS,cAAc,GAAIZ,CAAC,IAAK;IAC5BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBP,eAAe,CAAC,CAACD,YAAY,CAAC;IAC9BG,OAAO,CAACH,YAAY,GAAG,uBAAuB,GAAG,mBAAmB,EAAE;MACpEW,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAGtB,OAAO,CAACuB,aAAa,IAAIvB,OAAO,CAACuB,aAAa,GAAGvB,OAAO,CAACwB,KAAK,GACrFC,IAAI,CAACC,KAAK,CAAE,CAAC1B,OAAO,CAACuB,aAAa,GAAGvB,OAAO,CAACwB,KAAK,IAAIxB,OAAO,CAACuB,aAAa,GAAI,GAAG,CAAC,GACnF,IAAI;;EAER;EACA,MAAMI,MAAM,GAAG3B,OAAO,CAAC2B,MAAM,IAAI,GAAG;EACpC,MAAMC,SAAS,GAAGH,IAAI,CAACI,KAAK,CAACF,MAAM,CAAC;EACpC,MAAMG,WAAW,GAAGH,MAAM,GAAG,CAAC,IAAI,GAAG;EAErC,oBACEhC,OAAA;IACEoC,SAAS,EAAC,kLAAkL;IAC5LC,OAAO,EAAEf,kBAAmB;IAAAgB,QAAA,gBAG5BtC,OAAA;MAAKoC,SAAS,EAAC,qDAAqD;MAAAE,QAAA,gBAClEtC,OAAA;QACEuC,GAAG,EAAE5C,kBAAkB,CAACU,OAAO,CAAE;QACjCmC,GAAG,EAAEnC,OAAO,CAACa,IAAI,IAAI,eAAgB;QACrCkB,SAAS,EAAC,kFAAkF;QAC5FK,OAAO,EAAC,MAAM;QACdC,OAAO,EAAG5B,CAAC,IAAKjB,gBAAgB,CAACiB,CAAC,CAAE;QACpC6B,KAAK,EAAE;UACLC,OAAO,EAAE,OAAO;UAChBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,OAAO;UAAE;UACjBC,SAAS,EAAE;QACb;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGFnD,OAAA;QACEqC,OAAO,EAAEX,cAAe;QACxBU,SAAS,EAAE,+HACT5B,YAAY,GACR,iCAAiC,GACjC,6EAA6E,EAChF;QAAA8B,QAAA,eAEHtC,OAAA;UAAKoC,SAAS,EAAC,SAAS;UAACgB,IAAI,EAAE5C,YAAY,GAAG,cAAc,GAAG,MAAO;UAAC6C,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAhB,QAAA,eAC9GtC,OAAA;YAAMuD,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAA6H;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGRxB,kBAAkB,iBACjB3B,OAAA;QAAKoC,SAAS,EAAC,mGAAmG;QAAAE,QAAA,GAAC,GAChH,EAACX,kBAAkB,EAAC,GACvB;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNnD,OAAA;MAAKoC,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBAEnCtC,OAAA;QAAIoC,SAAS,EAAC,kHAAkH;QAAAE,QAAA,EAC7HjC,OAAO,CAACa,IAAI,IAAI;MAAiB;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAGLnD,OAAA;QAAKoC,SAAS,EAAC,wBAAwB;QAAAE,QAAA,gBACrCtC,OAAA;UAAKoC,SAAS,EAAC,mBAAmB;UAAAE,QAAA,GAC/B,CAAC,GAAGqB,KAAK,CAAC1B,SAAS,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAC9B9D,OAAA;YAAcoC,SAAS,EAAC,yBAAyB;YAAAE,QAAA,EAAC;UAAC,GAAxCwB,CAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA6C,CAC1D,CAAC,EACDhB,WAAW,iBAAInC,OAAA;YAAMoC,SAAS,EAAC,yBAAyB;YAAAE,QAAA,EAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACjE,CAAC,GAAGQ,KAAK,CAAC,CAAC,GAAG1B,SAAS,IAAIE,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACyB,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAC1D9D,OAAA;YAAcoC,SAAS,EAAC,uBAAuB;YAAAE,QAAA,EAAC;UAAC,GAAtCwB,CAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA2C,CACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNnD,OAAA;UAAMoC,SAAS,EAAC,4BAA4B;UAAAE,QAAA,GAAC,GAAC,EAACN,MAAM,EAAC,GAAC;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAGNnD,OAAA;QAAKoC,SAAS,EAAC,wBAAwB;QAAAE,QAAA,gBACrCtC,OAAA;UAAMoC,SAAS,EAAC,iCAAiC;UAAAE,QAAA,EAC9C5C,WAAW,CAACW,OAAO,CAACwB,KAAK,IAAI,CAAC;QAAC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,EACN9C,OAAO,CAACuB,aAAa,IAAIvB,OAAO,CAACuB,aAAa,GAAGvB,OAAO,CAACwB,KAAK,iBAC7D7B,OAAA;UAAMoC,SAAS,EAAC,yCAAyC;UAAAE,QAAA,EACtD5C,WAAW,CAACW,OAAO,CAACuB,aAAa;QAAC;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNnD,OAAA;QAAKoC,SAAS,EAAC,uBAAuB;QAAAE,QAAA,GAEnCjC,OAAO,CAAC0D,WAAW,iBAClB/D,OAAA;UAAGoC,SAAS,EAAC,oCAAoC;UAAAE,QAAA,EAC9CjC,OAAO,CAAC0D;QAAW;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACJ,eAGDnD,OAAA;UAAKoC,SAAS,EAAC,yDAAyD;UAAAE,QAAA,GACrEjC,OAAO,CAAC2D,KAAK,iBACZhE,OAAA;YAAMoC,SAAS,EAAC,UAAU;YAAAE,QAAA,gBACxBtC,OAAA;cAAMoC,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAC;YAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAAC9C,OAAO,CAAC2D,KAAK;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CACP,EACA9C,OAAO,CAAC4D,QAAQ,iBACfjE,OAAA;YAAMoC,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC7BtC,OAAA;cAAMoC,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAAC9C,OAAO,CAAC4D,QAAQ;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL,CAAC9C,OAAO,CAAC6D,mBAAmB,IAAI7D,OAAO,CAAC8D,oBAAoB,kBAC3DnE,OAAA;UAAKoC,SAAS,EAAC,6BAA6B;UAAAE,QAAA,gBAC1CtC,OAAA;YAAMoC,SAAS,EAAC,mCAAmC;YAAAE,QAAA,EAAC;UAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/DnD,OAAA;YAAKoC,SAAS,EAAC,0BAA0B;YAAAE,QAAA,gBACvCtC,OAAA;cAAKoC,SAAS,EAAC,qDAAqD;cAAAE,QAAA,eAClEtC,OAAA;gBACEoC,SAAS,EAAE,uBACT,CAAC/B,OAAO,CAAC6D,mBAAmB,IAAI7D,OAAO,CAAC8D,oBAAoB,KAAK,EAAE,GAAG,cAAc,GACpF,CAAC9D,OAAO,CAAC6D,mBAAmB,IAAI7D,OAAO,CAAC8D,oBAAoB,KAAK,EAAE,GAAG,eAAe,GAAG,YAAY,EACnG;gBACHxB,KAAK,EAAE;kBAAEE,KAAK,EAAE,GAAGxC,OAAO,CAAC6D,mBAAmB,IAAI7D,OAAO,CAAC8D,oBAAoB;gBAAI;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNnD,OAAA;cAAMoC,SAAS,EAAC,4BAA4B;cAAAE,QAAA,GAAEjC,OAAO,CAAC6D,mBAAmB,IAAI7D,OAAO,CAAC8D,oBAAoB,EAAC,MAAI;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA,CAAC9C,OAAO,CAAC+D,aAAa,KAAKC,SAAS,IAAIhE,OAAO,CAACiE,cAAc,KAAKD,SAAS,kBAC3ErE,OAAA;UAAKoC,SAAS,EAAC,mCAAmC;UAAAE,QAAA,gBAChDtC,OAAA;YAAMoC,SAAS,EAAC,mCAAmC;YAAAE,QAAA,EAAC;UAAM;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjEnD,OAAA;YAAMoC,SAAS,EAAE,uBACf,CAAC/B,OAAO,CAAC+D,aAAa,IAAI/D,OAAO,CAACiE,cAAc,IAAI,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAC7F;YAAAhC,QAAA,EACA,CAACjC,OAAO,CAAC+D,aAAa,IAAI/D,OAAO,CAACiE,cAAc,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG;UAAc;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL9C,OAAO,CAACkE,MAAM,iBACbvE,OAAA,CAACF,UAAU;QAACyE,MAAM,EAAElE,OAAO,CAACkE,MAAO;QAACC,OAAO,EAAE;MAAK;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACrD,eAGDnD,OAAA;QACEqC,OAAO,EAAExB,eAAgB;QACzB4D,QAAQ,EAAEnE,SAAS,IAAI,CAACD,OAAO,CAAC+D,aAAa,IAAI/D,OAAO,CAACiE,cAAc,IAAI,CAAC,KAAK,CAAE;QACnFlC,SAAS,EAAE,+EACT9B,SAAS,GACL,8CAA8C,GAC9C,CAACD,OAAO,CAAC+D,aAAa,IAAI/D,OAAO,CAACiE,cAAc,IAAI,CAAC,KAAK,CAAC,GAC3D,8CAA8C,GAC9C,0EAA0E,EAC7E;QAAAhC,QAAA,EAEFhC,SAAS,gBACRN,OAAA;UAAKoC,SAAS,EAAC,kCAAkC;UAAAE,QAAA,gBAC/CtC,OAAA;YAAKoC,SAAS,EAAC;UAAsF;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,aAE9G;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GACJ,CAAC9C,OAAO,CAAC+D,aAAa,IAAI/D,OAAO,CAACiE,cAAc,IAAI,CAAC,KAAK,CAAC,GAC7D,cAAc,GAEd;MACD;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;EAAA,QAhNuB3D,OAAO,EACFC,eAAe;AAAA,EA+M3C,CAAC;EAAA,QAhNsBD,OAAO,EACFC,eAAe;AAAA,EA+M1C;AAACiF,GAAA,GAnNGzE,2BAA2B;AAqNjCA,2BAA2B,CAAC0E,WAAW,GAAG,6BAA6B;AAEvE,eAAe1E,2BAA2B;AAAC,IAAAG,EAAA,EAAAsE,GAAA;AAAAE,YAAA,CAAAxE,EAAA;AAAAwE,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}