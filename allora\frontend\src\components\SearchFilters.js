import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '../config/api';

// Add custom CSS for slider styling
const sliderStyles = `
  .slider-green::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #10b981;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .slider-green::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #10b981;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .slider-green::-webkit-slider-track {
    background: linear-gradient(to right, #10b981 0%, #10b981 var(--value), #e5e7eb var(--value), #e5e7eb 100%);
    height: 8px;
    border-radius: 4px;
  }
`;

const SearchFilters = ({ 
  filters, 
  onFiltersChange, 
  onClearFilters,
  isLoading = false 
}) => {
  const [filterOptions, setFilterOptions] = useState({
    categories: [],
    brands: [],
    sellers: [],
    price_range: { min: 0, max: 10000 },
    rating_range: { min: 0, max: 5 },
    sustainability_range: { min: 0, max: 100 }
  });
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    brands: true,
    sellers: true,
    price: true,
    rating: true,
    sustainability: true
  });

  // Fetch filter options on component mount
  useEffect(() => {
    fetchFilterOptions();
  }, []);

  const fetchFilterOptions = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/search/filters`);
      if (response.ok) {
        const data = await response.json();
        setFilterOptions(data);
      }
    } catch (error) {
      console.error('Failed to fetch filter options:', error);
    }
  };

  const handleFilterChange = (filterType, value) => {
    const newFilters = { ...filters, [filterType]: value };
    onFiltersChange(newFilters);
  };

  const handlePriceRangeChange = (type, value) => {
    const newPriceRange = { ...filters.priceRange };
    newPriceRange[type] = parseFloat(value) || 0;
    handleFilterChange('priceRange', newPriceRange);
  };

  const handleRatingRangeChange = (type, value) => {
    const newRatingRange = { ...filters.ratingRange };
    newRatingRange[type] = parseFloat(value) || 0;
    handleFilterChange('ratingRange', newRatingRange);
  };

  const handleSustainabilityRangeChange = (type, value) => {
    const newSustainabilityRange = { ...filters.sustainabilityRange };
    newSustainabilityRange[type] = parseInt(value) || 0;
    handleFilterChange('sustainabilityRange', newSustainabilityRange);
  };

  const handleCategoryChange = (category) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];
    handleFilterChange('categories', newCategories);
  };

  const handleBrandChange = (brand) => {
    const newBrands = filters.brands.includes(brand)
      ? filters.brands.filter(b => b !== brand)
      : [...filters.brands, brand];
    handleFilterChange('brands', newBrands);
  };

  const handleSellerChange = (sellerId) => {
    const newSellers = filters.sellers?.includes(sellerId)
      ? filters.sellers.filter(s => s !== sellerId)
      : [...(filters.sellers || []), sellerId];
    handleFilterChange('sellers', newSellers);
  };

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.categories.length > 0) count++;
    if (filters.brands.length > 0) count++;
    if (filters.priceRange.min > filterOptions.price_range.min || 
        filters.priceRange.max < filterOptions.price_range.max) count++;
    if (filters.ratingRange.min > 0 || filters.ratingRange.max < 5) count++;
    if (filters.sustainabilityRange.min > 0 || filters.sustainabilityRange.max < 100) count++;
    if (filters.inStockOnly) count++;
    return count;
  };

  return (
    <>
      <style>{sliderStyles}</style>
      <div className="bg-white border-2 border-gray-200 rounded-2xl shadow-lg overflow-hidden">
        {/* Enhanced Filter Header */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Smart Filters</h3>
                <p className="text-sm text-gray-600">
                  {getActiveFiltersCount() > 0 ? `${getActiveFiltersCount()} filter${getActiveFiltersCount() > 1 ? 's' : ''} active` : 'Refine your search results'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {getActiveFiltersCount() > 0 && (
                <button
                  onClick={onClearFilters}
                  className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 font-medium rounded-lg transition-colors text-sm flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  Clear All
                </button>
              )}
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="lg:hidden p-2 bg-white rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
              >
                <svg className={`w-5 h-5 text-gray-600 transition-transform ${isExpanded ? 'rotate-180' : ''}`} fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Enhanced Filter Content */}
        <div className={`${isExpanded ? 'block' : 'hidden'} lg:block p-6 space-y-6`}>
        
        {/* Categories Filter */}
        <div className="border border-gray-200 rounded-xl overflow-hidden">
          <button
            onClick={() => toggleSection('categories')}
            className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">🏷️</span>
              <h4 className="font-semibold text-gray-900">Categories</h4>
              {filters.categories.length > 0 && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                  {filters.categories.length}
                </span>
              )}
            </div>
            <svg
              className={`w-5 h-5 text-gray-500 transition-transform ${expandedSections.categories ? 'rotate-180' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
          {expandedSections.categories && (
            <div className="p-4 space-y-3 max-h-48 overflow-y-auto">
              {filterOptions.categories.map((category) => (
                <label key={category} className="flex items-center group cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.categories.includes(category)}
                    onChange={() => handleCategoryChange(category)}
                    className="w-4 h-4 rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2"
                  />
                  <span className="ml-3 text-sm text-gray-700 group-hover:text-gray-900 transition-colors">{category}</span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Brands Filter */}
        <div className="border border-gray-200 rounded-xl overflow-hidden">
          <button
            onClick={() => toggleSection('brands')}
            className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">🏪</span>
              <h4 className="font-semibold text-gray-900">Brands</h4>
              {filters.brands.length > 0 && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                  {filters.brands.length}
                </span>
              )}
            </div>
            <svg
              className={`w-5 h-5 text-gray-500 transition-transform ${expandedSections.brands ? 'rotate-180' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
          {expandedSections.brands && (
            <div className="p-4 space-y-3 max-h-48 overflow-y-auto">
              {filterOptions.brands.map((brand) => (
                <label key={brand} className="flex items-center group cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.brands.includes(brand)}
                    onChange={() => handleBrandChange(brand)}
                    className="w-4 h-4 rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2"
                  />
                  <span className="ml-3 text-sm text-gray-700 group-hover:text-gray-900 transition-colors">{brand}</span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Sellers Filter */}
        <div className="border border-gray-200 rounded-xl overflow-hidden">
          <button
            onClick={() => toggleSection('sellers')}
            className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">🏪</span>
              <h4 className="font-semibold text-gray-900">Sellers</h4>
              {filters.sellers && filters.sellers.length > 0 && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                  {filters.sellers.length}
                </span>
              )}
            </div>
            <svg
              className={`w-5 h-5 text-gray-500 transition-transform ${expandedSections.sellers ? 'rotate-180' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
          {expandedSections.sellers && (
            <div className="p-4 space-y-3 max-h-48 overflow-y-auto">
              {filterOptions.sellers.map((seller) => (
                <label key={seller.id} className="flex items-center group cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.sellers?.includes(seller.id) || false}
                    onChange={() => handleSellerChange(seller.id)}
                    className="w-4 h-4 rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2"
                  />
                  <div className="ml-3 flex items-center gap-2">
                    <span className="text-sm text-gray-700 group-hover:text-gray-900 transition-colors">
                      {seller.store_name || seller.business_name}
                    </span>
                    {seller.is_verified && (
                      <span className="text-green-500 text-xs">✓</span>
                    )}
                  </div>
                </label>
              ))}
              {filterOptions.sellers.length === 0 && (
                <p className="text-sm text-gray-500 italic">No sellers available</p>
              )}
            </div>
          )}
        </div>

        {/* Price Range Filter */}
        <div className="border border-gray-200 rounded-xl overflow-hidden">
          <button
            onClick={() => toggleSection('price')}
            className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">💰</span>
              <h4 className="font-semibold text-gray-900">Price Range</h4>
              {(filters.priceRange.min > 0 || filters.priceRange.max < 10000) && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                  ₹{filters.priceRange.min}-{filters.priceRange.max}
                </span>
              )}
            </div>
            <svg
              className={`w-5 h-5 text-gray-500 transition-transform ${expandedSections.price ? 'rotate-180' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
          {expandedSections.price && (
            <div className="p-4 space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Min Price</label>
                  <input
                    type="number"
                    placeholder="₹0"
                    value={filters.priceRange.min || ''}
                    onChange={(e) => handlePriceRangeChange('min', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Max Price</label>
                  <input
                    type="number"
                    placeholder="₹10000"
                    value={filters.priceRange.max || ''}
                    onChange={(e) => handlePriceRangeChange('max', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>
              <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded-lg">
                Available range: ₹{filterOptions.price_range.min} - ₹{filterOptions.price_range.max}
              </div>
            </div>
          )}
        </div>

        {/* Rating Filter */}
        <div className="border border-gray-200 rounded-xl overflow-hidden">
          <button
            onClick={() => toggleSection('rating')}
            className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">⭐</span>
              <h4 className="font-semibold text-gray-900">Rating</h4>
              {filters.ratingRange.min > 0 && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                  {filters.ratingRange.min}+ stars
                </span>
              )}
            </div>
            <svg
              className={`w-5 h-5 text-gray-500 transition-transform ${expandedSections.rating ? 'rotate-180' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
          {expandedSections.rating && (
            <div className="p-4 space-y-3">
              {[4, 3, 2, 1].map((rating) => (
                <label key={rating} className="flex items-center group cursor-pointer p-2 rounded-lg hover:bg-gray-50">
                  <input
                    type="radio"
                    name="rating"
                    checked={filters.ratingRange.min === rating}
                    onChange={() => handleFilterChange('ratingRange', { min: rating, max: 5 })}
                    className="w-4 h-4 border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2"
                  />
                  <span className="ml-3 text-sm text-gray-700 group-hover:text-gray-900 flex items-center gap-1">
                    <span className="font-medium">{rating}+</span>
                    <span className="text-yellow-500">{'⭐'.repeat(rating)}</span>
                  </span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Sustainability Score Filter */}
        <div className="border border-gray-200 rounded-xl overflow-hidden">
          <button
            onClick={() => toggleSection('sustainability')}
            className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">🌱</span>
              <h4 className="font-semibold text-gray-900">Sustainability</h4>
              {filters.sustainabilityRange.min > 0 && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                  {filters.sustainabilityRange.min}+/100
                </span>
              )}
            </div>
            <svg
              className={`w-5 h-5 text-gray-500 transition-transform ${expandedSections.sustainability ? 'rotate-180' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
          {expandedSections.sustainability && (
            <div className="p-4 space-y-4">
              <div className="relative">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filters.sustainabilityRange.min}
                  onChange={(e) => handleSustainabilityRangeChange('min', e.target.value)}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-green"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0</span>
                  <span>50</span>
                  <span>100</span>
                </div>
              </div>
              <div className="text-center">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  {filters.sustainabilityRange.min}+ Sustainability Score
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Stock Filter */}
        <div className="border border-gray-200 rounded-xl overflow-hidden">
          <div className="p-4">
            <label className="flex items-center justify-between cursor-pointer group">
              <div className="flex items-center gap-2">
                <span className="text-lg">📦</span>
                <span className="font-semibold text-gray-900">In Stock Only</span>
              </div>
              <div className="relative">
                <input
                  type="checkbox"
                  checked={filters.inStockOnly}
                  onChange={(e) => handleFilterChange('inStockOnly', e.target.checked)}
                  className="sr-only"
                />
                <div className={`w-12 h-6 rounded-full transition-colors ${
                  filters.inStockOnly ? 'bg-green-500' : 'bg-gray-300'
                }`}>
                  <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform ${
                    filters.inStockOnly ? 'translate-x-6' : 'translate-x-0.5'
                  } mt-0.5`}></div>
                </div>
              </div>
            </label>
            {filters.inStockOnly && (
              <div className="mt-2 text-xs text-green-600 bg-green-50 p-2 rounded-lg">
                ✓ Only showing products currently in stock
              </div>
            )}
          </div>
        </div>

        {/* Clear Filters Button */}
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={onClearFilters}
            className="w-full px-4 py-3 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700 font-medium rounded-xl transition-all duration-200 flex items-center justify-center gap-2 group"
          >
            <svg className="w-4 h-4 group-hover:rotate-180 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
            Clear All Filters
          </button>
        </div>

        </div>
      </div>
    </>
  );
};

export default SearchFilters;
