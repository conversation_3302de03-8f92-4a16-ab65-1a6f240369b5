.admin-seller-management {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f5f9;
}

.management-header h1 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.back-btn {
    padding: 10px 20px;
    background: #64748b;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: #475569;
    transform: translateY(-1px);
}

.marketplace-stats {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-card h3 {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 8px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e293b;
}

.filters-section {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.filter-group {
    flex: 1;
}

.filter-select,
.search-input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    color: #475569;
    transition: all 0.2s ease;
}

.filter-select:focus,
.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.sellers-table {
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    margin-bottom: 20px;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 2fr 1fr 80px 1.2fr 100px 120px;
    background: #64748b;
    color: white;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-header > div {
    padding: 16px;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1.5fr 2fr 1fr 80px 1.2fr 100px 120px;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
}

.table-row:hover {
    background: #f8fafc;
}

.table-row > div {
    padding: 16px;
    display: flex;
    align-items: center;
    border-right: 1px solid #f1f5f9;
}

.seller-info {
    flex-direction: column;
    align-items: flex-start;
}

.business-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.store-name {
    font-size: 12px;
    color: #64748b;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    color: white;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.actions {
    gap: 8px;
}

.view-btn {
    padding: 6px 12px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
}

.page-btn {
    padding: 8px 16px;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    color: #475569;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
    border-color: #3b82f6;
    color: #3b82f6;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    color: #64748b;
    font-weight: 500;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-modal:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.modal-body {
    padding: 24px;
}

.seller-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.detail-section h3 {
    color: #1e293b;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #f1f5f9;
}

.detail-section p {
    margin: 8px 0;
    color: #475569;
    line-height: 1.5;
}

.detail-section strong {
    color: #1e293b;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.approve-btn {
    background: #10b981;
    color: white;
}

.reject-btn {
    background: #ef4444;
    color: white;
}

.suspend-btn {
    background: #f59e0b;
    color: white;
}

.commission-btn {
    background: #8b5cf6;
    color: white;
}

.approve-btn,
.reject-btn,
.suspend-btn,
.commission-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.approve-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.reject-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.suspend-btn:hover {
    background: #d97706;
    transform: translateY(-1px);
}

.commission-btn:hover {
    background: #7c3aed;
    transform: translateY(-1px);
}

.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-error {
    background: none;
    border: none;
    color: #dc2626;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f1f5f9;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .table-header,
    .table-row {
        grid-template-columns: 2fr 1.5fr 2fr 1fr 120px;
    }
    
    .table-header > div:nth-child(n+6),
    .table-row > div:nth-child(n+6) {
        display: none;
    }
}

@media (max-width: 768px) {
    .admin-seller-management {
        padding: 15px;
    }
    
    .management-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .filters-section {
        flex-direction: column;
        gap: 10px;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .table-header > div,
    .table-row > div {
        border-right: none;
        border-bottom: 1px solid #f1f5f9;
        padding: 12px;
    }
    
    .seller-details-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
