"""
Real-time Personalization Engine
===============================

Advanced personalization engine for real-time user preference learning,
contextual recommendations, and dynamic model updates.

Features:
- Real-time user profile management
- Dynamic preference learning
- Contextual recommendations
- Personalized ranking system
- Redis-based fast access
- A/B testing support

Author: Allora Development Team
Date: 2025-07-06
"""

import json
import redis
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
from collections import defaultdict, Counter
import hashlib

# Import architecture components
from recommendation_system_architecture import (
    UserProfile, RecommendationRequest, RecommendationResult,
    UserInteractionType, RecommendationType
)
from advanced_recommendation_models import RecommendationModelManager

class PersonalizationEngine:
    """
    Real-time personalization engine with dynamic preference learning
    """
    
    def __init__(self, redis_client=None, db_session=None):
        self.redis_client = redis_client
        self.db_session = db_session
        self.model_manager = RecommendationModelManager()
        
        # Configuration
        self.profile_cache_ttl = 3600  # 1 hour
        self.interaction_weight_decay = 0.95  # Daily decay factor
        self.min_interactions_for_personalization = 5
        self.context_weight = 0.3
        self.recency_weight = 0.4
        self.frequency_weight = 0.3
        
        # A/B testing configuration
        self.ab_test_variants = ['hybrid', 'collaborative_item', 'content_based']
        self.ab_test_split = [0.6, 0.3, 0.1]  # Distribution across variants
        
        # Initialize model manager
        if db_session:
            self.model_manager.load_models()
    
    def get_user_profile(self, user_id: str, force_refresh: bool = False) -> UserProfile:
        """Get or create user profile with caching"""
        cache_key = f"user_profile:{user_id}"
        
        # Try to get from cache first
        if not force_refresh and self.redis_client:
            try:
                cached_profile = self.redis_client.get(cache_key)
                if cached_profile:
                    profile_data = json.loads(cached_profile)
                    return UserProfile(**profile_data)
            except Exception as e:
                logging.warning(f"Error getting cached profile: {e}")
        
        # Build profile from interactions
        profile = self._build_user_profile(user_id)
        
        # Cache the profile
        if self.redis_client:
            try:
                self.redis_client.setex(
                    cache_key,
                    self.profile_cache_ttl,
                    json.dumps(profile.__dict__)
                )
            except Exception as e:
                logging.warning(f"Error caching profile: {e}")
        
        return profile
    
    def _build_user_profile(self, user_id: str) -> UserProfile:
        """Build user profile from interaction history"""
        try:
            # Get user interactions from database
            from app import UserInteractionLog, Product
            
            # Query recent interactions (last 90 days)
            cutoff_date = datetime.utcnow() - timedelta(days=90)
            
            interactions = self.db_session.query(UserInteractionLog).filter(
                UserInteractionLog.user_id == user_id,
                UserInteractionLog.timestamp >= cutoff_date
            ).order_by(UserInteractionLog.timestamp.desc()).all()
            
            if not interactions:
                return self._create_default_profile(user_id)
            
            # Analyze interactions to build preferences
            category_scores = defaultdict(float)
            brand_scores = defaultdict(float)
            price_ranges = []
            interaction_counts = Counter()
            
            total_weight = 0
            
            for interaction in interactions:
                # Calculate interaction weight (recency + type)
                days_ago = (datetime.utcnow() - interaction.timestamp).days
                recency_weight = self.interaction_weight_decay ** days_ago
                
                # Interaction type weights
                type_weights = {
                    'view': 1.0,
                    'click': 1.5,
                    'add_to_cart': 3.0,
                    'purchase': 5.0,
                    'rating': 4.0,
                    'wishlist_add': 2.0,
                    'share': 2.5
                }
                
                interaction_weight = recency_weight * type_weights.get(
                    interaction.interaction_type, 1.0
                )
                total_weight += interaction_weight
                
                # Get product details
                if interaction.product_id:
                    product = self.db_session.query(Product).filter(
                        Product.id == interaction.product_id
                    ).first()
                    
                    if product:
                        # Category preferences
                        if product.category:
                            category_scores[product.category] += interaction_weight
                        
                        # Brand preferences
                        if product.brand:
                            brand_scores[product.brand] += interaction_weight
                        
                        # Price preferences
                        if product.price:
                            price_ranges.append(product.price)
                
                # Count interaction types
                interaction_counts[interaction.interaction_type] += 1
            
            # Normalize scores
            if total_weight > 0:
                category_preferences = {
                    cat: score / total_weight 
                    for cat, score in category_scores.items()
                }
                brand_preferences = {
                    brand: score / total_weight 
                    for brand, score in brand_scores.items()
                }
            else:
                category_preferences = {}
                brand_preferences = {}
            
            # Calculate price preferences
            price_preferences = self._calculate_price_preferences(price_ranges)
            
            # Calculate engagement metrics
            engagement_score = min(1.0, len(interactions) / 100.0)  # Normalize to 0-1
            
            # Determine user segments
            segments = self._determine_user_segments(
                interaction_counts, category_preferences, price_preferences
            )
            
            return UserProfile(
                user_id=user_id,
                categories=category_preferences,
                brands=brand_preferences,
                price_range=price_preferences,
                interaction_history=dict(interaction_counts),
                engagement_score=engagement_score,
                segments=segments,
                last_updated=datetime.utcnow(),
                total_interactions=len(interactions)
            )
            
        except Exception as e:
            logging.error(f"Error building user profile: {e}")
            return self._create_default_profile(user_id)
    
    def _create_default_profile(self, user_id: str) -> UserProfile:
        """Create default profile for new users"""
        return UserProfile(
            user_id=user_id,
            categories={},
            brands={},
            price_range={'min': 0, 'max': 10000, 'preferred': 1000},
            interaction_history={},
            engagement_score=0.0,
            segments=['new_user'],
            last_updated=datetime.utcnow(),
            total_interactions=0
        )
    
    def _calculate_price_preferences(self, price_ranges: List[float]) -> Dict[str, float]:
        """Calculate user's price preferences"""
        if not price_ranges:
            return {'min': 0, 'max': 10000, 'preferred': 1000}
        
        prices = np.array(price_ranges)
        
        return {
            'min': float(np.min(prices)),
            'max': float(np.max(prices)),
            'preferred': float(np.median(prices)),
            'avg': float(np.mean(prices)),
            'std': float(np.std(prices))
        }
    
    def _determine_user_segments(self, interaction_counts: Counter, 
                                category_prefs: Dict, price_prefs: Dict) -> List[str]:
        """Determine user segments based on behavior"""
        segments = []
        
        # Engagement level
        total_interactions = sum(interaction_counts.values())
        if total_interactions > 50:
            segments.append('high_engagement')
        elif total_interactions > 10:
            segments.append('medium_engagement')
        else:
            segments.append('low_engagement')
        
        # Purchase behavior
        if interaction_counts.get('purchase', 0) > 5:
            segments.append('frequent_buyer')
        elif interaction_counts.get('purchase', 0) > 0:
            segments.append('occasional_buyer')
        else:
            segments.append('browser')
        
        # Price sensitivity
        if price_prefs.get('preferred', 1000) < 500:
            segments.append('budget_conscious')
        elif price_prefs.get('preferred', 1000) > 2000:
            segments.append('premium_buyer')
        else:
            segments.append('mid_range_buyer')
        
        # Category focus
        if len(category_prefs) <= 2:
            segments.append('focused_shopper')
        else:
            segments.append('diverse_shopper')
        
        return segments
    
    def update_user_profile(self, user_id: str, interaction_type: UserInteractionType,
                           product_id: str = None, context: Dict = None):
        """Update user profile based on new interaction"""
        try:
            # Get current profile
            profile = self.get_user_profile(user_id)
            
            # Update interaction history
            profile.interaction_history[interaction_type.value] = (
                profile.interaction_history.get(interaction_type.value, 0) + 1
            )
            profile.total_interactions += 1
            
            # Update engagement score
            profile.engagement_score = min(1.0, profile.total_interactions / 100.0)
            
            # If product interaction, update preferences
            if product_id and self.db_session:
                self._update_product_preferences(profile, product_id, interaction_type)
            
            # Update segments
            profile.segments = self._determine_user_segments(
                Counter(profile.interaction_history),
                profile.categories,
                profile.price_range
            )
            
            profile.last_updated = datetime.utcnow()
            
            # Cache updated profile
            if self.redis_client:
                cache_key = f"user_profile:{user_id}"
                self.redis_client.setex(
                    cache_key,
                    self.profile_cache_ttl,
                    json.dumps(profile.__dict__, default=str)
                )
            
            return profile
            
        except Exception as e:
            logging.error(f"Error updating user profile: {e}")
            return None
    
    def _update_product_preferences(self, profile: UserProfile, product_id: str,
                                   interaction_type: UserInteractionType):
        """Update product-based preferences"""
        try:
            from app import Product
            
            product = self.db_session.query(Product).filter(
                Product.id == product_id
            ).first()
            
            if not product:
                return
            
            # Interaction weights
            weights = {
                UserInteractionType.VIEW: 0.1,
                UserInteractionType.CLICK: 0.2,
                UserInteractionType.ADD_TO_CART: 0.5,
                UserInteractionType.PURCHASE: 1.0,
                UserInteractionType.RATING: 0.8,
                UserInteractionType.WISHLIST_ADD: 0.3
            }
            
            weight = weights.get(interaction_type, 0.1)
            
            # Update category preferences
            if product.category:
                current_score = profile.categories.get(product.category, 0)
                profile.categories[product.category] = current_score + weight
            
            # Update brand preferences
            if product.brand:
                current_score = profile.brands.get(product.brand, 0)
                profile.brands[product.brand] = current_score + weight
            
            # Update price preferences
            if product.price:
                current_preferred = profile.price_range.get('preferred', product.price)
                # Weighted average with new price
                total_interactions = profile.total_interactions
                if total_interactions > 1:
                    new_preferred = (
                        (current_preferred * (total_interactions - 1) + product.price * weight) /
                        total_interactions
                    )
                    profile.price_range['preferred'] = new_preferred
                
                # Update min/max if needed
                profile.price_range['min'] = min(
                    profile.price_range.get('min', product.price), product.price
                )
                profile.price_range['max'] = max(
                    profile.price_range.get('max', product.price), product.price
                )
            
        except Exception as e:
            logging.error(f"Error updating product preferences: {e}")
    
    def get_personalized_recommendations(self, request: RecommendationRequest) -> RecommendationResult:
        """Get personalized recommendations for a user"""
        try:
            # Get user profile
            profile = self.get_user_profile(request.user_id)
            
            # Determine algorithm variant (A/B testing)
            algorithm = self._get_algorithm_variant(request.user_id)
            
            # Get base recommendations
            recommendations = self.model_manager.get_recommendations(
                user_id=request.user_id,
                algorithm=algorithm,
                user_profile=profile.__dict__,
                n_recommendations=request.limit * 2  # Get more for filtering
            )
            
            # Apply personalization filters
            personalized_recs = self._apply_personalization_filters(
                recommendations, profile, request.context
            )
            
            # Apply contextual ranking
            ranked_recs = self._apply_contextual_ranking(
                personalized_recs, profile, request.context
            )
            
            # Limit results
            final_recs = ranked_recs[:request.limit]
            
            return RecommendationResult(
                user_id=request.user_id,
                recommendations=final_recs,
                algorithm=algorithm,
                context=request.context,
                timestamp=datetime.utcnow(),
                confidence_score=self._calculate_confidence_score(profile, final_recs)
            )
            
        except Exception as e:
            logging.error(f"Error getting personalized recommendations: {e}")
            return RecommendationResult(
                user_id=request.user_id,
                recommendations=[],
                algorithm='fallback',
                context=request.context,
                timestamp=datetime.utcnow(),
                confidence_score=0.0
            )
    
    def _get_algorithm_variant(self, user_id: str) -> str:
        """Get algorithm variant for A/B testing"""
        # Use user ID hash for consistent assignment
        user_hash = int(hashlib.md5(user_id.encode()).hexdigest(), 16)
        variant_index = user_hash % 100
        
        # Assign based on split percentages
        cumulative = 0
        for i, (variant, split) in enumerate(zip(self.ab_test_variants, self.ab_test_split)):
            cumulative += split * 100
            if variant_index < cumulative:
                return variant
        
        return self.ab_test_variants[0]  # Fallback
    
    def _apply_personalization_filters(self, recommendations: List[Tuple],
                                     profile: UserProfile, context: Dict) -> List[Tuple]:
        """Apply personalization filters to recommendations"""
        if not recommendations:
            return []
        
        filtered_recs = []
        
        for product_id, score in recommendations:
            # Apply profile-based scoring
            personalized_score = self._calculate_personalized_score(
                product_id, score, profile, context
            )
            
            if personalized_score > 0:  # Filter out negative scores
                filtered_recs.append((product_id, personalized_score))
        
        return filtered_recs
    
    def _calculate_personalized_score(self, product_id: str, base_score: float,
                                    profile: UserProfile, context: Dict) -> float:
        """Calculate personalized score for a product"""
        try:
            from app import Product
            
            product = self.db_session.query(Product).filter(
                Product.id == product_id
            ).first()
            
            if not product:
                return base_score
            
            personalization_boost = 0.0
            
            # Category preference boost
            if product.category in profile.categories:
                category_score = profile.categories[product.category]
                personalization_boost += category_score * 0.3
            
            # Brand preference boost
            if product.brand in profile.brands:
                brand_score = profile.brands[product.brand]
                personalization_boost += brand_score * 0.2
            
            # Price preference boost
            if product.price and 'preferred' in profile.price_range:
                preferred_price = profile.price_range['preferred']
                price_diff = abs(product.price - preferred_price) / preferred_price
                price_boost = max(0, 1 - price_diff) * 0.2
                personalization_boost += price_boost
            
            # Context-based boost
            if context:
                context_boost = self._calculate_context_boost(product, context)
                personalization_boost += context_boost * self.context_weight
            
            # Apply engagement score
            engagement_multiplier = 0.5 + (profile.engagement_score * 0.5)
            
            final_score = (base_score + personalization_boost) * engagement_multiplier
            
            return max(0, final_score)
            
        except Exception as e:
            logging.error(f"Error calculating personalized score: {e}")
            return base_score
    
    def _calculate_context_boost(self, product, context: Dict) -> float:
        """Calculate context-based score boost"""
        boost = 0.0
        
        # Time-based context
        if 'time_of_day' in context:
            # Example: boost certain categories at certain times
            time_hour = context['time_of_day']
            if product.category == 'electronics' and 18 <= time_hour <= 22:
                boost += 0.1  # Evening electronics boost
        
        # Device context
        if 'device_type' in context:
            if context['device_type'] == 'mobile' and product.category == 'mobile_accessories':
                boost += 0.15
        
        # Location context (if available)
        if 'location' in context and hasattr(product, 'availability_regions'):
            # Boost products available in user's region
            boost += 0.1
        
        # Seasonal context
        if 'season' in context:
            seasonal_categories = {
                'winter': ['clothing', 'home_heating'],
                'summer': ['outdoor', 'cooling'],
                'holiday': ['gifts', 'decorations']
            }
            
            season = context['season']
            if season in seasonal_categories and product.category in seasonal_categories[season]:
                boost += 0.2
        
        return boost
    
    def _apply_contextual_ranking(self, recommendations: List[Tuple],
                                profile: UserProfile, context: Dict) -> List[Tuple]:
        """Apply contextual ranking to recommendations"""
        # Sort by personalized score
        ranked = sorted(recommendations, key=lambda x: x[1], reverse=True)
        
        # Apply diversity filter to avoid too many similar items
        diversified = self._apply_diversity_filter(ranked, profile)
        
        return diversified
    
    def _apply_diversity_filter(self, recommendations: List[Tuple],
                              profile: UserProfile) -> List[Tuple]:
        """Apply diversity filter to recommendations"""
        if len(recommendations) <= 5:
            return recommendations
        
        diversified = []
        used_categories = set()
        used_brands = set()
        
        # First pass: ensure category diversity
        for product_id, score in recommendations:
            try:
                from app import Product
                product = self.db_session.query(Product).filter(
                    Product.id == product_id
                ).first()
                
                if product:
                    # Limit items per category (max 3)
                    category_count = sum(1 for p_id, _ in diversified 
                                       if self._get_product_category(p_id) == product.category)
                    
                    if category_count < 3:
                        diversified.append((product_id, score))
                        used_categories.add(product.category)
                        if product.brand:
                            used_brands.add(product.brand)
                else:
                    diversified.append((product_id, score))
                    
            except Exception:
                diversified.append((product_id, score))
        
        return diversified
    
    def _get_product_category(self, product_id: str) -> str:
        """Get product category"""
        try:
            from app import Product
            product = self.db_session.query(Product).filter(
                Product.id == product_id
            ).first()
            return product.category if product else 'unknown'
        except:
            return 'unknown'
    
    def _calculate_confidence_score(self, profile: UserProfile, 
                                  recommendations: List[Tuple]) -> float:
        """Calculate confidence score for recommendations"""
        if not recommendations:
            return 0.0
        
        # Base confidence on user profile completeness
        profile_completeness = 0.0
        
        if profile.categories:
            profile_completeness += 0.3
        if profile.brands:
            profile_completeness += 0.2
        if profile.total_interactions > 10:
            profile_completeness += 0.3
        if profile.engagement_score > 0.5:
            profile_completeness += 0.2
        
        # Adjust based on recommendation scores
        avg_score = np.mean([score for _, score in recommendations])
        score_confidence = min(1.0, avg_score / 5.0)  # Normalize to 0-1
        
        return (profile_completeness + score_confidence) / 2

# Export main class
__all__ = ['PersonalizationEngine']
