#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main app
try:
    from app import app
    print("✅ Successfully imported main app")
    
    # Check if routes are registered
    print(f"📊 Total routes registered: {len(list(app.url_map.iter_rules()))}")
    
    # Check for specific routes
    routes_to_check = ['/', '/api/products', '/api/signup']
    print(f"\n🎯 Checking specific routes:")
    for route in routes_to_check:
        found = False
        for rule in app.url_map.iter_rules():
            if rule.rule == route:
                print(f"  ✅ {route} -> {rule.endpoint} [{', '.join(rule.methods)}]")
                found = True
                break
        if not found:
            print(f"  ❌ {route} -> NOT FOUND")
    
    # Test the app context and manual route calling
    print(f"\n🧪 Testing manual route calling...")
    with app.app_context():
        print("  ✅ App context works")
        
        # Try to call the home function directly
        try:
            from app import home
            result = home()
            print(f"  ✅ Direct home() call works: {result}")
        except Exception as e:
            print(f"  ❌ Direct home() call failed: {e}")
            
        # Try to call get_products function directly
        try:
            from app import get_products
            result = get_products()
            print(f"  ✅ Direct get_products() call works: Status {result[1] if isinstance(result, tuple) else 'OK'}")
        except Exception as e:
            print(f"  ❌ Direct get_products() call failed: {e}")
            
except Exception as e:
    print(f"❌ Failed to import main app: {e}")
    import traceback
    traceback.print_exc()
