import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  MapPin,
  Calendar,
  AlertCircle,
  ExternalLink,
  RefreshCw,
  Phone,
  Mail
} from 'lucide-react';
import { API_BASE_URL } from '../config/api';

const OrderTracking = ({ orderId, trackingNumber, onClose }) => {
  const [trackingData, setTrackingData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (orderId || trackingNumber) {
      fetchTrackingData();
    }
  }, [orderId, trackingNumber]);

  const fetchTrackingData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const endpoint = orderId 
        ? `${API_BASE_URL}/api/fulfillment/orders/${orderId}/tracking`
        : `${API_BASE_URL}/api/fulfillment/track/${trackingNumber}`;

      const response = await fetch(endpoint);
      
      if (!response.ok) {
        throw new Error('Failed to fetch tracking information');
      }

      const data = await response.json();
      setTrackingData(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      case 'out_for_delivery':
        return <Truck className="w-6 h-6 text-blue-500" />;
      case 'in_transit':
        return <Package className="w-6 h-6 text-yellow-500" />;
      case 'exception':
        return <AlertCircle className="w-6 h-6 text-red-500" />;
      default:
        return <Clock className="w-6 h-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'out_for_delivery':
        return 'bg-blue-100 text-blue-800';
      case 'in_transit':
        return 'bg-yellow-100 text-yellow-800';
      case 'exception':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const formatEstimatedDelivery = (dateString) => {
    if (!dateString) return 'Not available';
    const date = new Date(dateString);
    const today = new Date();
    const diffTime = date - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays > 1) return `In ${diffDays} days`;
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="flex items-center justify-center">
            <RefreshCw className="w-8 h-8 animate-spin text-green-600" />
            <span className="ml-3 text-lg">Loading tracking information...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Tracking Error</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="flex space-x-3">
              <button
                onClick={() => fetchTrackingData()}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Order Tracking</h2>
            <p className="text-gray-600">
              {trackingData?.tracking_number && `Tracking: ${trackingData.tracking_number}`}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => fetchTrackingData(true)}
              disabled={refreshing}
              className="flex items-center px-3 py-2 text-green-600 hover:text-green-700 transition-colors"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6">
          {trackingData ? (
            <div className="space-y-8">
              {/* Current Status */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(trackingData.current_status)}
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">
                        {trackingData.current_status?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </h3>
                      <p className="text-gray-600">
                        Last updated: {formatDate(trackingData.last_updated)}
                      </p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(trackingData.current_status)}`}>
                    {trackingData.current_status?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </div>

                {/* Delivery Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Estimated Delivery</p>
                      <p className="font-medium text-gray-900">
                        {formatEstimatedDelivery(trackingData.estimated_delivery)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Truck className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Carrier</p>
                      <p className="font-medium text-gray-900">
                        {trackingData.carrier_name || trackingData.carrier_code}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tracking Timeline */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Tracking History</h4>
                <div className="space-y-4">
                  {trackingData.events && trackingData.events.length > 0 ? (
                    trackingData.events.map((event, index) => (
                      <div key={index} className="flex items-start space-x-4 pb-4 border-b border-gray-100 last:border-b-0">
                        <div className="flex-shrink-0 mt-1">
                          {getStatusIcon(event.status)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h5 className="text-sm font-medium text-gray-900">
                              {event.description || event.status?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </h5>
                            <span className="text-sm text-gray-500">
                              {formatDate(event.timestamp)}
                            </span>
                          </div>
                          {event.location && (
                            <div className="flex items-center mt-1">
                              <MapPin className="w-4 h-4 text-gray-400 mr-1" />
                              <span className="text-sm text-gray-600">{event.location}</span>
                            </div>
                          )}
                          {event.facility_name && (
                            <p className="text-sm text-gray-600 mt-1">
                              Facility: {event.facility_name}
                            </p>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Package className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-gray-500">No tracking events available yet</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Additional Information */}
              {(trackingData.origin_location || trackingData.destination_location) && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">Shipping Details</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {trackingData.origin_location && (
                      <div>
                        <p className="text-sm text-gray-600">From</p>
                        <p className="font-medium text-gray-900">{trackingData.origin_location}</p>
                      </div>
                    )}
                    {trackingData.destination_location && (
                      <div>
                        <p className="text-sm text-gray-600">To</p>
                        <p className="font-medium text-gray-900">{trackingData.destination_location}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Support Information */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Need Help?</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Phone className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Customer Support</p>
                      <p className="font-medium text-gray-900">1-800-ALLORA</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Email Support</p>
                      <p className="font-medium text-gray-900"><EMAIL></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Tracking Information</h3>
              <p className="text-gray-600">Tracking information is not available for this order yet.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderTracking;
