import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { API_BASE_URL } from '../config/api';
import ModernMinimalistProductCard from '../components/ModernMinimalistProductCard';
import { useNotification } from '../contexts/NotificationContext';
import './SellerStorePage.css';

const SellerStorePage = () => {
    const { storeSlug } = useParams();
    const [searchParams, setSearchParams] = useSearchParams();
    const { showNotification } = useNotification();

    const [store, setStore] = useState(null);
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({});

    // Filter states
    const [currentPage, setCurrentPage] = useState(1);
    const [category, setCategory] = useState('');
    const [sortBy, setSortBy] = useState('name');
    const [sortOrder, setSortOrder] = useState('asc');

    useEffect(() => {
        // Get filters from URL params
        setCurrentPage(parseInt(searchParams.get('page')) || 1);
        setCategory(searchParams.get('category') || '');
        setSortBy(searchParams.get('sort_by') || 'name');
        setSortOrder(searchParams.get('sort_order') || 'asc');
    }, [searchParams]);

    useEffect(() => {
        fetchSellerStore();
    }, [storeSlug, currentPage, category, sortBy, sortOrder]);

    const fetchSellerStore = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams({
                page: currentPage.toString(),
                per_page: '20',
                sort_by: sortBy,
                sort_order: sortOrder
            });

            if (category) {
                params.append('category', category);
            }

            const response = await fetch(`${API_BASE_URL}/sellers/${storeSlug}/store?${params}`);
            if (!response.ok) {
                throw new Error('Failed to fetch seller store');
            }

            const data = await response.json();
            setStore(data.store);
            setProducts(data.products);
            setPagination(data.pagination);
        } catch (err) {
            setError(err.message);
            showNotification('Failed to load seller store', 'error');
        } finally {
            setLoading(false);
        }
    };

    const updateFilters = (newFilters) => {
        const params = new URLSearchParams(searchParams);
        
        Object.entries(newFilters).forEach(([key, value]) => {
            if (value) {
                params.set(key, value);
            } else {
                params.delete(key);
            }
        });

        setSearchParams(params);
    };

    const handleCategoryChange = (newCategory) => {
        setCategory(newCategory);
        setCurrentPage(1);
        updateFilters({ category: newCategory, page: 1 });
    };

    const handleSortChange = (newSortBy, newSortOrder) => {
        setSortBy(newSortBy);
        setSortOrder(newSortOrder);
        updateFilters({ sort_by: newSortBy, sort_order: newSortOrder });
    };

    const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
        updateFilters({ page: newPage });
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(<span key={i} className="star filled">★</span>);
        }

        if (hasHalfStar) {
            stars.push(<span key="half" className="star half">★</span>);
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(<span key={`empty-${i}`} className="star empty">☆</span>);
        }

        return stars;
    };

    if (loading) {
        return (
            <div className="seller-store-loading">
                <div className="loading-spinner"></div>
                <p>Loading seller store...</p>
            </div>
        );
    }

    if (error || !store) {
        return (
            <div className="seller-store-error">
                <h2>Store Not Found</h2>
                <p>{error || 'The requested seller store could not be found.'}</p>
            </div>
        );
    }

    return (
        <div className="seller-store-page">
            {/* Store Header */}
            <div className="store-header">
                <div className="store-banner">
                    {store.store_banner && (
                        <img src={store.store_banner} alt={store.store_name} className="banner-image" />
                    )}
                    <div className="banner-overlay"></div>
                </div>

                <div className="store-info-container">
                    <div className="store-main-info">
                        {store.store_logo && (
                            <div className="store-logo">
                                <img src={store.store_logo} alt={store.store_name} />
                            </div>
                        )}
                        <div className="store-details">
                            <h1 className="store-name">{store.store_name}</h1>
                            <p className="business-name">{store.business_name}</p>
                            {store.contact_person && (
                                <p className="contact-person">by {store.contact_person}</p>
                            )}
                            
                            <div className="store-stats">
                                <div className="stat-item">
                                    <div className="rating-display">
                                        <div className="stars">
                                            {renderStars(store.rating)}
                                        </div>
                                        <span className="rating-value">({store.rating})</span>
                                    </div>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-label">Products:</span>
                                    <span className="stat-value">{store.total_products}</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-label">Orders:</span>
                                    <span className="stat-value">{store.total_orders}</span>
                                </div>
                                {store.is_verified && (
                                    <div className="verified-badge">
                                        <span className="verified-icon">✓</span>
                                        <span className="verified-text">Verified</span>
                                    </div>
                                )}
                            </div>

                            {store.store_description && (
                                <p className="store-description">{store.store_description}</p>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Filters and Sorting */}
            <div className="store-filters">
                <div className="filter-section">
                    <div className="category-filter">
                        <label>Category:</label>
                        <select 
                            value={category} 
                            onChange={(e) => handleCategoryChange(e.target.value)}
                        >
                            <option value="">All Categories</option>
                            {store.categories.map(cat => (
                                <option key={cat} value={cat}>{cat}</option>
                            ))}
                        </select>
                    </div>

                    <div className="sort-filter">
                        <label>Sort by:</label>
                        <select 
                            value={`${sortBy}-${sortOrder}`}
                            onChange={(e) => {
                                const [newSortBy, newSortOrder] = e.target.value.split('-');
                                handleSortChange(newSortBy, newSortOrder);
                            }}
                        >
                            <option value="name-asc">Name (A-Z)</option>
                            <option value="name-desc">Name (Z-A)</option>
                            <option value="price-asc">Price (Low to High)</option>
                            <option value="price-desc">Price (High to Low)</option>
                            <option value="rating-desc">Rating (High to Low)</option>
                            <option value="rating-asc">Rating (Low to High)</option>
                        </select>
                    </div>
                </div>

                <div className="results-info">
                    <p>Showing {products.length} of {pagination.total} products</p>
                </div>
            </div>

            {/* Products Grid */}
            <div className="store-products">
                {products.length > 0 ? (
                    <div className="products-grid">
                        {products.map(product => (
                            <ModernMinimalistProductCard 
                                key={product.id} 
                                product={product} 
                            />
                        ))}
                    </div>
                ) : (
                    <div className="no-products">
                        <h3>No Products Found</h3>
                        <p>This store doesn't have any products matching your criteria.</p>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
                <div className="pagination">
                    <button 
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={!pagination.has_prev}
                        className="pagination-btn"
                    >
                        Previous
                    </button>
                    
                    <div className="page-numbers">
                        {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                            const pageNum = Math.max(1, currentPage - 2) + i;
                            if (pageNum <= pagination.pages) {
                                return (
                                    <button
                                        key={pageNum}
                                        onClick={() => handlePageChange(pageNum)}
                                        className={`page-btn ${pageNum === currentPage ? 'active' : ''}`}
                                    >
                                        {pageNum}
                                    </button>
                                );
                            }
                            return null;
                        })}
                    </div>

                    <button 
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={!pagination.has_next}
                        className="pagination-btn"
                    >
                        Next
                    </button>
                </div>
            )}
        </div>
    );
};

export default SellerStorePage;
