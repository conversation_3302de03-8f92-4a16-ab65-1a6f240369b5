{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\allora_project\\\\allora\\\\frontend\\\\src\\\\contexts\\\\CartContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect, useCallback } from 'react';\nimport { API_BASE_URL } from '../config/api';\nimport { useWebSocket } from './WebSocketContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartContext = /*#__PURE__*/createContext();\nexport const useCart = () => {\n  _s();\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n_s(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const CartProvider = ({\n  children\n}) => {\n  _s2();\n  const [cartItems, setCartItems] = useState([]);\n  const [cartCount, setCartCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [guestSession, setGuestSession] = useState(null);\n  const {\n    addEventListener,\n    isConnected\n  } = useWebSocket();\n\n  // Define createGuestSession first\n  const createGuestSession = useCallback(async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/guest/session`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({})\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setGuestSession(data.session_id);\n        localStorage.setItem('guest_session_id', data.session_id);\n        return data.session_id;\n      } else {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || 'Failed to create guest session');\n      }\n    } catch (error) {\n      console.error('Failed to create guest session:', error);\n      throw error;\n    }\n  }, []);\n\n  // Initialize guest session\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      const existingSession = localStorage.getItem('guest_session_id');\n      if (existingSession) {\n        setGuestSession(existingSession);\n      } else {\n        createGuestSession();\n      }\n    }\n  }, [createGuestSession]);\n\n  // Fetch cart items and update count\n  const fetchCart = useCallback(async () => {\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token');\n      let response;\n      if (token) {\n        // Authenticated user cart\n        response = await fetch(`${API_BASE_URL}/cart`, {\n          headers: {\n            'Authorization': token\n          }\n        });\n      } else if (guestSession) {\n        // Guest user cart\n        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`);\n      } else {\n        setLoading(false);\n        return;\n      }\n      if (response.ok) {\n        const data = await response.json();\n        // Handle both old format (direct array) and new format (with items property)\n        const items = Array.isArray(data) ? data : data.items || [];\n        setCartItems(items);\n\n        // Calculate total quantity for cart count\n        const totalCount = items.reduce((total, item) => total + (item.quantity || 0), 0);\n        setCartCount(totalCount);\n      } else {\n        setCartItems([]);\n        setCartCount(0);\n      }\n    } catch (error) {\n      console.error('Failed to fetch cart:', error);\n      setCartItems([]);\n      setCartCount(0);\n    } finally {\n      setLoading(false);\n    }\n  }, [guestSession]);\n\n  // Add item to cart\n  const addToCart = async (productId, quantity = 1) => {\n    try {\n      // Validate input parameters\n      if (productId === null || productId === undefined || productId === '' || productId === 0) {\n        throw new Error('Product ID is required');\n      }\n\n      // Convert to string first, then parse to handle various input types\n      const productIdStr = String(productId).trim();\n      const quantityStr = String(quantity).trim();\n\n      // Ensure productId and quantity are integers\n      const validProductId = parseInt(productIdStr, 10);\n      const validQuantity = parseInt(quantityStr, 10);\n      if (isNaN(validProductId) || validProductId <= 0) {\n        throw new Error(`Invalid product ID: ${productId}`);\n      }\n      if (isNaN(validQuantity) || validQuantity <= 0) {\n        throw new Error(`Invalid quantity: ${quantity}`);\n      }\n      const token = localStorage.getItem('token');\n      let response;\n      if (token) {\n        // Authenticated user\n        response = await fetch(`${API_BASE_URL}/cart`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token\n          },\n          body: JSON.stringify({\n            product_id: validProductId,\n            quantity: validQuantity\n          })\n        });\n      } else {\n        // Guest user - ensure we have a session\n        let currentGuestSession = guestSession || localStorage.getItem('guest_session_id');\n        if (!currentGuestSession) {\n          // Create guest session if it doesn't exist\n          currentGuestSession = await createGuestSession();\n        }\n        if (currentGuestSession) {\n          response = await fetch(`${API_BASE_URL}/guest/cart/${currentGuestSession}`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              product_id: validProductId,\n              quantity: validQuantity\n            })\n          });\n        } else {\n          throw new Error('Failed to create guest session');\n        }\n      }\n      if (response && response.ok) {\n        // Refresh cart after adding\n        await fetchCart();\n        return true;\n      } else {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || 'Failed to add item to cart');\n      }\n    } catch (error) {\n      console.error('Failed to add to cart:', error);\n      throw error; // Re-throw to let the calling component handle it\n    }\n  };\n\n  // Update cart item quantity\n  const updateCartItem = async (cartItemId, quantity) => {\n    try {\n      const token = localStorage.getItem('token');\n      let response;\n      if (token) {\n        response = await fetch(`${API_BASE_URL}/cart`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token\n          },\n          body: JSON.stringify({\n            cart_item_id: cartItemId,\n            quantity\n          })\n        });\n      } else if (guestSession) {\n        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            cart_item_id: cartItemId,\n            quantity\n          })\n        });\n      }\n      if (response && response.ok) {\n        await fetchCart();\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Failed to update cart item:', error);\n      return false;\n    }\n  };\n\n  // Remove item from cart\n  const removeFromCart = async cartItemId => {\n    try {\n      const token = localStorage.getItem('token');\n      let response;\n      if (token) {\n        response = await fetch(`${API_BASE_URL}/cart`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token\n          },\n          body: JSON.stringify({\n            cart_item_id: cartItemId\n          })\n        });\n      } else if (guestSession) {\n        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            cart_item_id: cartItemId\n          })\n        });\n      }\n      if (response && response.ok) {\n        await fetchCart();\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Failed to remove from cart:', error);\n      return false;\n    }\n  };\n\n  // Clear entire cart\n  const clearCart = () => {\n    setCartItems([]);\n    setCartCount(0);\n  };\n\n  // Fetch cart on mount and when authentication changes\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token || guestSession) {\n      fetchCart();\n    }\n  }, [guestSession, fetchCart]);\n\n  // Listen for storage changes (login/logout)\n  useEffect(() => {\n    const handleStorageChange = () => {\n      fetchCart();\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, [fetchCart]);\n  const value = {\n    cartItems,\n    cartCount,\n    loading,\n    guestSession,\n    fetchCart,\n    addToCart,\n    updateCartItem,\n    removeFromCart,\n    clearCart\n  };\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 5\n  }, this);\n};\n_s2(CartProvider, \"fMY4XRpoYCRdeymbnvPcsVvhb8k=\", false, function () {\n  return [useWebSocket];\n});\n_c = CartProvider;\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "useCallback", "API_BASE_URL", "useWebSocket", "jsxDEV", "_jsxDEV", "CartContext", "useCart", "_s", "context", "Error", "CartProvider", "children", "_s2", "cartItems", "setCartItems", "cartCount", "setCartCount", "loading", "setLoading", "guestSession", "setGuestSession", "addEventListener", "isConnected", "createGuestSession", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "data", "json", "session_id", "localStorage", "setItem", "errorData", "catch", "error", "console", "token", "getItem", "existingSession", "fetchCart", "items", "Array", "isArray", "totalCount", "reduce", "total", "item", "quantity", "addToCart", "productId", "undefined", "productIdStr", "String", "trim", "quantityStr", "validProductId", "parseInt", "validQuantity", "isNaN", "product_id", "currentGuestSession", "updateCartItem", "cartItemId", "cart_item_id", "removeFromCart", "clearCart", "handleStorageChange", "window", "removeEventListener", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/allora_project/allora/frontend/src/contexts/CartContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';\nimport { API_BASE_URL } from '../config/api';\nimport { useWebSocket } from './WebSocketContext';\n\nconst CartContext = createContext();\n\nexport const useCart = () => {\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n\nexport const CartProvider = ({ children }) => {\n  const [cartItems, setCartItems] = useState([]);\n  const [cartCount, setCartCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [guestSession, setGuestSession] = useState(null);\n  const { addEventListener, isConnected } = useWebSocket();\n\n  // Define createGuestSession first\n  const createGuestSession = useCallback(async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/guest/session`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({})\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setGuestSession(data.session_id);\n        localStorage.setItem('guest_session_id', data.session_id);\n        return data.session_id;\n      } else {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || 'Failed to create guest session');\n      }\n    } catch (error) {\n      console.error('Failed to create guest session:', error);\n      throw error;\n    }\n  }, []);\n\n  // Initialize guest session\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      const existingSession = localStorage.getItem('guest_session_id');\n      if (existingSession) {\n        setGuestSession(existingSession);\n      } else {\n        createGuestSession();\n      }\n    }\n  }, [createGuestSession]);\n\n  // Fetch cart items and update count\n  const fetchCart = useCallback(async () => {\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token');\n      let response;\n\n      if (token) {\n        // Authenticated user cart\n        response = await fetch(`${API_BASE_URL}/cart`, {\n          headers: { 'Authorization': token },\n        });\n      } else if (guestSession) {\n        // Guest user cart\n        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`);\n      } else {\n        setLoading(false);\n        return;\n      }\n\n      if (response.ok) {\n        const data = await response.json();\n        // Handle both old format (direct array) and new format (with items property)\n        const items = Array.isArray(data) ? data : (data.items || []);\n        setCartItems(items);\n\n        // Calculate total quantity for cart count\n        const totalCount = items.reduce((total, item) => total + (item.quantity || 0), 0);\n        setCartCount(totalCount);\n      } else {\n        setCartItems([]);\n        setCartCount(0);\n      }\n    } catch (error) {\n      console.error('Failed to fetch cart:', error);\n      setCartItems([]);\n      setCartCount(0);\n    } finally {\n      setLoading(false);\n    }\n  }, [guestSession]);\n\n  // Add item to cart\n  const addToCart = async (productId, quantity = 1) => {\n    try {\n      // Validate input parameters\n      if (productId === null || productId === undefined || productId === '' || productId === 0) {\n        throw new Error('Product ID is required');\n      }\n\n      // Convert to string first, then parse to handle various input types\n      const productIdStr = String(productId).trim();\n      const quantityStr = String(quantity).trim();\n\n      // Ensure productId and quantity are integers\n      const validProductId = parseInt(productIdStr, 10);\n      const validQuantity = parseInt(quantityStr, 10);\n\n      if (isNaN(validProductId) || validProductId <= 0) {\n        throw new Error(`Invalid product ID: ${productId}`);\n      }\n\n      if (isNaN(validQuantity) || validQuantity <= 0) {\n        throw new Error(`Invalid quantity: ${quantity}`);\n      }\n\n      const token = localStorage.getItem('token');\n      let response;\n\n      if (token) {\n        // Authenticated user\n        response = await fetch(`${API_BASE_URL}/cart`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token,\n          },\n          body: JSON.stringify({ product_id: validProductId, quantity: validQuantity }),\n        });\n      } else {\n        // Guest user - ensure we have a session\n        let currentGuestSession = guestSession || localStorage.getItem('guest_session_id');\n\n        if (!currentGuestSession) {\n          // Create guest session if it doesn't exist\n          currentGuestSession = await createGuestSession();\n        }\n\n        if (currentGuestSession) {\n          response = await fetch(`${API_BASE_URL}/guest/cart/${currentGuestSession}`, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify({ product_id: validProductId, quantity: validQuantity }),\n          });\n        } else {\n          throw new Error('Failed to create guest session');\n        }\n      }\n\n      if (response && response.ok) {\n        // Refresh cart after adding\n        await fetchCart();\n        return true;\n      } else {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || 'Failed to add item to cart');\n      }\n    } catch (error) {\n      console.error('Failed to add to cart:', error);\n      throw error; // Re-throw to let the calling component handle it\n    }\n  };\n\n  // Update cart item quantity\n  const updateCartItem = async (cartItemId, quantity) => {\n    try {\n      const token = localStorage.getItem('token');\n      let response;\n\n      if (token) {\n        response = await fetch(`${API_BASE_URL}/cart`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token,\n          },\n          body: JSON.stringify({ cart_item_id: cartItemId, quantity }),\n        });\n      } else if (guestSession) {\n        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`, {\n          method: 'PUT',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ cart_item_id: cartItemId, quantity }),\n        });\n      }\n\n      if (response && response.ok) {\n        await fetchCart();\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Failed to update cart item:', error);\n      return false;\n    }\n  };\n\n  // Remove item from cart\n  const removeFromCart = async (cartItemId) => {\n    try {\n      const token = localStorage.getItem('token');\n      let response;\n\n      if (token) {\n        response = await fetch(`${API_BASE_URL}/cart`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': token,\n          },\n          body: JSON.stringify({ cart_item_id: cartItemId }),\n        });\n      } else if (guestSession) {\n        response = await fetch(`${API_BASE_URL}/guest/cart/${guestSession}`, {\n          method: 'DELETE',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ cart_item_id: cartItemId }),\n        });\n      }\n\n      if (response && response.ok) {\n        await fetchCart();\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Failed to remove from cart:', error);\n      return false;\n    }\n  };\n\n  // Clear entire cart\n  const clearCart = () => {\n    setCartItems([]);\n    setCartCount(0);\n  };\n\n  // Fetch cart on mount and when authentication changes\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token || guestSession) {\n      fetchCart();\n    }\n  }, [guestSession, fetchCart]);\n\n  // Listen for storage changes (login/logout)\n  useEffect(() => {\n    const handleStorageChange = () => {\n      fetchCart();\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, [fetchCart]);\n\n  const value = {\n    cartItems,\n    cartCount,\n    loading,\n    guestSession,\n    fetchCart,\n    addToCart,\n    updateCartItem,\n    removeFromCart,\n    clearCart\n  };\n\n  return (\n    <CartContext.Provider value={value}>\n      {children}\n    </CartContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC1F,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,YAAY,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,gBAAGT,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMU,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGX,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM;IAAEuB,gBAAgB;IAAEC;EAAY,CAAC,GAAGpB,YAAY,CAAC,CAAC;;EAExD;EACA,MAAMqB,kBAAkB,GAAGvB,WAAW,CAAC,YAAY;IACjD,IAAI;MACF,MAAMwB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,gBAAgB,EAAE;QAC5DyB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC;MAEF,IAAIN,QAAQ,CAACO,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QAClCb,eAAe,CAACY,IAAI,CAACE,UAAU,CAAC;QAChCC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEJ,IAAI,CAACE,UAAU,CAAC;QACzD,OAAOF,IAAI,CAACE,UAAU;MACxB,CAAC,MAAM;QACL,MAAMG,SAAS,GAAG,MAAMb,QAAQ,CAACS,IAAI,CAAC,CAAC,CAACK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,IAAI7B,KAAK,CAAC4B,SAAS,CAACE,KAAK,IAAI,gCAAgC,CAAC;MACtE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxC,SAAS,CAAC,MAAM;IACd,MAAM0C,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACD,KAAK,EAAE;MACV,MAAME,eAAe,GAAGR,YAAY,CAACO,OAAO,CAAC,kBAAkB,CAAC;MAChE,IAAIC,eAAe,EAAE;QACnBvB,eAAe,CAACuB,eAAe,CAAC;MAClC,CAAC,MAAM;QACLpB,kBAAkB,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMqB,SAAS,GAAG5C,WAAW,CAAC,YAAY;IACxCkB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMuB,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIlB,QAAQ;MAEZ,IAAIiB,KAAK,EAAE;QACT;QACAjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,OAAO,EAAE;UAC7C0B,OAAO,EAAE;YAAE,eAAe,EAAEc;UAAM;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAItB,YAAY,EAAE;QACvB;QACAK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,eAAekB,YAAY,EAAE,CAAC;MACtE,CAAC,MAAM;QACLD,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAIM,QAAQ,CAACO,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QAClC;QACA,MAAMY,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACf,IAAI,CAAC,GAAGA,IAAI,GAAIA,IAAI,CAACa,KAAK,IAAI,EAAG;QAC7D/B,YAAY,CAAC+B,KAAK,CAAC;;QAEnB;QACA,MAAMG,UAAU,GAAGH,KAAK,CAACI,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,IAAIC,IAAI,CAACC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QACjFpC,YAAY,CAACgC,UAAU,CAAC;MAC1B,CAAC,MAAM;QACLlC,YAAY,CAAC,EAAE,CAAC;QAChBE,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CzB,YAAY,CAAC,EAAE,CAAC;MAChBE,YAAY,CAAC,CAAC,CAAC;IACjB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACC,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMkC,SAAS,GAAG,MAAAA,CAAOC,SAAS,EAAEF,QAAQ,GAAG,CAAC,KAAK;IACnD,IAAI;MACF;MACA,IAAIE,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKC,SAAS,IAAID,SAAS,KAAK,EAAE,IAAIA,SAAS,KAAK,CAAC,EAAE;QACxF,MAAM,IAAI7C,KAAK,CAAC,wBAAwB,CAAC;MAC3C;;MAEA;MACA,MAAM+C,YAAY,GAAGC,MAAM,CAACH,SAAS,CAAC,CAACI,IAAI,CAAC,CAAC;MAC7C,MAAMC,WAAW,GAAGF,MAAM,CAACL,QAAQ,CAAC,CAACM,IAAI,CAAC,CAAC;;MAE3C;MACA,MAAME,cAAc,GAAGC,QAAQ,CAACL,YAAY,EAAE,EAAE,CAAC;MACjD,MAAMM,aAAa,GAAGD,QAAQ,CAACF,WAAW,EAAE,EAAE,CAAC;MAE/C,IAAII,KAAK,CAACH,cAAc,CAAC,IAAIA,cAAc,IAAI,CAAC,EAAE;QAChD,MAAM,IAAInD,KAAK,CAAC,uBAAuB6C,SAAS,EAAE,CAAC;MACrD;MAEA,IAAIS,KAAK,CAACD,aAAa,CAAC,IAAIA,aAAa,IAAI,CAAC,EAAE;QAC9C,MAAM,IAAIrD,KAAK,CAAC,qBAAqB2C,QAAQ,EAAE,CAAC;MAClD;MAEA,MAAMX,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIlB,QAAQ;MAEZ,IAAIiB,KAAK,EAAE;QACT;QACAjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,OAAO,EAAE;UAC7CyB,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAEc;UACnB,CAAC;UACDb,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEkC,UAAU,EAAEJ,cAAc;YAAER,QAAQ,EAAEU;UAAc,CAAC;QAC9E,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAIG,mBAAmB,GAAG9C,YAAY,IAAIgB,YAAY,CAACO,OAAO,CAAC,kBAAkB,CAAC;QAElF,IAAI,CAACuB,mBAAmB,EAAE;UACxB;UACAA,mBAAmB,GAAG,MAAM1C,kBAAkB,CAAC,CAAC;QAClD;QAEA,IAAI0C,mBAAmB,EAAE;UACvBzC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,eAAegE,mBAAmB,EAAE,EAAE;YAC1EvC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cAAE,cAAc,EAAE;YAAmB,CAAC;YAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cAAEkC,UAAU,EAAEJ,cAAc;cAAER,QAAQ,EAAEU;YAAc,CAAC;UAC9E,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,MAAM,IAAIrD,KAAK,CAAC,gCAAgC,CAAC;QACnD;MACF;MAEA,IAAIe,QAAQ,IAAIA,QAAQ,CAACO,EAAE,EAAE;QAC3B;QACA,MAAMa,SAAS,CAAC,CAAC;QACjB,OAAO,IAAI;MACb,CAAC,MAAM;QACL,MAAMP,SAAS,GAAG,MAAMb,QAAQ,CAACS,IAAI,CAAC,CAAC,CAACK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,IAAI7B,KAAK,CAAC4B,SAAS,CAACE,KAAK,IAAI,4BAA4B,CAAC;MAClE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAAC,CAAC;IACf;EACF,CAAC;;EAED;EACA,MAAM2B,cAAc,GAAG,MAAAA,CAAOC,UAAU,EAAEf,QAAQ,KAAK;IACrD,IAAI;MACF,MAAMX,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIlB,QAAQ;MAEZ,IAAIiB,KAAK,EAAE;QACTjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,OAAO,EAAE;UAC7CyB,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAEc;UACnB,CAAC;UACDb,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEsC,YAAY,EAAED,UAAU;YAAEf;UAAS,CAAC;QAC7D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIjC,YAAY,EAAE;QACvBK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,eAAekB,YAAY,EAAE,EAAE;UACnEO,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEsC,YAAY,EAAED,UAAU;YAAEf;UAAS,CAAC;QAC7D,CAAC,CAAC;MACJ;MAEA,IAAI5B,QAAQ,IAAIA,QAAQ,CAACO,EAAE,EAAE;QAC3B,MAAMa,SAAS,CAAC,CAAC;QACjB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAM8B,cAAc,GAAG,MAAOF,UAAU,IAAK;IAC3C,IAAI;MACF,MAAM1B,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIlB,QAAQ;MAEZ,IAAIiB,KAAK,EAAE;QACTjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,OAAO,EAAE;UAC7CyB,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAEc;UACnB,CAAC;UACDb,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEsC,YAAY,EAAED;UAAW,CAAC;QACnD,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIhD,YAAY,EAAE;QACvBK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxB,YAAY,eAAekB,YAAY,EAAE,EAAE;UACnEO,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEsC,YAAY,EAAED;UAAW,CAAC;QACnD,CAAC,CAAC;MACJ;MAEA,IAAI3C,QAAQ,IAAIA,QAAQ,CAACO,EAAE,EAAE;QAC3B,MAAMa,SAAS,CAAC,CAAC;QACjB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAM+B,SAAS,GAAGA,CAAA,KAAM;IACtBxD,YAAY,CAAC,EAAE,CAAC;IAChBE,YAAY,CAAC,CAAC,CAAC;EACjB,CAAC;;EAED;EACAjB,SAAS,CAAC,MAAM;IACd,MAAM0C,KAAK,GAAGN,YAAY,CAACO,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAID,KAAK,IAAItB,YAAY,EAAE;MACzByB,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACzB,YAAY,EAAEyB,SAAS,CAAC,CAAC;;EAE7B;EACA7C,SAAS,CAAC,MAAM;IACd,MAAMwE,mBAAmB,GAAGA,CAAA,KAAM;MAChC3B,SAAS,CAAC,CAAC;IACb,CAAC;IAED4B,MAAM,CAACnD,gBAAgB,CAAC,SAAS,EAAEkD,mBAAmB,CAAC;IACvD,OAAO,MAAMC,MAAM,CAACC,mBAAmB,CAAC,SAAS,EAAEF,mBAAmB,CAAC;EACzE,CAAC,EAAE,CAAC3B,SAAS,CAAC,CAAC;EAEf,MAAM8B,KAAK,GAAG;IACZ7D,SAAS;IACTE,SAAS;IACTE,OAAO;IACPE,YAAY;IACZyB,SAAS;IACTS,SAAS;IACTa,cAAc;IACdG,cAAc;IACdC;EACF,CAAC;EAED,oBACElE,OAAA,CAACC,WAAW,CAACsE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/D,QAAA,EAChCA;EAAQ;IAAAiE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACnE,GAAA,CA1QWF,YAAY;EAAA,QAKmBR,YAAY;AAAA;AAAA8E,EAAA,GAL3CtE,YAAY;AAAA,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}