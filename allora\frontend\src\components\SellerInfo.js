import React from 'react';
import { Link } from 'react-router-dom';
import './SellerInfo.css';

const SellerInfo = ({ seller, compact = false }) => {
    if (!seller) return null;

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(<span key={i} className="star filled">★</span>);
        }

        if (hasHalfStar) {
            stars.push(<span key="half" className="star half">★</span>);
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(<span key={`empty-${i}`} className="star empty">☆</span>);
        }

        return stars;
    };

    if (compact) {
        return (
            <div className="seller-info-compact">
                <div className="seller-compact-header">
                    <span className="sold-by-label">Sold by:</span>
                    <Link to={`/seller/${seller.store_slug}`} className="seller-name-link">
                        {seller.store_name || seller.business_name}
                    </Link>
                    {seller.is_verified && (
                        <span className="verified-badge-compact">
                            <span className="verified-icon">✓</span>
                        </span>
                    )}
                </div>
                <div className="seller-compact-rating">
                    <div className="stars">
                        {renderStars(seller.rating)}
                    </div>
                    <span className="rating-text">({seller.rating})</span>
                </div>
            </div>
        );
    }

    return (
        <div className="seller-info">
            <div className="seller-info-header">
                <h3>Seller Information</h3>
                {seller.is_verified && (
                    <div className="verified-badge">
                        <span className="verified-icon">✓</span>
                        <span className="verified-text">Verified Seller</span>
                    </div>
                )}
            </div>

            <div className="seller-details">
                <div className="seller-main-info">
                    {seller.store_logo && (
                        <div className="seller-logo">
                            <img src={seller.store_logo} alt={seller.store_name} />
                        </div>
                    )}
                    <div className="seller-text-info">
                        <h4 className="seller-store-name">
                            <Link to={`/seller/${seller.store_slug}`}>
                                {seller.store_name || seller.business_name}
                            </Link>
                        </h4>
                        <p className="seller-business-name">{seller.business_name}</p>
                        {seller.contact_person && (
                            <p className="seller-contact">Contact: {seller.contact_person}</p>
                        )}
                    </div>
                </div>

                <div className="seller-stats-grid">
                    <div className="stat-item">
                        <span className="stat-label">Rating</span>
                        <div className="rating-display">
                            <div className="stars">
                                {renderStars(seller.rating)}
                            </div>
                            <span className="rating-value">({seller.rating})</span>
                        </div>
                    </div>

                    <div className="stat-item">
                        <span className="stat-label">Products</span>
                        <span className="stat-value">{seller.total_products}</span>
                    </div>

                    {seller.commission_rate !== undefined && seller.commission_rate > 0 && (
                        <div className="stat-item">
                            <span className="stat-label">Commission</span>
                            <span className="stat-value">{seller.commission_rate}%</span>
                        </div>
                    )}
                </div>

                {seller.store_description && (
                    <div className="seller-description">
                        <p>{seller.store_description}</p>
                    </div>
                )}

                <div className="seller-actions">
                    <Link to={`/seller/${seller.store_slug}`} className="view-store-btn">
                        Visit Store
                    </Link>
                    <Link to={`/products?seller_id=${seller.id}`} className="view-products-btn">
                        View All Products
                    </Link>
                    {seller.email && (
                        <a href={`mailto:${seller.email}`} className="contact-seller-btn">
                            Contact Seller
                        </a>
                    )}
                </div>
            </div>
        </div>
    );
};

export default SellerInfo;
